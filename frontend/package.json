{"name": "stAdmin", "version": "1.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "cross-env REACT_APP_ENV=prod  APP_TITLE='拼搏者CRM' max build ", "build:jmt": "cross-env REACT_APP_ENV=prod APP_TITLE='嘉明特设备管理系统' HOMEPATH='/iframe-view?url=%2Fconsole%2Fdashboard-operator' max build ", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "cross-env UMI_ENV=dev umi-serve ", "start": "cross-env UMI_ENV=dev max dev MOCK=none ", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@alilc/lowcode-plugin-components-pane": "^2.0.0", "@alilc/lowcode-plugin-inject": "^1.2.1", "@alilc/lowcode-plugin-undo-redo": "^1.0.0", "@alilc/lowcode-react-renderer": "^1.1.2", "@alilc/lowcode-setter-behavior": "^1.0.0", "@alilc/lowcode-setter-title": "^1.0.2", "@alilc/lowcode-utils": "^1.3.2", "@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.48", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.7", "@mui/x-data-grid": "^7.27.3", "@superset-ui/embedded-sdk": "^0.1.3", "@umijs/route-utils": "^2.2.2", "@wangeditor-next/core": "^1.5.6", "@wangeditor-next/editor": "^5.5.0", "@wangeditor-next/editor-for-react": "^1.0.7", "ace-builds": "^1.36.2", "ag-grid-react": "^33.1.1", "ahooks": "^3.8.0", "antd": "^5.13.2", "antd-style": "^3.6.1", "classnames": "^2.5.1", "decimal.js": "^10.5.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lru-cache": "^11.0.0", "lu-lowcode-materials": "^1.2.8", "lu-lowcode-package-form": "^0.11.81", "md5": "^2.3.0", "moment": "^2.30.1", "omit.js": "^2.0.2", "querystring": "^0.2.1", "rc-menu": "^9.12.4", "rc-util": "^5.38.1", "rc-virtual-list": "^3.14.5", "react": "^18.2.0", "react-ace": "^12.0.0", "react-data-grid": "^7.0.0-beta.48", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-quill": "^2.0.0", "react-resizable": "^3.0.5", "react-shadow": "^20.4.0", "socket.io-client": "^4.8.1", "xflow-automation": "^0.0.18", "xflow-extend": "^1.0.11"}, "devDependencies": {"@alilc/lowcode-engine": "^1.1.2", "@alilc/lowcode-engine-ext": "^1.0.0", "@alilc/lowcode-types": "^1.1.1", "@ant-design/pro-cli": "^3.3.0", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@types/socket.io-client": "^3.0.0", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.1.1", "@umijs/max": "^4.1.1", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^8.56.0", "express": "^4.18.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "postcss-import": "^16.1.0", "postcss-nested": "^6.0.1", "prettier": "^2.8.8", "react-dev-inspector": "^1.9.0", "swagger-ui-dist": "^4.19.1", "tailwindcss": "^3.4.3", "ts-node": "^10.9.2", "typescript": "^5.3.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}