import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { ProTable } from '@ant-design/pro-components';
import { Resizable } from 'react-resizable';
// import 'react-resizable/css/styles.css';
import { createStyles } from 'antd-style';
import { throttle } from 'lodash';
import { createPortal } from 'react-dom';

const useStyles = createStyles(({ css }) => ({
  resizableHeader: css`
    position: relative;
    
    .react-resizable {
      position: relative;
      background-clip: padding-box;
    }
    
    .react-resizable-handle {
      position: absolute;
      right: -5px;
      bottom: 0;
      z-index: 10;
      width: 10px;
      height: 100%;
      cursor: col-resize;
    }
    
  `,
}));

// 可调整大小的表头组件
const ResizableTitle = (props) => {
  const { onResize, onResizeStart, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => e.stopPropagation()}
        />
      }
      onResize={onResize}
      onResizeStart={onResizeStart}
      draggableOpts={{
        enableUserSelectHack: false,
      }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

const ResizableProTable = ({ columns, tableKey, onColumnResize, ...restProps }) => {
  const { styles } = useStyles();
  const [resizableColumns, setResizableColumns] = useState([]);
  const [columnsWidth, setColumnsWidth] = useState({});

  // 使用ref管理拖动状态，避免不必要的重渲染
  const resizingStateRef = useRef(null);
  // 用于线条位置的状态
  const [resizeLinePosition, setResizeLinePosition] = useState(null);

  // 使用ref记录鼠标事件处理函数
  const mouseMoveHandlerRef = useRef(null);
  const mouseUpHandlerRef = useRef(null);

  // 计算表格总宽度
  const tableWidth = useMemo(() => {
    if (!columns) return 0;

    // 递归计算列宽（包括子列）
    const calculateColumnWidth = (column, columnIndex) => {
      // 如果列有子列，递归计算子列总宽度
      if (column.children && column.children.length > 0) {
        return column.children.reduce((subtotal, childColumn, childIndex) => {
          // 为子列构建一个唯一索引
          const childFullIndex = `${columnIndex}-${childIndex}`;
          return subtotal + calculateColumnWidth(childColumn, childFullIndex);
        }, 0);
      }

      // 没有子列时返回当前列宽度
      return columnsWidth[columnIndex] || column.width || 150;
    };

    return columns.reduce((total, column, index) => {
      return total + calculateColumnWidth(column, index);
    }, 0);
  }, [columns, columnsWidth]);

  // 加载保存的列宽
  useEffect(() => {
    if (!tableKey) return;

    try {
      const savedColumnsWidth = JSON.parse(localStorage.getItem(`table_columns_width_${tableKey}`) || '{}');
      setColumnsWidth(savedColumnsWidth);
    } catch (e) {
      console.error("Error loading saved column widths", e);
    }
  }, [tableKey]);

  // 更高效的鼠标移动处理函数
  const createMouseMoveHandler = useCallback(() => {
    // 使用 requestAnimationFrame 提高性能
    let animationFrameId = null;

    return throttle((e) => {
      if (!resizingStateRef.current) return;

      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {
        setResizeLinePosition(e.clientX);
      });
    }, 10);  // 使用更低的节流时间以提高响应性
  }, []);

  // 拖动开始时的处理函数
  const handleResizeStart = useCallback((index) => (e, { node }) => {
    e.stopPropagation();

    // 设置拖动状态
    resizingStateRef.current = {
      index,
      startX: e.clientX,
      initialWidth: columnsWidth[index] || 150,
    };

    // 创建鼠标移动处理函数
    const mouseMoveHandler = createMouseMoveHandler();
    mouseMoveHandlerRef.current = mouseMoveHandler;

    // 设置初始指示线位置
    setResizeLinePosition(e.clientX);

    // 创建鼠标释放处理函数
    const mouseUpHandler = (e) => {
      if (!resizingStateRef.current) return;

      // 计算新宽度
      const state = resizingStateRef.current;
      const deltaX = e.clientX - state.startX;
      const newWidth = Math.max(state.initialWidth + deltaX, 50);

      // 更新列宽状态
      const newColumnsWidth = { ...columnsWidth };
      newColumnsWidth[state.index] = newWidth;
      setColumnsWidth(newColumnsWidth);

      // 保存到localStorage
      if (tableKey) {
        localStorage.setItem(`table_columns_width_${tableKey}`, JSON.stringify(newColumnsWidth));
      }

      // 调用回调
      if (typeof onColumnResize === "function") {
        onColumnResize(newColumnsWidth);
      }

      // 清理
      document.removeEventListener('mousemove', mouseMoveHandlerRef.current);
      document.removeEventListener('mouseup', mouseUpHandlerRef.current);
      mouseMoveHandlerRef.current = null;
      mouseUpHandlerRef.current = null;
      resizingStateRef.current = null;
      setTimeout(() => {
        setResizeLinePosition(null);
      }, 50);
    };

    mouseUpHandlerRef.current = mouseUpHandler;

    // 添加全局事件监听
    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);

    // 阻止默认行为
    e.preventDefault();
  }, [columnsWidth, createMouseMoveHandler, onColumnResize, tableKey]);

  // 用于调整列宽的处理函数（供 Resizable 组件使用）
  const handleResize = useCallback((index) => (e, { size }) => {
    // 只需要更新指示线位置
    if (resizingStateRef.current && resizingStateRef.current.index === index) {
      setResizeLinePosition(e.clientX);
    }
  }, []);

  // 更新列配置
  useEffect(() => {
    if (!columns) return;

    console.log('[ResizableProTable] 原始columns:', columns.map(col => ({ dataIndex: col.dataIndex, fixed: col.fixed, title: col.title })));

    // 递归处理列和子列
    const processColumns = (cols, parentIndex = '') => {
      return cols.map((col, index) => {
        // 为每列创建唯一索引
        const fullIndex = parentIndex ? `${parentIndex}-${index}` : `${index}`;

        // 处理子列
        if (col.children && col.children.length > 0) {
          return {
            ...col,
            children: processColumns(col.children, fullIndex)
          };
        }

        // 为每个列设置固定宽度，避免自动分配宽度
        const width = columnsWidth[fullIndex] || col.width || 150;

        const processedCol = {
          ...col,
          width,
          // 明确保留fixed属性
          fixed: col.fixed,
          // 为了兼容性，同时设置其他可能的固定列属性
          ...(col.fixed === 'left' && { fixed: 'left' }),
          ...(col.fixed === 'right' && { fixed: 'right' }),
          onHeaderCell: (column) => ({
            width,
            onResizeStart: handleResizeStart(fullIndex),
            onResize: handleResize(fullIndex),
          }),
        };

        // 调试日志
        if (col.dataIndex === 'option') {
          console.log('[ResizableProTable] 处理操作列:', {
            原始fixed: col.fixed,
            处理后fixed: processedCol.fixed,
            width: processedCol.width
          });
        }

        return processedCol;
      });
    };

    const processedColumns = processColumns(columns);
    console.log('[ResizableProTable] 处理后columns:', processedColumns.map(col => ({ dataIndex: col.dataIndex, fixed: col.fixed, title: col.title })));
    setResizableColumns(processedColumns);
  }, [columns, columnsWidth, handleResizeStart, handleResize]);

  // 清理函数
  useEffect(() => {
    return () => {
      // 组件卸载时清理全局事件监听
      if (mouseMoveHandlerRef.current) {
        document.removeEventListener('mousemove', mouseMoveHandlerRef.current);
        mouseMoveHandlerRef.current = null;
      }

      if (mouseUpHandlerRef.current) {
        document.removeEventListener('mouseup', mouseUpHandlerRef.current);
        mouseUpHandlerRef.current = null;
      }
    };
  }, []);

  // 表格组件配置
  const components = {
    header: {
      cell: ResizableTitle,
    },
  };

  // 构建 scroll 配置，确保水平滚动正常
  const scroll = {
    ...(restProps.scroll || {}),
    x: tableWidth, // 设置表格的水平滚动宽度为所有列宽之和
  };

  return (
    <div className={styles.resizableHeader}>
      { resizeLinePosition !== null && createPortal(
        <div
          style={{
            position: 'fixed',
            top: 0,
            bottom: 0,
            width: '2px',
            backgroundColor: '#1890ff',
            zIndex: 9999,
            pointerEvents: 'none',
            opacity: 0.8,
            left: `${resizeLinePosition}px`,
            willChange: 'left'
          }}
        />,
        document.body
      )}
      {console.log('[ResizableProTable] 传递给ProTable的columns:', resizableColumns.map(col => ({ dataIndex: col.dataIndex, fixed: col.fixed, title: col.title })))}
      <ProTable
        {...restProps}
        columns={resizableColumns}
        components={components}
        // 设置表格布局为固定布局，防止列宽自动调整
        tableLayout="fixed"
        // 应用计算出的滚动配置
        scroll={scroll}
        // 确保表格容器可以横向滚动，不会因为总宽度限制而挤压各列
        style={{
          ...restProps.style,
          width: 'auto',
          overflowX: 'auto'
        }}
      />
    </div>
  );
};

export default ResizableProTable;
