import React, {useEffect, useState} from 'react';
import {
  DndContext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';

import {useSortable} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
const SortItem = ({item, index,renderItem, keyName})=>{
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({id: item[keyName]});
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  
  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      {renderItem(item, index,listeners,isDragging)}
    </div>
  );
}
const SortList = ({ items, setItems, renderItem,keyName = "key" })=> {

  const handleDragEnd = (event) => {
    const {active, over} = event;
    
    // 检查 over 是否存在，如果拖拽到无效区域，over 会是 null
    if (over && active.id !== over.id) {
      setItems((items) => {
        console.log("items",items)
        console.log("active",active,"over",over)
        const oldIndex = items.findIndex(item=>item[keyName]===active.id);
        const newIndex = items.findIndex(item=>item[keyName]===over.id);
        console.log("oldIndex",oldIndex,"newIndex",newIndex)
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  return (
    <DndContext 
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext 
        items={items.map(item=>item[keyName])}
        strategy={verticalListSortingStrategy}
      >
        {items.map((item,index) => {
          return <SortItem key={item[keyName]} keyName={keyName} item={item} index={index} renderItem={renderItem} />
        })}
      </SortableContext>
    </DndContext>
  );
  

}
export {SortList}