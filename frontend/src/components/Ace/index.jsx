import React, { forwardRef } from "react";
import { render } from "react-dom";
import AceEditor from "react-ace";

import "ace-builds/src-noconflict/mode-javascript";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/theme-monokai";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";
import 'ace-builds/src-noconflict/snippets/javascript';
import 'ace-builds/src-noconflict/snippets/sql';


const Ace = forwardRef(({value, onChange, placeholder, readOnly = false, theme = "monokai", mode = "javascript"}, ref) => {
    
    const handleChange = (change) => {
       typeof onChange=="function" &&  onChange(change)
    }
    return <AceEditor
        ref={ref}
        placeholder={placeholder}
        mode={mode}
        width="100%"
        height="100%"
        theme={theme}
        name="blah2"
        onChange={handleChange}
        debounceChangePeriod={100}
        fontSize={16}
        lineHeight={19}
        showPrintMargin={true}
        showGutter={true}
        highlightActiveLine={true}
        value={value}
        readOnly={readOnly}
        
        setOptions={{
            enableBasicAutocompletion: true,
            enableLiveAutocompletion: true,
            enableSnippets: true,
            showLineNumbers: true,
            tabSize: 2,
        }} />
});

export { Ace };

export * from "./editCode";