
import { useSearchParams } from 'umi';
import { useEffect, useState, useRef } from 'react';
import { Spin } from 'antd';
import { embedDashboard } from '@superset-ui/embedded-sdk';
import { GetGuestToken } from '@/services/crm-api/superset';

const SupersetDashboard = () => {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [iframeKey, setIframeKey] = useState(0); // 添加iframeKey状态用于强制重新加载
  const dashboardId = searchParams.get('id') || '';

  // 用于存储 ResizeObserver 实例
  const resizeObserverRef = useRef();
  const iframeRef = useRef(null);
  // 添加一个额外的useEffect来处理窗口大小变化
  useEffect(() => {
    const handleWindowResize = () => {
      console.log('窗口大小变化，尝试更新iframe高度');
      if (iframeRef.current) {
        // 触发iframe内容高度重新计算
        try {
          const iframe = iframeRef.current;
          const doc = iframe.contentDocument;
          if (doc && doc.body) {
            // 模拟内容变化，触发ResizeObserver
            doc.body.style.minHeight = `${doc.body.offsetHeight + 1}px`;
            setTimeout(() => {
              doc.body.style.minHeight = '';
            }, 50);
          }
        } catch (e) {
          console.error('窗口大小变化处理异常:', e);
        }
      }
    };

    window.addEventListener('resize', handleWindowResize);

    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, []);

  useEffect(() => {
    console.log('开始初始化dashboard, ID:', dashboardId);
    const initDashboard = async () => {
      try {
        setLoading(true);
        const response = await GetGuestToken(dashboardId);

        if (response.code !== 0) {
          throw new Error(response.message || '获取访问令牌失败');
        }

        let { token, url, uuid } = response.data;
        if (!url) url = window.location.origin;
        console.log('Embedding dashboard with:', {
          dashboardId,
          uuid,
          supersetDomain: url,
          token: token.substring(0, 20) + '...' // 只打印token的前20个字符
        });

        const dashboard = await embedDashboard({
          id: uuid, // 确保这个ID是正确的UUID格式
          supersetDomain: url,
          mountPoint: document.getElementById('superset_dashboard'),
          fetchGuestToken: async () => token,
          dashboardUiConfig: {
            hideTitle: true,
            urlParams: {
              responsive:true
            },
            // hideTab: false,
            // hideChartControls: true,
          },
        });

        // 获取iframe元素并保存到ref中
        const iframe = document.getElementById('superset_dashboard')?.querySelector('iframe');
        iframeRef.current = iframe;

        if (iframe) {
          iframe.style.width = '100%';
          // 初始高度设置为100%，后续会根据内容动态调整
          iframe.style.height = '100%';
          console.log('iframe元素已找到，初始样式已设置');
        } else {
          console.warn('未找到iframe元素');
        }


        const handleLoad = () => {
          try {
            console.log("iframe加载完成，开始处理高度自适应", iframe);

            // 确保iframe存在
            if (!iframe) {
              console.warn("handleLoad中iframe不存在");
              return;
            }

            // 尝试获取iframe的contentDocument
            const doc = iframe.contentDocument;
            if (!doc) {
              console.warn("无法获取iframe的contentDocument，可能是跨域限制");
              return;
            }

            // 初始高度设置函数
            const updateHeight = () => {
              try {
                // 等待一小段时间确保内容已完全渲染
                setTimeout(() => {
                  // 获取内容高度的多种方式
                  const height = Math.max(
                    doc.body.scrollHeight || 0,
                    doc.documentElement.scrollHeight || 0,
                    doc.body.offsetHeight || 0,
                    doc.documentElement.offsetHeight || 0,
                    doc.body.clientHeight || 0,
                    doc.documentElement.clientHeight || 0
                  );

                  console.log("计算得到的iframe内容高度:", height);

                  // 只有当高度有效且大于最小值时才设置
                  if (height > 100) {
                    iframe.style.height = `${height}px`;
                    console.log("已更新iframe高度为:", height);
                  } else {
                    console.warn("计算得到的高度异常，保持原高度");
                  }
                }, 300); // 延迟300ms确保内容已渲染
              } catch (err) {
                console.error("更新高度过程中出错:", err);
              }
            };

            // 立即执行一次高度更新
            updateHeight();

            // 清理之前的ResizeObserver
            if (resizeObserverRef.current) {
              console.log("清理之前的ResizeObserver");
              resizeObserverRef.current.disconnect();
            }

            // 创建新的ResizeObserver监听内容变化
            try {
              resizeObserverRef.current = new ResizeObserver((entries) => {
                console.log("ResizeObserver检测到变化", entries.length);
                updateHeight();
              });
              resizeObserverRef.current.observe(doc.body);
              console.log("ResizeObserver已设置，监听body元素变化");

              // 同时监听主要内容容器的变化
              const contentContainer = doc.querySelector('.dashboard');
              if (contentContainer) {
                resizeObserverRef.current.observe(contentContainer);
                console.log("ResizeObserver已设置，监听dashboard容器变化");
              }
            } catch (observerError) {
              console.error("设置ResizeObserver失败:", observerError);
            }

            // 监听子页面窗口大小变化
            try {
              doc.defaultView?.addEventListener('resize', updateHeight);
              console.log("已添加窗口resize事件监听");
            } catch (resizeError) {
              console.error("添加resize事件监听失败:", resizeError);
            }

            // 监听内容变化的MutationObserver
            try {
              const mutationObserver = new MutationObserver(() => {
                console.log("MutationObserver检测到DOM变化");
                updateHeight();
              });

              mutationObserver.observe(doc.body, {
                childList: true,
                subtree: true,
                attributes: true
              });

              // 保存到ref中以便后续清理
              iframe.mutationObserver = mutationObserver;
              console.log("MutationObserver已设置，监听DOM变化");
            } catch (mutationError) {
              console.error("设置MutationObserver失败:", mutationError);
            }

          } catch (e) {
            console.error('高度自适应处理异常:', e);
          }
        };
        // 绑定加载事件
        iframe.addEventListener('load', handleLoad);

        // 清理函数
        return () => {
          console.log('执行清理函数');
          if (iframe) {
            iframe.removeEventListener('load', handleLoad);

            // 清理MutationObserver
            if (iframe.mutationObserver) {
              iframe.mutationObserver.disconnect();
              console.log('已清理MutationObserver');
            }

            // 重置高度
            iframe.style.height = 'auto';
            console.log('已重置iframe高度');
          }

          // 清理ResizeObserver
          if (resizeObserverRef.current) {
            resizeObserverRef.current.disconnect();
            console.log('已清理ResizeObserver');
          }

          // 强制重新加载 iframe 当 URL 改变时
          setIframeKey(prev => prev + 1);
          console.log('已增加iframeKey，强制重新加载');
        };


      } catch (err) {
        console.error('Dashboard initialization failed:', err);
        setError(`初始化失败: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    if (dashboardId) {
      initDashboard();
    }

    return () => {
      // 清理工作（如果需要）
    };
  }, [dashboardId]);

  if (error) {
    return (
      <div className="error-message" style={{ padding: '20px', color: 'red' }}>
        <h3>加载失败</h3>
        <p>{error}</p>
        <pre style={{ whiteSpace: 'pre-wrap' }}>
          Dashboard ID: {dashboardId}
        </pre>
      </div>
    );
  }

  return (
    <div style={{ width: '100%', height: 'calc(100vh - 48px)', overflow: 'hidden' }}>
      {loading && (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      )}

      <div
        id="superset_dashboard"
        key={iframeKey} // 使用key强制重新渲染
        style={{
          width: '100%',
          height: '100%',
          display: loading ? 'none' : 'block',
        }}
      />
    </div>
  );
};

export default SupersetDashboard;
