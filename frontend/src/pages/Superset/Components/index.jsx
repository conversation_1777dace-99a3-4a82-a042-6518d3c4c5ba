import React, { useEffect, useRef, useState } from 'react';
import { jwtDecode } from 'jwt-decode';
import { FetchRefreshToken } from '@/services/crm-api/login';

/**
 * SupersetComponent
 *
 * This component embeds a Superset dashboard in an iframe and handles JWT authentication.
 *
 * @param {Object} props
 * @param {string} props.dashboardUrl
 */
const SupersetComponent = ({
    dashboardUrl,
}) => {
  const iframeRef = useRef(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to get token only once at component mount
  const getToken = async () => {
    try {
      setIsLoading(true);
      let jwtToken = localStorage.getItem('jwttoken');

      // 如果没有token，报错
      if (!jwtToken) {
        throw new Error('No JWT token found');
      }

      // 检查token是否已过期
      try {
        const jwtInfo = jwtDecode(jwtToken);
        const tokenExpiry = jwtInfo.exp * 1000;
        const now = new Date().getTime();

        // 如果token已过期或即将过期（小于60秒），刷新token
        if (tokenExpiry - now < 60 * 1000) {
          const refreshToken = localStorage.getItem('jwtrefreshToken');
          if (!refreshToken) {
            throw new Error('No refresh token found');
          }

          const response = await FetchRefreshToken({ refreshToken });
          if (response.code === 0) {
            jwtToken = response.data.token;
            localStorage.setItem('jwttoken', jwtToken);
          } else {
            throw new Error('Failed to refresh token');
          }
        }
      } catch (e) {
        console.error('Error checking token expiry:', e);
        // 继续使用现有token，即使它可能已过期
      }

      setToken(jwtToken);
      setError(null);
    } catch (error) {
      console.error('Error fetching JWT token:', error);
      setError('Failed to authenticate with Superset. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial token fetch - only once at component mount
  useEffect(() => {
    getToken();
  }, []);



  return (
    <div className="superset-dashboard-container">
      {isLoading && <div className="loading">Loading authentication...</div>}
      {error && <div className="error">{error}</div>}

      {token && (
        <iframe
          ref={iframeRef}
          src={dashboardUrl + '?token=' + token}
          style={{
            width: '100%',
            border: 'none',
            transition: 'height 0.3s ease-out', // 添加过渡动画
            height: 'calc(100vh - 110px)',
          }}
          title="Superset Dashboard"
        />
      )}
    </div>
  );
};


export default SupersetComponent;