import { Table } from 'antd';
import Decimal from 'decimal.js';
import CalculatorSum from './calculator-sum';

const TableSummary = ({ 
    tableColumns, 
    requestParams, 
    selectedRow, 
    onSelect, 
    mode 
}) => {
    // 收集所有需要汇总的字段信息
    const summaryFields = [];
    
    tableColumns.forEach(item => {
        if (item.children) {
            item.children.forEach(child => {
                if (child.needSum) {
                    summaryFields.push({
                        label: `${item.title}.${child.title}`,
                        fieldName: `${item.dataIndex}.${child.dataIndex}`,
                        key: `${item.key}.${child.key}`,
                        parentDataIndex: item.dataIndex,
                        dataIndex: child.dataIndex,
                        isChild: true
                    });
                }
            });
        } else if (item.needSum) {
            summaryFields.push({
                label: item.title,
                fieldName: item.dataIndex,
                key: item.key,
                dataIndex: item.dataIndex,
                isChild: false
            });
        }
    });
    
    // 计算选中行的统计数据，使用 decimal.js 避免精度问题
    const calculateSelectedRowsSum = (field) => {
        if (!selectedRow || selectedRow.length === 0) return "0";
        
        let sum = new Decimal(0);
        selectedRow.forEach(row => {
            let value = new Decimal(0);
            
            if (field.isChild) {
                // 处理子表数据
                const parentData = row[field.parentDataIndex];
                if (parentData) {
                    let subTableData = [];
                    try {
                        subTableData = typeof parentData === 'string' ? JSON.parse(parentData || "[]") : parentData;
                    } catch (e) {
                        subTableData = [];
                    }
                    
                    if (Array.isArray(subTableData)) {
                        subTableData.forEach(subRow => {
                            const subValue = subRow[field.dataIndex];
                            if (subValue !== null && subValue !== undefined && subValue !== '') {
                                try {
                                    value = value.plus(new Decimal(subValue));
                                } catch (e) {
                                    // 如果无法转换为数字，跳过这个值
                                }
                            }
                        });
                    }
                }
            } else {
                // 处理普通字段
                const fieldValue = row[field.dataIndex];
                let numValue = 0;
                
                if (typeof fieldValue === 'object' && fieldValue?.value !== undefined) {
                    numValue = fieldValue.value;
                } else {
                    numValue = fieldValue;
                }
                
                if (numValue !== null && numValue !== undefined && numValue !== '') {
                    try {
                        value = new Decimal(numValue);
                    } catch (e) {
                        // 如果无法转换为数字，保持为0
                        value = new Decimal(0);
                    }
                }
            }
            
            sum = sum.plus(value);
        });
        
        return sum.toString();
    };
    
    // 计算实际的列数量，包括子列
    const calculateActualColumnCount = () => {
        let count = 0;
        
        // 选择列
        if (typeof onSelect == "function") {
            count += 1;
        }
        
        // 遍历所有列，包括子列
        tableColumns.forEach(column => {
            if (column.children && column.children.length > 0) {
                // 有子列的情况，计算子列数量
                count += column.children.length;
            } else {
                // 普通列
                count += 1;
            }
        });
        
        return count;
    };
    
    // 如果没有统计字段且没有选择功能，则不显示汇总行
    if (summaryFields.length <= 0 && typeof onSelect !== "function") {
        return null;
    }
    
    return (
        <Table.Summary fixed>
            <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={calculateActualColumnCount()}>
                    <div className="flex flex-col gap-2 px-4 py-2 bg-gray-50 rounded">
                        {/* 全部数据汇总 - 只在有统计字段时显示 */}
                        {summaryFields.length > 0 && (
                            <div className="flex items-baseline">
                                <span className="font-semibold text-gray-700">汇总结果：</span>
                                <div className="flex flex-wrap gap-2 ml-2">
                                    {summaryFields.map((field, index) => (
                                        <div key={field.key} className="flex items-center gap-1">
                                            <span className="text-sm font-medium text-gray-600">{field.label}:</span>
                                            <span className="text-sm font-semibold text-blue-600">
                                                <CalculatorSum 
                                                    requestParams={requestParams} 
                                                    fieldName={field.fieldName} 
                                                />
                                            </span>
                                            {index < summaryFields.length - 1 && <span className="text-gray-400">|</span>}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                        
                        {/* 选中数据汇总 - 只在有选择功能时显示 */}
                        {typeof onSelect == "function" && selectedRow && selectedRow.length > 0 && (
                            <div className={`flex items-baseline ${summaryFields.length > 0 ? 'border-t pt-2' : ''}`}>
                                <span className="font-semibold text-orange-700"> {summaryFields.length > 0 ? <>选中汇总({selectedRow.length}条)：</> : '选中汇总：'} </span>
                                <div className="flex flex-wrap gap-2 ml-2">
                                    {summaryFields.length > 0 ? (
                                        // 有统计字段时显示统计数据
                                        summaryFields.map((field, index) => {
                                            const selectedSum = calculateSelectedRowsSum(field);
                                            return (
                                                <div key={`selected-${field.key}`} className="flex items-center gap-1">
                                                    <span className="text-sm font-medium text-gray-600">{field.label}:</span>
                                                    <span className="text-sm font-semibold text-orange-600">
                                                        {parseFloat(selectedSum).toLocaleString()}
                                                    </span>
                                                    {index < summaryFields.length - 1 && <span className="text-gray-400">|</span>}
                                                </div>
                                            );
                                        })
                                    ) : (
                                        // 没有统计字段时只显示选中提示
                                        <span className="text-sm text-gray-600">
                                            已选中 {selectedRow.length} 条记录
                                        </span>
                                    )}
                                </div>
                            </div>
                        )}
                        
                        {/* 当有选择功能但没有选中任何行时，显示提示 */}
                        {typeof onSelect == "function" && (!selectedRow || selectedRow.length === 0) && summaryFields.length === 0 && (
                            <div className="flex items-baseline">
                                <span className="font-semibold text-gray-700">选择提示：</span>
                                <span className="text-sm text-gray-600 ml-2">
                                    当前未选中任何记录
                                </span>
                            </div>
                        )}
                    </div>
                </Table.Summary.Cell>
            </Table.Summary.Row>
        </Table.Summary>
    );
};

export default TableSummary; 