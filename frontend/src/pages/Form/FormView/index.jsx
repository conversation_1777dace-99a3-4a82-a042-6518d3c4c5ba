import { FolderAddFilled, ExceptionOutlined, PrinterOutlined, InfoCircleOutlined, EditOutlined, DeleteOutlined, ImportOutlined, ExportOutlined, CopyOutlined, CheckOutlined, EllipsisOutlined, DownOutlined } from '@ant-design/icons';
import Decimal from 'decimal.js';
import {
    ProTable,
} from '@ant-design/pro-components';

import { Button, Popconfirm, Modal, message, Table, Dropdown, Form, Segmented, Spin } from 'antd';
import PageContainer from '@/components/CustomLayout/page-container';
import { GetFormTemplate } from '@/services/crm-api/form-template';
import { FetchGetFormDatas, FetchFormDataDelete, FetchUpdateSingleField } from '@/services/crm-api/form-view';
import { FetchPrint } from '@/services/crm-api/form-print-template';
import { FetchPrintHtmlService, GeneratePdfFromModifiedHtmlService } from '@/services/crm-api/form-print-actions';
import { FetchCreate as FetchCreateCountersignLog, FetchDelete as FetchDeleteCountersignLog } from '@/services/crm-api/form_countersign_logs';
import { ResizableProTable } from '@/components';
import {
    ProFormDatePicker,
    ProFormText,
    QueryFilter,
} from '@ant-design/pro-components';
import { useState, useRef, useEffect, useCallback } from 'react';
import { useModel } from '@umijs/max';

import { GetValueType } from '@/utils/column-mapping'
import PageSpin from '@/components/CustomLayout/page-spin';
import { formatColumnText, getFormItem } from '@/components'
import { DraggableModal } from '@/components';
import { MuiDataGridTable, AdGrid } from '@/components';
import { defaultLowcodeSchema, defaultTableSchema } from "../FormDesign/Util/default";
import { Editor, Toolbar } from '@wangeditor-next/editor-for-react';
import '@wangeditor-next/editor/dist/css/style.css';
import { useSearchParams } from 'umi';
import FormViewEdit from './edit'
import { findNodesInTree } from "../../../utils";
import { fixedFields } from '../FormDesign/Util';
import Info2Flow from "../../Flow/info2flow"
import { useRequest } from 'ahooks';
import { useAccess, Access } from 'umi';
import ImportExcel from './import-excel';
import ExportExcelHistory from './export-excel-history';
import Invalid from './invalid'
import { createStyles } from 'antd-style';
import CalculatorSum from './calculator-sum';
import TableSummary from './TableSummary';

import { FetchUploadImage } from "@/services/crm-api/upload-file";

// 可编辑列组件
const EditableColumn = ({ 
    element, 
    record, 
    data, 
    formatTableColumnText, 
    access, 
    accessMark, 
    onUpdate
}) => {
    const [isUpdating, setIsUpdating] = useState(false);
    const [currentData, setCurrentData] = useState(data);
    
    // 当外部data变化时，同步更新内部状态
    useEffect(() => {
        setCurrentData(data);
    }, [data]);
    
    // 检查是否支持列表编辑
    const canListEdit = element?.props?.allowListEdit && 
        access.canChange(...accessMark) &&
        (element.component_name === "Field.RadioGroup" || element.component_name === "Field.Select") 
    
    if (!canListEdit) {
        return formatTableColumnText(element, currentData);
    }

    // 获取选项数据
    const options = element?.props?.options || [];
    
    // 当前值
    const currentValue = typeof currentData === 'object' && currentData?.value !== undefined ? currentData.value : currentData;
    const currentLabel = formatTableColumnText(element, currentData);
    
    // 处理更新
    const handleUpdate = async (newValue) => {
        if (isUpdating) return; // 防止重复点击
        
        setIsUpdating(true);
        try {
          
            await onUpdate(record.id, element.id, newValue);
            setCurrentData(newValue);
        } catch (error) {
        } finally {
            setIsUpdating(false);
        }
    };
    
    // 构建下拉菜单选项
    const menuItems = options.map(option => ({
        key: option.value,
        label: option.label,
        disabled: isUpdating, // 更新中禁用选项
        onClick: () => {
            const newValue = element.component_name === "Field.Select" 
                ? { value: option.value, label: option.label }
                : option.value;
            handleUpdate(newValue);
        }
    }));
    
    return (
        <div className="flex items-center">
            <span style={{ opacity: isUpdating ? 0.6 : 1 }}>
                {currentLabel}
            </span>
            <Dropdown
                menu={{ items: menuItems }}
                trigger={['click']}
                placement="bottomLeft"
                disabled={isUpdating}
            >
                <Button 
                    type="text" 
                    size="small" 
                    icon={<EditOutlined />}
                    loading={isUpdating}
                    disabled={isUpdating}
                    onClick={(e) => e.stopPropagation()}
                />
            </Dropdown>
        </div>
    );
};

const useStyle = createStyles(({ css, token }) => {
    const { antCls } = token;
    return {
        customTable: css`
        ${antCls}-table {
          ${antCls}-table-tbody > tr:hover {
            background-color: red;
          }
        }
      `,
    };
});

// 定义打印预览模态框组件
const PrintPreviewModal = ({ open, onCancel, onConfirm, htmlContent, onHtmlChange, editorInstance, setEditorInstance, loading }) => {
    const [toolbarConfig] = useState({});
  
    let editorConfig = {
        placeholder: '请输入内容...',
        MENU_CONF: {},
    };
    // 添加自定义行高配置
    editorConfig.MENU_CONF['lineHeight'] = {
        lineHeightList: ['0.6', '0.8', '1', '1.5', '2', '2.5'],
    }
    editorConfig.MENU_CONF['uploadImage'] = {
        server: "/api/upload/image",
        // 单个文件的最大体积限制，默认为 2M
        maxFileSize: 1 * 1024 * 1024, // 1M
        // 最多可上传几个文件，默认为 100
        maxNumberOfFiles: 10,
        // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
        allowedFileTypes: ['image/*'],
        async customUpload(file, insertFn) {
            message.loading("上传中...")
            try {
                const formData = new FormData();
                formData.append('file', file);
                var fetchOptions = {
                }
                var response = await FetchUploadImage(formData, fetchOptions)
                if (response && response.code == 0 && response.data.urls.length > 0) {
                    insertFn(response.data.urls[0], "", "")
                }
                else {
                    const error_message = response?.message || "上传失败"
                    message.error(error_message)
                    options?.onError(error_message)
                }
            } catch (error) {

            }
            finally {
                message.destroy()
            }
        }
    }
    // 及时销毁 editor
    useEffect(() => {
        return () => {
            if (editorInstance == null) return;
            editorInstance.destroy();
            setEditorInstance(null);
        };
    }, [editorInstance]);

    return (
        <Modal
            title="打印预览和编辑"
            open={open}
            onCancel={onCancel}
            width={1140}
            style={{ top: 20 }}
            footer={[
                <Button key="back" onClick={onCancel}>
                    取消
                </Button>,
                <Button key="submit" type="primary" loading={loading} onClick={onConfirm} icon={<PrinterOutlined />}>
                    确认打印
                </Button>,
            ]}
            destroyOnClose // 关闭时销毁 Modal 里的子元素，确保编辑器状态正确
        >
            <Spin spinning={!htmlContent && open}> {/* 如果弹窗可见但htmlContent为空，则显示加载中 */}
                <div style={{ border: '1px solid #ccc', zIndex: 100, marginTop: '10px' }}>
                    <Toolbar
                        editor={editorInstance}
                        defaultConfig={toolbarConfig}
                        mode="default"
                        style={{ borderBottom: '1px solid #ccc' }}
                    />
                    <Editor
                        defaultConfig={editorConfig}
                        value={htmlContent}
                        onCreated={setEditorInstance}
                        onChange={editor => onHtmlChange(editor.getHtml())}
                        mode="default"
                        style={{ height: '500px', overflowY: 'auto' }}
                    />
                </div>
            </Spin>
        </Modal>
    );
};


const FormView = ({ comid, ruleParams, enabledLinkedData, sortByUsedCount, linkedData, onSelect, noTitle, onOk, onEmitShow, mode = "single" }) => {

    const { initialState } = useModel('@@initialState');
    const { currentUser } = initialState || {};
    const access = useAccess();
    const [messageApi, contextHolder] = message.useMessage();
    const [formTemplate, setFormTemplate] = useState(null);
    const [firstLoad, setFirstLoad] = useState(false);
    const [tableColumns, setTableColumns] = useState([]);
    const [subTable, setSubTable] = useState([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [importTemplateId, setImportTemplateId] = useState(0);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);

    const [exportTemplateId, setExportTemplateId] = useState(0);
    const [isExportModalOpen, setIsExportModalOpen] = useState(false);

    const [searchParams] = useSearchParams();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRow, setSelectedRow] = useState([]);
    const [dataSource, setDataSource] = useState([]);
    const tableActionRef = useRef();
    const showDetailRef = useRef();
    const editFormRef = useRef();
    const importExcelRef = useRef();
    const [formTemplateId, setFormTemplateId] = useState(0);
    const [formDataId, setFormDataId] = useState(0);
    const [formStatus, setFormStatus] = useState(0);
    const [scrollX, setScrollX] = useState(960);
    const submitLoading = useRef()
    const [accessMark, setAccessMark] = useState(["form", 0])
    const [currentPageSize, setCurrentPageSize] = useState(null)
    const searchFormRef = useRef();
    const [searchFilters, setSearchFilters] = useState({});
    const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
    const [currentPrintingData, setCurrentPrintingData] = useState({ formId: null, formPrintTemplateId: null });
    const [printHtmlContent, setPrintHtmlContent] = useState('');
    const [editorInstance, setEditorInstance] = useState(null);
    const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
    const [isLoadingHtml, setIsLoadingHtml] = useState(false);
    const [printMenuItems, setPrintMenuItems] = useState([]);

    const invalidateRef = useRef()

    const [queryFilter, setQueryFilter] = useState([])

    const [currentFormFlowStatus, setCurrentFormFlowStatus] = useState("有效数据")

    const [requestParams, setRequestParams] = useState({})
    const [copyFormDataId, setCopyFormDataId] = useState(0)
    // 加载模板信息，根据模板的form_columns构造表单的columns
    let id = 0;
    if (comid > 0) id = comid
    else if (searchParams.get('id')) id = searchParams.get('id');

    useEffect(() => {
        let status = Array.isArray(dataSource) && dataSource.length > 0
        typeof onEmitShow == "function" && onEmitShow(status)
    }, [dataSource])

    useEffect(() => {
        tableActionRef.current?.reload()
    }, [ruleParams])

    useEffect(() => {
        if (id && id > 0) {
            setCurrentFormFlowStatus("有效数据")
            setAccessMark(["form", id])
            setFormTemplateId(id)
            InitTemplateFetch(id);
        }
        else message.error("单据路由参数错误")
    }, [id]);

    useEffect(() => {
        if (formTemplate) {
            const basePrintItems = (formTemplate?.printTemplates || [])
                .filter(item => item.status === 1) // 保留只处理启用状态的模板
                .map(item => ({
                    key: item.id,
                    label: item.name, // 用于构造最终label
                    templateId: item.id,
                    allowEditBefore: item.allowEditBefore == 1 // 直接使用模板自身的属性
                }));
    
            let newPrintItems = [];
            basePrintItems.forEach(item => {
                newPrintItems.push({
                    key: `direct-${item.key}`,
                    label: `直接打印-${item.label}`,
                    actionType: 'direct',
                    templateId: item.templateId
                });
                // 根据模板自身的 allowEditBefore 属性决定是否添加编辑选项
                if (item.allowEditBefore) {
                    newPrintItems.push({
                        key: `edit-${item.key}`,
                        label: `打印前编辑-${item.label}`,
                        actionType: 'edit',
                        templateId: item.templateId
                    });
                }
            });
            setPrintMenuItems(newPrintItems);
        }
    }, [formTemplate]);

    const formatTableColumnText = (component, text) => {
        let result = formatColumnText(component, text)
        if (result && typeof result === "string") {
            return result.split('\n').map((line, index, array) => (
                <span key={index}>
                    {line}
                    {index < array.length - 1 && <br />}
                </span>
            ));
        }
        return result;
    }
    const InitTemplateFetch = async (id) => {
        let template = await GetFormTemplate(id);
        if (template.code > 0) {
            message.error(template.message);
            return
        }
        const templateData = template.data;
        const _formDesign = {
            id: templateData.id,
            formTitle: templateData.formTitle,
            formSchema: JSON.parse(templateData.formSchema || defaultLowcodeSchema),
            formColumns: JSON.parse(templateData.formColumns || "[]"),
            tableDesign: templateData.tableDesign ? JSON.parse(templateData.tableDesign) : defaultTableSchema,
            openFlow: templateData.openFlow == 1,
            openLog: templateData.openLog == 1,
            allowCustomAudit: templateData.allowCustomAudit == 1,
            allowCustomCc: templateData.allowCustomCc == 1,
            flowCode: templateData.flowCode,
            printTemplates: templateData.printTemplates,
            importTemplates: templateData.importTemplates,
            exportTemplates: templateData.exportTemplates,
        }

        document.title = `${templateData.formTitle}-${APP_TITLE}`;
        setFormTemplate(_formDesign)
    }

    const buildTableColumn = (element, search = true, parentid = null, columnSettle = {}) => {
        if (element.component_name == "Field.Table") {
            return {
                key: element.id,
                title: element.label,
                dataIndex: element.id,
                subTable: true,
                children: element.children.map(item => {
                    let chlidColumnSettle = columnSettle?.children.find(child => child.id == item.id)
                    return buildTableColumn(item, false, element.id, chlidColumnSettle)
                })

            }
        }
        else return {
            key: element.id,
            title: element.label,
            dataIndex: element.id,
            valueType: GetValueType(element.component_name),
            subTable: false,
            search: false,
            needSum: element.needSum,
            width: columnSettle?.width || 150,
            // search: search,
            render: (text, record, index, action) => {
                if (!parentid) {
                    let data = record[element.id]
                    if (element.id == "created_by") data = record["created_user"]
                    
                    return (
                        <EditableColumn
                            element={element}
                            record={record}
                            data={data}
                            formatTableColumnText={formatTableColumnText}
                            access={access}
                            accessMark={accessMark}
                            onUpdate={handleUpdateSingleField}
                        />
                    );
                }
                else {
                    let data = record[parentid]
                    let subTableData = [];
                    if (typeof data == "string") {
                        subTableData = JSON.parse(data || "[]");
                        record[parentid] = subTableData
                    } else if (Array.isArray(data)) {
                        subTableData = data
                    }
                    if (Array.isArray(subTableData)) {
                        return <div className='flex flex-col divide-y divide-gray-100 -my-2'>{subTableData.map(item => {
                            return <div className='h-10 flex items-center truncate w-full' title={formatColumnText(element, item[element.id])}>{formatColumnText(element, item[element.id])}</div>
                        })}</div>
                    }
                    return ""
                }
            },
            // /**
            //  * 自定义表单搜索
            //  * @param {*} item 当前列信息
            //  * @param {*} type 不知道干嘛的
            //  * @param {*} defaultRender 应该是默认的渲染函数
            //  * @param {*} rest 其他参数
            //  * @param {*} form 检索的表单信息
            //  */
            // renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
            //     return getFormItem(element, form.setFieldValue, formTemplate?.id, form)
            // },
        }
    }

    // 处理回签
    const handleCounterSignClick = async (formDataId) => {
        message.loading("正在回签...")
        try {
            let { code, data, message: msg } = await FetchCreateCountersignLog({
                formId: formTemplateId,
                dataId: formDataId
            })
            message.destroy()
            if (code > 0) {
                console.log(msg)
                message.error(msg)
            }
            else {
                message.success("回签成功")
                // 查找 dataSource 中的记录 ，修改回签状态 signback_type = 1
                setDataSource(prev => {
                    return prev.map(item => {
                        if (item.id == formDataId) {
                            item.signback_type = 2
                        }
                        return item
                    })
                })
            }
        } catch (error) {
            message.destroy()
            console.error(error)
        }
        finally {
        }
    }

    // 处理取消回签
    const handleCancelCounterSignClick = async (formDataId) => {
        message.loading("正在取消回签...")
        try {
            let { code, data, message: msg } = await FetchDeleteCountersignLog({
                formId: formTemplateId,
                dataId: formDataId
            })
            message.destroy()
            if (code > 0) {
                console.log(msg)
                message.error(msg)
            }
            else {
                message.success("取消回签成功")
                // 查找 dataSource 中的记录 ，修改回签状态 signback_type = 1
                setDataSource(prev => {
                    return prev.map(item => {
                        if (item.id == formDataId) {
                            item.signback_type = 1
                        }
                        return item
                    })
                })
            }
        } catch (error) {
            message.destroy()
            console.error(error)
        }
        finally {
        }
    }

    // 处理单个字段更新
    const handleUpdateSingleField = async (formDataId, fieldName, fieldValue) => {
        try {
            const response = await FetchUpdateSingleField(formTemplateId, formDataId, fieldName, fieldValue);
            if (response.code === 0) {
                message.success("更新成功");
                // 更新本地数据
                setDataSource(prev => {
                    return prev.map(item => {
                        if (item.id === formDataId) {
                            return { ...item, [fieldName]: fieldValue };
                        }
                        return item;
                    });
                });
                // 可选：重新加载数据确保数据一致性
                // await tableActionRef.current?.reload();
            } else {
                message.error(response.message || "更新失败");
                throw new Error(response.message || "更新失败");
            }
        } catch (error) {
            console.error("更新字段失败:", error);
            message.error("更新失败");
            throw error; // 重新抛出错误，让组件知道更新失败
        }
    };

    const handleDirectPrint = async (formId, printTemplateId) => {
        message.loading("正在准备打印...");
        try {
            const res = await FetchPrint(printTemplateId, formId);
            message.destroy();
            if (res.code === 0 && res.data && res.data.pdfUrl) {
                window.open(res.data.pdfUrl);
                setDataSource(prev =>
                    prev.map(item =>
                        item.id === formId ? { ...item, print_status: 2 } : item
                    )
                );
            } else {
                message.error(res.message || "直接打印失败");
            }
        } catch (error) {
            message.destroy();
            message.error("直接打印时发生错误");
            console.error("Error during direct print:", error);
        }
    };

    const handlePrintClick = async (formId, fromPrintTemplateId) => {
        console.log("[FormView] handlePrintClick triggered. formId:", formId, "fromPrintTemplateId:", fromPrintTemplateId);
        if (!formId || !fromPrintTemplateId) {
            message.error("打印参数错误，无法继续。");
            console.error("[FormView] handlePrintClick: formId or fromPrintTemplateId is missing.", { formId, fromPrintTemplateId });
            return;
        }
        setIsLoadingHtml(true);
        try {
            // 1. 调用后端服务获取HTML内容
            const htmlRes = await FetchPrintHtmlService(fromPrintTemplateId, formId); // Updated to service call
            setIsLoadingHtml(false);
            console.log("htmlRes", htmlRes)
            if (htmlRes.code === 0 && htmlRes.data && htmlRes.data.html) {
                setPrintHtmlContent(htmlRes.data.html);
                setCurrentPrintingData({ formId, formPrintTemplateId: fromPrintTemplateId });
                setIsPreviewModalOpen(true);
            } else {
                message.error(htmlRes.message || "获取打印内容失败");
            }
        } catch (error) {
            setIsLoadingHtml(false);
            message.error("获取打印内容时发生错误");
            console.error("Error fetching print HTML:", error);
        }
    };

    const handleConfirmPrintFromModal = async () => {
        if (!editorInstance) {
            message.error("编辑器实例不存在");
            return;
        }
        const modifiedHtml = editorInstance.getHtml();
        const { formId, formPrintTemplateId } = currentPrintingData;

        if (!formId || !formPrintTemplateId) {
            message.error("打印参数丢失");
            return;
        }

        setIsGeneratingPdf(true);
        message.loading("正在生成PDF文件...");

        try {
            const pdfRes = await GeneratePdfFromModifiedHtmlService(modifiedHtml, formId, formPrintTemplateId); // Updated to service call
            message.destroy(); // 清除loading消息
            setIsGeneratingPdf(false);

            if (pdfRes.code === 0 && pdfRes.data && pdfRes.data.pdfUrl) {
                message.success("PDF生成成功！");
                window.open(pdfRes.data.pdfUrl); // 在新标签页打开PDF
                setIsPreviewModalOpen(false); // 关闭模态框
                setPrintHtmlContent(''); // 清空HTML内容
                if (editorInstance) { // 销毁编辑器实例
                    editorInstance.destroy();
                    setEditorInstance(null);
                }


                // 更新前端列表中的打印状态
                setDataSource(prev =>
                    prev.map(item =>
                        item.id === formId ? { ...item, print_status: 2 } : item
                    )
                );
            } else {
                message.error(pdfRes.message || "生成PDF失败");
            }
        } catch (error) {
            message.destroy();
            setIsGeneratingPdf(false);
            message.error("生成PDF时发生错误");
            console.error("Error generating PDF from modified HTML:", error);
        }
    };

    const handleExport = async (exportTemplateId) => {
        const filters = searchFormRef.current?.getFieldsValue()
        console.log("filters", filters)
        setSearchFilters({ ...filters, showInvalid: currentFormFlowStatus == "有效数据" ? 1 : 2 })
        setExportTemplateId(exportTemplateId)
        setIsExportModalOpen(true)
    }

    // 检查是否允许显示打印按钮
    const checkPrintButton = (record) => {
        if (formTemplate.tableDesign.actions.includes("actionPrint")) {
            if (formTemplate.tableDesign?.actionSettings?.actionPrint?.actionPrintAllowUnAudit == true) {
                return true
            }
            else if (record.flow_status == 3) {
                return true
            }
        }
        return false
    }

    const printItems = (formTemplate?.printTemplates || []).filter(item => item.status == 1).map(item => ({ key: item.id, label: `打印${item.name}` }))
    const importItems = (formTemplate?.importTemplates || []).filter(item => item.status == 1).map(item => ({ key: item.id, label: `导入${item.name}` }))
    const exportItems = (formTemplate?.exportTemplates || []).filter(item => item.status == 1).map(item => ({ key: item.id, label: `导出${item.name}` }))

    useEffect(() => {
        if (!formTemplate) return
        let calculatedOptionWidth = 0;
        const tdActions = formTemplate.tableDesign.actions;
        const currentPrintItems = (formTemplate?.printTemplates || []).filter(item => item.status == 1);

        // Calculate width for Print button/dropdown
        if (tdActions.includes("actionPrint")) {
            if (currentPrintItems.length > 0) {
                calculatedOptionWidth += 30; // Adjusted for "打印" button (direct or as dropdown trigger)
            } else {
                calculatedOptionWidth += 30; // For "打印" button that shows "未配置打印模板"
            }
        }

        // Check for other actions that will go into the "More" dropdown
        const hasOtherActions = [
            "actionEdit", // This implies Edit or View
            "actionDelete",
            "actionVoid",
            "actionSignBack",
            "actionNew" // For "以此为模板快速新建"
        ].some(action => tdActions.includes(action));

        if (hasOtherActions) {
            calculatedOptionWidth += 30; // For the EllipsisOutlined button for "More" actions
        }
        
        // Ensure a minimum width if there are actions but calculated width is too small or zero
        if (calculatedOptionWidth === 0 && tdActions.length > 0) {
            calculatedOptionWidth = 30; // Minimum for a single action (e.g. view in dropdown, or basic print)
        }
        if (calculatedOptionWidth > 0) calculatedOptionWidth += 20;

        setScrollX(formTemplate.tableDesign.columns.reduce((acc, currentItem) => {
            let tmp = acc
            if (currentItem.switch) {
                if (Array.isArray(currentItem.children) && currentItem.children.length > 0) tmp += currentItem.children.filter(child => child.switch).length * 150
                else tmp += 150
            }
            return tmp
        }, calculatedOptionWidth))
        let allColumns = formTemplate.formColumns;
        allColumns = [...allColumns, ...fixedFields]
        allColumns = access.computeFieldPermission("form", formTemplate.id, allColumns)
        let queryFilterColumns = [];
        for (let index = 0; index < formTemplate.tableDesign.filter.length; index++) {
            let column = formTemplate.tableDesign.filter[index];
            if (!column.switch) continue;
            if (ruleParams && Object.keys(ruleParams).includes(column.id)) continue;
            let columnInfo = allColumns.find(item => item.id == column.id)
            if (column.id.split(".").length == 2) {
                const [parentid, columnid] = column.id.split(".").map(item => item.trim());
                let parentColumn = allColumns.find(item => item.id == parentid);
                columnInfo = parentColumn?.children.find(item => item.id == columnid);
                columnInfo = { ...columnInfo, id: column.id, label: `${parentColumn?.label}.${columnInfo.label}` }
            }
            if (!columnInfo) continue;
            if (!columnInfo.has_permission) continue;
            queryFilterColumns.push({
                title: columnInfo?.label,
                dataIndex: columnInfo?.dataIndex,
                key: columnInfo?.id,
                renderFormItem: () => {
                    return getFormItem({ ...columnInfo, label: "" }, searchFormRef.current?.setFieldValue, formTemplate?.id, searchFormRef.current)
                }
            })
        }
        setQueryFilter(queryFilterColumns)

        // 构造表单的columns
        let columns = [];
        let subTables = [];

        // 定义操作列
        const operationColumn = {
            title: '操作',
            valueType: 'option',
            dataIndex: 'option',
            fixed: "left",
            width: calculatedOptionWidth,
            // 确保操作列总是在最左边
            order: -1,
            render: (text, record, index, action) => {
                const actionMenuItems = [];

                // Edit/View Button logic
                actionMenuItems.push({
                    key: 'editOrView',
                    label: (
                        <Access
                            accessible={access.canChange(...accessMark)}
                            fallback={
                                <Button type="text" size="small" onClick={(e) => { e.stopPropagation(); handleShowDetail(record); }}>
                                    查看
                                </Button>
                            }
                        >
                            {!formTemplate?.openFlow && formTemplate.tableDesign.actions.includes("actionEdit") ? (
                                <Button type="text" size="small" onClick={(e) => { e.stopPropagation(); handleEdit(record); }}>
                                    编辑
                                </Button>
                            ) : (
                                <Button type="text" size="small" onClick={(e) => { e.stopPropagation(); handleShowDetail(record); }}>
                                    查看
                                </Button>
                            )}
                        </Access>
                    )
                });

                // Void Button
                if (formTemplate.tableDesign.actions.includes("actionVoid") && record?.flow_status == 3) {
                    actionMenuItems.push({
                        key: 'void',
                        label: (
                            <Button danger type="text" size="small" onClick={(e) => { e.stopPropagation(); handleInvalidate(record); }}>
                                作废
                            </Button>
                        )
                    });
                }

                // Delete Button
                if (formTemplate.tableDesign.actions.includes("actionDelete")) {
                    actionMenuItems.push({
                        key: 'delete',
                        label: (
                            <Popconfirm
                                title="删除后不能恢复，确认删除?"
                                onConfirm={() => handleDelete(record)}
                                onCancel={(e) => { e.stopPropagation(); }}
                                okText="确认删除"
                                cancelText="取消"
                                onClick={(e) => { e.stopPropagation() }} 
                            >
                                <Button type="text" danger size="small" onClick={(e) => e.stopPropagation()}>
                                    删除
                                </Button>
                            </Popconfirm>
                        )
                    });
                }

                // SignBack/Cancel SignBack Buttons
                if (formTemplate.tableDesign.actions.includes("actionSignBack")) {
                    if (record?.signback_type == 1) {
                        actionMenuItems.push({
                            key: 'signBack',
                            label: (
                                <Button type="text" size="small" onClick={(e) => { e.stopPropagation(); handleCounterSignClick(record.id); }}>
                                    回签
                                </Button>
                            )
                        });
                    } else if (record?.signback_type == 2) {
                        actionMenuItems.push({
                            key: 'cancelSignBack',
                            label: (
                                <Button type="text" size="small" onClick={(e) => { e.stopPropagation(); handleCancelCounterSignClick(record.id); }}>
                                    取消回签
                                </Button>
                            )
                        });
                    }
                }
                
                // "以此为模板快速新建" (Copy) button
                if ((record?.flow_status == 7 || record?.flow_status == 5) && access.canNew(...accessMark) && formTemplate.tableDesign.actions.includes("actionNew")) {
                    actionMenuItems.push({
                        key: 'copyNew',
                        label: (
                            <Button type='text' size="small" onClick={(e) => { e.stopPropagation(); handleCopy(record); }}>
                                以此为模板快速新建
                            </Button>
                        )
                    });
                }
                
                const finalActionMenuItems = actionMenuItems.filter(item => item.label !== null);

                return <div className='flex h-full items-center gap-x-0'>
                    <Access accessible={access.canPrint(...accessMark)}>
                        {
                            checkPrintButton(record) && (
                                <>
                                    {printMenuItems && printMenuItems.length === 1 && printMenuItems[0].actionType === 'direct' ? (
                                        <Button
                                            type="link"
                                            size="small"
                                            className='px-1'
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleDirectPrint(record.id, printMenuItems[0].templateId);
                                            }}
                                        >
                                            打印
                                        </Button>
                                    ) : (!printMenuItems || printMenuItems.length === 0) ? (
                                        <Button
                                            type="link"
                                            size="small"
                                            className='px-1'
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                message.error("未配置打印模板");
                                            }}
                                        >
                                            打印
                                        </Button>
                                    ) : (
                                        <Dropdown
                                            className='px-1'
                                            menu={{
                                                items: printMenuItems.map(item => ({ key: item.key, label: item.label})), // Ensure items have key and label
                                                onClick: (menuItem) => {
                                                    menuItem.domEvent.stopPropagation();
                                                    const selectedPrintItem = printMenuItems.find(pi => pi.key === menuItem.key);
                                                    if (selectedPrintItem) {
                                                        if (selectedPrintItem.actionType === 'direct') {
                                                            handleDirectPrint(record.id, selectedPrintItem.templateId);
                                                        } else if (selectedPrintItem.actionType === 'edit') {
                                                            handlePrintClick(record.id, selectedPrintItem.templateId);
                                                        }
                                                    }
                                                },
                                            }}
                                            placement="bottomLeft"
                                        >
                                            <Button type="link" size="small" onClick={(e) => e.stopPropagation()}>
                                                打印
                                            </Button>
                                        </Dropdown>
                                    )}
                                </>
                            )
                        }
                    </Access>
                    
                    {finalActionMenuItems.length > 0 && (
                        <Dropdown menu={{ items: finalActionMenuItems }} placement="bottomRight">
                            <Button type="text" size="small" icon={<EllipsisOutlined />} onClick={(e) => e.stopPropagation()} />
                        </Dropdown>
                    )}
                </div>
            }
        };
        
        // 将操作列添加到columns数组的开头
        columns.unshift(operationColumn);

        // 构造表单的columns
        for (let index = 0; index < formTemplate.tableDesign.columns.length; index++) {
            let column = formTemplate.tableDesign.columns[index];
            if (!column.switch) continue;
            let element = allColumns.find(item => item.id == column.id)
            if (!element || !element.has_permission) continue;
            let buildElement = { ...element, needSum: column?.needSum ?? false, children: [] };
            if (Array.isArray(column.children)) {
                for (let jdex = 0; jdex < column.children.length; jdex++) {
                    let item = column.children[jdex];
                    if (!item.switch) continue;
                    let subColumn = element.children.find(ec => ec.id == item.id);
                    if (!subColumn) continue;
                    if (!subColumn.has_permission) continue;
                    buildElement.children.push({ ...subColumn, needSum: item?.needSum ?? false })
                }
            }
            columns.push(buildTableColumn(buildElement, false, null, column))
        }
        console.log("columns", columns)
        console.log("操作列位置:", columns.findIndex(col => col.dataIndex === 'option'))
        console.log("[FormView] 传递给ResizableProTable的columns:", columns.map(col => ({ dataIndex: col.dataIndex, fixed: col.fixed, title: col.title, valueType: col.valueType })));
        console.log("calculatedOptionWidth", calculatedOptionWidth)
        setSubTable(subTables)
        setTableColumns(columns)

        setFirstLoad(true)

    }, [formTemplate, printMenuItems])


    const expandedRowRender = (record, index, indent, expanded) => {
        return subTable.map(item => {
            // 寻找数据源
            if (record?.[item.id]) {
                let data = JSON.parse(record[item.id] || [])
                data = data.map((item, index) => {
                    return { ...item, key: item?.key || index }
                })
                const columns = item.columns;
                return <>
                    <div className='text-lg h-8 mb-2 flex items-center'>{item?.title}</div>
                    <Table columns={columns} bordered={true} dataSource={data} pagination={false} className=' border-t rounded-md' /></>;
            }
        });
    }
    const handleDelete = async (record) => {
        const delRes = await FetchFormDataDelete(formTemplateId, record.id)
        if (delRes.code > 0) {
            message.error(delRes.message)
            return
        }
        await initList()
        message.success("删除成功")
        setSelectedRowKeys(prev => { return prev.filter(item => item != record.id) })
    }

    const handleAction = (record) => {
        if (!formTemplate?.openFlow && formTemplate.tableDesign.actions.includes("actionEdit") && access.canChange(...accessMark))
            handleEdit(record)
        else handleShowDetail(record)
    }
    const handleEdit = (record) => {
        console.log("handleEdit record", record)
        setCopyFormDataId(0)
        setFormDataId(record.id)
        setFormStatus(record.flow_status)
        setIsModalOpen(true)
    }
    const handleShowDetail = (record) => {
        setCopyFormDataId(0)
        setFormDataId(record.id)
        setFormStatus(record.flow_status)
        showDetailRef.current?.showModal()
    }
    const handleInvalidate = (record) => {
        setCopyFormDataId(0)
        setFormDataId(record.id)
        setFormStatus(record.flow_status)
        invalidateRef.current?.showInvalidateModal()
    }
    const handleNew = () => {
        setCopyFormDataId(0)
        setFormDataId(0)
        setFormStatus(0)
        setIsModalOpen(true)
    }
    const handleCopy = (record) => {
        setCopyFormDataId(record.id)
        setFormDataId(0)
        setFormStatus(0)
        setIsModalOpen(true)
    }
    const handleCancel = () => {
        setIsModalOpen(false)
    }
    const runSubmit = async () => {
        try {
            if (submitLoading.current) return;
            submitLoading.current = true
            message.loading("提交中...")
            await editFormRef.current?.handleSubmit()
            message.destroy()
            message.success("保存成功")
            await initList()
            setIsModalOpen(false)
            submitLoading.current = false
        }
        catch (e) {
            console.log(e.message)
            submitLoading.current = false
            message.destroy()
            message.error(e.message)
        }
        finally {
        }
    }

    const handleOk = async () => {
        try {
            await runSubmit()
        } catch (error) {
            console.error(error)
        }
    }
    const initList = async () => {
        await tableActionRef.current?.reload();
    }
    useEffect(() => {
        let selectdRow_ = [...selectedRow]
        selectdRow_ = selectedRowKeys.map(item => {
            let row = dataSource.find(ds => ds.id == item)
            if (!row) row = selectdRow_.find(sr => sr.id == item)
            return row
        })
        setSelectedRow(selectdRow_)
        typeof onSelect == "function" && onSelect(selectedRowKeys, selectdRow_)
    }, [selectedRowKeys])

    const [columnsStateMap, setColumnsStateMap] = useState(() => {
        //从缓存里面获取ColumnsState
        return JSON.parse(localStorage.getItem(`table_columns_state_${id}_${currentUser?.id}`)) || {};
    });

    const handleOnChangeColumn = (map, ColumnsState) => {
        setColumnsStateMap(map);
    }
    const handleSelectAll = () => {
        const allKeys = dataSource.map(item => item.id);
        setSelectedRowKeys((prev) => {
            return [...prev, ...allKeys.filter(id => !prev.includes(id))];
        });
    };

    const handleDeselectAll = () => {
        const allKeys = dataSource.map(item => item.id);
        const invertedKeys = dataSource
            .map(item => item.id)
            .filter(id => !selectedRowKeys.includes(id));
        setSelectedRowKeys((prev) => {
            prev = [...prev.filter(item => !allKeys.includes(item))];
            return [...prev, ...invertedKeys];
        });
    };


    return <PageContainer title={typeof onSelect == "function" || noTitle ? "" : formTemplate?.formTitle} key={`page_${id}`} >
        {!firstLoad && <PageSpin />}
        {firstLoad && <div className='flex flex-col gap-4'>
            <div className=' rounded-md bg-white'>
                <QueryFilter defaultCollapsed split formRef={searchFormRef}
                    onFinish={async () => {

                        tableActionRef.current?.reloadAndRest()
                    }}
                    optionRender={(searchConfig, formProps, dom) => {

                        return <div className='flex gap-4'>
                            <div className='flex gap-1'>{dom.map(item => {
                                return item
                            })}</div>
                            <Access accessible={access.canExport(...accessMark)}>{formTemplate.tableDesign.actions.includes("actionExportExcel") && <>
                                {exportItems.length > 0 ? <>
                                    {exportItems.length > 1 ? <Dropdown.Button
                                        icon={<ExportOutlined />}
                                        menu={{
                                            items: exportItems,
                                            onClick: (e) => {
                                                handleExport(e.key)
                                            },
                                        }}
                                        onClick={() => {
                                            handleExport(exportItems[0].key)
                                        }}
                                    >
                                        {exportItems[0].label}
                                    </Dropdown.Button> : <Button icon={<ExportOutlined />} onClick={() => {
                                        handleExport(exportItems[0].key)
                                    }}>{exportItems[0].label}</Button>}
                                </> : <Button icon={<ExportOutlined />} onClick={() => {
                                    message.error("未配置导出模板")
                                }}>导出数据到Excel</Button>}
                            </>}</Access>
                        </div>
                    }}>
                    {queryFilter.map(item => {
                        return <Form.Item label={item.title} name={item.key} key={item.key}>
                            {item.renderFormItem(item, searchFormRef)}
                        </Form.Item>
                    })}
                </QueryFilter>
            </div>
            <ResizableProTable
                scroll={{
                    x: scrollX,
                    y: Math.max(window.innerHeight - 450, 600), // 根据屏幕高度计算: 屏幕高度 - 300，最小值为600
                }}
                columnsState={{ //列设置-操作
                    value: columnsStateMap, //列状态的值，支持受控模式
                    onChange: handleOnChangeColumn, //列状态的值发生改变之后触发
                    persistenceKey: `table_columns_state_${id}_${currentUser?.id}`, //持久化列的 key，用于判断是否是同一个 table,会存在缓存里去
                    persistenceType: 'localStorage' //持久化列的类类型， localStorage 设置在关闭浏览器后也是存在的，sessionStorage 关闭浏览器后会丢失
                }}
                bordered
                expandable={subTable && subTable.length > 0 ? { expandedRowRender, defaultExpandedRowKeys: ['0'] } : false}
                actionRef={tableActionRef}
                params={{ formId: formTemplateId }}
                request={async (params) => {
                    const filter = searchFormRef.current?.getFieldsValue()
                    if (!filter?.flow_status) params = { ...params, showInvalid: currentFormFlowStatus == "有效数据" ? 1 : 2 }
                    let requestParams = { ...params, ...filter, ruleParams, enabledLinkedData, linkedData, sortByUsedCount /** ...(typeof onSelect == "function" ? { flow_status: 3 } : {}) */ }
                    setRequestParams(requestParams)
                    let res = await FetchGetFormDatas(requestParams)
                    const { success, data } = res
                    // console.log("res ///////////", JSON.parse(JSON.stringify(res)))
                    // // 如果 tableColumns 包含subTable==true的字段，则根据需要根据此字段数据，拆分成多行数据 (拆分行的方案会导致行数变多，从而antd 的 table 分页出现问题，所以暂时弃用)
                    // let processedData = [];
                    if (success) {
                        //     processedData = data.map(record => {
                        //         let item = {...record};
                        //         tableColumns.forEach(column => {
                        //             if (column.subTable && record[column.dataIndex]) {
                        //                 try {
                        //                     let subTableData = JSON.parse(record[column.dataIndex] || "[]");
                        //                     item[column.dataIndex] = subTableData
                        //                 } catch (e) {
                        //                 }
                        //             }
                        //         })
                        //         return item
                        //     })
                        //     setDataSource(processedData);
                        setDataSource(data)
                    }
                    // res = { ...res, data: processedData }

                    return res
                }}
                pagination={{
                    defaultPageSize: currentPageSize || 10, // 设置默认的 pageSize
                    showSizeChanger: true, // 允许用户改变每页条数   
                    // pageSizeOptions: ['5', '10', '20', '50'], // 提供的 pageSize 选项
                }}

                rowKey={(record) => {
                    return record.id
                }}
                //  tableStyle={{  overflow: 'auto' ,height: 'calc(100vh - 400px)' }}
                //  tableClassName=' oveflow-auto h-full'
                columns={tableColumns}
                search={false}
                toolbar={{
                    search: <div>
                        <Access accessible={access.canNew(...accessMark)}>{formTemplate.tableDesign.actions.includes("actionNew") && <Button type='primary' onClick={handleNew} icon={<FolderAddFilled />}>新建</Button>}</Access>
                    </div>,
                    actions: [<div className='flex items-center gap-x-2'>
                        <Access accessible={access.canImport(...accessMark)}>{formTemplate.tableDesign.actions.includes("actionImportExcel") && <>
                            {importItems.length > 0 ? <>
                                {importItems.length > 1 ? <Dropdown.Button
                                    icon={<ImportOutlined />}
                                    menu={{
                                        items: importItems,
                                        onClick: (e) => {
                                            setImportTemplateId(e.key)
                                            setIsImportModalOpen(true)
                                        },
                                    }}
                                    onClick={() => {
                                        setImportTemplateId(importItems[0].key)
                                        setIsImportModalOpen(true)
                                    }}
                                >
                                    {importItems[0].label}
                                </Dropdown.Button> :
                                    <Button icon={<ImportOutlined />} onClick={() => {
                                        setImportTemplateId(importItems[0].key)
                                        setIsImportModalOpen(true)
                                    }}>{importItems[0].label}</Button>}
                            </> : <Button icon={<ImportOutlined />} onClick={() => {
                                message.error("未配置导入模板")
                            }}>从Excel导入数据</Button>}
                        </>
                        }</Access>

                    </div>,
                    <Segmented
                        options={['有效数据', '回收站(撤销/作废)']}
                        onChange={(value) => {
                            setCurrentFormFlowStatus(value)
                            tableActionRef.current?.reload()
                        }}
                        value={currentFormFlowStatus}
                    />
                    ],
                }}

                onRow={typeof onSelect == "function" ?
                    (record) => ({
                        onDoubleClick: (e) => {
                            if (typeof onOk == "function") {
                                let row = dataSource.find(ds => ds.id == record.id)
                                if (row) onOk([row])
                            }
                        },
                        onClick: () => {
                            if (record.flow_status != 3) return;
                            if (selectedRowKeys.includes(record.id)) setSelectedRowKeys(prev => { return prev.filter(item => item != record.id) })
                            else {
                                if (mode == "single") setSelectedRowKeys([record.id])
                                else setSelectedRowKeys(prev => [...prev, record.id])
                            }
                        }
                    }) :
                    (record) => ({
                        onDoubleClick: () => {
                            handleAction(record)
                        },
                        // onClick: () => {
                        //     handleAction(record)
                        // }
                    })}
                tableAlertRender={false}
                rowSelection={typeof onSelect == "function" ? {
                    tableAlertOptionRender: false,
                    selectedRowKeys,
                    fixed: "left",
                    type: mode == "single" ? "radio" : "checkbox",
                    hideSelectAll: true,
                    getCheckboxProps: (record) => ({
                        disabled: record.flow_status != 3,
                    }),
                    onSelect: (record, selected, selectedRows, nativeEvent) => {
                        if (selected && !selectedRowKeys.includes(record.id)) {
                            if (mode == "single") setSelectedRowKeys([record.id])
                            else setSelectedRowKeys(prev => [...prev, record.id])
                        }
                        else if (!selected && selectedRowKeys.includes(record.id)) {
                            setSelectedRowKeys(prev => prev.filter(item => item != record.id))
                        }
                    },
                    // onChange: (selected, selectedRows, changeRows) => {
                    //     console.log("onChange", selected, selectedRows, changeRows)
                    //     setSelectedRowKeys(selectedRows.map(item => item.id))
                    // }
                } : false}
                summary={() => (
                    <TableSummary 
                        tableColumns={tableColumns}
                        requestParams={requestParams}
                        selectedRow={selectedRow}
                        onSelect={onSelect}
                        mode={mode}
                    />
                )}
            />
            {typeof onSelect == "function" && mode != "single" && (
                <div className='flex w-full items-center px-6'>
                    <div className='flex gap-2'>
                        <Button type="primary" onClick={() => { handleSelectAll() }}>全选</Button>
                        <Button onClick={() => { handleDeselectAll() }}>反选</Button>
                    </div>
                </div>
            )}
        </div>}

        <DraggableModal width={1280}
            destroyOnClose={true}
            title={formDataId > 0 ? "编辑单据" : "新建单据"}
            open={isModalOpen}
            maskClosable={false}
            // onOk={handleOk}
            zIndex={999}
            footer={
                formStatus == 7 ? null :
                    <div className='flex flex-row-reverse gap-x-2 '>
                        <Button type='primary' onClick={handleOk}>保存</Button>
                        <Button onClick={handleCancel}>取消</Button>
                    </div>
            }
            onCancel={handleCancel}>
            <FormViewEdit formTemplateId={formTemplateId} ref={editFormRef} formDataId={formDataId} copyFormDataId={copyFormDataId} />
        </DraggableModal>

        <DraggableModal width={600}
            destroyOnClose={true}
            title=""
            open={isImportModalOpen}
            zIndex={999}
            footer={null}
            onCancel={() => setIsImportModalOpen(false)}>
            <ImportExcel importTemplateId={importTemplateId} />
        </DraggableModal>


        <DraggableModal width={960}
            destroyOnClose={true}
            open={isExportModalOpen}
            zIndex={999}
            footer={null}
            onCancel={() => { setIsExportModalOpen(false) }}>
            <ExportExcelHistory searchFilters={searchFilters} ruleParams={ruleParams} exportTemplateId={exportTemplateId} />
        </DraggableModal>


        <Info2Flow formTemplateId={formTemplateId} formDataId={formDataId} ref={showDetailRef} />
        {contextHolder}

        {formTemplateId > 0 && <Invalid formTemplateId={formTemplateId} formDataId={formDataId} ref={invalidateRef} onOk={() => {
            tableActionRef.current?.reload()
        }} />}
        
        {/* 添加打印预览模态框 */}
        <PrintPreviewModal
            open={isPreviewModalOpen}
            onCancel={() => {
                setIsPreviewModalOpen(false);
                setPrintHtmlContent('');
                if (editorInstance) {
                    editorInstance.destroy();
                    setEditorInstance(null);
                }
            }}
            onConfirm={handleConfirmPrintFromModal}
            htmlContent={printHtmlContent}
            onHtmlChange={setPrintHtmlContent}
            editorInstance={editorInstance}
            setEditorInstance={setEditorInstance}
            loading={isGeneratingPdf || isLoadingHtml}
        />
    </PageContainer>
}

export default FormView;
