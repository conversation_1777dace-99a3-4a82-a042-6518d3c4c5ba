import { useContext, useEffect, useState } from 'react';
import { FormDesignContext, FormDesignProvider } from '@/components';
import { Switch, Modal, Form, Input, Button } from "antd";
import { SettingOutlined } from '@ant-design/icons';

// 组件注册表
const ComponentRegistry = {
    "Switch": Switch,
    "Input": Input,
    // 可以在这里添加更多组件
};

// 根据组件类型渲染对应的组件
const DynamicFormItem = ({ type, ...props }) => {
    const Component = ComponentRegistry[type] || Input;
    return <Component {...props} />;
};

const TableAction = () => {
    const { formDesign, updateFormDesign, setFormDesign } = useContext(FormDesignContext);
    const selectedIds = formDesign.table_design.actions;
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [currentAction, setCurrentAction] = useState(null);
    const [form] = Form.useForm();

    const toggleSelected = (columnId) => {
        const selectedIds = formDesign.table_design.actions;
        const updatedIds = selectedIds.includes(columnId)
            ? selectedIds.filter(id => id !== columnId)
            : [...selectedIds, columnId];

        updateFormDesign("table_design", {
            ...formDesign.table_design,
            actions: updatedIds,
        });
    };

    // 检查action是否有设置值
    const hasActionSettings = (actionId) => {
        return !!formDesign.table_design.actionSettings?.[actionId];
    };

    const handleSettingClick = (action) => {
        setCurrentAction(action);
        setIsModalVisible(true);
        // 如果有已保存的设置，设置表单初始值
        if (formDesign.table_design.actionSettings?.[action.id]) {
            form.setFieldsValue(formDesign.table_design.actionSettings[action.id]);
        }
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            updateFormDesign("table_design", {
                ...formDesign.table_design,
                actionSettings: {
                    ...formDesign.table_design.actionSettings,
                    [currentAction.id]: values
                }
            });
            setIsModalVisible(false);
            form.resetFields();
        } catch (error) {
            console.error('Validation failed:', error);
        }
    };

    const TableAction = [
        { id: "actionNew", label: "新建" },
        { id: "actionEdit", label: "修改" },
        { id: "actionDelete", label: "删除" },
        { id: "actionVoid", label: "作废" },
        {
            id: "actionPrint", label: "打印", setter: [
                {
                    id: "actionPrintAllowUnAudit",
                    label: "允许未审核前打印",
                    component: "Switch",
                    defaultValue: false,
                },
                // {
                //     id: "actionPrintAllowEditBefore",
                //     label: "允许打印前进行修改",
                //     component: "Switch",
                //     defaultValue: false,
                // }
            ]
        },
        { id: "actionExportExcel", label: "导出数据到Excel" },
        { id: "actionImportExcel", label: "从Excel导入数据" },
        { id: "actionSignBack", label: "回签" },
    ]

    return (
        <div className="-my-3">
            {TableAction.map(action => (
                <div className="h-7 flex gap-1 w-full justify-between items-center" key={action.id}>
                    <div className="flex items-center gap-2 flex-1">
                        <span>{action.label}</span>
                    </div>
                    {action.setter && action.setter.length > 0 && (
                        <Button 
                            size="small" 
                            className='px-2' 
                            type={hasActionSettings(action.id) ? "primary" : "default"}
                            onClick={() => handleSettingClick(action)}
                        >
                            <SettingOutlined/>
                        </Button>
                    )}
                    <Switch checked={selectedIds.includes(action.id)} onChange={() => toggleSelected(action.id)} />
                </div>
            ))}

            <Modal
                title={`${currentAction?.label || ''} 设置`}
                open={isModalVisible}
                onOk={handleModalOk}
                onCancel={() => {
                    setIsModalVisible(false);
                    form.resetFields();
                }}
            >
                <Form form={form} layout="vertical">
                    {currentAction?.setter?.map(setting => (
                        <Form.Item
                            key={setting.id}
                            name={setting.id}
                            label={setting.label}
                            initialValue={setting.defaultValue}
                        >
                            <DynamicFormItem type={setting.component} />
                        </Form.Item>
                    ))}
                </Form>
            </Modal>
        </div>
    )
}

export default TableAction;