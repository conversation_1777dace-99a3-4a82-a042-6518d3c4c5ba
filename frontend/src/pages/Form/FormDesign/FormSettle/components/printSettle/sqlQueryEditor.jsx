import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Select, Button, Space, Row, Col, Alert, Tabs, message } from 'antd';
import { Ace } from '@/components';
import SqlParameterBinder from './sqlParameterBinder';
import SqlColumnConfig from './sqlColumnConfig';
import SqlTestResult from './sqlTestResult';

const { TextArea } = Input;
const { TabPane } = Tabs;

const SqlQueryEditor = ({ value, sourceFields, onSave, onCancel, onTest }) => {
    const [formData, setFormData] = useState(value || {});
    const [testResult, setTestResult] = useState(null);
    const [testing, setTesting] = useState(false);
    const [activeTab, setActiveTab] = useState('basic');
    const aceRef = useRef(null);

    useEffect(() => {
        if (value) {
            setFormData(value);
        }
    }, [value]);

    const handleTest = async () => {
        if (!formData.sql?.trim()) {
            message.error('请输入SQL语句');
            return;
        }

        setTesting(true);
        try {
            const result = await onTest(formData);
            setTestResult(result);
            
            // 如果是表格类型，自动生成列配置
            if (formData.type === 'table' && result.data && result.data.length > 0) {
                const sampleRecord = result.data[0];
                const columns = Object.keys(sampleRecord).map(key => ({
                    key,
                    label: key,
                    alias: key,
                    show: true,
                    dataType: typeof sampleRecord[key]
                }));
                
                // 立即更新formData状态
                const updatedFormData = { ...formData, columns };
                setFormData(updatedFormData);
                
                // 提示用户列配置已生成
                message.success(`已自动生成 ${columns.length} 个列配置，可在"列配置"标签页中调整`);
                
                // 如果当前在基本设置页面，建议用户切换到列配置页面
                if (activeTab === 'basic') {
                    setTimeout(() => {
                        setActiveTab('columns');
                    }, 1000);
                }
            }

            // 切换到测试结果Tab
            setActiveTab('test');
        } catch (error) {
            message.error(`测试失败: ${error.message}`);
        } finally {
            setTesting(false);
        }
    };

    const handleSave = async () => {
        if (!formData.name?.trim()) {
            message.error('请输入查询名称');
            return;
        }
        if (!formData.sql?.trim()) {
            message.error('请输入SQL语句');
            return;
        }

        // 如果是表格类型且没有列配置，尝试自动调用测试接口获取列信息
        if (formData.type === 'table' && (!formData.columns || formData.columns.length === 0)) {
            try {
                setTesting(true);
                message.loading('正在获取列信息...', 0);
                
                const result = await onTest(formData);
                message.destroy(); // 清除loading消息
                
                // 如果测试成功且有数据，自动生成列配置
                if (result?.data?.data && result.data.data.length > 0) {
                    const sampleRecord = result.data.data[0];
                    const columns = Object.keys(sampleRecord).map(key => ({
                        key,
                        label: key,
                        alias: key,
                        show: true,
                        dataType: typeof sampleRecord[key]
                    }));
                    
                    // 更新表单数据并保存
                    const updatedFormData = { ...formData, columns };
                    setFormData(updatedFormData);
                    
                    // message.success(`已自动生成 ${columns.length} 个列配置并保存`);
                    
                    const { isNew, ...queryData } = updatedFormData;
                    onSave(queryData);
                    return;
                } else {
                    message.warning('SQL查询结果为空，无法生成列配置。查询已保存，请检查SQL语句。');
                }
            } catch (error) {
                message.destroy(); // 清除loading消息
                message.warning(`无法获取列信息: ${error.message || '请检查SQL语句'}。查询已保存，建议手动测试后完善列配置。`);
            } finally {
                setTesting(false);
            }
        }

        // 清理不需要的字段
        const { isNew, ...queryData } = formData;
        onSave(queryData);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => {
            const newData = { ...prev, [field]: value };
            if (field === 'columns') {
                // 列配置更新
            }
            return newData;
        });
    };

    const handleSqlChange = (sql) => {
        setFormData(prev => ({ ...prev, sql }));
        // 清空之前的测试结果
        setTestResult(null);
    };

    // 解析SQL中的参数占位符
    const extractParameters = (sql) => {
        if (!sql) return [];
        const regex = /#{([^}]+)}/g;
        const matches = [];
        let match;
        while ((match = regex.exec(sql)) !== null) {
            if (!matches.includes(match[1])) {
                matches.push(match[1]);
            }
        }
                return matches;
    };

    const sqlParameters = extractParameters(formData.sql);

    return (
        <div className="sql-query-editor">
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
                <TabPane tab="基本设置" key="basic">
                    <div className="p-4">
                        <Form layout="vertical">
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item label="查询名称" required>
                                        <Input 
                                            value={formData.name}
                                            onChange={(e) => handleFormChange('name', e.target.value)}
                                            placeholder="请输入查询名称"
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label="查询类型" required>
                                        <Select 
                                            value={formData.type}
                                            onChange={(type) => handleFormChange('type', type)}
                                        >
                                            <Select.Option value="table">表格数据</Select.Option>
                                            <Select.Option value="value">单个值</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Form.Item label="描述">
                                <TextArea 
                                    value={formData.description}
                                    onChange={(e) => handleFormChange('description', e.target.value)}
                                    placeholder="请输入查询描述"
                                    rows={2}
                                />
                            </Form.Item>

                            <Form.Item label="SQL语句" required>
                                <div className="border rounded-lg">
                                    <div className="bg-gray-100 px-3 py-2 border-b text-sm text-gray-600">
                                        SQL编辑器 - 支持参数绑定格式: #{`{field_name}`}
                                    </div>
                                    <div style={{ height: '300px' }}>
                                        <Ace 
                                            ref={aceRef}
                                            value={formData.sql}
                                            onChange={handleSqlChange}
                                            placeholder="-- 请输入SQL查询语句
-- 支持参数绑定，格式：#{field_name}
-- 例如：SELECT * FROM users WHERE id = #{user_id}

SELECT * FROM your_table 
WHERE created_at >= '2024-01-01'"
                                            theme="github"
                                            mode="sql"
                                        />
                                    </div>
                                </div>
                            </Form.Item>

                            {sqlParameters.length > 0 && (
                                <Alert
                                    type="info"
                                    message={`检测到 ${sqlParameters.length} 个参数: ${sqlParameters.join(', ')}`}
                                    className="mb-4"
                                />
                            )}
                        </Form>
                    </div>
                </TabPane>

                <TabPane tab="参数绑定" key="parameters">
                    <div className="p-4">
                        <SqlParameterBinder 
                            value={formData.parameters}
                            onChange={(parameters) => handleFormChange('parameters', parameters)}
                            sourceFields={sourceFields}
                            sqlParameters={sqlParameters}
                        />
                    </div>
                </TabPane>

                {formData.type === 'table' && (
                    <TabPane tab="列配置" key="columns">
                        <div className="p-4">
                            <SqlColumnConfig 
                                value={formData.columns}
                                onChange={(columns) => handleFormChange('columns', columns)}
                                testResult={testResult}
                            />
                        </div>
                    </TabPane>
                )}

                <TabPane tab="测试结果" key="test">
                    <div className="p-4">
                        <SqlTestResult 
                            result={testResult} 
                            type={formData.type}
                            loading={testing}
                        />
                    </div>
                </TabPane>
            </Tabs>

            <div className="flex justify-between items-center p-4 border-t bg-gray-50">
                <div>
                    <Button onClick={handleTest} loading={testing}>
                        测试查询
                    </Button>
                </div>
                <Space>
                    <Button onClick={onCancel}>
                        取消
                    </Button>
                    <Button type="primary" onClick={handleSave} loading={testing}>
                        保存
                    </Button>
                </Space>
            </div>
        </div>
    );
};

export default SqlQueryEditor; 