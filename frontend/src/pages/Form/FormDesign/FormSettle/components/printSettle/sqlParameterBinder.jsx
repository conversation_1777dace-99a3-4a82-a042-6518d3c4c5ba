import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Button, Select, Input, Space, Alert, Empty, TreeSelect, Modal } from 'antd';
import { SortList } from '@/components';
import { HolderOutlined, DeleteOutlined, PlusOutlined, FunctionOutlined } from '@ant-design/icons';
import { EditCode } from "@/components";

const ParameterBindingItem = ({ parameter, value, onChange, sourceFields, listeners }) => {
    const [selectedField, setSelectedField] = useState(value?.fieldPath || '');
    const [defaultValue, setDefaultValue] = useState(value?.defaultValue || '');
    const [showFuncCodeEdit, setShowFuncCodeEdit] = useState(false);

    useEffect(() => {
        setSelectedField(value?.fieldPath || '');
        setDefaultValue(value?.defaultValue || '');
    }, [value]);

    // 构建树形数据，使用 useMemo 缓存
    const treeData = useMemo(() => {
        console.log("buildTreeData ing");
        const treeData = [];
        
        // 当前用户信息
        treeData.push({
            title: '当前用户',
            value: 'currentUser',
            key: 'currentUser',
            selectable: false,
            children: [
                { title: '用户ID', value: 'currentUser.user_id', key: 'currentUser.user_id' },
                { title: '用户名', value: 'currentUser.username', key: 'currentUser.username' },
                { title: '部门ID', value: 'currentUser.dept_id', key: 'currentUser.dept_id' },
                { title: '部门IDs', value: 'currentUser.dept_ids', key: 'currentUser.dept_ids' },
                { title: '岗位IDs', value: 'currentUser.post_ids', key: 'currentUser.post_ids' },
                { title: '角色代码', value: 'currentUser.role_codes', key: 'currentUser.role_codes' },
                { title: '手机号', value: 'currentUser.phonenumber', key: 'currentUser.phonenumber' },
                { title: '邮箱', value: 'currentUser.email', key: 'currentUser.email' }
            ]
        });

        console.log("sourceFields", sourceFields);
        const formFields = sourceFields.filter(field => !field.key.startsWith('sql_')).map(field => {
            const children = field.fields.map(fieldItem => ({
                title: fieldItem.label,
                value: `${field.key}.${fieldItem.id}`,
                key: `${field.key}.${fieldItem.id}`,
            }));
            return {
                title: field.label,
                value: field.key,
                key: field.key,
                selectable: false,
                children: children
            };
        });
        
        console.log("formFields", formFields);
        treeData.push(...formFields);
        
        return treeData;
    }, [sourceFields]);

    // 简化的解析字段路径方法 - 直接从 treeData 中查找
    const parseFieldPath = useCallback((path) => {
        if (!path) return { label: '' };
        
        // 递归查找 treeData 中对应的 title
        const findLabelInTree = (nodes, targetValue) => {
            for (const node of nodes) {
                if (node.value === targetValue) {
                    return node.title;
                }
                if (node.children) {
                    const childResult = findLabelInTree(node.children, targetValue);
                    if (childResult) {
                        // 如果是子节点，返回父节点 > 子节点的格式
                        return `${node.title} > ${childResult}`;
                    }
                }
            }
            return null;
        };
        
        const label = findLabelInTree(treeData, path);
        return { label: label || path }; // 如果找不到就返回原始路径
    }, [treeData]);

    const handleFieldChange = (fieldPath) => {
        setSelectedField(fieldPath);
        const fieldInfo = parseFieldPath(fieldPath);
        onChange({
            ...value,
            parameter,
            fieldPath,
            fieldLabel: fieldInfo.label,
            defaultValue,
            required: true
        });
    };

    const handleDefaultValueChange = (defaultValue) => {
        setDefaultValue(defaultValue);
        onChange({
            ...value,
            defaultValue
        });
    };



    return (
        <>
            <div className="p-3 bg-gray-50 border rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                    <Button
                        type="text"
                        size="small"
                        icon={<HolderOutlined />}
                        style={{ cursor: 'move' }}
                        {...listeners}
                    />
                    <div className="flex-1 text-sm font-medium text-gray-800">
                        参数：#{`{${parameter}}`}
                    </div>
                    <Button 
                        type={value?.funcCode?.codeContent ? "primary" : "default"} 
                        size='small' 
                        icon={<FunctionOutlined />}
                        onClick={() => setShowFuncCodeEdit(true)}
                        title="代码处理"
                    />
                    <Button
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => onChange(null)}
                        title="删除"
                    />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                    <div>
                        <TreeSelect
                            value={selectedField}
                            onChange={handleFieldChange}
                            placeholder="选择绑定字段"
                            style={{ width: '100%' }}
                            showSearch
                            treeData={treeData}
                            treeDefaultExpandAll
                        />
                    </div>
                    <div>
                        <Input
                            value={defaultValue}
                            onChange={(e) => handleDefaultValueChange(e.target.value)}
                            placeholder="默认值（可选）"
                        />
                    </div>
                    <div className="text-xs text-gray-500 self-center">
                        {value?.fieldLabel && (
                            <span>已绑定: {value.fieldLabel}</span>
                        )}
                        {value?.funcCode?.codeContent && (
                            <span className="text-blue-600 ml-2">[已设置代码处理]</span>
                        )}
                    </div>
                </div>
            </div>

            <Modal
                destroyOnClose={true}
                width={960}
                zIndex={999}
                title={`编辑参数处理代码 - #{${parameter}} (编辑完成后直接关闭即可)`}
                open={showFuncCodeEdit}
                onCancel={() => setShowFuncCodeEdit(false)}
                footer={null}
            >
                <EditCode 
                    formColumns={sourceFields} 
                    value={value?.funcCode} 
                    onChange={(e) => {
                        return onChange({ ...value, funcCode: e })
                    }}
                    injectNotes={
                        <>
                            <br />__current : 当前参数的绑定值
                            <br />__formData : 当前表单数据，可使用__formData['字段名']获取表单字段值
                            <br />__userInfo : 当前用户信息对象
                        </>
                    }
                    otherNotes={
                        <div>
                            记得将处理过的参数值<span className='text-orange-500'>return</span>哦
                            <br />
                            <span className="text-sm text-gray-600">
                                此代码将在SQL参数替换时执行，可以对绑定的字段值进行自定义处理
                            </span>
                        </div>
                    }
                    label="" 
                    __id="funcCode" 
                />
            </Modal>
        </>
    );
};

const SqlParameterBinder = ({ value = [], onChange, sourceFields = [], sqlParameters = [] }) => {
    const [parameterBindings, setParameterBindings] = useState([]);

    useEffect(() => {
        // 根据SQL参数和现有绑定生成参数绑定列表
        const bindings = sqlParameters.map(param => {
            const existing = value.find(v => v.parameter === param);
            return existing || {
                parameter: param,
                fieldPath: '',
                fieldLabel: '',
                defaultValue: '',
                required: true,
                funcCode: null
            };
        });
        setParameterBindings(bindings);
    }, [sqlParameters, value]);

    const handleBindingChange = (index, binding) => {
        if (binding === null) {
            // 删除绑定
            const newBindings = parameterBindings.filter((_, i) => i !== index);
            setParameterBindings(newBindings);
            onChange(newBindings);
        } else {
            // 更新绑定
            const newBindings = [...parameterBindings];
            newBindings[index] = binding;
            setParameterBindings(newBindings);
            onChange(newBindings);
        }
    };

    if (sqlParameters.length === 0) {
        return (
            <div className="text-center py-8">
                <Empty
                    description="没有检测到SQL参数"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
                <div className="text-sm text-gray-500 mt-2">
                    在SQL语句中使用 #{`{参数名}`} 格式添加参数
                </div>
            </div>
        );
    }

    return (
        <div className="sql-parameter-binder">
            <div className="mb-4">
                <Alert
                    type="info"
                    message="参数绑定说明"
                    description={
                        <div>
                            <div>为SQL查询中的每个参数绑定对应的表单字段，运行时将自动替换参数值</div>
                            <div className="mt-2 text-xs">
                                <strong>支持的绑定类型：</strong>
                                <br />• <span className="text-green-600">当前用户</span>：绑定当前登录用户的信息（用户ID、用户名、部门等）
                                <br />• <span className="text-blue-600">当前表单</span>：直接绑定当前表单中的字段值
                                <br />• <span className="text-orange-600">关联表</span>：支持关联表的字段值，自动查询关联数据
                                <br />• <span className="text-purple-600">代码处理</span>：可以通过自定义代码对绑定值进行处理
                            </div>
                        </div>
                    }
                />
            </div>

            <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700">参数绑定配置</h4>
                <div className="text-xs text-gray-500">
                    检测到 {sqlParameters.length} 个参数需要绑定
                </div>
                
                {/* 添加表头 */}
                <div className="mt-3 grid grid-cols-3 gap-4 px-12 py-2 bg-gray-100 rounded text-sm text-gray-600 font-medium">
                    <div>绑定字段</div>
                    <div>默认值</div>
                    <div>状态信息</div>
                </div>
            </div>

            <SortList
                items={parameterBindings}
                setItems={(updater) => {
                    const newItems = typeof updater === 'function' ? updater(parameterBindings) : updater;
                    setParameterBindings(newItems);
                    onChange(newItems);
                }}
                renderItem={(item, index, listeners) => (
                    <ParameterBindingItem
                        key={item.parameter}
                        parameter={item.parameter}
                        value={item}
                        onChange={(binding) => handleBindingChange(index, binding)}
                        sourceFields={sourceFields}
                        listeners={listeners}
                    />
                )}
            />

            {parameterBindings.length > 0 && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="text-sm text-blue-800">
                        <strong>绑定预览：</strong>
                    </div>
                    <div className="mt-2 space-y-1">
                        {parameterBindings.map(binding => {
                            const isCurrentUser = binding.fieldPath?.startsWith('currentUser.');
                            const isCurrentForm = binding.fieldPath?.startsWith('current.');
                            const isRelatedField = binding.fieldPath?.includes('.') && !isCurrentUser && !isCurrentForm;
                            const hasCodeProcessor = binding.funcCode?.codeContent;
                            
                            return (
                                <div key={binding.parameter} className="text-xs text-blue-700">
                                    <code>#{`{${binding.parameter}}`}</code> → {binding.fieldLabel || '未绑定'}
                                    {isCurrentUser && <span className="text-green-600"> [当前用户]</span>}
                                    {isCurrentForm && <span className="text-blue-600"> [当前表单]</span>}
                                    {isRelatedField && <span className="text-orange-600"> [关联表]</span>}
                                    {hasCodeProcessor && <span className="text-purple-600"> [代码处理]</span>}
                                    {binding.defaultValue && <span className="text-gray-500"> (默认: {binding.defaultValue})</span>}
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SqlParameterBinder; 