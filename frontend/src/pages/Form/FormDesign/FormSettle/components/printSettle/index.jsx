import { FolderAddFilled } from "@ant-design/icons";
import { ProTable } from "@ant-design/pro-components";
import { <PERSON><PERSON>, Drawer, Switch, Popconfirm, message } from "antd";
import { Fetch<PERSON><PERSON><PERSON>, FetchList, FetchStatus, FetchAllowEditStatus } from "@/services/crm-api/form-print-template"
import React, { useEffect } from "react";
import Edit from "./edit";
const PrintTemplateList = ({ formTemplateId }) => {
    const tableActionRef = React.useRef();
    const [editPrintTemplate, setEditPrintTemplate] = React.useState(null);
    const [showEdit, setShowEdit] = React.useState(false);
    useEffect(() => {
        if (tableActionRef.current) {
            tableActionRef.current.reload();
        }
    }, [formTemplateId]);

    const columns = [
        {
            title: '打印模板名称',
            dataIndex: 'name',
            search: false,
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            valueType: 'dateTime',
            width: 200,
            search: false
        },
        {
            title: '是否启用',
            dataIndex: 'status',
            width: 100,
            search: false,
            render: (text, record, index, action) => {
                return <Switch checked={text == 1} onChange={(checked) => {
                    handlePrintTemplateAction(record, (id) => {
                        return FetchStatus(id, checked)
                    }, "修改状态")
                }} />
            }
        },
        {
            title: '允许打印前编辑',
            dataIndex: 'allowEditBefore',
            width: 150,
            search: false,
            render: (text, record, index, action) => {
                return <Switch checked={text == 1} onChange={(checked) => {
                    handlePrintTemplateAction(record, (id) => {
                        return FetchAllowEditStatus(id, checked)
                    }, "修改打印前编辑状态")
                }} />
            }
        },
        {
            title: '操作',
            valueType: 'option',
            dataIndex: 'option',
            fixed: "right",
            width: 150,
            render: (text, record, index, action) => {
                return <div>
                    <Button type="link" size="small" onClick={() => handleEdit(record)}>
                        编辑
                    </Button>

                    <Popconfirm
                        title="删除后不能恢复，确认删除?"
                        onConfirm={() => handlePrintTemplateAction(record, FetchDelete, "删除")}
                        okText="确认删除"
                        cancelText="取消"
                    >
                        <Button type="link" danger size="small">
                            删除
                        </Button>
                    </Popconfirm>
                </div>
            }
        }
    ]
    const handleNew = () => {
        console.log('新建')
        setEditPrintTemplate(null)
        setShowEdit(true)
    }

    const handleEdit = (record) => {
        console.log('编辑', record)
        setEditPrintTemplate(record)
        setShowEdit(true)
    }

    const handlePrintTemplateAction = async (record, fetch, actionName = "操作") => {
        if (fetch && typeof fetch === "function") {
            const res = await fetch(record.id)
            if (res.code > 0) {
                message.error(res.message)
                return
            }
            await tableActionRef?.current?.reload()
            message.success(`${actionName}成功`)
        }
    }

    return (
        <>
            <ProTable
                scroll={{
                    x: 660 + 150,
                }}
                actionRef={tableActionRef}
                request={(params) => {
                    params = { ...params, formTemplateId }
                    return FetchList(params)
                }}
                toolbar={{
                    search: <Button type='primary' onClick={handleNew} icon={<FolderAddFilled />}>新建</Button>
                }}
                search={false}
                columns={columns} />
            <Drawer width="100vw"
                closeIcon={null}
                className=' p-0'
                styles={{
                    body: {
                        padding: 0,
                        background: '#edeff3',
                    }
                }}
                open={showEdit}
                zIndex={999}
                destroyOnClose={true} >
                <Edit
                formTemplateId={formTemplateId}
                    id={editPrintTemplate?.id}
                    onCancel={() => { setShowEdit(false) }}
                    onOK={async () => {
                        await tableActionRef?.current?.reload()
                        message.success("保存成功")
                        setShowEdit(false)
                    }}
                />
            </Drawer>

        </>
    )
}
export default PrintTemplateList;