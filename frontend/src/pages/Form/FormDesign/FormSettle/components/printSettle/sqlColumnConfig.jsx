import React, { useState, useEffect } from 'react';
import { Switch, Input, Button, Alert, Empty, Tag } from 'antd';
import { SortList } from '@/components';
import { HolderOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';

const SqlColumnItem = ({ column, value, onChange, listeners }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [alias, setAlias] = useState(value?.alias || column.label);

    useEffect(() => {
        setAlias(value?.alias || column.label);
    }, [value, column]);

    const handleShowChange = (show) => {
        onChange({
            ...value,
            show
        });
    };

    const handleAliasChange = (newAlias) => {
        setAlias(newAlias);
        // 实时更新，但不立即保存到父组件
    };

    const handleAliasBlur = () => {
        setIsEditing(false);
        const finalAlias = alias.trim() || column.label;
        setAlias(finalAlias);
        
        // 失去焦点时才保存到父组件
        onChange({
            ...value,
            alias: finalAlias
                });
      };

    const handleAliasEnter = () => {
        handleAliasBlur(); // 按回车时也触发保存
    };

    const getDataTypeColor = (dataType) => {
        switch (dataType) {
            case 'string': return 'blue';
            case 'number': return 'green';
            case 'boolean': return 'orange';
            case 'object': return 'purple';
            default: return 'default';
        }
    };

    return (
        <div className="flex items-center gap-3 p-1 bg-white border-b rounded hover:shadow-sm transition-shadow">
            {/* 拖拽手柄 */}
            <Button
                type="text"
                size="small"
                icon={<HolderOutlined />}
                style={{ cursor: 'move' }}
                {...listeners}
            />
            
            {/* 显示/隐藏开关 */}
            <Switch
                size="small"
                checked={value?.show}
                onChange={handleShowChange}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
            />

            {/* 原字段名 */}
            <div className="w-32 flex items-center gap-1">
                <code className="bg-gray-100 px-2 py-1 rounded text-xs flex-1 truncate">
                    {column.key}
                </code>
                {column.dataType && (
                    <Tag color={getDataTypeColor(column.dataType)} size="small" className="text-xs">
                        {column.dataType}
                    </Tag>
                )}
            </div>
            
            {/* 显示名称 */}
            <div className="flex-1 min-w-0">
                {isEditing ? (
                    <Input
                        value={alias}
                        onChange={(e) => handleAliasChange(e.target.value)}
                        onBlur={handleAliasBlur}
                        onPressEnter={handleAliasEnter}
                        size="small"
                        autoFocus
                    />
                ) : (
                    <div
                        className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded text-sm truncate"
                        onClick={() => setIsEditing(true)}
                        title={alias || column.label}
                    >
                        {alias || column.label}
                    </div>
                )}
            </div>

            {/* 状态标签 */}
            <div className="w-12">
                <Tag 
                    color={value?.show ? 'success' : 'default'} 
                    size="small"
                    className="text-xs"
                >
                    {value?.show ? '显' : '隐'}
                </Tag>
            </div>
        </div>
    );
};

const SqlColumnConfig = ({ value = [], onChange, testResult }) => {
    const [columns, setColumns] = useState([]);

    useEffect(() => {
        // 确保 value 是数组
        const currentValue = Array.isArray(value) ? value : [];
        
        // 从测试结果中提取列信息
        if (testResult?.data?.data && testResult.data.data.length > 0) {
            const sampleRecord = testResult.data.data[0];
            const detectedColumns = Object.keys(sampleRecord).map(key => ({
                key,
                label: key,
                dataType: typeof sampleRecord[key]
            }));

            // 合并现有配置和检测到的列
            const mergedColumns = detectedColumns.map(detectedCol => {
                const existingCol = currentValue.find(v => v.key === detectedCol.key);
                return {
                    ...detectedCol,
                    show: existingCol?.show ?? true,
                    alias: existingCol?.alias || detectedCol.label
                };
            });

            setColumns(mergedColumns);
            
            // 如果配置发生变化，通知父组件
            if (JSON.stringify(mergedColumns) !== JSON.stringify(currentValue)) {
                onChange(mergedColumns);
            }
        } else if (currentValue.length > 0) {
            // 如果没有测试结果但有现有配置，使用现有配置
            setColumns(currentValue);
        }
    }, [testResult, value, onChange]);

    const handleColumnChange = (index, newColumn) => {
        const newColumns = [...columns];
        newColumns[index] = newColumn;
        setColumns(newColumns);
        
        // 立即通知父组件更新
        if (onChange) {
            onChange(newColumns);
        }
    };

    const handleColumnsReorder = (updater) => {
        const newColumns = typeof updater === 'function' ? updater(columns) : updater;
        setColumns(newColumns);
        // 立即通知父组件更新
        if (onChange) {
            onChange(newColumns);
        }
    };

    const handleShowAll = () => {
        const newColumns = columns.map(col => ({ ...col, show: true }));
        setColumns(newColumns);
        
        // 立即通知父组件更新
        if (onChange) {
            onChange(newColumns);
        }

    };

    const handleHideAll = () => {
        const newColumns = columns.map(col => ({ ...col, show: false }));
        setColumns(newColumns);
        
        // 立即通知父组件更新
        if (onChange) {
            onChange(newColumns);
        }

    };

    if (columns.length === 0) {
        return (
            <div className="text-center py-8">
                <Empty
                    description="暂无列配置"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
                <div className="text-sm text-gray-500 mt-2">
                    请先测试SQL查询以获取列信息
                </div>
            </div>
        );
    }

    const visibleCount = columns.filter(col => col.show).length;
    const totalCount = columns.length;

    return (
        <div className="sql-column-config">
            <div className="mb-4">
                <Alert
                    type="info"
                    message="列配置说明"
                    description="配置SQL查询结果表格的列显示，可以调整列的顺序、显示状态和显示名称"
                />
            </div>

            <div className="flex justify-between items-center mb-4">
                <div>
                    <h4 className="text-sm font-medium text-gray-700">列配置</h4>
                    <div className="text-xs text-gray-500">
                        共 {totalCount} 列，显示 {visibleCount} 列
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button size="small" onClick={handleShowAll}>
                        全部显示
                    </Button>
                    <Button size="small" onClick={handleHideAll}>
                        全部隐藏
                    </Button>
                </div>
            </div>

            {/* 表头 */}
            <div className="flex items-center gap-3 p-2 bg-gray-50 border rounded text-sm text-gray-600 font-medium">
                <div className="w-8"></div> {/* 拖拽手柄占位 */}
                <div className="w-8 text-center">显示</div> {/* 开关占位 */}
                <div className="w-32">原字段名</div>
                <div className="flex-1">显示名称</div>
                <div className="w-12 text-center">状态</div>
            </div>

            <SortList
                items={columns}
                setItems={handleColumnsReorder}
                keyName="key"
                renderItem={(item, index, listeners) => (
                    <SqlColumnItem
                        key={item.key}
                        column={item}
                        value={item}
                        onChange={(newColumn) => handleColumnChange(index, newColumn)}
                        listeners={listeners}
                    />
                )}
            />

            {visibleCount > 0 && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="text-sm text-green-800">
                        <strong>显示预览：</strong>
                    </div>
                    <div className="mt-2 flex flex-wrap gap-2">
                        {columns
                            .filter(col => col.show)
                            .map((col, index) => (
                                <Tag key={col.key} color="success">
                                    {index + 1}. {col.alias}
                                </Tag>
                            ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SqlColumnConfig; 