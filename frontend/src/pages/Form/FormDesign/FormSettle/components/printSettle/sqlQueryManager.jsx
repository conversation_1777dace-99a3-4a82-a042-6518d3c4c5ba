import React, { useState, useEffect, useRef } from 'react';
import { Button, Modal, message, Space, Popconfirm, Tag } from 'antd';
import { SortList } from '@/components';
import { HolderOutlined, DeleteOutlined, EditOutlined, PlayCircleOutlined, DatabaseOutlined } from '@ant-design/icons';
import SqlQueryEditor from './sqlQueryEditor';

const SqlQueryItem = ({ query, listeners, onEdit, onDelete, onTest, onInsert }) => {
    return (
        <div className="flex items-center gap-2 p-3 bg-white border rounded-lg">
            <Button
                type="text"
                size="small"
                icon={<HolderOutlined />}
                style={{ cursor: 'move' }}
                {...listeners}
            />
            <DatabaseOutlined className="text-blue-500" />
            <div className="flex-1">
                <div className="font-medium">{query.name}</div>
                <div className="text-gray-500 text-sm">
                    <Tag color={query.type === 'table' ? 'blue' : 'green'}>
                        {query.type === 'table' ? '表格数据' : '单值数据'}
                    </Tag>
                    {query.description && <span className="ml-2">{query.description}</span>}
                </div>
            </div>
            <Space>
                <Button size="small" type="primary" onClick={onInsert}>
                    插入模板
                </Button>
                <Button size="small" icon={<PlayCircleOutlined />} onClick={onTest}>
                    测试
                </Button>
                <Button size="small" icon={<EditOutlined />} onClick={onEdit}>
                    编辑
                </Button>
                <Popconfirm title="确定删除此SQL查询吗？" onConfirm={onDelete}>
                    <Button size="small" danger icon={<DeleteOutlined />}>
                        删除
                    </Button>
                </Popconfirm>
            </Space>
        </div>
    );
};

const SqlQueryManager = ({ value = [], onChange, sourceFields, onTest, onInsert, editingQuery: propEditingQuery }) => {
    const [editingQuery, setEditingQuery] = useState(null);
    const [showSqlEditor, setShowSqlEditor] = useState(false);

    // 处理从外部传入的编辑查询
    useEffect(() => {
        if (propEditingQuery) {
            setEditingQuery({ ...propEditingQuery, isNew: false });
            setShowSqlEditor(true);
        }
    }, [propEditingQuery]);

    const handleAddQuery = () => {
        setEditingQuery({
            id: Date.now().toString(),
            name: '',
            type: 'table',
            sql: '',
            description: '',
            parameters: [],
            columns: [],
            cache: true,
            mount: '',
            isNew: true
        });
        setShowSqlEditor(true);
    };

    const handleEditQuery = (query) => {
        setEditingQuery({ ...query, isNew: false });
        setShowSqlEditor(true);
    };

    const handleSaveQuery = (queryData) => {
        const newQueries = editingQuery.isNew 
            ? [...value, queryData]
            : value.map(q => q.id === queryData.id ? queryData : q);
        onChange(newQueries);
        setShowSqlEditor(false);
        setEditingQuery(null);
    };

    const handleDeleteQuery = (index) => {
        onChange(value.filter((_, i) => i !== index));
    };

    const handleTestQuery = async (queryData) => {
        if (typeof onTest === 'function') {
            try {
                await onTest(queryData);
            } catch (error) {
                console.error('测试查询失败:', error);
            }
        }
    };

    const handleInsertQuery = (queryData) => {
        if (typeof onInsert === 'function') {
            onInsert(queryData);
        }
    };

    return (
        <div className="sql-query-manager">
            <div className="flex justify-between items-center mb-4">
                <Button type="primary" onClick={handleAddQuery}>
                    添加SQL查询
                </Button>
            </div>
            
            {value.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                    <DatabaseOutlined style={{ fontSize: 48 }} />
                    <div className="mt-2">暂无SQL查询配置</div>
                    <Button type="link" onClick={handleAddQuery}>立即添加</Button>
                </div>
            ) : (
                <SortList
                    items={value}
                    setItems={(updater) => {
                        const newItems = typeof updater === 'function' ? updater(value) : updater;
                        onChange(newItems);
                    }}
                    renderItem={(item, index, listeners) => (
                        <SqlQueryItem 
                            key={item.id}
                            query={item}
                            listeners={listeners}
                            onEdit={() => handleEditQuery(item)}
                            onDelete={() => handleDeleteQuery(index)}
                            onTest={() => handleTestQuery(item)}
                            onInsert={() => handleInsertQuery(item)}
                        />
                    )}
                />
            )}

            <Modal
                title={`${editingQuery?.isNew ? '新建' : '编辑'}SQL查询`}
                open={showSqlEditor}
                onCancel={() => setShowSqlEditor(false)}
                width={1200}
                footer={null}
                zIndex={999}
                destroyOnClose={true}
            >
                <SqlQueryEditor
                    value={editingQuery}
                    sourceFields={sourceFields}
                    onSave={handleSaveQuery}
                    onCancel={() => setShowSqlEditor(false)}
                    onTest={onTest}
                />
            </Modal>
        </div>
    );
};

export default SqlQueryManager; 