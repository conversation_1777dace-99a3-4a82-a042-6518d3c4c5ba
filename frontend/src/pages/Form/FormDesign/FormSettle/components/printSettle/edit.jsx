import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useContext } from "react";
import '@wangeditor-next/editor/dist/css/style.css';
import { DomEditor, SlateTransforms, Node } from '@wangeditor-next/editor'
import { Editor, Toolbar, } from '@wangeditor-next/editor-for-react';
import { EditCode } from "@/components";

import { fixedFields } from '../../../Util';
import { Button, Input, message, Dropdown, Space, Modal, Switch, Popconfirm, Menu, Tooltip, Select, InputNumber, Slider, Row, Col, Card, Divider, Typography, Tag } from 'antd';
import { FetchUploadImage } from "@/services/crm-api/upload-file";
import { FormDesignContext, FormDesignProvider, AddWith } from '@/components';
import { SortList } from '@/components';
import { CaretDownFilled, DeleteFilled, DeleteOutlined, FunctionOutlined, DownOutlined, <PERSON><PERSON>ddOutlined, FolderAddOutlined, HolderOutlined, Down<PERSON>ir<PERSON>Filled, SettingOutlined, EyeOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { FetchDetail, FetchSave } from "@/services/crm-api/form-print-template"
import { isDragNodeDataObject } from '@alilc/lowcode-utils';
import { Buffer } from 'buffer';
import md5 from 'md5';
import { FetchGetSupportColumnList } from '@/services/crm-api/form-template';
import { FetchTestSqlQuery } from '@/services/crm-api/sql-query';
import ColumnSettle from './columnSettle';
import SqlQueryManager from './sqlQueryManager';

const EditTableFieldItem = ({ listeners, value, onChange, sourceFields }) => {

    const inputRef = useRef(null)
    const [showFuncCodeEdit, setShowFuncCodeEdit] = useState(false)
    useEffect(() => {
        if (value.edit && inputRef.current)
            inputRef.current.focus()
    }, [value.edit])
    return <>
        <div className={` flex items-center gap-2`} >
            <Button
                type="text"
                size="small"
                icon={<HolderOutlined />}
                style={{
                    cursor: 'move',
                }}
                {...listeners}
            />
            {!value.edit && <div className='flex-1 cursor-pointer' onDoubleClick={() => {
                onChange({ ...value, edit: true })
            }} >
                {value.alias}
            </div>}
            {value.edit && <div className='flex-1 cursor-pointer'   >
                <Input value={value.alias} ref={inputRef} onBlur={() => {
                    onChange({ ...value, edit: false })
                }} onChange={(e) => {
                    onChange({ ...value, alias: e.target.value })
                }} />
            </div>}
            <Button type={value?.funcCode?.codeContent ? "primary" : "default"} size='small' onClick={() => setShowFuncCodeEdit(true)}><FunctionOutlined /></Button>
            <Button size='small' onClick={() => {
                onChange(null)
            }}><DeleteOutlined /></Button>

        </div>
        <Modal
            destroyOnClose={true}
            width={960}
            zIndex={999}
            title="编辑函数代码(编辑完成后直接关闭即可)"
            open={showFuncCodeEdit}
            onCancel={() => setShowFuncCodeEdit(false)}
            footer={null}
        >
            <EditCode formColumns={sourceFields} value={value?.funcCode} onChange={(e) => {
                return onChange({ ...value, funcCode: e })
            }}
                injectNotes={<><br />__current : 当前行当前列数据<br />__currentRecord : 当前行数据，可使用__currentRecord['映射名称']获得所需的导入数据</>}
                otherNotes={<div>记得将处理过的数据<span className='text-orange-500'>return</span>哦</div>}
                label="" __id="funcCode" />
        </Modal>
    </>
}
const EditTableFields = ({ value, onChange, tableParamsItems, tableParamsItemsClick, currentColumn }) => {
    const inputRef = useRef(null)

    value = Array.isArray(value) ? value : []
    const [subTableParamsItems, setSubTableParamsItems] = useState([])
    useEffect(() => {

        console.log("tableParamsItems", tableParamsItems)
        console.log("currentColumn", currentColumn)
        setSubTableParamsItems(tableParamsItems.map(tab => {
            if (Array.isArray(tab?.children))
                tab.children = tab.children.filter(item => {
                    if (currentColumn.tagKey == tab.key && item.key === currentColumn.id) return false
                    var columnInfo = (tab.fields || []).find(field => field.id === item.key)
                    if (columnInfo && columnInfo.component_name == "Field.Table") return false;
                    return true
                })
            return tab
        }))
    }, [tableParamsItems])
    const subTableParamsItemsClick = (obj) => {
        const { key, keyPath } = obj
        if (key == "addParam" || key == "removeWith") {
            typeof tableParamsItemsClick === "function" && tableParamsItemsClick({ ...obj, mount: currentColumn.id })
            return
        }
        if (keyPath.length != 2) return
        const tagKey = keyPath[1]
        let tagItem = tableParamsItems.find(item => item.key == tagKey);
        let column = (tagItem?.fields ?? []).find(item => item.id == key)
        if (column)
            onChange([...value, { key: column.id, alias: `${tagItem.label}.${column.label}`, label: `${tagItem.label}.${column.label}`, tagKey: tagKey, isChildren: false, select: true }])

    }

    return <div className='flex flex-col gap-y-2 my-4'>
        <SortList
            items={value}
            setItems={onChange}
            renderItem={(item, index, listeners) => {
                let sourceFields = []
                let tagFields = tableParamsItems.find(tab => tab.key == item.tagKey)?.fields || []
                console.log("item", item)
                console.log("tagFields", tagFields)
                if (item.isChildren) {
                    sourceFields = tagFields.find(field => field.id == item.parent)?.children || []
                    console.log("sourceFields", sourceFields)
                }
                else sourceFields = tagFields
                return <EditTableFieldItem sourceFields={
                    (item.isChildren ? tableParamsItems.find(tab => tab.key == item.tagKey)?.fields.find(subTab => subTab.id == item.parent)?.children :
                        tableParamsItems.find(tab => tab.key == item.tagKey)?.fields) || []
                } key={`${item.tagKey}.${item.key}`} value={item} listeners={listeners} onChange={(newValue) => {
                    if (!newValue) {
                        value.splice(index, 1)
                        onChange([...value])
                    }
                    else {
                        value[index] = newValue
                        onChange([...value])
                    }
                }} />
            }}
        />
        <div>
            <Dropdown menu={{ items: subTableParamsItems, onClick: subTableParamsItemsClick }}>
                <Button type="default" icon={<FolderAddOutlined />} >添加参数</Button>
            </Dropdown>
        </div>


    </div>
}

const PrintSettleEdit = ({ id, formTemplateId, onCancel, onOK }) => {
    const { formDesign, updateFormDesign, setFormDesign } = useContext(FormDesignContext);
    const [editor, setEditor] = useState(null); // 存储 editor 实例
    const [html, setHtml] = useState('<p>hello</p>');
    const [data, setData] = useState({
        printSize: 'A4',
        printRotation: "vertical",
        id: 0,
        padding: "auto",
        width: 0,
        height: 0,
        name: '',
        templateContent: '',
        enterWithData: [],
        sqlQueries: []
    });
    const [showCustomSizeModal, setShowCustomSizeModal] = useState(false);
    const [customSize, setCustomSize] = useState({ width: 60, height: 40 });
    const [showCustomPaddingModal, setShowCustomPaddingModal] = useState(false);
    const [customPadding, setCustomPadding] = useState(10);
    const printSizeItems = [
        { key: 'A4', label: 'A4', width: 210, height: 297 },
        { key: 'A5', label: 'A5', width: 148, height: 210 },
        { key: 'custom', label: '自定义', width: customSize.width, height: customSize.height },
    ]
    const printRotationItems = [
        { key: 'vertical', label: '纵向' },
        { key: 'horizontal', label: '横向' },
    ]
    const paddingItems = [
        { key: 'auto', label: '自动' },
        { key: 'custom', label: '自定义' },
    ]

    const addWithRef = useRef(null)
    const [allowSelParams, setAllSelParams] = useState([])
    const [tableParamsItems, setTableParamsItems] = useState([])
    const [editorSize, setEditorSize] = useState({ width: "960px", height: "1357px" });
    const currentTable = useRef(null);
    const [currentTableFields, setCurrentTableFields] = useState([]);
    const [showEditTable, setShowEditTable] = useState(false);
    const [showWithTable, setShowWithTable] = useState(false);
    const withDataMount = useRef("table");
    const [withConditionColumns, setWithConditionColumns] = useState([]);
    const [showEditColumnSettle, setShowEditColumnSettle] = useState(false);
    const [currentFields, setCurrentFields] = useState([]);
    const currentColumn = useRef("");
    const [showSqlManager, setShowSqlManager] = useState(false);
    const [editingSqlQuery, setEditingSqlQuery] = useState(null);
    const [relatedTablesMap, setRelatedTablesMap] = useState(new Map()); // 存储关联表信息

    const printRotationClick = ({ key }) => {
        setData(prev => ({ ...prev, printRotation: key }))
    };
    const printSizeClick = ({ key }) => {
        if (key === 'custom') {
            setShowCustomSizeModal(true);
        } else {
            setData(prev => ({ ...prev, printSize: key }))
        }
    };
    const paddingClick = ({ key }) => {
        if (key === 'custom') {
            setShowCustomPaddingModal(true);
        } else {
            setData(prev => ({ ...prev, padding: key }))
        }
    };


    const buildAllSelParams = async () => {
        var _tagItems = []
        let form_columns = [...formDesign.form_columns, ...(fixedFields.map(item => {
            if (item.id == "created_by") item.id = "created_user"
            return item
        }))]
        
        // 先收集所有关联表信息
        const newRelatedTablesMap = new Map(); // key: tableId, value: 关联表字段列表
        if (data.enterWithData) {
            for (let index = 0; index < data.enterWithData.length; index++) {
                const element = data.enterWithData[index];
                // 获取关联表字段
                const res = await FetchGetSupportColumnList({ table: element.withTable?.table_name, type: element.withTable?.type, show_fixed_fields: true })
                if (res.code == 0 && res.data && res.data.list.length > 0) {
                    const tableFields = res.data.list.map((item) => ({
                        ...item,
                        key: item.id,
                    }));
                    newRelatedTablesMap.set(element.id, {
                        element: element,
                        fields: tableFields
                    });
                }
            }
        }
        
        if (form_columns) {
            form_columns = [{ key: "__global_identifier", id: "__global_identifier", label: "全局标识", component_name: "Field.CodeMachine", closable: true, mount: "", fields: [] }, ...form_columns]
            
            // 构建菜单项，为表格字段添加子菜单
            let menuChildren = [];
            
            form_columns.forEach(item => {
                if (item.component_name === "Field.Table" && item.children && item.children.length > 0) {
                    // 表格字段：添加常规选项和字段子菜单
                    menuChildren.push({ key: item.id, label: `${item.label}(整表)` });
                    
                    // 添加子表字段，包括序号
                    const tableFieldChildren = [
                        {
                            key: `table_field_${item.id}.serial_number`,
                            label: `└ 序号`
                        },
                        ...item.children.map(child => ({
                            key: `table_field_${item.id}.${child.id}`,
                            label: `└ ${child.label}`
                        }))
                    ];
                    
                    // 查找mount到当前子表的关联表字段
                    for (const [relatedId, relatedInfo] of newRelatedTablesMap) {
                        if (relatedInfo.element.mount === item.id) {
                            // 添加分隔线
                            tableFieldChildren.push({ type: 'divider' });
                            
                            // 添加关联表标题
                            tableFieldChildren.push({
                                key: `related_divider_${relatedId}`,
                                label: `--- ${relatedInfo.element.withTable.label} ---`,
                                disabled: true
                            });
                            
                            // 添加关联表字段 - 使用 table_field_ 前缀但添加 related_ 标识
                            relatedInfo.fields.forEach(field => {
                                if (field.component_name !== "Field.Table") {
                                    tableFieldChildren.push({
                                        key: `table_field_${item.id}.related_${relatedId}.${field.id}`,
                                        label: `└ ${field.label}`,
                                        relatedTableId: relatedId
                                    });
                                }
                            });
                        }
                    }
                    
                    menuChildren.push({
                        type: 'group',
                        label: `${item.label} 字段`,
                        children: tableFieldChildren
                    });
                } else {
                    // 普通字段
                    menuChildren.push({ key: item.id, label: item.label });
                }
            });
            
            _tagItems.push({
                key: "fieldsValue",
                label: "当前表单",
                mount: "",
                children: menuChildren,
                fields: form_columns,
            })

        }
        // 构建关联表菜单项（复用之前收集的字段信息）
        for (const [relatedId, relatedInfo] of newRelatedTablesMap) {
            if (relatedInfo.element.mount == "table" || relatedInfo.element.mount == currentTable.current?.id) {
                if (_tagItems.some(item => item.key === relatedId)) continue
                const itemLabel = `关联表-${relatedInfo.element.withTable.label}`
                
                // 构建关联表菜单项，为表格字段添加子菜单
                let relatedTableChildren = [
                    { key: "removeWith", label: "删除关联", type: "primary", icon: <DeleteOutlined /> },
                    { type: 'divider' }
                ];
                
                relatedInfo.fields.forEach(item => {
                    if (item.component_name === "Field.Table" && item.children && item.children.length > 0) {
                        // 表格字段：添加常规选项和字段子菜单
                        relatedTableChildren.push({ key: item.id, label: `${item.label}(整表)` });
                        
                        // 添加子表字段，包括序号
                        const relatedTableFieldChildren = [
                            {
                                key: `table_field_${item.id}.serial_number`,
                                label: `└ 序号`
                            },
                            ...item.children.map(child => ({
                                key: `table_field_${item.id}.${child.id}`,
                                label: `└ ${child.label}`
                            }))
                        ];
                        
                        relatedTableChildren.push({
                            type: 'group',
                            label: `${item.label} 字段`,
                            children: relatedTableFieldChildren
                        });
                    } else {
                        // 普通字段
                        relatedTableChildren.push({ key: item.id, label: item.label });
                    }
                });
                
                _tagItems.push({
                    key: relatedId,
                    mount: "",
                    label: itemLabel,
                    fields: relatedInfo.fields,
                    children: relatedTableChildren,
                    closable: true,
                })
            }
        }

        // 新增：添加SQL查询参数
        if (data.sqlQueries && data.sqlQueries.length > 0) {
            for (const sqlQuery of data.sqlQueries) {
                if (sqlQuery.type === 'table') {
                    console.log("sqlQuery ****", sqlQuery)
                    _tagItems.push({
                        key: `sql_table_${sqlQuery.id}`,
                        label: `SQL表格-${sqlQuery.name}`,
                        mount: sqlQuery.mount || "table",
                        fields: sqlQuery.columns.map(col => ({
                            id: col.key,
                            label: col.alias || col.label,
                            component_name: "Field.Text"
                        })),
                        children: [
                            { key: "editSqlQuery", label: "编辑", type: "default", icon: <EditOutlined /> },
                            { key: "removeSqlQuery", label: "删除", type: "primary", icon: <DeleteOutlined /> },
                            { key: "insertSqlToTemplate", label: "插入模板", type: "default", icon: <PlusOutlined /> }
                        ],
                        sqlQuery: true,
                        sqlConfig: sqlQuery
                    });
                } else if (sqlQuery.type === 'value') {
                    _tagItems.push({
                        key: `sql_value_${sqlQuery.id}`,
                        label: `SQL值-${sqlQuery.name}`,
                        mount: "",
                        fields: [{
                            id: 'value',
                            label: sqlQuery.name,
                            component_name: "Field.Text"
                        }],
                        children: [
                            { key: "editSqlQuery", label: "编辑", type: "default", icon: <EditOutlined /> },
                            { key: "removeSqlQuery", label: "删除", type: "primary", icon: <DeleteOutlined /> },
                            { key: "insertSqlToTemplate", label: "插入模板", type: "default", icon: <PlusOutlined /> }
                        ],
                        sqlQuery: true,
                        sqlConfig: sqlQuery
                    });
                }
            }
        }
        console.log("allselparams", [{ key: "addParam", label: "增加关联表", type: "primary", icon: <FileAddOutlined /> }, {
            type: 'divider',
        }, ..._tagItems])
        // _tagItems.shift({ key: "__global_identifier", label: "全局标识", component_name: "Field.CodeMachine",closable: true, mount: ""})
        setAllSelParams(_tagItems)
        setRelatedTablesMap(newRelatedTablesMap) // 设置关联表信息到状态
        setTableParamsItems([
            { key: "addParam", label: "增加关联表", type: "primary", icon: <FileAddOutlined /> }, 
            { key: "addSqlQuery", label: "添加SQL查询", type: "primary", icon: <FunctionOutlined /> },
            { type: "divider" }, 
            ..._tagItems
        ])
    }

    const handleEditTableOk = () => {
        const fields = currentTableFields.filter(item => item.select).map(item => ({ ...item, column: item.key, alias: item.alias && item.alias != item.label ? item.alias : "" }))
        
        let tableSettle;
        if (currentTable.current.sqlQuery) {
            tableSettle = {
                sqlTable: currentTable.current.id,
                tagKey: currentTable.current.tagKey,
                sqlConfig: currentTable.current.sqlConfig,
                fields: fields
            };
        } else {
            tableSettle = {
                table: currentTable.current.id,
                tagKey: currentTable.current.tagKey,
                fields: fields
            };
        }
        
        console.log("tableSettle", JSON.stringify(tableSettle))
        editor.restoreSelection()
        editor.insertNode({
            type: 'link',
            url: Buffer.from(JSON.stringify({
                type: currentTable.current.sqlQuery ? "sql_table" : "table",
                data: tableSettle
            })).toString('base64'),
            children: [{ text: `${currentTable.current.sqlQuery ? 'SQL表格' : '表格'}：${currentTable.current.label}` }]
        })
        currentTable.current = null
        setShowEditTable(false)
    }
    const handleWithTableOk = () => {
        try {
            const withData = addWithRef.current?.getValues()
            if (!withData) throw new Error("请设置关联表条件")
            if (!withData.withTable) throw new Error("请选择关联表")
            if (!withData.withCondition || withData.withCondition.length <= 0) throw new Error("请设置关联条件")
            const emptyCondition = withData.withCondition.find(item => !item.column || !item.value)
            if (emptyCondition) throw new Error(`请设置完整的关联条件`)
            console.log("withData***", withData)
            const hash = md5(JSON.stringify(withData))
            const shortHash = hash.substring(0, 16);
            withData.id = shortHash
            withData.mount = withDataMount.current
            setData(prev => { return { ...prev, enterWithData: [...prev.enterWithData, withData] } })
            // setEnterWithData(prevItems => [...prevItems, withData])
            setShowWithTable(false)
        }
        catch (error) {
            message.error(error.message)
        }
    }
    const handleColumnSettleOk = () => {
        console.log("currentColumn.current", currentColumn.current)
        if (currentColumn.current) {
            if (currentColumn.current.contentPresentation == 1) {
                editor.restoreSelection()
                editor.insertNode({
                    type: 'link', url: Buffer.from(JSON.stringify({
                        type: "key", data: {
                            tagKey: currentColumn.current.tagKey,
                            column: currentColumn.current.column,
                            contentPresentation: currentColumn.current.contentPresentation,
                            contentFormat: currentColumn.current.contentFormat,
                            contentFormatCode: currentColumn.current.contentFormatCode
                        },
                    })).toString('base64'), children: [{ text: currentColumn.current.label }]
                })
            }
            else if (currentColumn.current.contentPresentation == 2 || currentColumn.current.contentPresentation == 3) {
                let style = { width: "100px", height: "100px" }
                if (currentColumn.current.contentPresentation == 2) {
                    style = { width: "300px", height: "100px" }
                }
                editor.restoreSelection()
                editor.insertNode({
                    type: 'image',
                    src: PUBLIC_PATH + (currentColumn.current.contentPresentation == 2 ? "/images/demo-barcode.jpg" : "/images/demo-qrcode.jpg"),
                    alt: Buffer.from(JSON.stringify({
                        type: "key", data: {
                            tagKey: currentColumn.current.tagKey,
                            column: currentColumn.current.column,
                            contentPresentation: currentColumn.current.contentPresentation,
                            contentFormat: currentColumn.current.contentFormat,
                            contentFormatCode: currentColumn.current.contentFormatCode
                        },
                    })).toString('base64'), style: style, children: [{ text: currentColumn.current.label }]
                })
            }
        }
        setShowEditColumnSettle(false)
        //     console.log("currentColumn.current //////////////",currentColumn.current)
        //   editor.restoreSelection()
        //   editor.insertNode({ type: 'link', url: Buffer.from(JSON.stringify({ type: "key", data: { tagKey: tagKey, column: key }, })).toString('base64'), children: [{ text: `${tagItem.label}.${column.label}` }] })
        // setShowEditColumnSettle(false)
    }
    const tableParamsItemsClick = ({ key, keyPath, mount }) => {
        if (editor == null) return;
        
        if (key == "addParam") {
            if (mount) { withDataMount.current = mount }
            else withDataMount.current = "table"
            setShowWithTable(true)
            return;
        }

        if (key == "addSqlQuery") {
            setEditingSqlQuery(null); // 清空编辑状态，表示新增
            setShowSqlManager(true);
            return;
        }

        if (key == "editSqlQuery") {
            const tagKey = keyPath[1];
            if (tagKey.startsWith('sql_')) {
                const sqlId = tagKey.replace(/^sql_(table|value)_/, '');
                const sqlQuery = data.sqlQueries.find(q => q.id === sqlId);
                if (sqlQuery) {
                    setEditingSqlQuery(sqlQuery);
                    setShowSqlManager(true);
                }
            }
            return;
        }

        if (key == "removeSqlQuery") {
            const tagKey = keyPath[1];
            if (tagKey.startsWith('sql_')) {
                const sqlId = tagKey.replace(/^sql_(table|value)_/, '');
                setData(prev => ({
                    ...prev,
                    sqlQueries: prev.sqlQueries.filter(q => q.id !== sqlId)
                }));
            }
            return;
        }

        if (key == "insertSqlToTemplate") {
            const tagKey = keyPath[1];
            if (tagKey.startsWith('sql_')) {
                const sqlId = tagKey.replace(/^sql_(table|value)_/, '');
                const sqlQuery = data.sqlQueries.find(q => q.id === sqlId);
                if (sqlQuery) {
                    handleInsertSqlQuery(sqlQuery);
                }
            }
            return;
        }

        if (keyPath.length != 2) return
        const tagKey = keyPath[1]

        if (key == "removeWith") {
            const index = data.enterWithData.findIndex(item => item.id === tagKey)
            if (index >= 0) {
                setData(prev => ({ ...prev, enterWithData: prev.enterWithData.filter((_, i) => i !== index) }))
            }
        }
        else {
            // setData(prev => ({...prev, tableColumn: key }))
            let tagItem = allowSelParams.find(item => item.key == tagKey);
            
            // 处理SQL查询字段插入
            if (tagItem && tagItem.sqlQuery) {
                if (tagItem.sqlConfig.type === 'table') {
                    // 处理SQL表格
                    let tableFields = tagItem.sqlConfig.columns
                        .filter(col => col.show)
                        .map(col => ({
                            key: col.key,
                            label: col.alias || col.label,
                            alias: col.alias || col.label,
                            tagKey: tagKey,
                            select: true,
                            edit: false,
                            sqlQuery: true
                        }));
                    
                    setCurrentTableFields(tableFields);
                    currentTable.current = {
                        id: tagItem.sqlConfig.id,
                        label: tagItem.sqlConfig.name,
                        tagKey: tagKey,
                        sqlQuery: true,
                        sqlConfig: tagItem.sqlConfig
                    };
                    setShowEditTable(true);
                } else if (tagItem.sqlConfig.type === 'value') {
                    // 处理SQL单值
                    editor.restoreSelection();
                    editor.insertNode({
                        type: 'link',
                        url: Buffer.from(JSON.stringify({
                            type: "sql_value",
                            data: {
                                sqlId: tagItem.sqlConfig.id,
                                sqlName: tagItem.sqlConfig.name,
                                sql: tagItem.sqlConfig.sql,
                                parameters: tagItem.sqlConfig.parameters
                            }
                        })).toString('base64'),
                        children: [{ text: tagItem.sqlConfig.name }]
                    });
                }
                return;
            }

            let column = (tagItem?.fields ?? []).find(item => item.id == key)
            setCurrentFields(tagItem?.fields ?? [])
            if (column) {
                if (column.component_name == "Field.Table") {
                    let tableFields = (column.children || []).map(item => ({ key: item.id, label: `${tagItem.label}.${column.label}.${item.label}`, alias: `${tagItem.label}.${column.label}.${item.label}`, tagKey: tagKey, isChildren: true, parent: column.id, select: true, edit: false }))
                    tableFields = [{ key: "serial_number", label: `序号`, alias: `序号`, tagKey: tagKey, isChildren: true, parent: column.id, select: true, edit: false }, ...tableFields]
                    setCurrentTableFields(tableFields)
                    currentTable.current = { ...column, tagKey: tagKey, }
                    setShowEditTable(true)
                }
                else {
                    setShowEditColumnSettle(true)
                    currentColumn.current = { tagKey: tagKey, column: key, label: `${tagItem.label}.${column.label}`, alias: `${tagItem.label}.${column.label}`, isChildren: false, select: true }
                    // editor.restoreSelection()
                    // editor.insertNode({ type: 'link', url: Buffer.from(JSON.stringify({ type: "key", data: { tagKey: tagKey, column: key }, })).toString('base64'), children: [{ text: `${tagItem.label}.${column.label}` }] })
                }
            }

            // 处理表格字段插入请求（新增功能）
            else if (key.startsWith('table_field_')) {
                const tableFieldKey = key.replace('table_field_', '');
                const keyParts = tableFieldKey.split('.');
                
                console.log("tableFieldKey", tableFieldKey);
                console.log("keyParts", keyParts);
                
                // 检查是否是关联表字段
                if (keyParts.length >= 3 && keyParts[1].startsWith('related_')) {
                    // 关联表字段格式: table_field_${tableId}.related_${relatedId}.${fieldId}
                    const [tableName, relatedPart, fieldName] = keyParts;
                    const relatedTableId = relatedPart.replace('related_', '');
                    
                    console.log("关联表字段", { tableName, relatedTableId, fieldName });
                    
                    // 查找关联表信息
                    const relatedTableInfo = relatedTablesMap.get(relatedTableId);
                    const relatedElement = data.enterWithData.find(item => item.id === relatedTableId);
                    const fieldInfo = relatedTableInfo?.fields.find(field => field.id === fieldName);
                    
                    if (relatedElement && fieldInfo) {
                        const displayText = `${tagItem.label}.${relatedElement.withTable.label}.${fieldInfo.label}`;
                        console.log("关联表字段 displayText", displayText);
                        
                        editor.restoreSelection();
                        editor.insertNode({
                            type: 'link',
                            url: Buffer.from(JSON.stringify({
                                type: "table_field",
                                data: {
                                    tagKey: relatedTableId, // 使用关联表的ID作为tagKey
                                    tableName: relatedElement.withTable.id || relatedElement.withTable.table_name,
                                    column: fieldName,
                                    tableLabel: relatedElement.withTable.label,
                                    fieldLabel: fieldInfo.label
                                }
                            })).toString('base64'),
                            children: [{ text: displayText }]
                        });
                    }
                } else {
                    // 普通子表字段格式: table_field_${tableId}.${fieldId}
                    const [tableName, fieldName] = keyParts;
                    
                    // 找到对应的表格字段信息
                    let tableColumn = (tagItem?.fields ?? []).find(item => item.id == tableName);
                    console.log("子表字段", { tableColumn, tableName, fieldName });
                    
                    if (tableColumn && tableColumn.component_name == "Field.Table") {
                        let fieldLabel, fieldColumn;
                        
                        if (fieldName === 'serial_number') {
                            // 序号字段
                            fieldLabel = "序号";
                        } else {
                            // 普通字段
                            fieldColumn = (tableColumn.children || []).find(item => item.id == fieldName);
                            fieldLabel = fieldColumn?.label;
                        }
                        
                        if (fieldLabel) {
                            // 使用正确的显示标签
                            const displayText = `${tagItem.label}.${tableColumn.label}.${fieldLabel}`;
                            console.log("子表字段 displayText", displayText);
                            
                            // 统一插入逻辑
                            editor.restoreSelection();
                            editor.insertNode({
                                type: 'link',
                                url: Buffer.from(JSON.stringify({
                                    type: "table_field",
                                    data: {
                                        tagKey: tagKey,
                                        tableName: tableName,
                                        column: fieldName,
                                        tableLabel: tableColumn.label,
                                        fieldLabel: fieldLabel
                                    }
                                })).toString('base64'),
                                children: [{ text: displayText }]
                            });
                        }
                    }
                }
            }
        }
    };
    // 删除A标签的时候,一次性删除干净
    const handleKeyDown = (event) => {
        if (event.key === 'Backspace' || event.key === 'Delete') {
            setTimeout(() => {
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);

                // 找到选中的 <a> 标签
                let linkNode = range.commonAncestorContainer;
                while (linkNode && linkNode.nodeName !== 'A') {
                    linkNode = linkNode.parentNode;
                }
                console.log("selection ", editor.selection)
                // 如果选中了 <a> 标签，删除它及其内容
                if (linkNode && linkNode.nodeName === 'A') {
                    SlateTransforms.removeNodes(editor, {
                        match: (n) => {
                            return n.type == 'link'
                        }
                    })
                }
            }, 200);
        }
    };
    // 测试SQL查询
    const handleTestSqlQuery = async (queryData) => {
        try {
            const result = await FetchTestSqlQuery(queryData);
            if (result.code === 0) {
                message.success('SQL查询测试成功');
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            message.error(`SQL查询测试失败: ${error.message}`);
            throw error;
        }
    };

    // 插入SQL查询到编辑器
    const handleInsertSqlQuery = (queryData, closeManager = false) => {
        if (!editor) {
            message.error('编辑器未准备就绪');
            return;
        }

        try {
            // 使用a标签+base64编码方案，这是你一直在使用的稳定方案
            const icon = queryData.type === 'table' ? '📋' : '📊';
            const typeText = queryData.type === 'table' ? '表格' : '数值';
            
            // 将SQL查询配置编码为base64
            const sqlConfig = {
                type: "sql_query",
                sqlType: queryData.type,
                sqlId: queryData.id,
                sqlName: queryData.name,
                sql: queryData.sql || '',
                parameters: queryData.parameters || []
            };
            
            const base64Config = Buffer.from(JSON.stringify(sqlConfig)).toString('base64');
            
            // 使用editor.insertNode插入a标签节点
            editor.restoreSelection();
            editor.insertNode({
                type: 'link',
                url: base64Config,
                children: [{ text: `${icon} ${queryData.name}` }]
            });
            
            // 根据参数决定是否关闭SQL管理器
            if (closeManager) {
                setShowSqlManager(false);
            }
            
            message.success(`已插入SQL查询: ${queryData.name}`);
            
        } catch (error) {
            console.error('插入SQL查询失败:', error);
            message.error('插入SQL查询失败');
        }
    };

    const getDetail = async (id) => {
        if (id && id > 0) {
            let { code, data: fetchData, message: msg } = await FetchDetail(id);
            if (code == 0) {
                if (typeof fetchData.enterWithData == "string" && fetchData.enterWithData && fetchData.enterWithData.length > 0) {
                    fetchData.enterWithData = JSON.parse(fetchData.enterWithData)
                }
                // 反序列化SQL查询配置
                if (typeof fetchData.sqlQueries === "string" && fetchData.sqlQueries) {
                    fetchData.sqlQueries = JSON.parse(fetchData.sqlQueries);
                } else if (!fetchData.sqlQueries) {
                    fetchData.sqlQueries = [];
                }
                setData(fetchData)
                if(fetchData?.printSize == "custom") {
                    setCustomSize({ width:fetchData?.width, height: fetchData?.height})
                }
                setHtml(fetchData?.templateContent ?? "")
            }
            else message.error(msg)
        }
    }
    useEffect(() => {
        getDetail(id)
    }, [id])

    useEffect(() => {
        console.log("formDesign.form_columns", formDesign.form_columns)
    }, [])
    useEffect(() => {
       
        let editorSize_ = { width: 210, minHeight: 297 }
        if (data?.printSize) {
            const size = printSizeItems.find(item => item.key === data.printSize)
            editorSize_ = { width: size.width, minHeight: size.height }
        }
        if (data?.printRotation == "horizontal")
            editorSize_ = { width: editorSize_.minHeight, minHeight: editorSize_.width }

        editorSize_.width = ensurePx(editorSize_.width)
        editorSize_.minHeight = ensurePx(editorSize_.minHeight)

        // console.log("data",data)
        // console.log("editorsize", editorSize)
        setEditorSize(editorSize_)

    }, [data, customSize])
    const ensurePx = (value) => {
        // 正则验证是否是数字
        if (/^\d+$/.test(value)) {
            return (parseFloat(value) * 5.05) + 'px';
        }
        return "1000px";
    };


    useEffect(() => {
        if (editor) {
            editor.on('keydown', handleKeyDown);

        }
        return () => {
            if (editor) {
                editor.off('keydown', handleKeyDown);
            }
        };
    }, [editor])

    let toolbarConfig = {
        insertKeys: {
            index: -5,
            keys: ["uploadImage"],
        },
        excludeKeys: [
            'fullScreen', "group-video", "group-image", "insertLink"
        ],
    };
    let editorConfig = {
        placeholder: '请输入内容...',
        MENU_CONF: {},
        // 配置编辑器允许的HTML标签和属性
        hoverbarKeys: {
            // 禁用悬浮工具栏，避免干扰SQL占位符
            'sql-query-placeholder': {
                menuKeys: []
            }
        },
        // 自定义粘贴处理，保留SQL占位符
        customPaste: (editor, event, callback) => {
            // 默认粘贴处理
            typeof callback == "function" && callback(true);
        }
    };
    // 添加自定义行高配置
    editorConfig.MENU_CONF['lineHeight'] = {
        lineHeightList: ['0.6', '0.8', '1', '1.5', '2', '2.5'],
    }
    editorConfig.MENU_CONF['uploadImage'] = {
        server: "/api/upload/image",
        // 单个文件的最大体积限制，默认为 2M
        maxFileSize: 1 * 1024 * 1024, // 1M
        // 最多可上传几个文件，默认为 100
        maxNumberOfFiles: 10,
        // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
        allowedFileTypes: ['image/*'],
        async customUpload(file, insertFn) {
            message.loading("上传中...")
            try {
                const formData = new FormData();
                formData.append('file', file);
                var fetchOptions = {
                }
                var response = await FetchUploadImage(formData, fetchOptions)
                if (response && response.code == 0 && response.data.urls.length > 0) {
                    insertFn(response.data.urls[0], "", "")
                }
                else {
                    const error_message = response?.message || "上传失败"
                    message.error(error_message)
                    options?.onError(error_message)
                }
            } catch (error) {

            }
            finally {
                message.destroy()
            }
        }
    }
    // 及时销毁 editor
    useEffect(() => {
        return () => {
            if (editor == null) return;
            editor.destroy();
            setEditor(null);
        };
    }, [editor]);


    const handleCancel = () => {
        typeof onCancel == "function" && onCancel()
    }
    const handleSave = async () => {
        try {
            let formData = { 
                ...data, 
                templateContent: editor.getHtml(), 
                formTemplateId: formTemplateId,
                sqlQueries: JSON.stringify(data.sqlQueries || []) // 序列化SQL查询配置
            }
            if (!formTemplateId || formTemplateId <= 0) {
                message.error("未绑定表单模板，保存失败")
                return
            }
            console.log("formData.name", formData?.name)
            console.log("formData", formData)
            if (!formData?.name) {
                message.error("请输入打印模板名称")
                return
            }

            const { code, data: saveData, message: msg } = await FetchSave(formData)
            if (code == 0 && saveData?.id) {
                typeof onOK == "function" && onOK()
            }
            else message.error(msg)
        } catch (error) {
            let err_msg = error?.errorFields?.[0].errors?.[0] || ""
            if (err_msg) message.error(err_msg)
        }
    }

    useEffect(() => {
        console.log("data.enterWithData", data.enterWithData)
        if (!showEditTable) currentTable.current = null
        buildAllSelParams();
    }, [data.enterWithData, data.sqlQueries, withDataMount.current, showEditTable])

    useEffect(() => {
        console.log("allowSelParams *//////////////////////", allowSelParams)
        let _withConditionColumns = []
        _withConditionColumns = allowSelParams.map(allowSelParam => {
            let children = allowSelParam.fields.filter(item => item.component_name != "Field.Table").map(item => ({ id: item.id, key: item.id, label: item.label, parent: "" }))
            if (allowSelParam.key == "fieldsValue" && withDataMount.current != "table") {
                let mountItem = allowSelParam.fields.find(item => item.id == withDataMount.current)
                if (mountItem) children.push(...(mountItem.children.map(item => ({ id: item.id, key: item.id, label: `${mountItem.label}.${item.label}`, parent: mountItem.id }))))
            }
            return {
                key: allowSelParam.key,
                label: allowSelParam.label,
                mount: "",
                children: children,
                fields: children,
            }
        })
        // let children = formDesign.form_columns.filter(item => item.component_name != "Field.Table").map(item => ({ id: item.id, key: item.id, label: item.label, parent: "" }))
        // if (withDataMount.current != "table") {
        //     let mountItem = formDesign.form_columns.find(item => item.id == withDataMount.current)
        //     if (mountItem) children.push(...(mountItem.children.map(item => ({ id: item.id, key: item.id, label: `${mountItem.label}.${item.label}`, parent: mountItem.id }))))
        // }
        // _withConditionColumns.push({
        //     key: "fieldsValue",
        //     label: "当前表单",
        //     mount: "",
        //     children: children,
        //     fields: children,
        // })
        // console.log("withConditionColumns", _withConditionColumns)
        setWithConditionColumns(_withConditionColumns)
    }, [withDataMount.current, showEditTable, allowSelParams])

    const handleCustomSizeOk = () => {
        setData(prev => ({ ...prev, printSize: 'custom', width: customSize.width, height: customSize.height }))
        setShowCustomSizeModal(false);
    };

    const handleCustomPaddingOk = () => {
        setData(prev => ({ ...prev, padding: 'custom' }));
        setShowCustomPaddingModal(false);
    };

    return (
        <>
            <div className='flex w-full h-full flex-col bg-gray-50 gap-y-6 items-center'>
                <div className='min-h-12 flex items-center bg-white border-b border-gray-200 gap-x-2 px-2 w-full'>
                    <div className="border-b-0 border-gray-200 w-64">
                        <Input style={{ width: "100%" }} value={data.name} onChange={(e) => setData({ ...data, name: e.target.value })} size="large" placeholder="请输入打印模板名称" variant="borderless" />
                    </div>
                    <Dropdown menu={{ items: printSizeItems, onClick: printSizeClick }}>
                        <div className='cursor-pointer flex items-center gap-x-1'>尺寸<CaretDownFilled style={{ fontSize: '10px' }} /></div>
                    </Dropdown>
                    <Dropdown menu={{ items: printRotationItems, onClick: printRotationClick }}>
                        <div className='cursor-pointer flex items-center gap-x-1'>方向<CaretDownFilled style={{ fontSize: '10px' }} /></div>
                    </Dropdown>
                    <Dropdown menu={{ items: paddingItems, onClick: paddingClick }}>
                        <div className='cursor-pointer flex items-center gap-x-1'>边距<CaretDownFilled style={{ fontSize: '10px' }} /></div>
                    </Dropdown>
                    <Dropdown menu={{ items: tableParamsItems, onClick: tableParamsItemsClick }}>
                        <div className='cursor-pointer flex items-center gap-x-1'>表单参数<CaretDownFilled style={{ fontSize: '10px' }} /></div>
                    </Dropdown>
                    
                  

                    <div className='flex-1 flex border-l'>
                        <Toolbar
                            editor={editor}
                            defaultConfig={toolbarConfig}
                            mode="default"
                        />
                    </div>
                    <div className='flex gap-2'>
                        <Button onClick={handleCancel}>取消</Button>
                        <Button type='primary' onClick={handleSave}>保存</Button>
                    </div>
                </div>

                <div className='flex-1 w-full overflow-auto pb-6 flex flex-col items-center'>
                    <div className='rounded-lg p-12 shadow-lg box-border bg-white'
                        style={{
                            ...editorSize,
                            ...(data.padding == 'auto' ? {} : {
                                padding: `${parseFloat(data.padding) * 5.05}px`
                            })
                        }}
                        onKeyDown={handleKeyDown}>
                        <Editor
                            defaultConfig={editorConfig}
                            value={html}
                            onCreated={setEditor}
                            onChange={(editor) => setHtml(editor.getHtml())}
                            mode="default"
                            style={{ height: '100%', width: '100%' }}
                        />
                    </div>
                </div>
            </div>
            <Modal destroyOnClose={true}
                width={450}
                zIndex={999}
                title="选择需要打印的字段"
                open={showEditTable}
                onCancel={() => setShowEditTable(false)}
                onOk={() => handleEditTableOk()}>
                <EditTableFields value={currentTableFields} onChange={(newValue) => {
                    setCurrentTableFields(newValue)
                }} tableParamsItems={tableParamsItems} tableParamsItemsClick={tableParamsItemsClick} currentColumn={currentTable.current} />
            </Modal>
            <Modal destroyOnClose={true}
                width={600}
                zIndex={999}
                title="选择关联表单"
                open={showWithTable}
                onCancel={() => setShowWithTable(false)}
                onOk={() => handleWithTableOk()}>
                <AddWith ref={addWithRef} SupportedFields={withConditionColumns} allowWithBeValue={true}></AddWith>
            </Modal>
            <Modal destroyOnClose={true}
                width={900}
                zIndex={999}
                title="数据展现方式"
                open={showEditColumnSettle}
                footer={null}
                onCancel={() => setShowEditColumnSettle(false)}>
                <ColumnSettle
                    fields={currentFields.map(item => ({ ...item, value: item.id }))}
                    onOK={() => {
                        handleColumnSettleOk()
                    }} onCancel={() => setShowEditColumnSettle(false)} onChange={(value) => {
                        currentColumn.current = { ...currentColumn.current, ...(value || {}) }
                    }} />
            </Modal>
            <Modal
                title="自定义尺寸"
                open={showCustomSizeModal}
                onOk={handleCustomSizeOk}
                onCancel={() => setShowCustomSizeModal(false)}
            >
                <div className="flex flex-col gap-y-4">
                    <div className="flex items-center gap-x-2">
                        <span className="w-16">宽度：</span>
                        <Input
                            value={customSize.width}
                            onChange={(e) => setCustomSize(prev => ({ ...prev, width: e.target.value }))}
                            placeholder="例如：210"
                            suffix="mm"
                        />
                    </div>
                    <div className="flex items-center gap-x-2">
                        <span className="w-16">高度：</span>
                        <Input
                            value={customSize.height}
                            onChange={(e) => setCustomSize(prev => ({ ...prev, height: e.target.value }))}
                            placeholder="例如： 297"
                            suffix="mm"
                        />
                    </div>
                </div>
            </Modal>
            <Modal
                title="自定义边距"
                open={showCustomPaddingModal}
                onOk={handleCustomPaddingOk}
                onCancel={() => setShowCustomPaddingModal(false)}
            >
                <div className="flex flex-col gap-y-4">
                    <div className="flex items-center gap-x-2">
                        <span className="w-16">边距：</span>
                        <Input
                            value={customPadding}
                            onChange={(e) => setCustomPadding(e.target.value)}
                            placeholder="例如：10"
                            suffix="mm"
                        />
                    </div>
                </div>
            </Modal>
            
            <Modal
                destroyOnClose={true}
                width={1400}
                zIndex={999}
                title="SQL查询管理"
                open={showSqlManager}
                onCancel={() => {
                    setShowSqlManager(false);
                    setEditingSqlQuery(null); // 关闭时清空编辑状态
                }}
                footer={null}
            >
                <SqlQueryManager
                    value={data.sqlQueries}
                    onChange={(sqlQueries) => setData(prev => ({ ...prev, sqlQueries }))}
                    sourceFields={allowSelParams}
                    onTest={handleTestSqlQuery}
                    onInsert={(queryData) => handleInsertSqlQuery(queryData, true)} // 从管理器插入时关闭管理器
                    editingQuery={editingSqlQuery} // 传递编辑状态
                />
            </Modal>
        </>
    );
}

export default PrintSettleEdit;