import React from 'react';
import { Table, Alert, Spin, Tag, Empty, Descriptions } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

const SqlTestResult = ({ result, type, loading }) => {
    if (loading) {
        return (
            <div className="flex justify-center items-center py-12">
                <Spin size="large" />
                <span className="ml-3">执行SQL查询中...</span>
            </div>
        );
    }

    if (!result) {
        return (
            <div className="text-center py-8">
                <Empty
                    description="暂无测试结果"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
                <div className="text-sm text-gray-500 mt-2">
                    点击"测试查询"按钮执行SQL语句
                </div>
            </div>
        );
    }

    const { code, data, message, executionTime, rowCount } = result;
    const isSuccess = code === 0;

    // 渲染执行状态
    const renderStatus = () => (
        <Alert
            type={isSuccess ? 'success' : 'error'}
            icon={isSuccess ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
            message={
                <div className="flex justify-between items-center">
                    <span>
                        {isSuccess ? 'SQL执行成功' : 'SQL执行失败'}
                        {message && `: ${message}`}
                    </span>
                    {executionTime && (
                        <Tag color="blue">
                            耗时: {executionTime}ms
                        </Tag>
                    )}
                </div>
            }
            className="mb-4"
        />
    );

    // 渲染单值结果
    const renderValueResult = () => {
        if (!isSuccess || !data) return null;

        let displayValue = data;
        let valueType = typeof data;

        // 处理复杂数据类型
        if (typeof data === 'object') {
            displayValue = JSON.stringify(data, null, 2);
            valueType = 'object';
        }

        const getValueTypeColor = (type) => {
            switch (type) {
                case 'string': return 'blue';
                case 'number': return 'green';
                case 'boolean': return 'orange';
                case 'object': return 'purple';
                default: return 'default';
            }
        };

        return (
            <div className="bg-gray-50 border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                    <h4 className="text-sm font-medium text-gray-700">查询结果</h4>
                    <Tag color={getValueTypeColor(valueType)}>{valueType}</Tag>
                </div>
                <div className="bg-white border rounded p-3">
                    {valueType === 'object' ? (
                        <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                            {displayValue}
                        </pre>
                    ) : (
                        <div className="text-lg font-mono text-gray-800">
                            {String(displayValue)}
                        </div>
                    )}
                </div>
            </div>
        );
    };

    // 渲染表格结果
    const renderTableResult = () => {
        if (!isSuccess || !data || !Array.isArray(data)) return null;

        if (data.length === 0) {
            return (
                <Alert
                    type="warning"
                    icon={<InfoCircleOutlined />}
                    message="查询成功，但没有返回数据"
                    className="mb-4"
                />
            );
        }

        // 从第一行数据生成列配置
        const sampleRecord = data[0];
        const columns = Object.keys(sampleRecord).map(key => ({
            title: key,
            dataIndex: key,
            key: key,
            width: 150,
            render: (text) => {
                if (text === null || text === undefined) {
                    return <Tag color="default">NULL</Tag>;
                }
                if (typeof text === 'boolean') {
                    return <Tag color={text ? 'success' : 'error'}>{text ? 'TRUE' : 'FALSE'}</Tag>;
                }
                if (typeof text === 'object') {
                    return <code className="text-xs">{JSON.stringify(text)}</code>;
                }
                return String(text);
            }
        }));

        // 为数据添加唯一key
        const tableData = data.map((row, index) => ({
            ...row,
            _key: index
        }));

        return (
            <div>
                <div className="flex justify-between items-center mb-3">
                    <h4 className="text-sm font-medium text-gray-700">查询结果</h4>
                    <Tag color="blue">{data.length} 条记录</Tag>
                </div>
                <Table
                    columns={columns}
                    dataSource={tableData}
                    rowKey="_key"
                    size="small"
                    scroll={{ x: 'max-content', y: 400 }}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条记录`
                    }}
                />
            </div>
        );
    };

    // 渲染执行信息
    const renderExecutionInfo = () => {
        if (!isSuccess) return null;

        const items = [
            {
                key: 'rowCount',
                label: '影响行数',
                children: rowCount ?? (Array.isArray(data) ? data.length : 1)
            },
            {
                key: 'executionTime',
                label: '执行耗时',
                children: executionTime ? `${executionTime}ms` : '未知'
            },
            {
                key: 'dataType',
                label: '结果类型',
                children: type === 'table' ? '表格数据' : '单值数据'
            }
        ];

        return (
            <div className="mt-4">
                <Descriptions
                    title="执行信息"
                    size="small"
                    column={3}
                    items={items}
                    bordered
                />
            </div>
        );
    };

    return (
        <div className="sql-test-result">
            {renderStatus()}
            
            {isSuccess && (
                <>
                    {type === 'value' ? renderValueResult() : renderTableResult()}
                    {renderExecutionInfo()}
                </>
            )}

            {!isSuccess && message && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-red-800 mb-2">错误详情</h4>
                    <pre className="text-sm text-red-700 whitespace-pre-wrap">
                        {message}
                    </pre>
                </div>
            )}
        </div>
    );
};

export default SqlTestResult; 