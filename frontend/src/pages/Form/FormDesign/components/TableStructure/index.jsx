import React, { useEffect, useState } from 'react';
import { Table, Typography, Tag, Spin, Button, message, Tooltip } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { FetchGetSupportColumnList } from '@/services/crm-api/form-template';

const { Title, Text } = Typography;

const TableStructure = ({ formTemplateId, formTableName }) => {
  const [loading, setLoading] = useState(true);
  const [columns, setColumns] = useState([]);

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    // 检查navigator.clipboard是否可用 这玩意得在https下使用
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      navigator.clipboard.writeText(text)
        .then(() => {
          message.success('已复制到剪贴板');
        })
        .catch(() => {
          // 如果Clipboard API失败，尝试使用传统方法
          fallbackCopyTextToClipboard(text);
        });
    } else {
      // 如果Clipboard API不可用，使用传统方法
      fallbackCopyTextToClipboard(text);
    }
  };

  // 传统复制方法作为回退
  const fallbackCopyTextToClipboard = (text) => {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // 使textArea不可见
      textArea.style.position = 'fixed';
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.width = '2em';
      textArea.style.height = '2em';
      textArea.style.padding = '0';
      textArea.style.border = 'none';
      textArea.style.outline = 'none';
      textArea.style.boxShadow = 'none';
      textArea.style.background = 'transparent';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        message.success('已复制到剪贴板');
      } else {
        message.error('复制失败');
      }
    } catch (err) {
      message.error('复制失败: ' + err.message);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!formTableName) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const { code, data, message: msg } = await FetchGetSupportColumnList({
          table: formTableName,
          type: 0,
          show_fixed_fields: true
        });

        if (code === 0 && data?.list) {
          // 处理数据，确保children属性正确
          const processedData = data.list.map(item => {
            // 确保children是数组，如果为空则设为undefined以便表格正确处理展开/折叠
            if (item.children && Array.isArray(item.children) && item.children.length > 0) {
              return {
                ...item,
                children: item.children.map(child => ({
                  ...child,
                  // 如果子项也有children但为空，设为undefined
                  children: (child.children && Array.isArray(child.children) && child.children.length > 0)
                    ? child.children
                    : undefined
                }))
              };
            }
            return {
              ...item,
              children: undefined
            };
          });

          setColumns(processedData);
        } else {
          console.error('获取表结构失败:', msg);
        }
      } catch (error) {
        console.error('获取表结构出错:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [formTemplateId, formTableName]);

  const tableColumns = [
    {
      title: '字段ID',
      dataIndex: 'id',
      key: 'id',
      width: 220,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 8 }}>{text}</span>
          <Tooltip title="复制ID">
            <Button
              type="text"
              icon={<CopyOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation(); // 防止触发行展开
                copyToClipboard(text);
              }}
            />
          </Tooltip>
        </div>
      )
    },
    {
      title: '字段名称',
      dataIndex: 'label',
      key: 'label',
      width: 150,
    },
    {
      title: '组件类型',
      dataIndex: 'component_name',
      key: 'component_name',
      width: 180,
      render: (text) => {
        const componentName = text ? text.replace('Field.', '') : '';
        return <Tag color="blue">{componentName}</Tag>;
      }
    },
    {
      title: '数据库类型',
      dataIndex: 'column_type',
      key: 'column_type',
      width: 150,
    },
    {
      title: '是否必填',
      dataIndex: 'props',
      key: 'required',
      width: 100,
      render: (props, record) => {
        console.log("props", props)
        // 优先使用props.isRequired，如果不存在则使用record.required
        const isRequired = props && typeof props.isRequired !== 'undefined'
          ? props.isRequired
          : record.required;

        return isRequired ? <Tag color="red">必填</Tag> : <Tag color="default">选填</Tag>;
      },
    },
    {
      title: '是否固定字段',
      dataIndex: 'is_fixed',
      key: 'is_fixed',
      width: 120,
      render: (isFixed) => (
        isFixed ? <Tag color="green">是</Tag> : <Tag color="default">否</Tag>
      ),
    }
  ];

  return (
    <div style={{ padding: '0 10px' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '30px 0' }}>
          <Spin tip="加载中..." />
        </div>
      ) : (
        <>
          <Table
            dataSource={columns}
            columns={tableColumns}
            rowKey="id"
            pagination={false}
            scroll={{ y: 500 }}
            size="small"
            expandable={{
              defaultExpandAllRows: false,
              indentSize: 20
            }}
          />
        </>
      )}
    </div>
  );
};

export default TableStructure;
