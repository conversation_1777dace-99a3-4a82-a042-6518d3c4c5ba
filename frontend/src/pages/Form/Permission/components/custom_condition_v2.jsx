import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Button,
  Select,
  Input,
  Form,
  Space,
  Card,
  Divider,
  Radio,
  Popconfirm,
  Tag,
  message,
  Tabs
} from 'antd';
import { PlusOutlined, DeleteOutlined, TableOutlined, UserOutlined } from '@ant-design/icons';
import { FetchGetSupportTableList, FetchGetSupportFieldsList } from '../../../../services/crm-api/form-template';

// 生成唯一ID
const generateUniqueId = () => {
  return 'ref_' + Math.random().toString(36).substr(2, 9);
};

// 确保条件组和规则都有ID
const ensureIdsInConditionGroups = (conditionGroups) => {
  if (!conditionGroups || !conditionGroups.conditions) return conditionGroups;

  return {
    ...conditionGroups,
    conditions: conditionGroups.conditions.map(group => ({
      ...group,
      id: group.id || generateUniqueId(),
      rules: (group.rules || []).map(rule => ({
        ...rule,
        id: rule.id || generateUniqueId()
      }))
    }))
  };
};

// 内置数据源字段选项
const currentUserFields = [
  { label: '用户ID', value: 'user_id' },
  { label: '用户名', value: 'username' },
  { label: '主部门ID', value: 'dept_id' },
  { label: '部门ID列表', value: 'dept_ids' },
  { label: '岗位ID列表', value: 'post_ids' },
  { label: '角色编码列表', value: 'role_codes' },
];

// 操作符选项
const operatorOptions = [
  { label: '等于', value: '=' },
  { label: '不等于', value: '!=' },
  { label: '包含', value: 'in' },
  { label: '不包含', value: 'not in' },
  { label: '大于', value: '>' },
  { label: '小于', value: '<' },
  { label: '大于等于', value: '>=' },
  { label: '小于等于', value: '<=' },
  { label: '模糊匹配', value: 'like' },
];

// 值类型选项
const valueTypeOptions = [
  { label: '固定值', value: 'fixed' },
  { label: '引用数据', value: 'refData' },
];

// 条件字段组件
const ConditionFields = React.memo(({
  value = {},
  onChange,
  formColumns,
  refDatas = [],
  relationTableColumns = {}
}) => {
  // 获取引用数据源选项
  const refDataSourceOptions = useMemo(() => {
    const options = [
      { label: '当前用户', value: 'currentUser' }
    ];
    
    // 添加自定义数据源
    refDatas.forEach(refData => {
      options.push({
        label: refData.tableLabel || refData.tableName,
        value: refData.id
      });
    });
    
    return options;
  }, [refDatas]);

  // 获取数据源字段选项
  const refDataSourceFieldOptions = useMemo(() => {
    if (!value.refDataSourceId) return [];
    
    if (value.refDataSourceId === 'currentUser') {
      return currentUserFields;
    } else {
      // 查找对应的refData
      const refData = refDatas.find(r => r.id === value.refDataSourceId);
      if (refData && refData.tableName) {
        // 从relationTableColumns中获取该表的字段列表
        const tableColumns = relationTableColumns[refData.tableName] || [];
        return tableColumns.map(column => ({
          label: column.label || column.column_name,
          value: column.column_name
        }));
      }
    }
    
    return [];
  }, [value.refDataSourceId, refDatas, relationTableColumns]);

  const handleFieldChange = useCallback((field, newValue) => {
    const updates = { [field]: newValue };
    
    // 当改变数据源时，清空字段选择
    if (field === 'refDataSourceId') {
      updates.refDataSourceFieldId = undefined;
    }
    
    onChange?.({
      ...value,
      ...updates
    });
  }, [value, onChange]);

  const handleValueTypeChange = useCallback((val) => {
    onChange?.({
      ...value,
      valueType: val,
      value: undefined,
      refDataSourceId: undefined,
      refDataSourceFieldId: undefined
    });
  }, [value, onChange]);

  const commonStyle = { marginBottom: 8 };

  return (
    <div className='flex items-center gap-2'>
      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>字段</label>
        <Select
          placeholder="选择字段"
          style={{ width: 120 }}
          value={value.field}
          onChange={(val) => handleFieldChange('field', val)}
          options={formColumns?.map(c => ({
            label: c.label,
            value: c.column_name
          }))}
        />
      </div>

      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>操作符</label>
        <Select
          options={operatorOptions}
          style={{ width: 100 }}
          value={value.operator}
          onChange={(val) => handleFieldChange('operator', val)}
        />
      </div>

      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>值类型</label>
        <Select
          options={valueTypeOptions}
          style={{ width: 100 }}
          value={value.valueType}
          onChange={handleValueTypeChange}
        />
      </div>

      {value.valueType === 'fixed' && (
        <div style={commonStyle}>
          <label style={{ display: 'block', marginBottom: 4 }}>值</label>
          <Input
            placeholder="输入固定值"
            style={{ width: 120 }}
            value={value.value}
            onChange={(e) => handleFieldChange('value', e.target.value)}
          />
        </div>
      )}

      {value.valueType === 'refData' && (
        <>
          <div style={commonStyle}>
            <label style={{ display: 'block', marginBottom: 4 }}>数据源</label>
            <Select
              options={refDataSourceOptions}
              style={{ width: 120 }}
              value={value.refDataSourceId}
              onChange={(val) => handleFieldChange('refDataSourceId', val)}
              placeholder="选择数据源"
            />
          </div>

          <div style={commonStyle}>
            <label style={{ display: 'block', marginBottom: 4 }}>字段</label>
            <Select
              options={refDataSourceFieldOptions}
              style={{ width: 120 }}
              value={value.refDataSourceFieldId}
              onChange={(val) => handleFieldChange('refDataSourceFieldId', val)}
              placeholder="选择字段"
              disabled={!value.refDataSourceId}
            />
          </div>
        </>
      )}
    </div>
  );
});

// 条件组组件
const ConditionGroup = React.memo(({
  value = { logic: 'AND', rules: [] },
  onChange,
  formColumns,
  refDatas = [],
  relationTableColumns = {}
}) => {
  const handleLogicChange = useCallback((e) => {
    onChange?.({
      ...value,
      logic: e.target.value
    });
  }, [value, onChange]);

  const handleRuleChange = useCallback((index, newRule) => {
    const updatedRules = [...(value.rules || [])];
    updatedRules[index] = newRule;
    onChange?.({
      ...value,
      rules: updatedRules
    });
  }, [value, onChange]);

  const handleAddRule = useCallback(() => {
    const newRules = [...(value.rules || []), { id: generateUniqueId() }];
    onChange?.({
      ...value,
      rules: newRules
    });
  }, [value, onChange]);

  const handleRemoveRule = useCallback((index) => {
    const updatedRules = (value.rules || []).filter((_, i) => i !== index);
    onChange?.({
      ...value,
      rules: updatedRules
    });
  }, [value, onChange]);

  return (
    <div style={{
      border: '1px solid #d9d9d9',
      borderRadius: 6,
      padding: 16,
      marginBottom: 12,
      backgroundColor: '#fafafa'
    }}>
      <div style={{ marginBottom: 12 }}>
        <label style={{ display: 'block', marginBottom: 4 }}>条件逻辑</label>
        <Radio.Group
          value={value.logic}
          onChange={handleLogicChange}
        >
          <Radio value="AND">AND (且)</Radio>
          <Radio value="OR">OR (或)</Radio>
        </Radio.Group>
      </div>

      {(value.rules || []).map((rule, index) => (
        <div key={rule.id || `rule-${index}`} className='flex items-center gap-2' style={{
          border: '1px solid #e6e6e6',
          borderRadius: 4,
          padding: 12,
          marginBottom: 8,
          backgroundColor: '#ffffff'
        }}>
          <div style={{ flex: 1 }}>
            <ConditionFields
              value={rule}
              onChange={(val) => handleRuleChange(index, val)}
              formColumns={formColumns}
              refDatas={refDatas}
              relationTableColumns={relationTableColumns}
            />
          </div>

          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveRule(index)}
            size="small"
          />
        </div>
      ))}

      <Button
        type="dashed"
        onClick={handleAddRule}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
        size="small"
      >
        添加条件
      </Button>
    </div>
  );
});

// 主条件组组件
const MainConditionGroups = React.memo(({
  value = { logic: 'AND', conditions: [] },
  onChange,
  formColumns,
  refDatas = [],
  relationTableColumns = {}
}) => {
  const handleLogicChange = useCallback((e) => {
    onChange?.({
      ...value,
      logic: e.target.value
    });
  }, [value, onChange]);

  const handleConditionGroupChange = useCallback((index, newGroup) => {
    const updatedConditions = [...(value.conditions || [])];
    updatedConditions[index] = newGroup;
    onChange?.({
      ...value,
      conditions: updatedConditions
    });
  }, [value, onChange]);

  const handleAddConditionGroup = useCallback(() => {
    const newConditions = [...(value.conditions || []), {
      id: generateUniqueId(),
      logic: 'AND',
      rules: []
    }];
    onChange?.({
      ...value,
      conditions: newConditions
    });
  }, [value, onChange]);

  const handleRemoveConditionGroup = useCallback((index) => {
    const updatedConditions = (value.conditions || []).filter((_, i) => i !== index);
    onChange?.({
      ...value,
      conditions: updatedConditions
    });
  }, [value, onChange]);

  return (
    <Card
      title="过滤条件"
      size="small"
      style={{ marginBottom: 12 }}
    >
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'block', marginBottom: 4 }}>条件组逻辑</label>
        <Radio.Group
          value={value.logic}
          onChange={handleLogicChange}
        >
          <Radio value="AND">AND (且)</Radio>
          <Radio value="OR">OR (或)</Radio>
        </Radio.Group>
      </div>

      {(value.conditions || []).map((conditionGroup, index) => (
        <div key={conditionGroup.id || `group-${index}`} style={{ position: 'relative', marginBottom: 16 }}>
          <div style={{
            position: 'absolute',
            top: -10,
            left: 10,
            backgroundColor: '#f0f0f0',
            padding: '2px 8px',
            borderRadius: 4,
            fontSize: 12,
            color: '#666'
          }}>
            条件组 {index + 1}
          </div>

          <ConditionGroup
            value={conditionGroup}
            onChange={(val) => handleConditionGroupChange(index, val)}
            formColumns={formColumns}
            refDatas={refDatas}
            relationTableColumns={relationTableColumns}
          />

          {(value.conditions || []).length > 0 && (
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveConditionGroup(index)}
              size="small"
              style={{
                position: 'absolute',
                top: 8,
                right: 8
              }}
            />
          )}
        </div>
      ))}

      <Button
        type="dashed"
        onClick={handleAddConditionGroup}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
      >
        添加条件组
      </Button>
    </Card>
  );
});

// 引用数据配置组件
const RefDataConfig = React.memo(({
  value = {},
  onChange,
  supportTables,
  relationTableColumns,
  onTableSelect
}) => {
  const handleFieldChange = useCallback((field, newValue) => {
    onChange?.({
      ...value,
      [field]: newValue
    });
  }, [value, onChange]);

  const handleTableSelect = useCallback(async (tableName) => {
    const selectedTable = supportTables.find(t => t.table_name === tableName);
    if (selectedTable) {
      // 一次性更新多个字段，避免多次渲染和状态竞态
      onChange?.({
        ...value,
        tableName: tableName,
        tableLabel: selectedTable.label
      });
      await onTableSelect?.(tableName, selectedTable.type || 0);
    }
  }, [supportTables, onTableSelect, value, onChange]);

  return (
    <div style={{
      border: '1px solid #d9d9d9',
      borderRadius: 6,
      padding: 16,
      marginBottom: 12,
      backgroundColor: '#fafafa'
    }}>
      <div className='flex items-center gap-2' style={{ marginBottom: 12 }}>
        <div>
          <label style={{ display: 'block', marginBottom: 4 }}>数据源ID</label>
          <Input
            placeholder="自动生成"
            style={{ width: 150 }}
            value={value.id}
            disabled
          />
        </div>
        
        <div>
          <label style={{ display: 'block', marginBottom: 4 }}>表名</label>
          <Select
            placeholder="选择表"
            style={{ width: 200 }}
            value={value.tableName}
            onChange={handleTableSelect}
            options={supportTables?.map(t => ({
              label: t.label,
              value: t.table_name
            }))}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: 4 }}>表标签</label>
          <Input
            placeholder="表显示名称"
            style={{ width: 150 }}
            value={value.tableLabel}
            onChange={(e) => handleFieldChange('tableLabel', e.target.value)}
          />
        </div>
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: 8 }}>过滤条件</label>
        <MainConditionGroups
          value={value.filterCondition || { logic: 'AND', conditions: [] }}
          onChange={(val) => handleFieldChange('filterCondition', val)}
          formColumns={relationTableColumns[value.tableName] || []}
          refDatas={[]} // 引用数据的过滤条件中暂不支持嵌套引用
          relationTableColumns={relationTableColumns}
        />
      </div>
    </div>
  );
});

// 主组件
const CustomConditionV2 = ({ value, onChange, form_table }) => {
  const [form] = Form.useForm();
  const [formColumns, setFormColumns] = useState([]);
  const [supportTables, setSupportTables] = useState([]);
  const [relationTableColumns, setRelationTableColumns] = useState({});
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [pendingValue, setPendingValue] = useState(null);

  // 当前引用数据列表 - 使用Form.useWatch实时监听表单字段变化
  const currentRefDatas = Form.useWatch('refDatas', form) || [];

  useEffect(() => {
    if (form_table) {
      const loadInitialData = async () => {
        setLoading(true);
        try {
          await Promise.all([
            fetchFormColumns(),
            fetchSupportTables()
          ]);
          setDataLoaded(true);
        } catch (error) {
          console.error('加载初始数据失败:', error);
        } finally {
          setLoading(false);
        }
      };
      loadInitialData();
    }
  }, [form_table]);

  useEffect(() => {
    if (dataLoaded && pendingValue) {
      setFormValueWithDependencies(pendingValue);
      setPendingValue(null);
    }
  }, [dataLoaded, pendingValue]);

  useEffect(() => {
    if (value) {
      if (dataLoaded) {
        setFormValueWithDependencies(value);
      } else {
        setPendingValue(value);
      }
    }
  }, [value, dataLoaded]);

  const setFormValueWithDependencies = async (valueToSet) => {
    try {
      const valueWithIds = {
        refDatas: [],
        conditionGroups: { logic: 'AND', conditions: [] },
        ...valueToSet,
        conditionGroups: ensureIdsInConditionGroups(valueToSet.conditionGroups || { logic: 'AND', conditions: [] })
      };

      // 为没有ID的refData生成ID
      if (valueWithIds.refDatas) {
        valueWithIds.refDatas = valueWithIds.refDatas.map(refData => ({
          ...refData,
          id: refData.id || generateUniqueId(),
          filterCondition: ensureIdsInConditionGroups(refData.filterCondition || { logic: 'AND', conditions: [] })
        }));
      }

      // 加载引用数据表的字段
      if (valueWithIds.refDatas && valueWithIds.refDatas.length > 0) {
        const loadPromises = valueWithIds.refDatas.map(async (refData) => {
          if (refData?.tableName && !relationTableColumns[refData.tableName]) {
            const selectedTable = supportTables.find(t => t.table_name === refData.tableName);
            if (selectedTable) {
              await fetchRelationTableColumns(refData.tableName, selectedTable.type || 0);
            }
          }
        });

        await Promise.all(loadPromises);
      }

      form.setFieldsValue(valueWithIds);
    } catch (error) {
      console.error('设置表单值失败:', error);
    }
  };

  const fetchFormColumns = async () => {
    try {
      const response = await FetchGetSupportFieldsList({
        table: form_table,
        type: 0
      });
      if (response?.code === 0) {
        setFormColumns(response.data?.list || []);
        return response.data?.list || [];
      } else {
        message.error(response?.message || '获取表单字段失败');
        return [];
      }
    } catch (error) {
      message.error('获取表单字段失败');
      return [];
    }
  };

  const fetchSupportTables = async () => {
    try {
      const response = await FetchGetSupportTableList();
      if (response?.code === 0) {
        setSupportTables(response.data?.list || []);
        return response.data?.list || [];
      } else {
        message.error(response?.message || '获取支持表列表失败');
        return [];
      }
    } catch (error) {
      message.error('获取支持表列表失败');
      return [];
    }
  };

  const fetchRelationTableColumns = useCallback(async (tableName, tableType = 0) => {
    try {
      const response = await FetchGetSupportFieldsList({
        table: tableName,
        type: tableType
      });
      if (response?.code === 0) {
        const columns = response.data?.list || [];
        setRelationTableColumns(prev => ({
          ...prev,
          [tableName]: columns
        }));
        return columns;
      } else {
        message.error(`获取表${tableName}字段失败`);
        return [];
      }
    } catch (error) {
      message.error(`获取表${tableName}字段失败`);
      return [];
    }
  }, []);

  const handleFormChange = useCallback((changedFields, allValues) => {
    // 确保conditionGroups有正确的ID
    if (allValues.conditionGroups) {
      allValues.conditionGroups = ensureIdsInConditionGroups(allValues.conditionGroups);
    }

    // 确保refDatas中的filterCondition有正确的ID
    if (allValues.refDatas) {
      allValues.refDatas = allValues.refDatas.map(refData => ({
        ...refData,
        filterCondition: ensureIdsInConditionGroups(refData.filterCondition || { logic: 'AND', conditions: [] })
      }));
    }

    onChange?.(allValues);
  }, [onChange]);

  const handleTableSelect = useCallback(async (tableName, tableType) => {
    if (!relationTableColumns[tableName]) {
      await fetchRelationTableColumns(tableName, tableType);
    }
  }, [relationTableColumns, fetchRelationTableColumns]);

  // 渲染引用数据配置
  const renderRefDatas = () => (
    <Card 
      title={
        <span>
          <TableOutlined style={{ marginRight: 8 }} />
          引用数据源配置
        </span>
      } 
      size="small" 
      style={{ marginBottom: 12 }}
    >
      <Form.List name="refDatas">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <div key={key} style={{ position: 'relative', marginBottom: 16 }}>
                <div style={{
                  position: 'absolute',
                  top: -10,
                  left: 10,
                  backgroundColor: '#e6f7ff',
                  padding: '2px 8px',
                  borderRadius: 4,
                  fontSize: 12,
                  color: '#1890ff',
                  zIndex: 1
                }}>
                  数据源 {name + 1}
                </div>

                <Form.Item
                  {...restField}
                  name={[name]}
                  style={{ marginBottom: 0 }}
                >
                  <RefDataConfig
                    supportTables={supportTables}
                    relationTableColumns={relationTableColumns}
                    onTableSelect={handleTableSelect}
                  />
                </Form.Item>

                <Popconfirm
                  title="确认删除"
                  description="删除此数据源将同时清空相关的条件配置，确认删除？"
                  onConfirm={() => remove(name)}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button
                    type="link"
                    danger
                    icon={<DeleteOutlined />}
                    size="small"
                    style={{
                      position: 'absolute',
                      top: 8,
                      right: 8
                    }}
                  />
                </Popconfirm>
              </div>
            ))}
            <Button
              type="dashed"
              onClick={() => {
                add({
                  id: generateUniqueId(),
                  tableName: '',
                  tableLabel: '',
                  filterCondition: {
                    logic: 'AND',
                    conditions: []
                  }
                });
              }}
              icon={<PlusOutlined />}
              style={{ width: '100%', marginTop: 8 }}
            >
              添加数据源
            </Button>
          </>
        )}
      </Form.List>
    </Card>
  );

  // 渲染条件规则
  const renderConditionRules = () => {
    return (
      <Form.Item
        name="conditionGroups"
        initialValue={{ logic: 'AND', conditions: [] }}
      >
        <MainConditionGroups
          formColumns={formColumns}
          refDatas={currentRefDatas}
          relationTableColumns={relationTableColumns}
        />
      </Form.Item>
    );
  };

  if (loading && !dataLoaded) {
    return <div style={{ padding: 20, textAlign: 'center' }}>加载中...</div>;
  }

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        refDatas: [],
        conditionGroups: {
          logic: 'AND',
          conditions: []
        }
      }}
      onValuesChange={handleFormChange}
      style={{ margin: 0 }}
    >
      <Tabs
        defaultActiveKey="refDatas"
        items={[
          {
            key: 'refDatas',
            label: (
              <span>
                <TableOutlined />
                数据源配置
              </span>
            ),
            children: renderRefDatas()
          },
          {
            key: 'conditions',
            label: (
              <span>
                <UserOutlined />
                条件配置
              </span>
            ),
            children: renderConditionRules()
          }
        ]}
      />
    </Form>
  );
};

// 渲染组件
CustomConditionV2.Render = ({ value }) => {
  if (!value) return null;

  const { refDatas = [], conditionGroups = {} } = value;

  // 计算总规则数
  const totalRules = conditionGroups.conditions?.reduce((sum, group) => {
    return sum + (group.rules?.length || 0);
  }, 0) || 0;

  return (
    <div>
      {refDatas?.length > 0 && (
        <div style={{ marginBottom: 8 }}>
          <strong>数据源:</strong>
          {refDatas.map((refData, index) => (
            <Tag key={index} color="blue" style={{ margin: '0 4px' }}>
              {refData.tableLabel || refData.tableName}
            </Tag>
          ))}
        </div>
      )}
      {conditionGroups.conditions?.length > 0 && (
        <div>
          <strong>条件:</strong>
          <Tag color="green">{conditionGroups.logic}</Tag>
          <span>
            {conditionGroups?.conditions?.length}个条件组，
            共{totalRules}个规则
          </span>
        </div>
      )}
      {refDatas?.length === 0 && conditionGroups?.conditions?.length === 0 && (
        <span style={{ color: '#999' }}>未配置条件</span>
      )}
    </div>
  );
};

export {CustomConditionV2};
