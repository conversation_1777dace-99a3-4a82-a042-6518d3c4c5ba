/**
 * 旧的自定义条件设置组件，高度自由，可以关联表，可能更适合流程条件设置，暂时弃用
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Button,
  Select,
  Input,
  Form,
  Space,
  Card,
  Divider,
  Radio,
  Popconfirm,
  Tag,
  message
} from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { FetchGetSupportTableList, FetchGetSupportColumnList, FetchGetSupportFieldsList } from '../../../../services/crm-api/form-template';

// 生成随机ID作为表别名
const generateTableAlias = () => {
  return 't' + Math.random().toString(36).substr(2, 8);
};

// 生成唯一ID
const generateUniqueId = () => {
  return 'id_' + Math.random().toString(36).substr(2, 9);
};

// 确保条件组和规则都有ID
const ensureIdsInConditionGroups = (conditionGroups) => {
  if (!conditionGroups || !conditionGroups.conditions) return conditionGroups;

  return {
    ...conditionGroups,
    conditions: conditionGroups.conditions.map(group => ({
      ...group,
      id: group.id || generateUniqueId(),
      rules: (group.rules || []).map(rule => ({
        ...rule,
        id: rule.id || generateUniqueId()
      }))
    }))
  };
};

// 用户属性选项
const userPropertyOptions = [
  { label: '用户ID', value: 'id' },
  { label: '部门ID列表', value: 'dept_ids' },
  { label: '岗位ID列表', value: 'post_ids' },
];

// 操作符选项
const operatorOptions = [
  { label: '等于', value: '=' },
  { label: '不等于', value: '!=' },
  { label: '包含', value: 'in' },
  { label: '不包含', value: 'not in' },
  { label: '大于', value: '>' },
  { label: '小于', value: '<' },
  { label: '大于等于', value: '>=' },
  { label: '小于等于', value: '<=' },
  { label: '模糊匹配', value: 'like' },
];

// 连接类型选项
const joinTypeOptions = [
  { label: 'INNER JOIN', value: 'INNER' },
  { label: 'LEFT JOIN', value: 'LEFT' },
  { label: 'RIGHT JOIN', value: 'RIGHT' },
];

// 值类型选项
const valueTypeOptions = [
  { label: '固定值', value: 'fixed' },
  { label: '引用', value: 'variable' },
];

// 条件字段渲染组件
const ConditionFields = React.memo(({
  value = {},
  onChange,
  formColumns,
  relationTableColumns,
  withTableMaps = []
}) => {
  // 使用 useMemo 计算字段来源选项，避免不必要的重新计算
  const fieldSourceOptions = useMemo(() => {
    const options = [{ label: '当前表', value: 'current' }];

    // 只显示已完整配置的关联表
    (withTableMaps || []).forEach((table, index) => {
      if (table?.alias && table?.tableName && table?.tableLabel) {
        options.push({
          label: table.tableLabel,
          value: table.alias
        });
      }
    });

    return options;
  }, [withTableMaps]);

  // 使用 useMemo 计算字段选项
  const fieldOptions = useMemo(() => {
    if (!value.fieldSource) return [];

    if (value.fieldSource === 'current') {
      return (formColumns || []).map(c => ({
        label: c.label,
        value: c.column_name
      }));
    } else {
      // 关联表字段
      const selectedTable = (withTableMaps || []).find(table =>
        table?.alias === value.fieldSource
      );
      if (selectedTable?.tableName) {
        const tableColumns = relationTableColumns[selectedTable.tableName] || [];
        return tableColumns.map(c => ({
          label: c.label,
          value: c.column_name
        }));
      }
    }

    return [];
  }, [value.fieldSource, formColumns, relationTableColumns, withTableMaps]);

  // 使用 useCallback 避免不必要的函数重新创建
  const handleFieldSourceChange = useCallback((val) => {
    onChange?.({
      ...value,
      fieldSource: val,
      field: undefined // 清空字段选择
    });
  }, [value, onChange]);

  const handleFieldChange = useCallback((field, newValue) => {
    onChange?.({
      ...value,
      [field]: newValue
    });
  }, [value, onChange]);

  const handleValueTypeChange = useCallback((val) => {
    onChange?.({
      ...value,
      valueType: val,
      value: undefined,
      variable: undefined
    });
  }, [value, onChange]);

  const commonStyle = { marginBottom: 8 };

  return (
    <div className='flex items-center gap-2'>
      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>字段来源</label>
        <Select
          placeholder="选择字段来源"
          style={{ width: 120 }}
          value={value.fieldSource}
          onChange={handleFieldSourceChange}
          options={fieldSourceOptions}
        />
      </div>

      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>字段</label>
        <Select
          placeholder="选择字段"
          style={{ width: 120 }}
          value={value.field}
          onChange={(val) => handleFieldChange('field', val)}
          options={fieldOptions}
          disabled={!value.fieldSource}
        />
      </div>

      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>操作符</label>
        <Select
          options={operatorOptions}
          style={{ width: 100 }}
          value={value.operator}
          onChange={(val) => handleFieldChange('operator', val)}
        />
      </div>

      <div style={commonStyle}>
        <label style={{ display: 'block', marginBottom: 4 }}>值类型</label>
        <Select
          options={valueTypeOptions}
          style={{ width: 100 }}
          value={value.valueType}
          onChange={handleValueTypeChange}
        />
      </div>

      {value.valueType === 'fixed' && (
        <div style={commonStyle}>
          <label style={{ display: 'block', marginBottom: 4 }}>值</label>
          <Input
            placeholder="输入固定值"
            style={{ width: 120 }}
            value={value.value}
            onChange={(e) => handleFieldChange('value', e.target.value)}
          />
        </div>
      )}

      {value.valueType === 'variable' && (
        <div style={commonStyle}>
          <label style={{ display: 'block', marginBottom: 4 }}>引用</label>
          <Select
            options={userPropertyOptions}
            style={{ width: 120 }}
            value={value.variable}
            onChange={(val) => handleFieldChange('variable', val)}
            placeholder="选择用户属性"
          />
        </div>
      )}
    </div>
  );
});

// 条件组组件
const ConditionGroup = React.memo(({
  value = { logic: 'AND', rules: [] },
  onChange,
  formColumns,
  relationTableColumns,
  withTableMaps = []
}) => {
  const handleLogicChange = useCallback((e) => {
    onChange?.({
      ...value,
      logic: e.target.value
    });
  }, [value, onChange]);

  const handleRuleChange = useCallback((index, newRule) => {
    const updatedRules = [...(value.rules || [])];
    updatedRules[index] = newRule;
    onChange?.({
      ...value,
      rules: updatedRules
    });
  }, [value, onChange]);

  const handleAddRule = useCallback(() => {
    const newRules = [...(value.rules || []), { id: generateUniqueId() }];
    onChange?.({
      ...value,
      rules: newRules
    });
  }, [value, onChange]);

  const handleRemoveRule = useCallback((index) => {
    const updatedRules = (value.rules || []).filter((_, i) => i !== index);
    onChange?.({
      ...value,
      rules: updatedRules
    });
  }, [value, onChange]);

  return (
    <div style={{
      border: '1px solid #d9d9d9',
      borderRadius: 6,
      padding: 16,
      marginBottom: 12,
      backgroundColor: '#fafafa'
    }}>
      <div style={{ marginBottom: 12 }}>
        <label style={{ display: 'block', marginBottom: 4 }}>条件逻辑</label>
        <Radio.Group
          value={value.logic}
          onChange={handleLogicChange}
        >
          <Radio value="AND">AND (且)</Radio>
          <Radio value="OR">OR (或)</Radio>
        </Radio.Group>
      </div>

      {(value.rules || []).map((rule, index) => (
        <div key={rule.id || `rule-${index}`} className='flex items-center gap-2' style={{
          border: '1px solid #e6e6e6',
          borderRadius: 4,
          padding: 12,
          marginBottom: 8,
          backgroundColor: '#ffffff'
        }}>
          <div style={{ flex: 1 }}>
            <ConditionFields
              value={rule}
              onChange={(val) => handleRuleChange(index, val)}
              formColumns={formColumns}
              relationTableColumns={relationTableColumns}
              withTableMaps={withTableMaps}
            />
          </div>

          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveRule(index)}
            size="small"
          />
        </div>
      ))}

      <Button
        type="dashed"
        onClick={handleAddRule}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
        size="small"
      >
        添加条件
      </Button>
    </div>
  );
});

// 主条件组组件
const MainConditionGroups = React.memo(({
  value = { logic: 'AND', conditions: [] },
  onChange,
  formColumns,
  relationTableColumns,
  withTableMaps = []
}) => {
  const handleLogicChange = useCallback((e) => {
    onChange?.({
      ...value,
      logic: e.target.value
    });
  }, [value, onChange]);

  const handleConditionGroupChange = useCallback((index, newGroup) => {
    const updatedConditions = [...(value.conditions || [])];
    updatedConditions[index] = newGroup;
    onChange?.({
      ...value,
      conditions: updatedConditions
    });
  }, [value, onChange]);

  const handleAddConditionGroup = useCallback(() => {
    const newConditions = [...(value.conditions || []), {
      id: generateUniqueId(),
      logic: 'AND',
      rules: []
    }];
    onChange?.({
      ...value,
      conditions: newConditions
    });
  }, [value, onChange]);

  const handleRemoveConditionGroup = useCallback((index) => {
    const updatedConditions = (value.conditions || []).filter((_, i) => i !== index);
    onChange?.({
      ...value,
      conditions: updatedConditions
    });
  }, [value, onChange]);

  return (
    <Card
      title="过滤条件"
      size="small"
      style={{ marginBottom: 12 }}
    >
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'block', marginBottom: 4 }}>条件组逻辑</label>
        <Radio.Group
          value={value.logic}
          onChange={handleLogicChange}
        >
          <Radio value="AND">AND (且)</Radio>
          <Radio value="OR">OR (或)</Radio>
        </Radio.Group>
      </div>

      {(value.conditions || []).map((conditionGroup, index) => (
        <div key={conditionGroup.id || `group-${index}`} style={{ position: 'relative', marginBottom: 16 }}>
          <div style={{
            position: 'absolute',
            top: -10,
            left: 10,
            backgroundColor: '#f0f0f0',
            padding: '2px 8px',
            borderRadius: 4,
            fontSize: 12,
            color: '#666'
          }}>
            条件组 {index + 1}
          </div>

          <ConditionGroup
            value={conditionGroup}
            onChange={(val) => handleConditionGroupChange(index, val)}
            formColumns={formColumns}
            relationTableColumns={relationTableColumns}
            withTableMaps={withTableMaps}
          />

          {(value.conditions || []).length > 0 && (
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveConditionGroup(index)}
              size="small"
              style={{
                position: 'absolute',
                top: 8,
                right: 8
              }}
            />
          )}
        </div>
      ))}

      <Button
        type="dashed"
        onClick={handleAddConditionGroup}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
      >
        添加条件组
      </Button>
    </Card>
  );
});

// 主组件
const CustomCondition = ({ value, onChange, form_table }) => {
  const [form] = Form.useForm();
  const [formColumns, setFormColumns] = useState([]);
  const [supportTables, setSupportTables] = useState([]);
  const [relationTableColumns, setRelationTableColumns] = useState({});
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [pendingValue, setPendingValue] = useState(null); 

  // 使用 useMemo 来避免不必要的重新渲染
  const currentWithTableMaps = useMemo(() => {
    return form.getFieldValue('withTableDataMaps') || [];
  }, [form.getFieldValue('withTableDataMaps')]);

  useEffect(() => {
    if (form_table) {
      const loadInitialData = async () => {
        setLoading(true);
        try {
          // 并行加载基础数据
          await Promise.all([
            fetchFormColumns(),
            fetchSupportTables()
          ]);
          setDataLoaded(true);
        } catch (error) {
          console.error('加载初始数据失败:', error);
        } finally {
          setLoading(false);
        }
      };
      loadInitialData();
    }
  }, [form_table]);

  useEffect(() => {
    if (dataLoaded && pendingValue) {
      setFormValueWithDependencies(pendingValue);
      setPendingValue(null);
    }
  }, [dataLoaded, pendingValue]);

  useEffect(() => {
    if (value) {
      console.log("value", JSON.stringify(value))
      if (dataLoaded) {
        // 如果数据已加载，直接设置
        setFormValueWithDependencies(value);
      } else {
        // 如果数据未加载，缓存待设置
        setPendingValue(value);
      }
    }
  }, [value, dataLoaded]);

  const setFormValueWithDependencies = async (valueToSet) => {
    try {
      // 确保所有条件组和规则都有ID
      const valueWithIds = {
        withTableDataMaps: [],
        conditionGroups: { logic: 'AND', conditions: [] },
        ...valueToSet,
        conditionGroups: ensureIdsInConditionGroups(valueToSet.conditionGroups || { logic: 'AND', conditions: [] })
      };

      if (valueWithIds.withTableDataMaps && valueWithIds.withTableDataMaps.length > 0) {
        const loadPromises = valueWithIds.withTableDataMaps.map(async (table) => {
          if (table?.tableName && !relationTableColumns[table.tableName]) {
            const selectedTable = supportTables.find(t => t.table_name === table.tableName);
            if (selectedTable) {
              await fetchRelationTableColumns(table.tableName, selectedTable.type || 0);
            }
          }
        });

        await Promise.all(loadPromises);
      }

      // 设置表单值
      form.setFieldsValue(valueWithIds);

    } catch (error) {
      console.error('设置表单值失败:', error);
    }
  };

  const fetchFormColumns = async () => {
    try {
      const response = await FetchGetSupportFieldsList({
        table: form_table,
        type: 0
      });
      if (response?.code === 0) {
        setFormColumns(response.data?.list || []);
        return response.data?.list || [];
      } else {
        message.error(response?.message || '获取表单字段失败');
        return [];
      }
    } catch (error) {
      message.error('获取表单字段失败');
      return [];
    }
  };

  const fetchSupportTables = async () => {
    try {
      const response = await FetchGetSupportTableList();
      if (response?.code === 0) {
        setSupportTables(response.data?.list || []);
        return response.data?.list || [];
      } else {
        message.error(response?.message || '获取支持表列表失败');
        return [];
      }
    } catch (error) {
      message.error('获取支持表列表失败');
      return [];
    }
  };
  const [fieldsUpdateKey, setFieldsUpdateKey] = useState(0);

  // 在 relationTableColumns 更新后触发重新渲染
  const fetchRelationTableColumns = useCallback(async (tableName, tableType = 0) => {
    try {
      const response = await FetchGetSupportFieldsList({
        table: tableName,
        type: tableType
      });
      if (response?.code === 0) {
        const columns = response.data?.list || [];
        setRelationTableColumns(prev => ({
          ...prev,
          [tableName]: columns
        }));

        // 🔥 强制字段选择器重新渲染
        setFieldsUpdateKey(prev => prev + 1);

        return columns;
      } else {
        message.error(`获取表${tableName}字段失败`);
        return [];
      }
    } catch (error) {
      message.error(`获取表${tableName}字段失败`);
      return [];
    }
  }, []);

  // 优化表单变化处理
  const handleFormChange = useCallback((changedFields, allValues) => {
    // 处理tableLabel的自动更新
    let updatedAllValues = { ...allValues };

    if (updatedAllValues.withTableDataMaps) {
      updatedAllValues.withTableDataMaps = updatedAllValues.withTableDataMaps.map(table => {
        if (table?.tableName && !table.tableLabel) {
          const selectedTable = supportTables.find(t => t.table_name === table.tableName);
          if (selectedTable) {
            return {
              ...table,
              tableLabel: selectedTable.label
            };
          }
        }
        return table;
      });
    }

    // 确保conditionGroups有正确的ID
    if (updatedAllValues.conditionGroups) {
      updatedAllValues.conditionGroups = ensureIdsInConditionGroups(updatedAllValues.conditionGroups);
    }

    // 传递更新后的完整数据
    onChange?.(updatedAllValues);
  }, [supportTables, onChange]);

  // 当选择关联表时，自动获取该表的字段
  const handleTableSelect = useCallback(async (tableName, tableType) => {
    if (!relationTableColumns[tableName]) {
      await fetchRelationTableColumns(tableName, tableType);
    }
  }, [relationTableColumns, fetchRelationTableColumns]);

  // 渲染关联表配置
  const renderWithTableMaps = () => (
    <Card title="关联表配置" size="small" style={{ marginBottom: 12 }}>
      <Form.List name="withTableDataMaps">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <div key={key} className='flex items-center gap-2' style={{
                border: '1px solid #d9d9d9',
                borderRadius: 6,
                padding: 12,
                marginBottom: 8,
                backgroundColor: '#fafafa'
              }}>
                <Form.Item
                  {...restField}
                  name={[name, 'tableName']}
                  label="关联表"
                  rules={[{ required: true, message: '请选择关联表' }]}
                  style={{ marginBottom: 8 }}
                >
                  <Select
                    placeholder="选择关联表"
                    style={{ width: 200 }}
                    options={supportTables.map(t => ({
                      label: t.label,
                      value: t.table_name
                    }))}
                    onChange={async (value) => { 
                      const selectedTable = supportTables.find(t => t.table_name === value);
                      if (selectedTable) {
                        // 使用 setFieldsValue 批量设置值，确保嵌套结构正确
                        const currentValues = form.getFieldValue('withTableDataMaps') || [];
                        const updatedValues = [...currentValues];

                        // 确保当前索引的对象存在
                        if (!updatedValues[name]) {
                          updatedValues[name] = {};
                        }

                        // 设置表相关信息
                        updatedValues[name] = {
                          ...updatedValues[name],
                          tableLabel: selectedTable.label,
                          tableName: value,
                          // 确保 joinCondition 对象存在且有正确的结构
                          joinCondition: {
                            currentField: updatedValues[name]?.joinCondition?.currentField || '',
                            relationField: updatedValues[name]?.joinCondition?.relationField || ''
                          }
                        };

                        form.setFieldValue('withTableDataMaps', updatedValues);

                        await handleTableSelect(value, selectedTable.type || 0);
                      }
                    }}
                  />
                </Form.Item>

                {/* 隐藏字段存储自动生成的别名和表标签 */}
                <Form.Item
                  {...restField}
                  name={[name, 'alias']}
                  style={{ display: 'none' }}
                >
                  <Input />
                </Form.Item>

                <Form.Item
                  {...restField}
                  name={[name, 'tableLabel']}
                  style={{ display: 'none' }}
                >
                  <Input />
                </Form.Item>

                <Form.Item
                  {...restField}
                  name={[name, 'joinType']}
                  label="连接类型"
                  rules={[{ required: true, message: '请选择连接类型' }]}
                  style={{ marginBottom: 8 }}
                >
                  <Select options={joinTypeOptions} style={{ width: 120 }} />
                </Form.Item>

                <Form.Item
                  {...restField}
                  name={[name, 'joinCondition', 'currentField']}
                  label="左侧字段"
                  key={`current-field-${name}-${fieldsUpdateKey}`}
                  rules={[{ required: true, message: '请选择字段' }]}
                  style={{ marginBottom: 8 }}
                >
                  <Select
                    placeholder="选择来源表.字段"
                    style={{ width: 180 }}
                    options={(() => {
                      const allTableMaps = form.getFieldValue('withTableDataMaps') || [];
                      const availableTables = [
                        { label: '当前表', alias: 'current', tableName: form_table }
                      ];

                      // 添加之前配置的关联表
                      for (let i = 0; i < name; i++) {
                        const prevTable = allTableMaps[i];
                        if (prevTable?.tableName) {
                          availableTables.push({
                            label: prevTable.tableLabel || prevTable.tableName,
                            alias: prevTable.alias,
                            tableName: prevTable.tableName
                          });
                        }
                      }

                      return availableTables.map(table => {
                        const columns = table.alias === 'current'
                          ? formColumns
                          : (relationTableColumns[table.tableName] || []);

                        return {
                          label: table.label,
                          options: columns.map(col => ({
                            label: `${table.label}.${col.label}`,
                            value: `${table.alias}.${col.column_name}`
                          }))
                        };
                      });
                    })()}
                  />
                </Form.Item>


                <Form.Item
                  {...restField}
                  name={[name, 'joinCondition', 'relationField']}
                  label="关联表字段"
                  key={`relation-field-${name}-${fieldsUpdateKey}`} // 🔥 使用 updateKey
                  rules={[{ required: true, message: '请选择关联表字段' }]}
                  style={{ marginBottom: 8 }}
                >
                  <Select
                    placeholder="选择关联表字段"
                    style={{ width: 120 }}
                    options={(() => {
                      const currentValues = form.getFieldValue('withTableDataMaps') || [];
                      const currentTable = currentValues[name];
                      const selectedTableName = currentTable?.tableName;
                      const tableColumns = relationTableColumns[selectedTableName] || [];

                      return tableColumns.map(c => ({
                        label: c.label,
                        value: c.column_name
                      }));
                    })()}
                  />
                </Form.Item>

                <Popconfirm
                  title="确认删除"
                  description="删除此关联表将同时清空相关的条件配置，确认删除？"
                  onConfirm={() => remove(name)}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button
                    type="link"
                    danger
                    icon={<DeleteOutlined />}
                    style={{ marginTop: 22 }}
                  />
                </Popconfirm>
              </div>
            ))}
            <Button
              type="dashed"
              onClick={() => {
                add({
                  alias: generateTableAlias(),
                  tableName: '',
                  tableLabel: '',
                  joinType: 'INNER',
                  joinCondition: {
                    currentField: '',
                    relationField: ''
                  }
                });

                setFieldsUpdateKey(prev => prev + 1);
              }}
              icon={<PlusOutlined />}
              style={{ width: '100%', marginTop: 8 }}
            >
              添加关联表
            </Button>
          </>
        )}
      </Form.List>
    </Card>
  );

  // 渲染条件规则
  const renderConditionRules = () => {
    return (
      <Form.Item
        name="conditionGroups"
        initialValue={{ logic: 'AND', conditions: [] }}
      >
        <MainConditionGroups
          formColumns={formColumns}
          relationTableColumns={relationTableColumns}
          withTableMaps={currentWithTableMaps}
        />
      </Form.Item>
    );
  };

  if (loading && !dataLoaded) {
    return <div style={{ padding: 20, textAlign: 'center' }}>加载中...</div>;
  }

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        withTableDataMaps: [],
        conditionGroups: {
          logic: 'AND',
          conditions: []
        }
      }}
      onValuesChange={handleFormChange}
      style={{ margin: 0 }}
    >
      {renderWithTableMaps()}
      <Divider style={{ margin: '12px 0' }} />
      {renderConditionRules()}
    </Form>
  );
};

// 渲染组件
CustomCondition.Render = ({ value }) => {
  if (!value) return null;

  const { withTableDataMaps = [], conditionGroups = {} } = value;

  // 计算总规则数
  const totalRules = conditionGroups.conditions?.reduce((sum, group) => {
    return sum + (group.rules?.length || 0);
  }, 0) || 0;

  return (
    <div>
      {withTableDataMaps.length > 0 && (
        <div style={{ marginBottom: 8 }}>
          <strong>关联表:</strong>
          {withTableDataMaps.map((table, index) => (
            <Tag key={index} color="blue" style={{ margin: '0 4px' }}>
              {table.tableLabel || table.tableName}
            </Tag>
          ))}
        </div>
      )}
      {conditionGroups.conditions?.length > 0 && (
        <div>
          <strong>条件:</strong>
          <Tag color="green">{conditionGroups.logic}</Tag>
          <span>
            {conditionGroups.conditions.length}个条件组，
            共{totalRules}个规则
          </span>
        </div>
      )}
      {withTableDataMaps.length === 0 && conditionGroups.conditions?.length === 0 && (
        <span style={{ color: '#999' }}>未配置条件</span>
      )}
    </div>
  );
};

export default CustomCondition;
