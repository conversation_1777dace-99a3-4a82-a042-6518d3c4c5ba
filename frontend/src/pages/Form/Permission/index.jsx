import { CloseOutlined, FolderAddFilled, ReloadOutlined, RestOutlined } from '@ant-design/icons';
import {
    nanoid,
    ProTable,
} from '@ant-design/pro-components';

import { Button, Space, Popconfirm, Modal, Drawer, Checkbox, Tag, message } from 'antd';
import PageContainer from '../../../components/CustomLayout/page-container';
import { FetchFormTemplateDelete, GetFormTemplatePermissions, FetchFormTemplatePermissionSetting } from '../../../services/crm-api/form-template';

import { useState, useRef, useEffect } from 'react';
import FormView from '../FormView/index'
import { isEqual, cloneDeep } from 'lodash';
import { SpecifiedUsers ,SpecifiedPosition,ProjectLeader, ProjectRole,FieldUsers,CustomConditionV2 } from './components';


import { useAccess, Access } from 'umi';
const accessMark = ["menu", "SystemSettle-Form-Permission"]

// 假设还有其他组件引入

const FormPermission = () => {
    
    const access = useAccess();
    const tableActionRef = useRef();
    const [showEdit, setShowEdit] = useState("none");
    const [changePermissions, setChangePermissions] = useState([]);
    const [formTemplateId, setFormTemplateId] = useState(0);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalContent, setModalContent] = useState(null);  // 动态渲染Modal内容
    const [selectedData, setSelectedData] = useState([]);
    const currentActionRef = useRef(null);
    const currentRecordRef = useRef(null);
    useEffect(() => {
        console.log("Updated selectedData:", selectedData);
    }, [selectedData]);

    const tableActionId = useRef();
    const plainOptions = [
        {
            label: '本人', value: 'self', default: {
                enabled: false
            }
        },
        {
            label: '本部门', value: 'dept', default: {
                enabled: false
            }
        },
        {
            label: '本部门负责人', value: 'dept_leader', default: {
                enabled: false
            }
        },
        {
            label: '表单字段中关联的人', value: 'field_users', default: {
                enabled: false,
                data: []
            }
        },
        {
            label: '指定人员', value: 'specified_users', default: {
                enabled: false,
                data: []
            }
        },
        {
            label: '指定岗位', value: 'specified_position', default: {
                enabled: false,
                data: []
            }
        },
        {
            label: '项目负责人', value: 'project_leader', default: {
                enabled: false,
                data: null
            }
        },
        {
            label: '项目角色', value: 'project_role', default: {
                enabled: false,
                data: {
                    list: [],
                    columnName: null
                }
            }
        },
        {
            label: '自定义条件', value: 'custom_condition', default: {
                enabled: false,
                data: {
                    withTableDataMaps: [],
                    conditions: {
                        logic: 'AND',
                        rules: []
                    }
                }
            }
        },
    ];

    const defaultPermissions = plainOptions.reduce((acc, option) => {
        acc[option.value] = option.default;
        return acc;
    }, {});

    const getList = async (params) => {
        const templates = await GetFormTemplatePermissions(params);
        if (templates?.code == 0 && templates?.data) {
            const initialData = templates.data.map(item => ({
                ...item,
                permissions: item.permissions ? JSON.parse(item.permissions) : cloneDeep(defaultPermissions)
            }));
            return { ...templates, data: initialData };
        }
        return templates;
    };

    const handleSave = async () => {
        const { code, data, message: msg } = await FetchFormTemplatePermissionSetting({ permissions: changePermissions })
        if (code == 0) {
            message.success("保存成功");
            handleReset()
        }
        else message.error(msg);
    };

    const handleReset = () => {
        setChangePermissions([]);
        initList();
    };

    const initList = () => {
        tableActionRef.current?.reload();
        tableActionId.current = nanoid();
    };

    const handleModalOk = () => {
        if (currentRecordRef.current && currentActionRef.current) {
            handleChangeCheckInternal(true);
        }
        setIsModalVisible(false);
        setSelectedData([]);   // 清空选择数据
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
        setSelectedData([]);   // 清空选择数据
    };

    const renderModalContent = (action, data,record) => {
        switch (action) {
            case 'specified_users':
                return <SpecifiedUsers value={data} onChange={setSelectedData} />;
            case 'specified_position':
                return <SpecifiedPosition value={data} onChange={setSelectedData} />;
            case 'project_leader':
                return <ProjectLeader value={data} onChange={setSelectedData} form_table={record.formTableName} />;
            case 'project_role':
                return <ProjectRole value={data} onChange={setSelectedData} form_table={record.formTableName} />;
            case 'field_users':
                return <FieldUsers value={data} onChange={setSelectedData} form_table={record.formTableName} />;
            case 'custom_condition':
                return <CustomConditionV2 value={data} onChange={setSelectedData} form_table={record.formTableName} />;
            default:
                return null;
        }
    };

    const renderData = (action, data) => {
        switch (action) {
            case 'specified_users':
                return <SpecifiedUsers.Render value={data} />;
            case 'specified_position':
                return <SpecifiedPosition.Render value={data} />;
            case 'project_leader':
                return <ProjectLeader.Render value={data} />;
            case 'project_role':
                return <ProjectRole.Render value={data} />;
            case 'field_users':
                return <FieldUsers.Render value={data} />;
            case 'custom_condition':
                return <CustomConditionV2.Render value={data} />;
            default:
                return null;
        }
    }

    const handleChangeCheckInternal = (checked) => {
        var permissions = cloneDeep(currentRecordRef.current?.permissions);
        var changePermissionsItem = changePermissions.find(item => item.id === currentRecordRef.current.id);
        if (changePermissionsItem) permissions = changePermissionsItem.permissions;

        if (!(currentActionRef.current in permissions)) permissions[currentActionRef.current] = defaultPermissions[currentActionRef.current];
        permissions[currentActionRef.current].enabled = checked;
        if (checked && ['specified_users', 'specified_position', 'project_leader', 'project_role','field_users', 'custom_condition'].includes(currentActionRef.current)) {
            permissions[currentActionRef.current].data = selectedData;
        }

        const changeItem = {
            id: currentRecordRef.current.id,
            permissions: permissions
        };
        setChangePermissions(prev => {
            const newPermissions = [...prev];
            const index = newPermissions.findIndex(item => item.id === currentRecordRef.current.id);
            if (index >= 0) {
                if (isEqual(currentRecordRef.current.permissions, permissions)) {
                    newPermissions.splice(index, 1);
                } else {
                    newPermissions[index] = changeItem;
                }
            } else {
                newPermissions.push(changeItem);
            }
            console.log("Updated changePermissions:", JSON.stringify(newPermissions));
            return newPermissions;
        });

    };

    const handleChangeCheck = (e, record, action) => {
        if (!access.canChange(...accessMark)) return 
        currentRecordRef.current = record;
        currentActionRef.current = action;
        var permissions = cloneDeep(currentRecordRef.current?.permissions);
        var changePermissionsItem = changePermissions.find(item => item.id === currentRecordRef.current.id);
        if (changePermissionsItem) permissions = changePermissionsItem.permissions;
        if (['specified_users', 'specified_position', 'project_leader', 'project_role' , 'field_users', 'custom_condition'].includes(action) && e.target.checked) {
            var renderData = null 
            if ( action in permissions) {
                renderData = cloneDeep(permissions[action].data)
            }
            setModalContent(renderModalContent(action, renderData,record));
            setIsModalVisible(true);
        } else {
            handleChangeCheckInternal(e.target.checked);
        }
    };

    const columns = [
        {
            title: '表单名称',
            dataIndex: 'formTitle',
            valueType: 'text',
            search: true,
            render: (text, record) => {
                var itemPermissions = record.permissions;
                var changePermissionsItem = changePermissions.find(item => item.id === record.id);
                if (changePermissionsItem) itemPermissions = changePermissionsItem.permissions;
                return (
                    <div className='flex justify-center gap-6 flex-col lg:flex-row lg:justify-start lg:items-center'>
                        <div className='flex gap-1 flex-col w-64'>
                            <div className=' text-lg'>{text}</div>
                            <div className=' text-xs text-gray-500'>{record?.formTableName}</div>
                        </div>
                        <div className='flex flex-wrap gap-x-6'>
                            {plainOptions.map((option, index) => (
                                <div className='max-w-60 flex flex-col'>
                                    <Checkbox
                                        checked={itemPermissions[option.value]?.enabled}
                                        onChange={(e) => handleChangeCheck(e, record, option.value)}
                                        key={`checkbox-${record?.id}-${tableActionId.current}-${index}`}
                                    >
                                        {option.label}
                                    </Checkbox>
                                    {renderData(option.value, itemPermissions[option.value]?.data)}
                                </div>

                            ))}
                        </div>
                    </div>
                );
            }
        }
    ];

    return (
        <PageContainer
            title={'表单数据权限设置'}
            subtitle={'决定表单每一行数据能否被用户看到和管理（注意：如果未勾选任何权限，该表单的数据将公开，任何登录人都可以查看和修改）'}
        >
            <ProTable
                scroll={{ x: 400 }}
                actionRef={tableActionRef}
                request={getList}
                pagination={{ defaultPageSize: 15 }}
                columns={columns}
                options={false}
                search={{ labelWidth: '80', filterType: 'query' }}
                toolbar={{
                    actions: (
                        <Access accessible={access.canChange(...accessMark)}>
                    <div className='flex gap-x-2'>
                            <Button onClick={handleReset} icon={<ReloadOutlined />}>恢复</Button>
                            <Popconfirm
                                title="变更提示"
                                description={<>{changePermissions.length}个表单权限发生了变更，确认要提交吗？</>}
                                onConfirm={handleSave}
                                okText="确认提交"
                                cancelText="取消"
                            >
                                <Button type='primary' icon={<FolderAddFilled />}>保存变更</Button>
                            </Popconfirm>
                        </div>
                </Access>
                    ),
                    search: (
                        <Access accessible={access.canChange(...accessMark)}><div className='flex items-center'>
                            {changePermissions.length > 0 ? (
                                <>
                                    <span className='text-orange-500'>{changePermissions.length}</span>个表单权限发生了变更
                                </>
                            ) : "暂无变更"}
                        </div></Access>
                    )
                }}
            />

            <Modal
                title={currentActionRef.current === 'custom_condition' ? "自定义条件设置" : "选择设置"}
                open={isModalVisible}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                width={currentActionRef.current === 'custom_condition' ? 800 : 520}
                style={currentActionRef.current === 'custom_condition' ? { top: 20 } : {}}
            >
                {modalContent}
            </Modal>
        </PageContainer>
    );
};

export default FormPermission;
