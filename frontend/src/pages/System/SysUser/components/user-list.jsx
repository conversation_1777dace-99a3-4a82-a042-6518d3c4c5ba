import { FolderAddFilled, EllipsisOutlined } from "@ant-design/icons";
import { ProTable } from "@ant-design/pro-components";
import { But<PERSON>, Modal, Popconfirm, message, Dropdown } from "antd";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FetchUserResetPwd } from "../../../../services/crm-api/sys-user"
import React, { useEffect } from "react";
import UserEdit from "./user-edit";
import {UserStatus} from "../enum"
import { useAccess, Access } from 'umi';
const accessMark = ["menu", "SystemSettle-Dept-User"]
const UserList = ({ deptId }) => {
    const access = useAccess();
    const tableActionRef = React.useRef();
    const [editUser, setEditUser] = React.useState(null);
    const [showEdit, setShowEdit] = React.useState(false);
    useEffect(() => {
        if (tableActionRef.current) {
            tableActionRef.current.reload();
        }
    }, [deptId]);

    const columns = [
        // {
        //     title: '员工编号',
        //     dataIndex: 'code',
        //     search: true,
        // },
        
        {
            title: '员工姓名',
            dataIndex: 'username',
            search: true,
            fixed: "left",
            width:100
        },

        {
            title: '所在部门',
            dataIndex: 'deptName',
            search: false,
            render: (text, record, index, action) => {
                let names = [];
                if (record.depts && Array.isArray(record.depts)) {
                    record.depts.forEach(item => {
                        names.push(item?.dept?.deptName)
                    })
                }
                return names.join(',')
            }
        },
       
        {
            title: '系统账号',
            dataIndex: 'systemAccount',
            search: true,
            width:100
        },
        {
            title: '职位',
            dataIndex: 'postName',
            search: false,
            render: (text, record, index, action) => {
                let names = [];
                if (record.posts && Array.isArray(record.posts)) {
                    record.posts.forEach(item => {
                        names.push(item?.post?.postName)
                    })
                }
                return names.join(',')
            }
        },
        {
            title: '联系电话',
            dataIndex: 'contactPhone',
            search: true,
        },
        {
            title: '状态',
            dataIndex: 'status',
            valueEnum: UserStatus,
            search: true,
        },
        {
            title: '邮箱',
            dataIndex: 'email',
            search: true,
        },
       
    ]

    
    if (access.canChange(...accessMark)){
        columns.unshift( {
            title: '操作',
            valueType: 'option',
            dataIndex: 'option',
            fixed: "left",
            width: 50,
            render: (text, record, index, action) => {
                const items = [
                    {
                        key: 'edit',
                        label: (
                            <Button type="link" size="small" onClick={() => handleEdit(record)}>
                                编辑
                            </Button>
                        ),
                    },
                    {
                        key: 'delete',
                        label: (
                            <Popconfirm
                                title="删除后不能恢复，确认删除?"
                                onConfirm={() => handleUserAction(record, FetchUserDelete, "删除")}
                                okText="确认删除"
                                cancelText="取消"
                            >
                                <Button type="link" danger size="small">
                                    删除
                                </Button>
                            </Popconfirm>
                        ),
                    },
                    {
                        key: 'resetPwd',
                        label: (
                            <Popconfirm
                                title="确认重置用户的密码?"
                                onConfirm={() => handleUserAction(record, FetchUserResetPwd, "重置密码")}
                                okText="确认重置"
                                cancelText="取消"
                            >
                                <Button type="link" size="small">
                                    重置密码
                                </Button>
                            </Popconfirm>
                        ),
                    },
                ];
                
                return (
                    <Dropdown menu={{ items }} placement="bottomRight">
                        <Button type="text" icon={<EllipsisOutlined />} />
                    </Dropdown>
                );
            }
        })
    }
    const handleNew = () => {
        console.log('新建')
        setEditUser(null)
        setShowEdit(true)
    }

    const handleEdit = (record) => {
        console.log('编辑', record)
        setEditUser(record)
        setShowEdit(true)
    }

    const handleUserAction = async (record, fetch, actionName = "操作") => {
        if (fetch && typeof fetch === "function") {
            const res = await fetch(record.id)
            if (res.code > 0) {
                message.error(res.message)
                return
            }
            await tableActionRef?.current?.reload()
            message.success(`${actionName}成功`)
        }
    }

    return (
        <>
            <ProTable
              scroll={{
                x: 1336,
            }}
                actionRef={tableActionRef}
                request={(params) => {
                    params = { ...params, deptId }
                    return FetchUserList(params)
                }}
                onRow={
                    (record) => ({
                        onDoubleClick: () => {
                            if (access.canChange(...accessMark))
                                handleEdit(record)
                        }
                    })
                }
                toolbar={{
                    search: <Access accessible={access.canNew(...accessMark)}>
                   <Button type='primary' onClick={handleNew} icon={<FolderAddFilled />}>新建</Button>
                </Access>
                }}
                columns={columns} />
            <Modal
                destroyOnClose={true}
                width={960}
                title={editUser ?
                    `编辑${editUser.username}` : "添加"}
                open={showEdit}
                onCancel={() => setShowEdit(false)} footer={null}
                zIndex={999}
                >
                
                <UserEdit
                    id={editUser?.id}
                    onCancel={() => { setShowEdit(false) }}
                    
                    onOK={async () => {
                        await tableActionRef?.current?.reload()
                        message.success("保存成功")
                        setShowEdit(false)
                    }}
                />
            </Modal>
        </>
    )
}
export default UserList;