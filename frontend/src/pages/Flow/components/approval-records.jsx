import { Button, message, Spin, Modal, Form, Tabs } from "antd";
import { Field } from "lu-lowcode-package-form"
import { UserAvatar } from "@/components";
import { IconFlowPass, IconFlowCancel, IconFlowReject } from "@/components";
import { ApartmentOutlined, WarningOutlined, EditOutlined } from "@ant-design/icons";
import { ImageList, FileList } from "@/components";
import { useEffect, useState, forwardRef, useRef, useImperativeHandle } from "react";
import { useCreation } from 'ahooks';
import { FetchGet, FetchApprove, FetchAuditInstances, FetchInstanceInfo } from "@/services/crm-api/flow-instance";
import { FlowStatus, HistoryStatus, NodeCondition, NodeActionType } from "../enum"

import _ from "lodash"
import { useModel } from '@umijs/max';
import FormViewEdit from "../../Form/FormView/edit"

import { FetchSaveFormData } from "../../../services/crm-api/form-view";

import HandleApprove from "./handle-approve"
import FullFlow from "./full-flow";
import { isDragNodeDataObject } from "@alilc/lowcode-utils";
import IconNode from "./icon-node"
import { createPromiseWrapper } from '../../../utils';


const ConditionText = ({ condition }) => {
    const contionText = NodeCondition.find(item => item.value == condition)?.labelShort || ""
    return contionText ? `(${contionText})` : ""
}

const FlowStats = ({ status }) => {
    return (FlowStatus.find(item => item.value == status)?.icon) || ""
}
const HistoryStats = ({ status }) => {
    const statusText = (HistoryStatus.find(item => item.value == status)?.label) || "未知状态"
    return statusText
}
const IconAuditorState = ({ state }) => {
    const NodeAction = NodeActionType.find(item => item.value === state)
    if (NodeAction && NodeAction.icon) {
        const Icon = NodeAction.icon
        return <div className="w-4 h-4" style={{ color: NodeAction.color }}><Icon /></div>
    }
    return ""
}
const SpanAuditorText = ({ auditor }) => {
    const NodeAction = NodeActionType.find(item => item.value === auditor.approvalStatus)
    if (NodeAction) {
        let label = NodeAction.label
        if (auditor?.isAutoPass == 1)
            label = "通过"
        else if (NodeAction?.value == "agree" && auditor?.nodeType != "AudioNode") {
            label = "已办理"
        }

        return <span className=" text-right flex-1">{label} {auditor?.completionTime ? auditor?.completionTime : ""}</span>
    }
    return ""
}

const ApprovalRecordTabItem = ({ instanceId, onChangeDenyEdit, getFormValues, formTemplateId, formDataId }) => {
    const lowcodePreViewRef = useRef();
    const fullFlowRef = useRef();
    const { initialState } = useModel('@@initialState');
    const { currentUser } = initialState || {};
    const [flowInstance, setFlowInstance] = useState();
    const [error, setError] = useState();
    const [loadings, setLoadings] = useState(false);
    const [approvalStatus, setApprovalStatus] = useState("")
    const [historyAuditorId, setHistoryAuditorId] = useState("")
    const [stepId, setStepId] = useState("")
    const HandleApproveRef = useRef()
    const [beforeHandleApprove, setBeforeHandleApprove] = useState(null)
    const [denyEdit, setDenyEdit] = useState(true)
    const [nodeShape, setNodeShape] = useState("")
    const [previewHistory, setPreviewHistory] = useState([])
    const [pendingAuditors, setPendingAuditors] = useState([])

    useEffect(() => {
        initData()
    }, [instanceId])

    useEffect(() => {
        typeof onChangeDenyEdit == "function" && onChangeDenyEdit(denyEdit)
    }, [denyEdit])


    const initData = async () => {
        try {
            setLoadings(true)
            const { data, code } = await FetchInstanceInfo(instanceId);
            if (code != 0) {
                setError("获取审批记录失败");
                return
            }
            setError("");
            data.finishdSteps = JSON.parse(data.finishdSteps || "[]")
            data.flowSteps = JSON.parse(data.flowSteps || "{}")
            data.currentSteps = JSON.parse(data.currentSteps || "[]")
            data.flowTemplateSnap = data.flowTemplateSnap ? JSON.parse(data.flowTemplateSnap) : null
            if (data.flowTemplateSnap) {
                const preNodes = data.flowSteps?.steps || []
                const preEdges = data.flowSteps?.edges || []
                if (Array.isArray(data.flowTemplateSnap?.nodes) && Array.isArray(preNodes)) {
                    data.flowTemplateSnap.nodes = data.flowTemplateSnap.nodes.map(node => {
                        const preNode = preNodes.find(item => item.id == node.id)
                        if (!preNode)
                            node.data.ignore = true
                        if (Array.isArray(data.finishdSteps) && data.finishdSteps.findIndex(item => item == node.id) >= 0)
                            node.data.isFinishd = true
                        if (Array.isArray(data.currentSteps) && data.currentSteps.findIndex(item => item.id == node.id) >= 0)
                            node.data.isFinishd = true
                        return node
                    })
                }
                if (Array.isArray(data.flowTemplateSnap?.edges) && Array.isArray(preEdges)) {
                    data.flowTemplateSnap.edges = data.flowTemplateSnap.edges.map(edge => {
                        const preEdge = preEdges.find(item => item.source == edge?.source?.cell && item.target == edge?.target?.cell)
                        if (!preEdge) {
                            edge.shape = "ignore_edge"
                            if (edge?.label) {
                                edge.label.attrs.text.opacity = 0.3
                            }
                        }
                        return edge
                    })
                }
                if (data.flowInstanceType == 1)
                    setDenyEdit(!(
                        data?.currentSteps &&
                        data?.currentSteps.findIndex(item => {
                            if (item.shape == "StartNode") {
                                return data?.historys.findIndex(item => item.nodeType == "StartNode" && item.status == 2) >= 0
                            }
                            return false
                        }) >= 0 &&
                        data?.createdBy == currentUser?.id))
                setFlowInstance(data)
                
                // 查找当前用户待审批的所有节点
                const pendingNodes = [];
                if (data?.historys && Array.isArray(data?.historys)) {
                    data.historys.forEach(history => {
                        if (history?.nodeType === "AudioNode" && history?.auditors && Array.isArray(history?.auditors)) {
                            history.auditors.forEach(auditor => {
                                if (auditor?.approvalStatus === "pending" && auditor?.userId === currentUser?.id) {
                                    pendingNodes.push({
                                        auditorId: auditor.id,
                                        stepId: history.stepId
                                    });
                                }
                            });
                        }
                    });
                }
                setPendingAuditors(pendingNodes);
            }
        } catch (error) {
            console.error("error", error)
        }
        finally {
            setLoadings(false)
        }
    }
    // 获得允许退回的节点id列表
    const getAllowReturnNodes = (stepId, depth = 0) => {
        var allowReturnNodes = []
        const { flowSteps } = flowInstance
        if (depth <= 100 && flowSteps && flowSteps?.steps && flowSteps?.edges) {
            const { edges, steps } = flowSteps
            if (edges.length > 0 && steps.length > 0) {
                // 获得上一个节点
                const preStepEdge = edges.find(item => {
                    if (item.target == stepId) {
                        // // 获得分支个数
                        // const branchCount = edges.filter(edge => edge.source == item.source).length
                        // return branchCount == 1

                        return true
                    }
                    return false
                })
                if (preStepEdge) {
                    const preStep = steps.find(item => item.id == preStepEdge.source)
                    if (preStep) {
                        allowReturnNodes = [...allowReturnNodes, { label: preStep.name, value: preStep.id }]
                        const upperStep = getAllowReturnNodes(preStep.id, depth + 1)
                        allowReturnNodes = [...allowReturnNodes, ...upperStep]
                    }
                }
            }
        }
        // lodash 去重
        allowReturnNodes = _.uniqBy(allowReturnNodes, "value")
        return allowReturnNodes
    }
    const handleApprovalOk = async (approveData) => {
        message.success("提交成功")
        // 如果是同意操作且存在多个待审批节点，自动提交其他节点的同意操作
        if (approvalStatus === "agree" && pendingAuditors.length > 1) {
            const currentAuditor = pendingAuditors.find(item => item.auditorId === historyAuditorId);
            if (currentAuditor) {
                // 过滤出其他待审批节点
                const otherAuditors = pendingAuditors.filter(item => item.auditorId !== historyAuditorId);
                
                // 依次提交其他节点的同意操作
                for (const auditor of otherAuditors) {
                    try {
                        await FetchApprove({
                            historyAuditorId: auditor.auditorId, 
                            approvalStatus: "agree",
                            instanceId: flowInstance?.id,
                            approvalComment:  "",
                            // approvalComment: approveData?.approvalComment || "",
                            // pics: approveData?.pics || [],
                            // attachment: approveData?.attachment || []
                        });
                    } catch (error) {
                        console.error("自动审批其他节点失败", error);
                    }
                }
            }
        }
        
        setTimeout(() => {
            // 审核完成后刷新数据
            initData()
        }, 200);
    }
    const handleReturn = (id, stepId) => {
        setNodeShape("")
        showHandleApprove("return", id, stepId)
    }
    const handleReject = (id, stepId) => {
        setNodeShape("")
        showHandleApprove("reject", id, stepId)
    }
    const handleAgree = (id, stepId) => {
        setNodeShape("")
        showHandleApprove("agree", id, stepId)
    }
    const formSubmit = async () => {
        try {
            const values = await getFormValues()
            const result = await FetchSaveFormData(formTemplateId, formDataId, values);
            if (result.code > 0) return Promise.reject(new Error(result.message))
            else return Promise.resolve(result.data)
        } catch (error) {
            if (error?.errorFields?.[0].errors?.[0]) return Promise.reject(new Error(error?.errorFields?.[0].errors?.[0]))
            else return Promise.reject(error)
        }
    }
    // 重新提交
    const handleSubmit = () => {
        const startNode = flowInstance?.historys?.find(item => item.nodeType == "StartNode" && item.status == 2)
        console.log("startNode", startNode)
        if (startNode) {
            var stepId = startNode?.stepId
            var id = startNode?.auditors?.[0]?.id
            setNodeShape(startNode.nodeType)
            showHandleApprove("agree", id, stepId)
        }
    }
    const showHandleApprove = (approvalStatus, id, stepId) => {
        console.log("showHandleApprove", approvalStatus, id, stepId)
        setApprovalStatus(approvalStatus)
        setStepId(stepId)
        setHistoryAuditorId(id)
        HandleApproveRef.current?.showModal()
    }
    // 撤销
    const handleCancel = () => {
        setNodeShape("")
        showHandleApprove("cancel")
    }

    useEffect(() => {
        if (flowInstance && flowInstance?.flowSteps && flowInstance?.flowSteps?.steps.length > 0 && flowInstance?.flowSteps?.edges.length > 0) {
            const { steps, edges } = flowInstance.flowSteps
            const startNode = steps.find(item => item.shape == "StartNode")
            const previewHistory_ = []
            if (startNode) {
                const nodeMap = {}
                const nodePool = [startNode]
                nodeMap[startNode.id] = startNode
                while (nodePool.length > 0) {
                    const currentStep = nodePool.shift()
                    previewHistory_.push(currentStep)
                    if (currentStep.shape == "EndNode") break;
                    const nextEdge = edges.find(item => item.source == currentStep.id)
                    if (nextEdge && !nodeMap[nextEdge.target]) {
                        const nextStep = steps.find(item => item.id == nextEdge.target)
                        if (nextStep) {
                            nodePool.push(nextStep)
                            nodeMap[nextStep.id] = nextStep
                        }
                    }
                }
            }
            setPreviewHistory(previewHistory_)
        }
    }, [flowInstance])
    return <>
        {flowInstance && flowInstance?.id && <div className="pb-3 flex flex-col relative ">
            <div className="flex items-center h-8 pb-2 ">
                {flowInstance?.flowInstanceType == 2 && <div className="flex-1 text-orange-500" ><WarningOutlined />&nbsp;&nbsp;此流程为单据作废审批</div>}
                {flowInstance?.flowInstanceType == 1 && <div className="flex-1" ><EditOutlined />&nbsp;&nbsp;此流程为单据创建审批</div>}
                <Button size="small" onClick={() => { fullFlowRef.current.showModal() }}>跟踪完整审批流程</Button>
            </div>
            <FlowStats status={flowInstance?.instanceState} />
            <div className="flex flex-col gap-y-4 ">
                {flowInstance?.historys && Array.isArray(flowInstance?.historys) && flowInstance?.historys.map((history, history_index) => {
                    return <div className="flex gap-x-2" key={`history_${history?.id}`}>
                        <div className=" rounded-full w-6 h-6 bg-blue-500 text-white flex items-center justify-center">
                            <IconNode type={history?.nodeType} />
                        </div>
                        <div className="flex-1 flex flex-col gap-y-2">
                            <div className=" ">{history?.stepName} {(history_index > 0 && history?.nodeType != "EndNode") && <> - <HistoryStats status={history?.status} /></>}</div>
                            {history?.auditors && Array.isArray(history?.auditors) && <>
                                {history?.auditors.length > 1 && <div className=" text-sm text-gray-500">{history?.auditors.length}人<ConditionText condition={history?.condition} /></div>}
                                {history?.auditors.map((auditor, auditor_index) => {
                                    return <div className=" flex items-start gap-x-2 ">
                                        <img src={auditor?.auditorUser?.avatar || PUBLIC_PATH + "/user-default.png"} className="w-8 h-8 rounded-full " alt="User Avatar" />
                                        <div className="flex flex-col gap-y-2  flex-1 mt-1">
                                            <div className="flex  items-center gap-x-1">
                                                <span className="">{auditor?.auditorUser?.username || "--"}</span>
                                                <IconAuditorState state={auditor?.approvalStatus} />
                                                <SpanAuditorText auditor={auditor} />
                                            </div>
                                            {history?.nodeType == "AudioNode" && auditor?.approvalStatus == "pending" && auditor?.userId == currentUser?.id && <div className="flex flex-row-reverse gap-x-2">
                                                <Button type="primary" onClick={() => { handleAgree(auditor?.id, history?.stepId) }}>同意</Button>
                                                <Button onClick={() => { handleReject(auditor?.id, history?.stepId) }}>拒绝</Button>
                                                {flowInstance?.flowInstanceType == 1 && <Button onClick={() => { handleReturn(auditor?.id, history?.stepId) }}>退回</Button>}
                                            </div>}
                                            {auditor?.approvalComment && <div className=" text-gray-600">{auditor?.approvalComment}</div>}
                                            {auditor?.pics && <ImageList images={auditor?.pics} />}
                                            {auditor?.attachment && <FileList files={auditor?.attachment} />}

                                        </div>
                                    </div>
                                })}
                            </>}
                        </div>
                    </div>
                })}
                {/* 预览剩余的流程节点 */}
                {/* {previewHistory && Array.isArray(previewHistory) && previewHistory.map((step, step_index) => {
                if (flowInstance?.historys && Array.isArray(flowInstance?.historys) && flowInstance?.historys.findIndex(item => item.stepId == step.id) >= 0) {
                    return null
                }
                return <div className="flex gap-x-2" key={`preview_${step?.id}`}>
                      <div className=" rounded-full w-6 h-6 bg-blue-500 text-white flex items-center justify-center">
                        <IconNode type={step?.shape} />
                    </div>
                    <div className="flex-1 flex flex-col gap-y-2 text-gray-400">
                        <div className=" ">{step?.name} {(step_index > 0 && step?.shape != "EndNode") && <> - <HistoryStats status={1} /></>}</div>
                        {step?.approver && Array.isArray(step?.approver) && <>
                            {step?.approver.length > 1 && <div className=" text-sm text-gray-500">{step?.approver.length}人<ConditionText condition={step?.condition} /></div>}
                            {step?.approver.map((auditor, auditor_index) => {
                                return <div className=" flex items-start gap-x-2 ">
                                    <img src={auditor?.avatar || PUBLIC_PATH + "/user-default.png"} className="w-8 h-8 rounded-full " alt="User Avatar" />
                                    <div className="flex flex-col gap-y-2  flex-1 mt-1">
                                        <div className="flex  items-center gap-x-1">
                                            <span className="">{auditor?.username || "--"}</span>
                                            <IconAuditorState state="pending" />
                                        </div>
                                    </div>
                                </div>
                            })}
                        </>}
                    </div>
                </div>
            })} */}

                {/* 预览抄送节点 */}
                {flowInstance?.flowTemplateSnap?.previewCcNode && previewHistory && Array.isArray(previewHistory) && previewHistory.map((step, step_index) => {
                    if (step.shape != "CcNode") return null
                    if (flowInstance?.historys && Array.isArray(flowInstance?.historys) && flowInstance?.historys.findIndex(item => item.stepId == step.id) >= 0) {
                        return null
                    }
                    return <div className="flex gap-x-2" key={`preview_${step?.id}`}>
                        <div className=" rounded-full w-6 h-6 bg-blue-500 text-white flex items-center justify-center">
                            <IconNode type={step?.shape} />
                        </div>
                        <div className="flex-1 flex flex-col gap-y-2 text-gray-400">
                            <div className=" ">{step?.name} {(step_index > 0 && step?.shape != "EndNode") && <> - <HistoryStats status={1} /></>}</div>
                            {step?.approver && Array.isArray(step?.approver) && <>
                                {step?.approver.length > 1 && <div className=" text-sm text-gray-500">{step?.approver.length}人<ConditionText condition={step?.condition} /></div>}
                                {step?.approver.map((auditor, auditor_index) => {
                                    return <div className=" flex items-start gap-x-2 ">
                                        <img src={auditor?.avatar || PUBLIC_PATH + "/user-default.png"} className="w-8 h-8 rounded-full " alt="User Avatar" />
                                        <div className="flex flex-col gap-y-2  flex-1 mt-1">
                                            <div className="flex  items-center gap-x-1">
                                                <span className="">{auditor?.username || "--"}</span>
                                                <IconAuditorState state="pending" />
                                            </div>
                                        </div>
                                    </div>
                                })}
                            </>}
                        </div>
                    </div>
                })}

            </div>

        </div>}

        <div className="flex mt-4">
            {flowInstance && flowInstance?.createdBy == currentUser?.id && <div className="flex flex-row-reverse gap-x-2" >
                {!denyEdit && <Button type="primary" onClick={() => { handleSubmit() }}>提交修改</Button>}
                {flowInstance?.flowTemplateSnap?.allowRevoke && flowInstance?.instanceState == 2 && <Button onClick={() => { handleCancel() }}>撤销审批</Button>}
            </div>}
        </div>
        <HandleApprove beforeSubmit={formSubmit}
            shape={nodeShape}
            onOk={handleApprovalOk}
            onError={(error) => { 
                message.error(error.message) 
            }}
            approvalStatus={approvalStatus}
            stepId={stepId}
            getAllowReturnNodes={getAllowReturnNodes}
            historyAuditorId={historyAuditorId}
            instanceId={flowInstance?.id}
            ref={HandleApproveRef}
        />
        <FullFlow flowSchema={flowInstance?.flowTemplateSnap} ref={fullFlowRef} />
    </>
}

const ApprovalRecords = ({ formTemplateId, formDataId, flowInstanceId, closeModal }) => {
    const promiseRef = useCreation(() => {
        return createPromiseWrapper();
    }, []);
    const lowcodePreViewRef = useRef();
    const { initialState } = useModel('@@initialState');
    const [error, setError] = useState();
    const [loadings, setLoadings] = useState(false);
    const [denyEdit, setDenyEdit] = useState(true)
    const [instanceList, setInstanceList] = useState([])
    const [activeInstanceId, setActiveInstanceId] = useState("")
    // useEffect(()=>{
    //     console.log("ApprovalRecords denyEdit ///////////////////",denyEdit)
    // },[denyEdit])
    const onFormInited = async () => {
        console.log("onFormInited")
        promiseRef?.resolve(true);
    }
    const getFormValues = async () => {
        // return await lowcodePreViewRef.current.validateFormData()
        let formInstace = await lowcodePreViewRef.current.getFormInstance()
        // const values = formInstace?.getFieldsValue()
        return await formInstace?.validateFields({
            validateOnly: false,
        })
    }
    const initAuditInstances = async () => {
        setLoadings(true)
        try {
            const { data, code } = await FetchAuditInstances({ form_template_id: formTemplateId, form_id: formDataId });
            if (code != 0) {
                setError("获取审批记录失败");
                return
            }
            let instanceList_ = data?.list?.map(item => {
                const flowStatusLabel = FlowStatus.find(flowState => flowState.value == item.instanceState)?.label || ""
                let label = `单据创建${flowStatusLabel ? ` (${flowStatusLabel})` : ""}`
                if (item.flowInstanceType == 2)
                    label = <span className="text-orange-500">单据作废 {flowStatusLabel ? `(${flowStatusLabel})` : ""}</span>
                return { label: label, key: item.id }
            })
            console.log("flowInstanceId", flowInstanceId)
            if (flowInstanceId) setActiveInstanceId(flowInstanceId)
            else if (instanceList_.length > 0)
                setActiveInstanceId(instanceList_[instanceList_.length - 1].key)
            setInstanceList(instanceList_)
        } catch (error) {

        }
        finally {
            setLoadings(false)
        }
    }
    const handleInstanceChange = (key) => {
        setActiveInstanceId(key)
    }
    const initData = async () => {
        await initAuditInstances();
    }


    useEffect(() => {
        initData()
    }, [formTemplateId, formDataId])



    return <>
        <FormViewEdit key={`${formTemplateId}-${formDataId}-${denyEdit}`} denyEdit={denyEdit} showTitle={true} formTemplateId={formTemplateId} formDataId={formDataId} ref={lowcodePreViewRef} handleFlowChange={initData} onFormInited={onFormInited} />
        {loadings && <div className="h-10 flex items-center justify-center"><Spin></Spin></div>}
        {error && <div className="h-10 flex items-center text-red-500 text-center">{error}</div>}
        {instanceList.length > 0 && <div className="border-t pb-3 pt-1 mt-2 flex flex-col relative ">
            <div className="flex items-center h-10">
                <div className="flex-1 text-base flex items-center gap-x-2"><ApartmentOutlined className="text-lg" />审批流程</div>
            </div>
            <Tabs activeKey={activeInstanceId} items={instanceList} onChange={handleInstanceChange} type="card" />
            <ApprovalRecordTabItem instanceId={activeInstanceId} onChangeDenyEdit={async (value) => {
                if (value == denyEdit)  return 
                // await promiseRef.promise;
                setDenyEdit(value)
            }} getFormValues={getFormValues} formTemplateId={formTemplateId} formDataId={formDataId} />
        </div>}


    </>
}

export default ApprovalRecords;