import { useCallback, useEffect, useRef } from 'react';

const useAdvancedClick = ({
  onClick,
  onDoubleClick,
  onLongPress,
  longPressDelay = 500,
  doubleClickDelay = 300
}) => {
  const clickCountRef = useRef(0);
  const longPressTimerRef = useRef(null);
  const doubleClickTimerRef = useRef(null);
  const isLongPressRef = useRef(false);

  const clearTimers = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
    if (doubleClickTimerRef.current) {
      clearTimeout(doubleClickTimerRef.current);
      doubleClickTimerRef.current = null;
    }
  }, []);

  const handleMouseDown = useCallback(() => {
    isLongPressRef.current = false;
    longPressTimerRef.current = setTimeout(() => {
      isLongPressRef.current = true;
      onLongPress && onLongPress();
    }, longPressDelay);
  }, [onLongPress, longPressDelay]);

  const handleMouseUp = useCallback(() => {
    clearTimeout(longPressTimerRef.current);
    longPressTimerRef.current = null;
  }, []);

  const handleClick = useCallback(() => {
    if (isLongPressRef.current) {
      // 如果是长按，不处理点击事件
      return;
    }

    clickCountRef.current += 1;

    if (clickCountRef.current === 1) {
      doubleClickTimerRef.current = setTimeout(() => {
        if (clickCountRef.current === 1) {
          onClick && onClick();
        }
        clickCountRef.current = 0;
      }, doubleClickDelay);
    } else if (clickCountRef.current === 2) {
      clearTimeout(doubleClickTimerRef.current);
      onDoubleClick && onDoubleClick();
      clickCountRef.current = 0;
    }
  }, [onClick, onDoubleClick, doubleClickDelay]);

  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  return {
    onMouseDown: handleMouseDown,
    onMouseUp: handleMouseUp,
    onMouseLeave: handleMouseUp,
    onTouchStart: handleMouseDown,
    onTouchEnd: handleMouseUp,
    onClick: handleClick,
  };
};

export default useAdvancedClick;