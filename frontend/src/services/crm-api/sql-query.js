import { request } from '@umijs/max';

// 执行SQL查询
export const FetchExecuteSqlQuery = async (queryData) => {
    return request('/api/sql-query/execute', {
        method: 'POST',
        data: queryData
    });
};

// 测试SQL查询
export const FetchTestSqlQuery = async (queryData) => {
    return request('/api/sql-query/test', {
        method: 'POST',
        data: queryData
    });
};

// 验证SQL语法
export const FetchValidateSql = async (sql) => {
    return request('/api/sql-query/validate', {
        method: 'POST',
        data: { sql }
    });
};

// 获取数据库表结构（可选，用于智能提示）
export const FetchDatabaseSchema = async () => {
    return request('/api/sql-query/schema', {
        method: 'GET'
    });
}; 