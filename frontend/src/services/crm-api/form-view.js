import { request } from '@umijs/max'
import { responseHand<PERSON> } from './base'

export async function FetchGetFormDatas(
    params,
    options,
) {
    const { current, pageSize, formId, ruleParams,enabledLinkedData, linkedData, sortByUsedCount, showInvalid, ...otherParams } = params;
    params = {
        PageNum: current,
        PageSize: pageSize,
        formId: formId,
        ruleFilter:ruleParams,
        enabledLinkedData:enabledLinkedData,
        linkedData:linkedData,
        sortByUsedCount:sortByUsedCount,
        showInvalid: showInvalid,
        filter: {
            ...otherParams,
        }
    }

    return request('/api/form_data/list', {
        method: 'GET',
        params: {
            ...params,
        },
        ...(options || {}),
    }).then(response => responseHandler(response));
}
export async function FetchGetFormDataSum(
    params,
    options,
) {
    const {current, pageSize, fieldName, formId, ruleParams,enabledLinkedData, linkedData, sortByUsedCount, showInvalid, ...otherParams } = params;
    params = {
        formId: formId,
        ruleFilter:ruleParams,
        enabledLinkedData:enabledLinkedData,
        linkedData:linkedData,
        sortByUsedCount:sortByUsedCount,
        showInvalid: showInvalid,
        fieldName: fieldName,
        filter: {
            ...otherParams,
        }
    }

    return request('/api/form_data/list_sum', {
        method: 'GET',
        params: {
            ...params,
        },
        ...(options || {}),
    })
}
export async function FetchGetCustomFormDatas(params,  options) {
    return request('/api/form_data/list_custom', {
        method: 'GET',
        params: {
            ...params,
        },
        ...(options || {}),
    })
}

export async function FetchGetExpandInfo(params,  options) {
    return request('/api/form_data/get_expand_info', {
        method: 'GET',
        params: {
            ...params,
        },
        ...(options || {}),
    })
}
export async function FetchSaveFormData(formId, formDataId, formData,extraData,expandDataId, options) {
    
    let postData = {
        formId,
        id: formDataId,
        data: formData,
        extraData: extraData,
        expandDataId: expandDataId,
    }

    let result = request('/api/form_data/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: postData,
        ...(options || {}),
    });

    return result;
}

export async function FetchInvalidate(formId, formDataId, extraData, options) {
    let postData = {
        formId,
        id: formDataId,
        extraData: extraData,
    }

    let result = request('/api/form_data/invalidate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: postData,
        ...(options || {}),
    });

    return result;
}




export async function FetchFormDataDetail(formTemplateId, formDataId, options) {
    return request('/api/form_data/info', {
        method: 'GET',
        params: {
            formId: formTemplateId,
            id: formDataId
        },
        ...(options || {}),
    });
}


export async function FetchFormDataDelete(formTemplateId, formDataId, options) {
    return request('/api/form_data/delete', {
        method: 'POST',
        data: {
            formId: formTemplateId,
            id: formDataId,
        },
        ...(options || {}),
    });
}

// 更新单个字段值
export async function FetchUpdateSingleField(formTemplateId, formDataId, fieldName, fieldValue, options) {
    return request('/api/form_data/update_field', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: {
            formId: formTemplateId,
            id: formDataId,
            fieldName: fieldName,
            fieldValue: fieldValue,
        },
        ...(options || {}),
    }).then(response => responseHandler(response));
}
