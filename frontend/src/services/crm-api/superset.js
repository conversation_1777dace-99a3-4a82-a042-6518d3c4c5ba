import { request } from '@umijs/max'
import { responseHand<PERSON> } from './base';

export async function GetDashBoradList(
    params,
    options,
) {
    params = params || {};
    params.PageNum = params?.current || 0;
    return request('/api/superset/dashboard/list', {
        method: 'GET',
        params: {
            ...params,
        },
        ...(options || {}),
    }).then(response => responseHandler(response) );
}
export async function GetAccessToken(
    options,
) {
    return request('/api/superset/access_token', {
        method: 'GET',
        params: {},
        ...(options || {}),
    })
}
export async function GetGuestToken (dashboardId,allowedDomains, options) {
    return request('/api/superset/dashboard/guest_token', {
        method: 'GET',
        params: {
            dashboardId,
            allowedDomains,
        },
        ...(options || {}),
    })
}

