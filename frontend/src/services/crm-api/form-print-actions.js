import { request } from '@umijs/max'
import { responseHandler } from './base';

/**
 * 获取表单打印的HTML内容
 * @param {number} formPrintTemplateId 打印模板ID
 * @param {number} formId 表单数据ID
 */
export async function FetchPrintHtmlService(formPrintTemplateId, formId, options) {
    return request('/api/print/generate-html', {
        method: 'POST',
        data: {
            formPrintTemplateId,
            formId,
        },
        ...(options || {}),
    });
}

/**
 * 使用修改后的HTML内容生成PDF
 * @param {string} htmlContent 修改后的HTML内容
 * @param {number} formId 表单数据ID
 * @param {number} formPrintTemplateId 打印模板ID
 */
export async function GeneratePdfFromModifiedHtmlService(htmlContent, formId, formPrintTemplateId, options) {
    return request('/api/print/generate-pdf-from-html', {
        method: 'POST',
        data: {
            htmlContent,
            formId,
            formPrintTemplateId,
        },
        ...(options || {}),
    });
} 