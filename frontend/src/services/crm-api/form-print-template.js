import { request } from '@umijs/max'
import { response<PERSON><PERSON><PERSON> } from './base';

export async function FetchList(params, options) {
    return request('/api/print_template/list', {
        method: 'GET',
        params: {
            ...params,
        },
        ...(options || {}),
    }).then(response => response<PERSON><PERSON><PERSON>(response));
}
export async function FetchDetail(id, options) {
    return request('/api/print_template/detail', {
        method: 'GET',
        params: {
            id,
        },
        ...(options || {}),
    });
}


export async function FetchSave(body, options) {

    let result = request('/api/print_template/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: body,
        ...(options || {}),
    });

    return result;
}

export async function FetchDelete(id, options) {
    return request('/api/print_template/delete', {
        method: 'POST',
        data: {
            id,
        },
        ...(options || {}),
    });
}

export async function FetchStatus(id,enabled, options) {
    return request('/api/print_template/status', {
        method: 'POST',
        data: {
            id,
            enabled,
        },
        ...(options || {}),
    });
}

export async function FetchPrint(formPrintTemplateId,formId, options) {
    return request('/api/print/generate', {
        method: 'POST',
        data: {
            formPrintTemplateId,
            formId,
        },
        ...(options || {}),
    });
}

// 新增：更新打印模板的是否允许打印前编辑状态
export async function FetchAllowEditStatus(id, allowEdit, options) {
    return request('/api/print_template/allow_edit_status', {
        method: 'POST',
        data: {
            id,
            allowEdit, // 后端API定义中是 allowEdit
        },
        ...(options || {}),
    });
}
