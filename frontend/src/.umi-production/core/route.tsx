// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"登录","path":"/user/login","parentId":"1","id":"2"},"3":{"path":"/welcome","name":"欢迎","icon":"smile","parentId":"ant-design-pro-layout","id":"3"},"4":{"path":"/iframe-view","name":"欢迎","icon":"smile","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/superset","name":"Superset","icon":"superset","parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/superset-dashboard","name":"SupersetDashboard","icon":"superset","parentId":"ant-design-pro-layout","id":"6"},"7":{"path":"/team","name":"人资","icon":"team","access":"canAdmin","parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/team/deptusers","name":"部门人员","parentId":"7","id":"8"},"9":{"path":"/team/posts","name":"岗位","parentId":"7","id":"9"},"10":{"path":"/team/organization","name":"组织架构图","parentId":"7","id":"10"},"11":{"path":"/project","name":"基础设置","icon":"project","access":"canAdmin","parentId":"ant-design-pro-layout","id":"11"},"12":{"path":"/project/project","name":"项目管理","parentId":"11","id":"12"},"13":{"path":"/project/project-role","name":"项目角色管理","parentId":"11","id":"13"},"14":{"path":"/automation","name":"自动化","icon":"config","access":"canAdmin","parentId":"ant-design-pro-layout","id":"14"},"15":{"path":"/automation/config","name":"自动化配置管理","parentId":"14","id":"15"},"16":{"path":"/flow","name":"审批流","icon":"project","access":"canAdmin","parentId":"ant-design-pro-layout","id":"16"},"17":{"path":"/flow/list","name":"审批列表","parentId":"16","id":"17"},"18":{"path":"/flow/pending","name":"待我处理","parentId":"16","id":"18"},"19":{"path":"/flow/completed","name":"已处理","parentId":"16","id":"19"},"20":{"path":"/flow/initiated","name":"我发起的","parentId":"16","id":"20"},"21":{"path":"/flow/cc","name":"抄送我的","parentId":"16","id":"21"},"22":{"path":"/system","name":"系统设置","icon":"setting","access":"canAdmin","parentId":"ant-design-pro-layout","id":"22"},"23":{"path":"/system/menus","name":"菜单管理","parentId":"22","id":"23"},"24":{"path":"/system/roles","name":"角色权限","parentId":"22","id":"24"},"25":{"path":"/system/notify","name":"通知管理","parentId":"22","id":"25"},"26":{"path":"/system/log","name":"日志管理","parentId":"22","id":"26"},"27":{"path":"/system/log/actions","name":"操作日志","parentId":"26","id":"27"},"28":{"path":"/system/log/sys","name":"系统日志","parentId":"26","id":"28"},"29":{"path":"/system/company","name":"个人设置","parentId":"22","id":"29"},"30":{"path":"/system/form-permission","name":"表单权限设置","parentId":"22","id":"30"},"31":{"path":"/system/form-settle","name":"表单列表","parentId":"22","id":"31"},"32":{"path":"/system/form-settle/edit","name":"表单编辑","parentId":"22","id":"32"},"33":{"path":"/system/form-list","name":"表单测试","parentId":"22","id":"33"},"34":{"path":"/system/flow-templates","name":"流程模板","parentId":"22","id":"34"},"35":{"path":"/admin","name":"管理页","icon":"crown","access":"canAdmin","parentId":"ant-design-pro-layout","id":"35"},"36":{"path":"/admin","redirect":"/admin/sub-page","parentId":"35","id":"36"},"37":{"path":"/admin/sub-page","name":"二级管理页","parentId":"35","id":"37"},"38":{"name":"查询表格","icon":"table","path":"/list","parentId":"ant-design-pro-layout","id":"38"},"39":{"name":"角色管理","icon":"table","path":"/roles","parentId":"ant-design-pro-layout","id":"39"},"40":{"name":"扫码回签","icon":"table","path":"/scan-signin","parentId":"ant-design-pro-layout","id":"40"},"41":{"path":"/","redirect":"/welcome","parentId":"ant-design-pro-layout","id":"41"},"42":{"path":"*","layout":false,"id":"42"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__User__Login__index" */'@/pages/User/Login/index.jsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__Welcome" */'@/pages/Welcome.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__IframeView__index" */'@/pages/IframeView/index.jsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__Superset__index" */'@/pages/Superset/index.jsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__Superset__SupersetDashboard" */'@/pages/Superset/SupersetDashboard.jsx')),
'7': React.lazy(() => import('./EmptyRoute')),
'8': React.lazy(() => import(/* webpackChunkName: "p__System__SysUser__index" */'@/pages/System/SysUser/index.jsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__System__SysPost__index" */'@/pages/System/SysPost/index.jsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'11': React.lazy(() => import('./EmptyRoute')),
'12': React.lazy(() => import(/* webpackChunkName: "p__Project__Project__index" */'@/pages/Project/Project/index.jsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__Project__ProjectRole__index" */'@/pages/Project/ProjectRole/index.jsx')),
'14': React.lazy(() => import('./EmptyRoute')),
'15': React.lazy(() => import(/* webpackChunkName: "p__Automation__Config__index" */'@/pages/Automation/Config/index.jsx')),
'16': React.lazy(() => import('./EmptyRoute')),
'17': React.lazy(() => import(/* webpackChunkName: "p__Flow__FlowInstance__index" */'@/pages/Flow/FlowInstance/index.jsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__Flow__Pending__index" */'@/pages/Flow/Pending/index.jsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__Flow__Completed__index" */'@/pages/Flow/Completed/index.jsx')),
'20': React.lazy(() => import(/* webpackChunkName: "p__Flow__Initiated__index" */'@/pages/Flow/Initiated/index.jsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__Flow__Cc__index" */'@/pages/Flow/Cc/index.jsx')),
'22': React.lazy(() => import('./EmptyRoute')),
'23': React.lazy(() => import(/* webpackChunkName: "p__System__SysMenu__index" */'@/pages/System/SysMenu/index.jsx')),
'24': React.lazy(() => import(/* webpackChunkName: "p__System__SysRole__index" */'@/pages/System/SysRole/index.jsx')),
'25': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'26': React.lazy(() => import('./EmptyRoute')),
'27': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'28': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'29': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__Form__Permission__index" */'@/pages/Form/Permission/index.jsx')),
'31': React.lazy(() => import(/* webpackChunkName: "p__Form__FormDesign__index" */'@/pages/Form/FormDesign/index.jsx')),
'32': React.lazy(() => import(/* webpackChunkName: "p__Form__FormDesign__edit" */'@/pages/Form/FormDesign/edit.jsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__Form__FormView__index" */'@/pages/Form/FormView/index.jsx')),
'34': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'35': React.lazy(() => import('./EmptyRoute')),
'36': React.lazy(() => import('./EmptyRoute')),
'37': React.lazy(() => import(/* webpackChunkName: "p__Admin" */'@/pages/Admin.tsx')),
'38': React.lazy(() => import(/* webpackChunkName: "p__TableList__index" */'@/pages/TableList/index.tsx')),
'39': React.lazy(() => import(/* webpackChunkName: "p__Role__index" */'@/pages/Role/index.jsx')),
'40': React.lazy(() => import(/* webpackChunkName: "p__ScanSignin__index" */'@/pages/ScanSignin/index.jsx')),
'41': React.lazy(() => import('./EmptyRoute')),
'42': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "t__plugin-layout__Layout" */'/home/<USER>/Work/CRM/frontend/src/.umi-production/plugin-layout/Layout.tsx')),
},
  };
}
