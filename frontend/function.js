var __columnMap = {}
var __service = {}
var __current = {}


// 根据表信息，过滤条件，获得历史数据
function getDatasByRule(tableName, tableType, ruleFilter) {
    // 根据条件获得当前表的历史数据
    let params = {}
    params.PageSize = 999
    params.RuleFilter = ruleFilter
    params.TableName = tableName
    params.TableType = tableType
    return __service.GetDatas(params)
}


// 得到子表数据
let __currentTable = __current[__columnMap.columns.proList.formTableName]
// 处理数据，格式化列名
let proList = __currentTable.map(item => (
    {
        stockNumber: item[__columnMap.columns.proList.stockNumber]?.value,
        stockLabel: item[__columnMap.columns.proList.batchNumber],
        outboundQuantity: item[__columnMap.columns.proList.outboundQuantity]
    }))
// 去重取和
proList = _(proList)
    .groupBy('stockNumber')  // 根据 stockNumber 分组
    .map((items, stockNumber) => ({  // 对每一组进行处理
        stockNumber: stockNumber,
        stockLabel: items[0]?.stockLabel,
        outboundQuantity: _.sumBy(items, item => parseFloat(item.outboundQuantity)) // 对 outboundQuantity 求和
    }))
    .value();

// 验证每个库存的剩余量
let tableName = __columnMap.columns.withTable.formTableName
let tableType = __columnMap.columns.withTable.formTableType
let blanceName = __columnMap.columns.withTable.columns.blance
let lockedInventoryName = __columnMap.columns.withTable.columns.lockedInventory
for (let i = 0; i < proList.length; i++) {
    const item = proList[i];
    // 设置过滤规则
    let ruleFilter = {}
    ruleFilter["id"] = item.stockNumber
    let response = getDatasByRule(tableName, tableType, ruleFilter)
    let { Code, Data, Message } = response
    if (Code == 0 && Array.isArray(Data?.List) && Data.List.length > 0) {
        if (parseFloat(Data.List[0]?.[blanceName]) - parseFloat(Data.List[0]?.[lockedInventoryName]) < item.outboundQuantity) {
            throw new Error(`\n\n批次${item.stockLabel}库存不足，剩余库存：${Data.List[0]?.[blanceName]}，已锁定库存：${Data.List[0]?.[lockedInventoryName]}\n\n`)
        }
    }
    else throw new Error(`\n\n查询批次${item.stockLabel}库存失败\n\n`)
}
return true

__service.Log(__current)
return __current

if (!__current) return "[]"
__current = JSON.parse(__current)
__current = __current.map(item => {
    return { ...item, "node_ocm3i6pksn32": item?.["node_ocm3i6pksn29"] }
})
var result = JSON.stringify(__current)
__service.Log(result)
return result

if (!__current) return null
__current = JSON.parse(__current)
__current.label = __currentRecord?.node_oclzsask5w5
return JSON.stringify(__current)

if (!__current) return 0
__current = JSON.parse(__current)
var result = __current.reduce((acc, item) => {
    __service.Log(item)
    return acc + parseInt(item?.["node_ocm3i6pksn29"])
}, 0)
__service.Log(result)
return result

if (!__current) return 0
__current = JSON.parse(__current)
return __current?.value

if (!__current) return "[]"
__current = JSON.parse(__current)
__current = __current.map(item => {
    return { ...item, "node_ocm3i6pksn32": item?.["node_ocm3i6pksn29"] }
})
var result = JSON.stringify(__current)
__service.Log(result)
return result


if (!__current) return ""
__current = JSON.parse(__current)
return __current?.label || ""


let newValue = __currentRecord?.id
return JSON.stringify({ label: __current, value: newValue })


// 更新订单中每个商品的未出数量
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__currentRecord) return "[]"
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return "[]"

    let orderProductList = __currentRecord?.orderProductList || "[]"
    let outProductList = __currentRecord?.outProductList || "[]"
    orderProductList = JSON.parse(orderProductList)
    outProductList = JSON.parse(outProductList)
    __service.Log(orderProductList)
    __service.Log(outProductList)

    // 计算当前出库单每个商品待出的数量
    let wOutProductList = outProductList.reduce((acc, item) => {
        let productCode = item?.node_ocm0en9eta3f
        let productId = productCode?.value
        let productIndex = acc.findIndex(item => item?.productId == productId)
        if (productIndex == -1) {
            acc.push({ productId, outQuantity: 0 })
            productIndex = acc.length - 1
        }
        acc[productIndex].outQuantity += parseFloat(item?.node_ocm36xsuo5v)
        return acc
    }, [])
    __service.Log(wOutProductList)

    orderProductList = orderProductList.map(orderProduct => {
        let productCode = orderProduct?.node_ocm3i6i7g8n
        let productId = productCode?.value

        let wOutAmount = wOutProductList.find(item => item?.productId == productId)?.outQuantity || 0
        let outedSumAmount = IncrByFloat(`xsdd_${orderProductCode}_${productId}`, wOutAmount)
        // 计算未出数量
        let unOutAmount = parseFloat(orderProduct?.node_ocm3i6pksn29) - outedSumAmount
        return { ...orderProduct, "node_ocm3i6pksn32": parseFloat(unOutAmount.toFixed(4)) }
    })

    return JSON.stringify(orderProductList)

}


// 计算总未出数量
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__current) return 0
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return 0
    let orderProductList = JSON.parse(__current)
    let result = orderProductList.reduce((acc, orderProduct) => {
        let productCode = orderProduct?.node_ocm3i6i7g8n
        let productId = productCode?.value
        let outedSumAmount = IncrByFloat(`xsdd_${orderProductCode}_${productId}`, 0)
        // 计算未出数量
        let unOutAmount = parseFloat(orderProduct?.node_ocm3i6pksn29) - outedSumAmount
        return acc + unOutAmount
    }, 0)
    __service.Log(result)
    return result
}

{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__currentRecord) return "[]"
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return "[]"

    let orderProductList = __currentRecord?.orderProductList || "[]"
    orderProductList = JSON.parse(orderProductList)
    __service.Log(orderProductList)



    orderProductList = orderProductList.map(orderProduct => {
        let productCode = orderProduct?.node_ocm3i6i7g8n
        let productId = productCode?.value

        let wOutAmount = 0
        __service.Log(__currentRecord?.onelineProductCode)
        if (__currentRecord?.onelineProductCode?.value == productId) {
            wOutAmount = parseFloat(__currentRecord?.onelineProductAmount)
        }
        let outedSumAmount = IncrByFloat(`xsdd_${orderProductCode}_${productId}`, wOutAmount)
        // 计算未出数量
        let unOutAmount = parseFloat(orderProduct?.node_ocm3i6pksn29) - outedSumAmount
        return { ...orderProduct, "node_ocm3i6pksn32": unOutAmount }
    })

    return JSON.stringify(orderProductList)


}

{

    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__current) return 0
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return 0
    let orderProductList = JSON.parse(__current)
    let result = orderProductList.reduce((acc, orderProduct) => {
        let productCode = orderProduct?.node_oclzmgoa7m17
        let productId = productCode?.value
        let outedSumAmount = IncrByFloat(`ddck_${orderProductCode}_${productId}`, 0)
        // 计算未出数量
        let unOutAmount = parseFloat(orderProduct?.node_oclzmgoa7m4j) - outedSumAmount
        if (unOutAmount >= 0) return acc + unOutAmount
        return acc
    }, 0)
    __service.Log(result)
    result = parseFloat(result.toFixed(4))
    return result
}

{
    if (!__currentRecord) return null
    return JSON.stringify({ ...__currentRecord, "value": __currentRecord?.id, "label": __currentRecord?.node_oclzsaswee1 })
    return __current ? __current?.value : ""
    return __current ? JSON.parse(__current)?.value : ""
    return __current ? JSON.parse(__current)?.label : ""

    return __current ? -__current : 0
}

{

    __service.Log({ "info": "baoxiaoxinxi", "data": __current })
    return __current ? JSON.parse(__current)?.node_oclz0yj7731 : ""
    __service.Log({ "info": "__currentRecord?.onelineProductCode", "data": __currentRecord?.onelineProductCode })

    if (!__current) return "[]"
    __current = JSON.parse(__current)
    let newStructure = __current.map(item => {
        return {
            __id: item?.__id,
            node_ocm7lbl9zm19: item?.node_ocm50mb43f39, // 待报销编号 
            node_ocm7lbl9zm2o: item?.node_ocm50pzgyn25, // 报销类型
            node_ocm7lbl9zm3n: item?.node_ocm5yr06ou1d, // 费用类型
            node_ocm7lbl9zm4i: item?.node_ocm50mb8ip52, // 费用名称
            node_ocm7lbl9zm5f: item?.node_ocm50mb8ip6v,  // 金额
            node_ocm7lbl9zm5w: item?.node_ocm5yr06ou36, // 用途
            node_ocm7lbl9zm6t: item?.node_ocm50mb8ip9o, // 费用发生日期
            node_ocm7lbl9zm7q: item?.node_ocm50mb8iprk, // 凭证
            node_ocm7lbl9zm8b: item?.node_ocm50mb8ipme  // 备注
        }
    })
    return JSON.stringify(newStructure)


}
{
    if (!__current) return "[]"
    __current = JSON.parse(__current)

    // 按照费用类型和费用名称进行分组，并对金额求和
    let grouped = __current.reduce((acc, item) => {
        let key = `${item?.node_ocm5yr06ou1d}-${item?.node_ocm50mb8ip52?.value}`; // 费用类型 + 费用名称
        if (!acc[key]) {
            acc[key] = {
                __id: item?.__id, // 取原数据分组的第一行的__id
                node_ocm7lbl9zm3n: item?.node_ocm5yr06ou1d, // 费用类型
                node_ocm7lbl9zm4i: item?.node_ocm50mb8ip52, // 费用名称
                node_ocm7lbl9zm5f: 0 // 初始化金额
            };
        }
        acc[key].node_ocm7lbl9zm5f += parseFloat(item?.node_ocm50mb8ip6v) || 0; // 累加金额
        return acc;
    }, {});

    // 转换为数组并保留两位小数
    let newStructure = Object.values(grouped).map(group => ({
        __id: group.__id,
        node_ocm7lbl9zm3n: group.node_ocm7lbl9zm3n,
        node_ocm7lbl9zm4i: group.node_ocm7lbl9zm4i,
        node_ocm7lbl9zm5f: group.node_ocm7lbl9zm5f.toFixed(2) // 保留两位小数
    }));

    // 根据 node_ocm7lbl9zm3n 排序
    newStructure.sort((a, b) => a.node_ocm7lbl9zm3n.localeCompare(b.node_ocm7lbl9zm3n));

    return JSON.stringify(newStructure)
}
{
    // 根据表信息，过滤条件，获得历史数据
    function getDatasByRule(tableName, tableType, ruleFilter) {
        // 根据条件获得当前表的历史数据
        let params = {}
        params.PageSize = 999
        params.RuleFilter = ruleFilter
        params.TableName = tableName
        params.TableType = tableType
        return __service.GetDatas(params)
    }
    // 得到子表数据
    let __currentTable = __current[__columnMap.columns.proList.formTableName]
    // 处理数据，格式化列名
    let proList = __currentTable.map(item => (
        {
            stockNumber: item[__columnMap.columns.proList.stockNumber]?.value,
            stockLabel: item[__columnMap.columns.proList.batchNumber],
            outboundQuantity: item[__columnMap.columns.proList.outboundQuantity]
        }))
    // 去重取和
    proList = _(proList)
        .groupBy('stockNumber')  // 根据 stockNumber 分组
        .map((items, stockNumber) => ({  // 对每一组进行处理
            stockNumber: stockNumber,
            stockLabel: items[0]?.stockLabel,
            outboundQuantity: _.sumBy(items, item => parseFloat(item.outboundQuantity)) // 对 outboundQuantity 求和
        }))
        .value();

    // 验证每个库存的剩余量
    let tableName = __columnMap.columns.withTable.formTableName
    let tableType = __columnMap.columns.withTable.formTableType
    let blanceName = __columnMap.columns.withTable.columns.blance
    let lockedInventoryName = __columnMap.columns.withTable.columns.lockedInventory
    for (let i = 0; i < proList.length; i++) {
        const item = proList[i];
        // 设置过滤规则
        let ruleFilter = {}
        ruleFilter["id"] = item.stockNumber
        let response = getDatasByRule(tableName, tableType, ruleFilter)
        let { Code, Data, Message } = response
        if (Code == 0 && Array.isArray(Data?.List) && Data.List.length > 0) {
            if (parseFloat(Data.List[0]?.[blanceName]) - parseFloat(Data.List[0]?.[lockedInventoryName]) < item.outboundQuantity) {
                throw new Error(`\n\\n<br />批次${item.stockLabel}库存不足，剩余库存：${Data.List[0]?.[blanceName]}，已锁定库存：${Data.List[0]?.[lockedInventoryName]}\n\n`)
            }
        }
        else throw new Error(`\n\n查询批次${item.stockLabel}库存失败\n\n`)
    }
    return true

}

{

    let __currentTable = __current[__columnMap.columns.proList.formTableName]
    let stockList = __currentTable.map(item => item[__columnMap.columns.proList.stockNumber]?.value)
    let uniqueStockNumbers = new Set(stockList);
    if (uniqueStockNumbers.size !== stockNumbers.length) return false
    return true
}

{
    let stockList = [
        "385",
        "385",
    ]
    let uniqueStockNumbers = new Set(stockList);
    console.log(uniqueStockNumbers.size)
}

{

    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    let incrResult = IncrByFloat("WLBM-NEXTID-" + __current, 1);
    let formattedResult = String(Math.floor(incrResult)).padStart(6, '0');
    return "N-" + __current + "-" + formattedResult
}

// 更新订单中每个商品的未出数量
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__currentRecord) return "[]"
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return "[]"

    let orderProductList = __currentRecord?.orderProductList || "[]"
    orderProductList = JSON.parse(orderProductList)
    __service.Log(orderProductList)

    let onelineProductAmount = parseFloat(__currentRecord?.onelineProductAmount)
    let onelineProductId = JSON.parse(__currentRecord?.onelineProductCode)?.value

    let unOutAmountKey = "node_ocm3z8w6ga29"  // 未出数量列名
    let productCodeKey = "node_oclzmjfvd1t"   // 产品编码列名
    let orderAmountKey = "node_oclzmjfvd15n"  // 订单数量列名
    let incrbyKey = "ypck"                    // 已出数量key

    let outedSumAmounts = orderProductList.reduce((acc, orderProduct) => {
        let productCode = orderProduct?.[productCodeKey]              // 产品编码
        let productId = productCode?.value
        if (!(productId in acc)) {
            acc[productId] = IncrByFloat(`${incrbyKey}_${orderProductCode}_${productId}`, onelineProductId == productId ? onelineProductAmount : 0)
        }
        return acc
    }, {})
    orderProductList = orderProductList.map(orderProduct => {
        let productCode = orderProduct?.[productCodeKey]              // 产品编码
        let productId = productCode?.value
        if (!(productId in outedSumAmounts)) return orderProduct

        let orderAmount = parseFloat(orderProduct?.[orderAmountKey])  // 订单数量
        let unOutAmount = orderAmount
        let currentOutAmount = orderAmount > outedSumAmounts[productId] ? outedSumAmounts[productId] : orderAmount
        unOutAmount = orderAmount - currentOutAmount                // 计算未出数量
        outedSumAmounts[productId] -= currentOutAmount              // 更新已出的总数，后面如果还有相同商品，还要接着扣
        return { ...orderProduct, [unOutAmountKey]: parseFloat(unOutAmount.toFixed(4)) }
    })

    return JSON.stringify(orderProductList)

}

let test = { aaa: 2, bbb: 3 }
test.aaa -= 1
console.log(test)

// 计算订单总未出数量
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__current) return 0
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return 0
    let orderProductList = JSON.parse(__current)
    // 按 productId 分组
    let unOutAmountKey = "node_ocm3z8w6ga29"   // 未出数量列名
    let productCodeKey = "node_oclzmjfvd1t"   // 产品编码列名
    let orderAmountKey = "node_oclzmjfvd15n"  // 订单数量列名
    let incrbyKey = "ypck"                    // 已出数量key
    let groupedProducts = _.groupBy(orderProductList, orderProduct => orderProduct?.[productCodeKey]?.value)
    let result = _.reduce(groupedProducts, (acc, products, productId) => {
        let totalOrderAmount = _.sumBy(products, product => parseFloat(product?.[orderAmountKey]))
        let outedSumAmount = IncrByFloat(`${incrbyKey}_${orderProductCode}_${productId}`, 0)
        let unOutAmount = totalOrderAmount - outedSumAmount
        if (unOutAmount >= 0) return acc + unOutAmount
        return acc
    }, 0)
    result = parseFloat(result.toFixed(4))
    __service.Log(result)
    return result
}




// 更新订单中每个商品的未出数量
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__currentRecord) return "[]"
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return "[]"

    let orderProductList = __currentRecord?.orderProductList || "[]"
    orderProductList = JSON.parse(orderProductList)
    __service.Log(orderProductList)

    let onelineProductAmount = parseFloat(__currentRecord?.onelineProductAmount)
    let onelineProductId = JSON.parse(__currentRecord?.onelineProductCode)?.value

    let unOutAmountKey = "node_ocm3z8vwltt"  // 未出数量列名
    let productCodeKey = "node_oclzmgoa7m17"   // 产品编码列名
    let orderAmountKey = "node_oclzmgoa7m4j"  // 订单数量列名
    let incrbyKey = "ddck"                    // 已出数量key

    let outedSumAmounts = orderProductList.reduce((acc, orderProduct) => {
        let productCode = orderProduct?.[productCodeKey]              // 产品编码
        let productId = productCode?.value
        if (!(productId in acc)) {
            acc[productId] = IncrByFloat(`${incrbyKey}_${orderProductCode}_${productId}`, onelineProductId == productId ? onelineProductAmount : 0)
        }
        return acc
    }, {})
    __service.Log({ "name": "outedSumAmounts", "data": outedSumAmounts })
    orderProductList = orderProductList.map(orderProduct => {
        let productCode = orderProduct?.[productCodeKey]              // 产品编码
        let productId = productCode?.value
        if (!(productId in outedSumAmounts)) return orderProduct

        let orderAmount = parseFloat(orderProduct?.[orderAmountKey])  // 订单数量
        let unOutAmount = orderAmount
        let currentOutAmount = orderAmount > outedSumAmounts[productId] ? outedSumAmounts[productId] : orderAmount
        unOutAmount = orderAmount - currentOutAmount                // 计算未出数量
        outedSumAmounts[productId] -= currentOutAmount              // 更新已出的总数，后面如果还有相同商品，还要接着扣
        return { ...orderProduct, [unOutAmountKey]: parseFloat(unOutAmount.toFixed(4)) }
    })

    return JSON.stringify(orderProductList)

}

// 计算订单总未出数量
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__current) return 0
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return 0
    let orderProductList = JSON.parse(__current)
    // 按 productId 分组
    let unOutAmountKey = "node_ocm3z8vwltt"  // 未出数量列名
    let productCodeKey = "node_oclzmgoa7m17"   // 产品编码列名
    let orderAmountKey = "node_oclzmgoa7m4j"  // 订单数量列名
    let incrbyKey = "ddck"                    // 已出数量key
    let groupedProducts = _.groupBy(orderProductList, orderProduct => orderProduct?.[productCodeKey]?.value)
    let result = _.reduce(groupedProducts, (acc, products, productId) => {
        let totalOrderAmount = _.sumBy(products, product => parseFloat(product?.[orderAmountKey]))
        let outedSumAmount = IncrByFloat(`${incrbyKey}_${orderProductCode}_${productId}`, 0)
        let unOutAmount = totalOrderAmount - outedSumAmount
        if (unOutAmount >= 0) return acc + unOutAmount
        return acc
    }, 0)
    result = parseFloat(result).toFixed(2)
    __service.Log(result)
    return result
}
return JSON.parse(__current)?.[0]?.id
JSON.parse(__current)?.[0]?.username
return __current ? dayjs(__current).format('YYYY-MM-DD') : ''
return __current ? dayjs(__current).format('YYYY年MM月DD日') : "" 
return __current ? parseFloat(__current).toFixed(4) : ''
console.log(1 + parseFloat(undefined ?? 0) > 0)

{
    let productionDate = __currentRecord?.productionDate
    let shelfLifeMonths = __currentRecord?.shelfLifeMonths

    __service.Log({ "name": "productionDate", "data": productionDate })
    __service.Log({ "name": "shelfLifeMonths", "data": shelfLifeMonths })
    if (productionDate && shelfLifeMonths) {
        let expirationDate = dayjs(productionDate).add(shelfLifeMonths, 'month').format('YYYY-MM-DD');
        __service.Log({ "name": "expirationDate", "data": expirationDate })
        return expirationDate
    } else {
        return '2099-01-01'
    }
}

{
    let records = []
    records.push(__currentRecord?.['客户测试情况（一）'])
    records.push(__currentRecord?.['客户测试情况（二）'])
    records.push(__currentRecord?.['客户测试情况（三）'])
    records.push(__currentRecord?.['客户测试情况（四）'])

    records = records.filter(item => item)
    let result = records.map((item, index) => {
        return {
            "__id": "import_record_" + index,
            "node_ocm7id5spt3v": item
        }
    })
    __service.Log({ "name": "客户测试情况", "data": records })
    return JSON.stringify(result)
}
{
    if (!__current) return "[]"
    let originalList = JSON.parse(__current)
    if (!Array.isArray(originalList)) return "[]"
    let result = _.map(originalList, (item) => {
        return {
            "node_ocm5m4rw2w1h": item?.node_ocm50gpsk516,
            "node_ocm5m4rw2w22": item?.node_ocm50gpsk52l,
            "node_ocm5m4rw2w4d": item?.node_ocm50gpsk536,
            "node_ocm5m4rw2w6y": item?.node_ocm50gpsk53v,
        }
    })
    return JSON.stringify(result)
}
{

    let result = __current ? JSON.parse(__current)?.label : ""
    __service.Log({ "name": "订单编号", "data": result })
    return result

    return __current.toString()
}
{
    var result = 0
    if (__current) {
        result = JSON.parse(__current)?.value
    }
    return result
}
{
    if (!__currentRecord || !__currentRecord?.node_ocm25vigzjl || !__currentRecord?.node_ocm25vinao1) return ""
    return __currentRecord?.node_ocm25vigzjl + __currentRecord?.node_ocm25vinao1

}
{
    if (!__current) return 0
    __service.Log({ "name": "__current", "data": __current })
    let arrList = JSON.parse(__current)
    if (!Array.isArray(arrList)) return 0
    return _.reduce(arrList, (acc, item) => acc + parseFloat(item?.node_ocm50mq9dq1o || 0), 0)
}
{

    var type = __currentRecord?.node_ocm5ex7r1z2
    if (type) type = type.toString()
    var result;
    if (__recordIndex >= 0) {
        var tableRows = __currentRecord?.node_ocm5ex7r1zf
        tableRows = JSON.parse(tableRows || "[]")
        __service.Log({ "name": "Array.isArray(tableRows)", "data": Array.isArray(tableRows) })
        __service.Log({ "name": "__recordIndex", "data": __recordIndex })
        var rowData = null;
        if (Array.isArray(tableRows) && tableRows.length > __recordIndex) {
            rowData = tableRows[__recordIndex]
        }
        __service.Log({ "name": "rowData", "data": rowData })
        switch (type) {
            case "1":
                result = rowData?.node_ocm5ewg1s619
                break;
            case "2":
                result = rowData?.node_ocm5ewg1s62w
                break;
            case "3":
                result = rowData?.node_ocm5ewg1s649
                break;
            default:
                result = null
                break;
        }
    }

    __service.Log({ "name": "code_type", "data": type })
    __service.Log({ "name": "code_result", "data": result })
    return result
}

{
    if (typeof __currentTable == "string") __currentTable = JSON.parse(__currentTable)
    var type = __currentRecord?.type
    var result;
    switch (type) {
        case "1":
            result = __currentRecord?.code1
            break;
        case "2":
            result = __currentRecord?.code2
            break;
        case "3":
            result = __currentRecord?.code3
            break;
        default:
            result = null
            break;
    }
    __service.Log({ "name": "code_type", "data": type })
    __service.Log({ "name": "code_result", "data": result })


    return result
}

{
    var demo = `{"id":13,"value":13}`
    return __current ? JSON.parse(__current)?.value : ""
    return __current ? JSON.parse(__current)?.value : 0
    return __current ? JSON.parse(__current)?.label : ""
    return __current ? __current : null
    let newValue = __currentRecord?.id
    return JSON.stringify({ label: __current, value: newValue })
        `` || ``
    let test = { "value": JSON.parse(JSON.stringify({ "id": 13, "value": 13 }))?.value, "label": JSON.parse(JSON.stringify({ "id": 13, "value": 13 }))?.label }
    console.log("test", test)
    // {"value":JSON.parse(JSON.stringify(拉拉))?.value, "label":JSON.parse(JSON.stringify(拉拉))?.label}


    let result = JSON.parse(__current)
    let newLabel = __currentRecord?.node_oclzmjewnm9
    return JSON.stringify({ label: newLabel, value: result?.value })
    __service.Log({ "name": "deptids", "data": __current })
    if (!Array.isArray(__current)) return null
    const sortedIds = __current
        .filter(item => item.dept)
        .sort((a, b) => a.dept.parentId - b.dept.parentId)
        .map(item => item.dept.id);
    return sortedIds.length > 0 ? JSON.stringify([sortedIds[0]]) : null;
    return ""
}

{
    // 根据条件获取表的数据
    function getDatasByColumns(ruleFilter, formTableName, formTableType) {
        // 根据条件获得当前表的历史数据
        let params = {}
        params.PageSize = 999
        params.RuleFilter = ruleFilter
        params.TableName = formTableName
        params.TableType = formTableType
        return __service.GetDatas(params)
    }

    // 根据id验证报销明细的状态是否处于“待报销”状态
    function checkStatus(id) {
        let tableName = __columnMap.columns.withTable.formTableName
        let tableType = __columnMap.columns.withTable.formTableType
        let statusName = __columnMap.columns.status
        let ruleFilter = { id }
        const response = getDatasByColumns(ruleFilter, tableName, tableType)
        const { Code, Data, Message } = response
        // 判断数据状态
        if (Code == 0 && Array.isArray(Data?.List))
            return _.size(_.filter(Data?.List, item => {
                return item[statusName] != 1
            })) <= 0
        return false
    }

    __service.Log({ "name": "__columnMap", "data": __columnMap })
    __service.Log({ "name": "__current", "data": __current })
    var arrList = __current.arrList
    if (arrList && typeof arrList == "string") arrList = JSON.parse(arrList || "[]")
    return _.every(arrList, (item) => {
        let code = item?.code
        if (code) {
            if (typeof code == "string")
                code = JSON.parse(code)
            let id = code?.value
            return checkStatus(id)
        }
        return false
    })

}

{

    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }

    function getIncrByKey(orderid, orderProductId) {
        let incrbyKey = "采购明细入库数量"
        let result = []
        result.push(incrbyKey)
        result.push(orderid)
        result.push(orderProductId)
        __service.Log({ "name": "getIncrByKey", "data": result })
        return CryptoJS.MD5(("accumulation_" + JSON.stringify(result)).toString())
    }

    if (!__currentRecord) return "[]"
    let id = __currentRecord?.id || ""
    if (!id) return "[]"

    let orderProductListKey = "node_oclzi9x0uc4"
    let unInAmountKey = "node_ocm5x8h9201c"  // 未入数量列名
    let orderAmountKey = "node_oclzi9x0uc92"  // 订单数量列名
    let orderProductList = __currentRecord?.[orderProductListKey] || "[]"
    orderProductList = JSON.parse(orderProductList)
    __service.Log(orderProductList)
    orderProductList = orderProductList.map(orderProduct => {
        let __id = orderProduct?.__id
        let unOutAmount = IncrByFloat(getIncrByKey(id, __id), 0)
        return { ...orderProduct, [unInAmountKey]: parseFloat(orderProduct?.[orderAmountKey] - unOutAmount.toFixed(4)) }
    })

    return JSON.stringify(orderProductList)
}
{
    if (!__current) return null

    let userinfo = __service.GetUserByUsername(__current)
    if (!userinfo) return null


    return JSON.stringify([{ id: userinfo?.id, username: userinfo?.username }])

}

{
    let enums = [{
        "label": "测试OK",
        "value": "1"
    }, {
        "label": "测试OK，待订单 ",
        "value": "2"
    }, {
        "label": "测试OK，已成交",
        "value": "3"
    }, {
        "label": "测试NG",
        "value": "4"
    }, {
        "label": "不用跟进测试结果",
        "value": "5"
    }, {
        "label": "测试中",
        "value": "6"
    }, {
        "label": "项目暂停",
        "value": "7"
    }, {
        "label": "放弃测试",
        "value": "8"
    }, {
        "label": "样品失效",
        "value": "9"
    }, {
        "label": "样品丢失",
        "value": "10"
    }]
    let result = enums.find(item => item.label == __current)
    return result?.value || 6


}
{

    // 生成一个随机的12位字符串
    function generateRandomString() {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 12; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }

    let logs = []
    if (__current) logs = JSON.parse(__current)
    if (!Array.isArray(logs)) logs = []
    let newLog = {
        __id: __currentRecord?.Code,
        node_ocm7iobtup1n: __currentRecord?.FeedbackNumber, // 反馈单号
        node_ocm7id5spt28: __currentRecord?.FeedbackDate, // 反馈日期
        node_ocm7id5spt9h: __currentRecord?.FeedbackUser, // 反馈人
        node_ocm7id5spt3v: __currentRecord?.TestSituation, // 测试情况
        node_ocm7iobr2z11: __currentRecord?.TestReport, // 测试报告
    }
    logs.push(newLog)
    return JSON.stringify(logs)

    __service.Log({ "name": "userinfo", "data": __currentRecord })
    return __current


    if (!__current) return null
    return JSON.parse(__current)?.username
}


{
    let logs = []
    if (__current) logs = JSON.parse(__current)
    if (!Array.isArray(logs)) logs = []
    logs = _.filter(logs, item => item?.__id != __currentRecord?.Code)
    return JSON.stringify(logs)
}
{
    let enums = [{
        "label": "财务费用",
        "value": "1"
    }, {
        "label": "销售费用",
        "value": "2"
    }, {
        "label": "研发费用",
        "value": "3"
    }, {
        "label": "管理费用",
        "value": "4"
    }, {
        "label": "营业税及附加",
        "value": "5"
    }, {
        "label": "其他",
        "value": "6"
    }]
    let result = enums.find(item => item.value == __current)
    return result?.label
}
{
    (() => {

        let result = `1111`;

        let hasG = '﻿﻿﻿﻿﻿﻿﻿﻿当前表单.供应商名称﻿﻿﻿﻿﻿﻿﻿﻿'.length > 0

        let hasK = '﻿﻿﻿﻿﻿﻿﻿﻿当前表单.客户名称﻿﻿﻿﻿﻿﻿﻿﻿'.length > 0

        if (hasG) result = JSON.parse('﻿﻿﻿﻿﻿﻿﻿﻿当前表单.供应商名称﻿﻿﻿﻿﻿﻿﻿﻿')?.label

        if (hasK) result = JSON.parse('﻿﻿﻿﻿﻿﻿﻿﻿当前表单.客户名称﻿﻿﻿﻿﻿﻿﻿﻿')?.label

        console.log(result)

        return result

    })()

    return Math.abs(__current)

}

{
    if (typeof __allowUpdatePartly == "boolean" && __allowUpdatePartly) return true
        (() => {
            const date1 = new Date('2024-03-20 10:00:00');
            const date2 = new Date('2024-03-20 11:30:00');

            const diffInMs = date2 - date1;  // 得到毫秒差
            return diffInMs
        })()
}

{
    return JSON.stringify({ label: __current, value: __currentRecord?.id })

    let __current = ""
    return (__current && __current.length > 0) ? '<span style="font-size: 14px;">储存条件：' + __current + '</span>' : ''



    function maxDecimals(num, decimals) {
        return parseFloat(num.toFixed(decimals));
    }

    // 示例
    console.log(maxDecimals(3.1415926, 2)); // 3.14
    console.log(maxDecimals(3.1, 4));       // 3.1
    console.log(parseFloat((3.14).toFixed(4)));       // 3.14


    let weishui = parseFloat((2.61 / (1 + 0.13)).toFixed(8))


    let hanshui = parseFloat((weishui * (1 + 0.13)).toFixed(8))
    console.log("weishui", weishui)
    console.log("hanshui", hanshui)
}

{

    let result = 6
    if (__current) logs = JSON.parse(__current)
    if (Array.isArray(logs) && logs.length > 0) {
        result = logs[logs.length - 1]?.node_ocm96ubrmf1p
    }
    return result;
}

{

    let result = "1970-01-01 00:00:00"
    if (__current) logs = JSON.parse(__current)
    if (Array.isArray(logs) && logs.length > 0) {
        result = logs[logs.length - 1]?.node_ocm7id5spt28
    }
    return result;
}

{
    if (!__current) return "[]"
    __current = JSON.parse(__current)

    // 按照费用类型和费用名称进行分组，并对金额求和
    let grouped = __current.reduce((acc, item) => {
        let key = `${item?.node_ocm5yr06ou1d}-${item?.node_ocm50mb8ip52?.value}`; // 费用类型 + 费用名称
        if (!acc[key]) {
            acc[key] = {
                __id: item?.__id, // 取原数据分组的第一行的__id
                node_ocm7lbl9zm3n: item?.node_ocm5yr06ou1d, // 费用类型
                node_ocm7lbl9zm4i: item?.node_ocm50mb8ip52, // 费用名称
                node_ocm7lbl9zm5f: 0 // 初始化金额
            };
        }
        acc[key].node_ocm7lbl9zm5f += parseFloat(item?.node_ocm50mb8ip6v) || 0; // 累加金额
        return acc;
    }, {});

    // 转换为数组并保留两位小数 
    let newStructure = Object.values(grouped).map(group => ({
        __id: group.__id,
        node_ocmadcmnrs4n: group.node_ocm7lbl9zm3n,
        node_ocmadcmnrs5a: group.node_ocm7lbl9zm4i,
        node_ocmadcmnrs67: group.node_ocm7lbl9zm5f.toFixed(2) // 保留两位小数
    }));

    // 根据 node_ocm7lbl9zm3n 排序
    newStructure.sort((a, b) => a.node_ocmadcmnrs4n.localeCompare(b.node_ocm7lbl9zm3n));

    return JSON.stringify(newStructure)

}


{


    let options = [{
        "label": "上午",
        "value": "1"
    }, {
        "label": "下午",
        "value": "2"
    }]
    let item = options.find(item => item.value == __current)
    return item?.label


}
{
    function IncrByFloat(incrKey, currentNumber) {
        var result = __service.IncrByFloat(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__currentRecord) return "[]"
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return "[]"

    let orderProductList = __currentRecord?.orderProductList || "[]"
    orderProductList = JSON.parse(orderProductList)
    __service.Log(orderProductList)

    let onelineProductAmount = parseFloat(__currentRecord?.onelineProductAmount)
    let onelineProductId = JSON.parse(__currentRecord?.onelineProductCode)?.value

    let unOutAmountKey = "node_ocm3z8vwltt"  // 未出数量列名
    let productCodeKey = "node_oclzmgoa7m17"   // 产品编码列名
    let orderAmountKey = "node_oclzmgoa7m4j"  // 订单数量列名
    let incrbyKey = "ddck"                    // 已出数量key


    let outedSumAmounts = orderProductList.reduce((acc, orderProduct) => {
        let productCode = orderProduct?.[productCodeKey]              // 产品编码
        let productId = productCode?.value
        if (!(productId in acc)) {
            acc[productId] = IncrByFloat(`${incrbyKey}_${orderProductCode}_${productId}`, onelineProductId == productId ? onelineProductAmount : 0)
        }
        return acc
    }, {})
    orderProductList = orderProductList.map(orderProduct => {
        let productCode = orderProduct?.[productCodeKey]              // 产品编码
        let productId = productCode?.value
        if (!(productId in outedSumAmounts)) return orderProduct

        let orderAmount = parseFloat(orderProduct?.[orderAmountKey])  // 订单数量
        let unOutAmount = orderAmount
        let currentOutAmount = orderAmount > outedSumAmounts[productId] ? outedSumAmounts[productId] : orderAmount
        unOutAmount = orderAmount - currentOutAmount                // 计算未出数量
        outedSumAmounts[productId] -= currentOutAmount              // 更新已出的总数，后面如果还有相同商品，还要接着扣
        return { ...orderProduct, [unOutAmountKey]: parseFloat(unOutAmount.toFixed(4)) }
    })

    return JSON.stringify(orderProductList)
}

{
    if (!__currentRecord) return "[]"

    let orderAmountKey = "node_oclzmgoa7m4j"  // 订单数量列名
    let unOutAmountKey = "node_ocm3z8vwltt"  // 未出数量列名
    __service.Log({ action: "__currentRecord", data: __currentRecord })
    let onelineID = __currentRecord?.onelineID || "" // 子表ID
    let onelineOutPutCount = __currentRecord?.onelineOutPutCount || 0 // 子表已出的总数量
    let orderProductList = __currentRecord?.orderProductList || "[]"
    if (onelineID) {
        orderProductList = JSON.parse(orderProductList)
        orderProductList = orderProductList.map(orderProduct => {
            __service.Log({ action: "orderProduct", data: orderProduct })
            if (orderProduct?.__id == onelineID) {
                let orderAmount = parseFloat(orderProduct?.[orderAmountKey])  // 订单数量
                let unOutAmount = orderAmount
                let currentOutAmount = orderAmount > onelineOutPutCount ? onelineOutPutCount : orderAmount
                unOutAmount = orderAmount - currentOutAmount
                orderProduct[unOutAmountKey] = parseFloat(unOutAmount.toFixed(4))
            }
            return orderProduct
        })
        orderProductList = JSON.stringify(orderProductList)
    }
    return orderProductList
}

{
    function IncrByFloatAccumulation(incrKey, currentNumber) {
        incrKey = "accumulation_" + incrKey
        var result = __service.IncrByFloatMD5(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }

    if (!__currentRecord) return "[]"

    let orderAmountKey = "node_oclzmgoa7m4j"  // 订单数量列名
    let unOutAmountKey = "node_ocm3z8vwltt"  // 未出数量列名
    let orderProductCode = __currentRecord?.code || ""
    __service.Log({ action: "__currentRecord", data: __currentRecord })
    let orderProductList = __currentRecord?.orderProductList || "[]"
    if (orderProductCode) {
        let keyPrefix = ["OrderSubTableOutputCount", orderProductCode]
        orderProductList = JSON.parse(orderProductList)
        orderProductList = orderProductList.map(orderProduct => {
            // 使用子表ID作为键获取已出数量
            let currentRowId = orderProduct?.__id || ""  // 当前行的子表ID
            let onelineOutPutCount = IncrByFloatAccumulation(JSON.stringify([...keyPrefix, currentRowId]), 0)
            let orderAmount = parseFloat(orderProduct?.[orderAmountKey])  // 订单数量
            let unOutAmount = orderAmount
            let currentOutAmount = orderAmount > onelineOutPutCount ? onelineOutPutCount : orderAmount
            unOutAmount = orderAmount - currentOutAmount
            orderProduct[unOutAmountKey] = parseFloat(unOutAmount.toFixed(4))
            return orderProduct
        })
        orderProductList = JSON.stringify(orderProductList)
    }
    return orderProductList
}
{
    function IncrByFloatAccumulation(incrKey, currentNumber) {
        incrKey = "accumulation_" + incrKey
        var result = __service.IncrByFloatMD5(incrKey, currentNumber)
        __service.Log(result)
        return parseFloat(result)
    }
    if (!__current) return 0
    let orderProductCode = __currentRecord?.code || ""
    if (!orderProductCode) return 0

    let orderProductList = JSON.parse(__current)

    let orderAmountKey = "node_oclzmgoa7m4j"  // 订单数量列名

    let keyPrefix = ["OrderSubTableOutputCount", orderProductCode]
    // 不再分组，直接计算总的未出数量
    let result = orderProductList.reduce((acc, orderProduct) => {
        let orderAmount = parseFloat(orderProduct?.[orderAmountKey] || 0)  // 订单数量
        let currentRowId = orderProduct?.__id || ""  // 当前行的子表ID

        // 使用子表ID作为键获取已出数量
        let outedAmount = IncrByFloatAccumulation(JSON.stringify([...keyPrefix, currentRowId]), 0)

        let unOutAmount = orderAmount - outedAmount  // 计算未出数量

        if (unOutAmount >= 0) {
            return acc + unOutAmount
        }
        return acc
    }, 0)

    result = parseFloat(result.toFixed(4))
    __service.Log(result)
    return result
}

{

(()=>{
    function addDaysToCurrentDate(n) {
        const currentDate = new Date();
        const futureDate = new Date(currentDate);
        futureDate.setDate(currentDate.getDate() + n);
        return futureDate.toISOString().split('T')[0];
    }
    return addDaysToCurrentDate(parseInt(1))
})()
}

{
    (() => {
        let chuku = decimal(当前表单.产品列表.出库数量)
        let huansuan = decimal(当前表单.产品列表.换算倍率)
        let result = chuku.dividedBy(huansuan)
        return parseFloat(result.toFixed(8))
    })()
}
