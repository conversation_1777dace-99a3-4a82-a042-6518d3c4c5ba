import snippets from './snippets';

export default {
  snippets,
  componentName: 'Field.Table',
  title: '子表',
  category: '容器',
  props: [
    {
      name: 'label',
      title: { label: '标签', tip: '标签' },
      propType: 'string',
      defaultValue: '标签',
      setter: 'StringSetter',
    },
    {
      name: 'isRequired',
      title: { label: '是否必填', tip: '是否必填' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'isAllowCopy',
      title: { label: '允许复制行', tip: '允许复制行' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'isAllowAdd',
      title: { label: '允许新增行', tip: '允许新增行' },
      propType: 'bool',
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: 'hideNoData',
      title: { label: '无数据时隐藏', tip: '如果子表无数据，则不显示此表' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'hideEditMode',
      title: { label: '编辑模式隐藏', tip: '如果子表处于编辑模式，则不显示此表' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'columnsWidth',
      title: { label: '列宽设置', tip: '列宽设置' },
      propType: 'string',
      setter: 'TableColWidthSetter',
    },
    // {
    //   name: 'withVisible',
    //   title: { label: '关联显示', tip: '通过计算得到一个bool值，来决定是否显示' },
    //   propType: 'string',
    //   setter: 'FormulaSetter',
    // },
  ],
  configure: {
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: [
          'Field.Input', 
          'Field.TextArea',
          'Field.Password', 
          'Field.WithSingleSelect', 
          'Field.WithMultipleSelect', 
          'Field.SingleSelect', 
          'Field.MultipleSelect', 
          'Field.UploadFile', 
          'Field.UploadImage', 
          'Field.DatePicker', 
          'Field.Switch',
          'Field.RadioGroup',
          'Field.Number']
      }
    },
    supports: {
    },
  },
};
