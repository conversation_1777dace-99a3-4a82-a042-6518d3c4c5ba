import snippets from './snippets';

export default {
  snippets,
  componentName: 'Field.RadioGroup',
  title: '单选框',
  category: '表单',
  props: [
    {
      name: 'label',
      title: { label: '标签', tip: '标签' },
      defaultValue: '标签',
      propType: 'string',
      setter: 'StringSetter'
    },

    {
      name: 'optionType',
      title: { label: '选项显示方式',},
      defaultValue: '标签',
      propType: 'default',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          defaultValue: "1",
          options: [
            { label: '默认', value: 'default' },
            { label: '按钮', value: 'button' },
          ],
        },
      },
    },
    {
      name: 'options',
      title: { label: '选项'},
      setter: "JsonSetter",
    },
    
    {
      name: 'defaultValue',
      title: { label: '默认值', tip: '默认值' },
      defaultValue: '',
      propType: 'string',
      setter: 'StringSetter'
    },
    {
      name: 'isRequired',
      title: { label: '是否必填', tip: '是否必填' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    {
      name: 'disabled',
      title: { label: '禁止输入', tip: '是否禁止输入' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },

    {
      name:'calcHidden',
      title:{label:'隐式计算', tip:'组件不在页面上显示，但参与计算，以及数据持久化'},
      propType:'bool',
      defaultValue:false,
      setter:'BoolSetter'
    },
    {
      name: 'withVisible',
      title: { label: '关联显示', tip: '通过计算得到一个bool值，来决定是否显示' },
      propType: 'string',
      setter: 'FormulaSetter',
    },
    {
      name: 'allowListEdit',
      title: { label: '允许列表编辑', tip: '是否允许直接在列表编辑这个组件的值' },
      propType: 'bool',
      defaultValue: false,
      setter: 'BoolSetter',
    },
    
  ],
  configure: {
    props: {
      isExtends: true,

    },
    supports: {
      style: false,

    },
  },
};
