import { Input, Select as OriginalSelect, Spin } from 'antd';
import React, { useEffect, useState, forwardRef, useCallback, useRef } from 'react';
import { BaseWrapper } from "../base"
import { debounce, isEqual } from 'lodash';
import { createPromiseWrapper } from "../../../utils"
import { useCreation } from 'ahooks';


const SearchSelect = forwardRef(({ addWrapper = true, form, fieldName, fieldsValue, shouldUpdateKey, value, type, defaultValue, onChange, option_label, option_value, option_search, options, request, requestParams, callError, subRequest, sub_option_label = "label", mode = "single", sub_option_value = "id", rightIconRender, rightIcon, rightIconClick, recordFieldsChange, getAllWithIds, removeLastFieldsValues, ...props }, ref) => {
    const [nOptions, setNOptions] = React.useState([])
    const [fetching, setFetching] = useState(false);

    const currentPageRef = useRef(1);
    const currentPageSize = 10
    const isAllLoadedRef = useRef(false)
    const isLoadingRef = useRef(false);
    const promiseRef = useCreation(() => {
        return createPromiseWrapper();
    }, []);
    const callbackQueue = useRef([]);
    const debounceFetchOptionsRef = useRef(null);


    useEffect(() => {
        // console.log("SearchSelect useEffect props", props)
        if (value && value?.value) {
            console.log(`SearchSelect ${fieldName} value`, value)
            let item = null
            if (Array.isArray(nOptions) && nOptions.length > 0) {
                item = nOptions.find(item => item.value == value?.value || item.value == value)
                if (item && !isEqual(item, value)) {
                    console.log("new value /////", item)
                    typeof onChange === 'function' && onChange(item)
                }
            }

            // 没有找到与value相等的选项，说明该项数据可能尚未加载，此时需要重新加载数据 
            if (!item) {
                loadSelectOptions(value)
            }
        }
    }, [value])



    useEffect(() => {
        initData(requestParams)
    }, [shouldUpdateKey, requestParams])


    // const loadSelectOptions = useCallback(debounce( async (loadValue) => {
    //     if (request && option_value) {
    //         console.log("loadSelectOptions debounceFetchOptions params" + props?.__id,loadValue)
    //         await promiseRef.promise
    //         debounceFetchOptions({ ...requestParams, [option_value]: loadValue?.value }, null,(selectOptions) => {
    //             console.log("loadSelectOptions selectOptions", selectOptions) 
    //             if (selectOptions && selectOptions.length > 0) {
    //                 let item = selectOptions.find(item => item.value == loadValue?.value || item.value == loadValue)
    //                 console.log("loadSelectOptions item", item)
    //                 if (item && !isEqual(item, loadValue)) {
    //                     console.log("new value /////", item)
    //                     typeof onChange === 'function' && onChange(item)
    //                 }
    //             }
    //         })

    //     }
    // },100), [])



    const loadSelectOptions = async (loadValue) => {
        if (request && option_value) {
            // console.log(`[${props?.label}]loadSelectOptions loadValue`, loadValue);
            console.log(`[${props?.label}]loadSelectOptions loadValue2`, loadValue);
            // callbackQueue.current.push((selectOptions) => {
            //     if (selectOptions && selectOptions.length > 0) {
            //         console.log("callback selectOptions", selectOptions)
            //         console.log("callback loadValue", loadValue)
            //         let item = selectOptions.find(item => item.value == loadValue?.value || item.value == loadValue);
            //         if (item && !isEqual(item, loadValue)) {
            //             typeof onChange === 'function' && onChange(item);
            //         }
            //     }
            // });
            debounceFetchOptions({ ...requestParams, [option_value]: loadValue?.value }, null, (selectOptions) => {
                if (selectOptions && selectOptions.length > 0) {
                    // console.log(`[${props?.label}]callback selectOptions`, selectOptions)
                    // console.log(`[${props?.label}]callback loadValue`, loadValue)
                    let item = selectOptions.find(item => item.value == loadValue?.value || item.value == loadValue);
                    if (item && !isEqual(item, loadValue)) {
                        typeof onChange === 'function' && onChange(item);
                    }
                }
            });
        }
    };

    const initData = async (params) => {

        let item = null
        const ruleParams = {}
        currentPageRef.current = 1
        isAllLoadedRef.current = false
        if (request && typeof request === 'function') {
            debounceFetchOptions(params, ruleParams)
        }
        if (options && options.length > 0) {
            setNOptions(options)
        }
    }


    const handleSearch = async (value) => {
        const params = { ...requestParams }
        params[option_search] = value
        await debounceFetchOptions(params)
    }

    const debounceFetchOptions = (params, ruleParams, callback) => {
        if (typeof callback == "function") callbackQueue.current.push(callback);
        if (debounceFetchOptionsRef.current) clearTimeout(debounceFetchOptionsRef.current);
        debounceFetchOptionsRef.current = setTimeout(() => {
            const callbacks = callbackQueue.current;
            callbackQueue.current = [];
            fetchOptions(params, ruleParams, fieldName).then(result => {
                while (callbacks.length > 0) {
                    const callback = callbacks.shift();
                    if (typeof callback === 'function') {
                        callback(result);
                    }
                }   
            });
        }, 200);
    }

    // const debounceFetchOptions = useCallback(debounce((params, ruleParams) => {
    //     console.log(`[${props?.label}]debounceFetchOptions params`, params);
    //     // 取出callbackQueue.current中的callback
    //     const callbacks = callbackQueue.current;
    //     console.log(`[${props?.label}]debounceFetchOptions callbackQueue`, JSON.parse(JSON.stringify(callbacks)));
    //     callbackQueue.current = [];
    //     fetchOptions(params, ruleParams, fieldName).then(result => {
    //         console.log(`[${props?.label}]debounceFetchOptions result`, result);
    //         while (callbacks.length > 0) {
    //             const callback = callbacks.shift();
    //             if (typeof callback === 'function') {
    //                 callback(result);
    //             }
    //         }
    //     });
    // }, 200), [fieldName]);

    const fetchList = async (params, ruleParams, newFieldName) => {
        let list = []
        if (!(ruleParams && Object.values(ruleParams).some(value => value === "##norequest##"))) {
            setFetching(true)
            const response = await request({ ...params, ruleParams }, form, newFieldName);
            console.log(`[${props?.label}]fetchList response`, response)
            setFetching(false)
            if (response.code > 0) {
                callError && typeof callError === 'function' && callError(response.message);
            }
            else list = response.data?.list || response.data
        }
        return list
    }
    const handleOptions = async (list) => {
        if (Array.isArray(list)) {
            for (let index = 0; index < list.length; index++) {
                const item = list[index];
                if (typeof subRequest == "function") {
                    let subList = []
                    const { data } = await subRequest(item)
                    if (data?.list && Array.isArray(data.list)) {
                        subList = data.list.map(subItem => ({ label: `${item[option_label]}-${subItem[sub_option_label]}`, value: `${item[option_value]}-${subItem[sub_option_value]}` }))
                    }
                    list[index] = { ...item, label: item[option_label], title: item[option_label], options: subList }
                }
                list[index] = { ...item, label: item[option_label], value: item[option_value] }
            }
            if (nOptions.length > 0 && !isEqual(nOptions, list) && !list.some(item => item.value == value?.value)) {
                console.log(`[${props?.label}]handleOptions onChange(undefined)`)
                // 如果选项发生变化，并且选项中没有当前已经选择的数据，则清空当前已经选择的数据(场景举例：出库时已选了库位A，此时如果变更出库仓库，新的仓库已经没有了库位A，则需要清空当前已经选择的库位，否则可能会造成操作错误)
                typeof onChange === 'function' && onChange(undefined)
            }
        }
        return list
    }

    const fetchOptions = async (params, ruleParams, newFieldName) => {
        try {
            let list = await fetchList(params, ruleParams, newFieldName)
            list = await handleOptions(list)
            setNOptions(Array.isArray(list) ? list : [])
            return list
        } catch (error) {
            console.error("fetchOptions", error)
            setFetching(false)
        }
        finally {
        }
    }

    const handleChange = (value) => {
        console.log(`[${props?.label}]handleChange value`, value)
        if (Array.isArray(value)) {
            onChange(value.map(item => nOptions.find(option => option.value === item)))
        }
        else {
            onChange(nOptions.find(item => item.value === value))
        }
    }

    const handleScroll = (event) => {
        const { target } = event;
        if (target.scrollTop + target.clientHeight >= target.scrollHeight - 50) {
            // 滚动到底部，加载更多数据
            loadMoreOptions();
        }
    };


    const loadMoreOptions = async () => {
        if (isAllLoadedRef.current || isLoadingRef.current) return; // 检查是否已加载完毕或正在加载

        isLoadingRef.current = true; // 设置加载状态为 true

        try {
            const nextPageParams = { ...requestParams, page: currentPageRef.current + 1, pageSize: currentPageSize };
            let newOptions = await fetchList(nextPageParams, null, fieldName);
            newOptions = await handleOptions(newOptions);
            if (Array.isArray(newOptions) && newOptions.length > 0) {
                setNOptions(prevOptions => {
                    const existingValues = new Set(prevOptions.map(option => option.value));
                    const filteredNewOptions = newOptions.filter(option => !existingValues.has(option.value));
                    return [...prevOptions, ...filteredNewOptions];
                });
            } else {
                isAllLoadedRef.current = true;
            }
            currentPageRef.current += 1;
        } catch (error) {
            console.error("加载更多选项时出错", error);
        } finally {
            isLoadingRef.current = false; // 重置加载状态
        }
    };
    return addWrapper ? (
        <BaseWrapper {...props}>
            <OriginalSelect
                ref={ref}
                notFoundContent={fetching ? <Spin size="small" /> : null}
                value={value}
                {...props}
                filterOption={false}
                showSearch={request && option_search ? true : false}
                onSearch={request && option_search ? handleSearch : null}
                onChange={handleChange}
                style={{ width: '100%', flex: 1 }}
                options={nOptions}
                onPopupScroll={typeof request === 'function' ? handleScroll : null}
                mode={mode} >

            </OriginalSelect>
            {!props?.disabled && rightIcon}
            {!props?.disabled && typeof rightIconRender === 'function' && rightIconRender({ value, onChange, form, fieldName, recordFieldsChange, getAllWithIds, removeLastFieldsValues })}
        </BaseWrapper>
    ) : (<>
        <OriginalSelect
            ref={ref}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            value={value}
            {...props}
            filterOption={false}
            showSearch={request && option_search ? true : false}
            onSearch={request && option_search ? handleSearch : null}
            onChange={handleChange}
            style={{ width: '100%' }}
            options={nOptions}
            onPopupScroll={typeof request === 'function' ? handleScroll : null}
            mode={mode}  >

        </OriginalSelect>
        {!props?.disabled && rightIcon}
        {!props?.disabled && typeof rightIconRender === 'function' && rightIconRender({ value, onChange, form, fieldName, recordFieldsChange, getAllWithIds, removeLastFieldsValues })}
    </>
    )

})

export { SearchSelect }