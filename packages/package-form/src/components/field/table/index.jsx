import React, { useEffect, useRef, useState } from "react";
import { Button, Form, Input, Popconfirm } from "antd";
import { CopyOutlined, DeleteOutlined } from "@ant-design/icons";
import { BaseWrapper } from "../base.jsx"
import { nanoid } from "nanoid";
import { eventEmitter } from '../../../utils/events'
import { useMemoizedFn } from 'ahooks';
import DragHead from "./drag-head"
import _ from "lodash"

const TableAction = ({ label, subTableIndex, children, subTableHead = false, ...props }) => {
    //fsticky fright-0 
    return <div className="fsticky fright-0 fbg-white fborder-b " style={{
        boxShadow: '-6px 0 6px -6px rgba(50,50,50,0.15)',
        flex: '0 0 50px',
        width: '50px'
    }}>
        <div className="frelative fw-full fh-full fflex fflex-col foverflow-hidden">
            {label && subTableHead && <div className='fh-12 ffont-semibold fbg-[#fafafa] ftext-sm fflex fflex-nowrap ftext-nowrap fjustify-between fitems-center fpx-2 fborder-b'>{label}</div>}
            <div className="fw-full fflex-1 fflex fitems-center fjustify-center fp-2 fbox-border fgap-2">
                {children}
            </div>
        </div>
    </div>
}

const RowID = ({ value, onChange, ...props }) => {
    useEffect(() => {
        if (!value) onChange(nanoid())
    }, [value])
    return (
        <Input {...props} value={value} onChange={onChange} hidden={true} />
    )
}

const TableCol = ({ children, width, hidden, ...props }) => {
    const [sWidth, setSWidth] = useState(0);
    useEffect(() => {
        setSWidth(width);
    }, [width]);
    if (hidden) return children;
    return <div className="fflex-1 fpx-1 fborder-b" style={{ minWidth: `${sWidth}px` }} {...props}>
        {children}
    </div>
}

const Table = ({ children, onTableAddRow, disabled, readonly, onTableRemoveRow, form, fieldName, hideNoData, hideEditMode,initializeFormRender, recordFieldsChange, getTableWithIds, removeLastFieldsValues, mode, isAllowCopy = false, isAllowAdd = true, getDependencyMapItem, ...props }) => {
   
    const [initialWidth, setInitialWidth] = useState([])
    const [headWidths, setHeadWidths] = useState([])
    const tableComponentId = props.componentId || props.__id
    const newidRefs = useRef(React.Children.map(children, () => nanoid()));
    const [init, setInit] = useState(false);
    const name = props.componentId || props.__id
    const childrenIds = React.Children.map(children, (child) => `${name}.${child.props.componentId || child.props.__id}`)
    const rules = []
    const [childrenDesc, setChildrenDesc] = useState([])
    const handleAdd = () => {
        if (!init) setInit(true);
        form?.setFieldValue(fieldName, [{}])
    }
    const [tableId, setTableId] = useState()
    const handleReloadTable = (target) => {
        if (fieldName == target) {
            setTableId(nanoid())
        }
    }
    useEffect(() => {
        setTableId(nanoid())
        eventEmitter.on('reloadTable', handleReloadTable);
        return () => {
            eventEmitter.off('reloadTable', handleReloadTable);
        }
    }, [])
    useEffect(() => {
        // console.log("Table form reload", form)
    }, [form])

    useEffect(() => {
        if (Array.isArray(props?.columnsWidth) && !_.isEqual(props?.columnsWidth, headWidths)) {
            setHeadWidths(props?.columnsWidth)
            setInitialWidth(props?.columnsWidth)
        }
    }, [props?.columnsWidth])

    useEffect(()=>{
       console.log("Table headWidths", headWidths)
    },[headWidths])

    useEffect(()=>{
        let childrenDesc = React.Children.map(children, (child) => {
            const hidden = (child?.props?.calcHidden || !getDependencyMapShow(child?.props?.componentId || child?.props?.__id)) && mode != "desgin";
            return {id:child.props.componentId || child.props.__id,label:child.props.label, hidden }
        })
        console.log("Emit Table childrenDesc", childrenDesc)
        eventEmitter.emit("tableChildrenDesc", childrenDesc)
        setChildrenDesc(childrenDesc)
    },[children])


    const getColumnWidth = (index) => {
        return headWidths[index] || 150
    }
    const handleChangeWidth = (newHeadWidths) => {
        setHeadWidths(newHeadWidths)
    }
    const getAllWithIds = () => {
        if (typeof getTableWithIds === 'function') {
            return getTableWithIds(childrenIds)
        }
        return []
    }

    const getDependencyMapShow = (identifier) => {
        if (typeof getDependencyMapItem == "function") {
            const dependencyMapItem = getDependencyMapItem(`${tableComponentId}.${identifier}`)
            if (dependencyMapItem) return dependencyMapItem?.show
        }
        return true
    }
    const handleCopy = useMemoizedFn((index) => {
        let tableValues = form.getFieldValue(fieldName)
        const copyRow = { ...tableValues[index], __id: nanoid() }
        tableValues.splice(index + 1, 0, copyRow)
        form?.setFieldValue(fieldName, tableValues)

        let changedFields = {}
        let tableName = fieldName;
        for (let i = index + 1; i < tableValues.length; i++) {
            let value = tableValues[i]
            for (let key in value) {
                let changedFieldName = [tableName, i, key]
                changedFields[changedFieldName] = { name: changedFieldName, value: value[key] }
            }
        }
        typeof recordFieldsChange == "function" && recordFieldsChange(changedFields, true)
    })

    const handleRemove = useMemoizedFn((index) => {
        let tableValues = form.getFieldValue(fieldName)
        tableValues.splice(index, 1)
        form?.setFieldValue(fieldName, tableValues)
        typeof onTableRemoveRow === "function" && onTableRemoveRow(childrenIds);
        typeof removeLastFieldsValues === "function" && removeLastFieldsValues(`${name},${index}`, true)
    })
    if (props.isRequired)
        rules.push({ required: true, message: `子表[${props.label}]必须填写` });
    return <div className={"fw-full fflex fflex-col fgap-2"}>
        <Form.List name={name} rules={rules}>
            {(fields, { add, remove }) => {
                if (fields.length === 0 && !hideNoData && !hideEditMode && !init) handleAdd();
                return <div className={"fw-full " + ((hideNoData && fields.length == 0 ) || (hideEditMode && !readonly) ? " fhidden" : "")}>

                    <div className="fw-full frelative fmin-h-20 foverflow-x-auto" style={{ position: 'relative' }}>
                        <DragHead changeWidth={handleChangeWidth} initialWidth={initialWidth} showAction={disabled != true && readonly != true} childrenDesc={childrenDesc}/>
                        {fields.length === 0 && <div key={`tableHead`} className="fborder-b fflex flex-nowrap fmin-w-full ">
                            {React.Children.map(children, (child, childIndex) => {
                                const hidden = (child?.props?.calcHidden || !getDependencyMapShow(child?.props?.componentId || child?.props?.__id)) && mode != "desgin";
                                return <TableCol width={getColumnWidth(childIndex)} key={`row_${0}_col_${childIndex}`} hidden={hidden}>
                                    {hidden ? null : React.cloneElement(child, {
                                        key: `row_${0}_child_${childIndex}`,
                                        subTable: true,
                                        subTableHead: true,
                                        subTableContent: false,
                                    })}
                                </TableCol>
                            })}
                            {disabled != true && readonly != true && <TableAction subTableHead={true} key={`row_${0}_action`} subTable={true} subTableIndex={0} label={"操作"}>
                            </TableAction>}
                        </div>}
                        {fields.map((field, index) => (
                            <div key={`${tableId}.${field.key}`} className=" fflex flex-nowrap fmin-w-full ">
                                <Form.Item hidden={true} name={[field.name, "__id"]}>
                                    <RowID />
                                </Form.Item>
                                {React.Children.map(children, (child, childIndex) => {
                                    let { props } = child;
                                    const col_id = child?.props?.componentId || child?.props?.__id || childIndex;
                                    // if (field?.[col_id] === undefined) field[col_id] = "";
                                    const rules = []
                                    if (props.isRequired)
                                        rules.push({ required: true, message: `${props.label}必须填写` });
                                    if (props.rules)
                                        if (Array.isArray(props.rules)) {
                                            const pattern = props.rules.join("|")
                                            rules.push({ pattern: new RegExp(pattern), message: props.rulesFailMessage ? props.rulesFailMessage : `${props.label}格式错误` })
                                        }
                                        else {
                                            rules.push({ pattern: new RegExp(props.rules), message: props.rulesFailMessage ? props.rulesFailMessage : `${props.label}格式错误` })
                                        }

                                    const { componentId, __id, _componentName, ...otherProps } = child.props;
                                    const componentName = child.type?.displayName || _componentName;
                                    const identifier = componentId || __id;

                                    const hidden = (props.calcHidden || !getDependencyMapShow(identifier)) && mode != "desgin";
                                    return <TableCol width={getColumnWidth(childIndex)} key={`row_${field.key}_col_${childIndex}`} hidden={hidden}>
                                        <Form.Item
                                            style={{ margin: 0 }}
                                            hidden={hidden}
                                            shouldUpdate={(prevValues, curValues) => {
                                                let result = false;
                                                if (
                                                    (componentName === "Field.WithSingleSelect" || componentName === "Field.WithMultipleSelect") &&
                                                    Array.isArray(otherProps.filterRules) &&
                                                    otherProps.filterRules.length > 0
                                                ) {
                                                    // 验证关联表字段是否需要重新渲染
                                                    for (let rule of otherProps.filterRules) {
                                                        let prevFieldValue, curFieldValue;
                                                        if (rule.valueType != "variable") continue
                                                        if ("parent" in rule?.value && rule.value.parent != "") {
                                                            prevFieldValue = prevValues?.[rule?.value?.parent]?.[field.name]?.[rule?.value?.field_key];
                                                            curFieldValue = curValues?.[rule?.value?.parent]?.[field.name]?.[rule?.value?.field_key];
                                                        } else {
                                                            prevFieldValue = prevValues?.[rule.value.field_key];
                                                            curFieldValue = curValues?.[rule.value.field_key];
                                                        }

                                                        if (prevFieldValue !== curFieldValue) {
                                                            result = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                                if (result) {
                                                    newidRefs.current[childIndex] = nanoid();
                                                    console.log("newidRefs.current[childIndex]", newidRefs.current[childIndex])
                                                }
                                                return result;
                                            }}
                                        >
                                            {({ getFieldsValue }) => {
                                                const fieldsValue = getFieldsValue();
                                                // const newid = nanoid()
                                                // console.log(`[${child.props.label}] key`,`row_${field.key}_child_${childIndex}_${newidRefs.current[childIndex]}`    )
                                                return <Form.Item
                                                    label=""
                                                    style={{ marginBottom: 0 }}
                                                    name={[field.name, col_id]}
                                                    rules={rules}
                                                >
                                                    {React.cloneElement(child, {
                                                        // key: `row_${field.key}_child_${childIndex}_${newidRefs.current[childIndex]}`,
                                                        key: `row_${field.key}_child_${childIndex}`,
                                                        subTable: true,
                                                        subTableIndex: index,
                                                        subTableHead: index === 0,
                                                        form,
                                                        shouldUpdateKey: newidRefs.current[childIndex],
                                                        fieldName: [fieldName, field.name, col_id],
                                                        fieldsValue: fieldsValue,
                                                        initializeFormRender,
                                                        recordFieldsChange,
                                                        getAllWithIds: getAllWithIds,
                                                        removeLastFieldsValues
                                                    })}
                                                </Form.Item>
                                            }}
                                        </Form.Item>

                                    </TableCol>
                                })}
                                {disabled != true && readonly != true && <TableAction subTableHead={index == 0} key={`row_${index}_action`} subTable={true} subTableIndex={index} label={"操作"}>
                                    {isAllowCopy && <CopyOutlined className="fcursor-pointer" onClick={() => handleCopy(index)} />}
                                    <Popconfirm
                                        title="删除确认"
                                        description="确定要删除这一行吗？"
                                        onConfirm={() => {
                                            handleRemove(index)
                                        }}
                                        okText="确定"
                                        cancelText="取消"
                                    >
                                        <DeleteOutlined className="fcursor-pointer ftext-red-500" />
                                    </Popconfirm>
                                </TableAction>}
                            </div>
                        ))}
                    </div>
                    {disabled != true && readonly != true && isAllowAdd && <Button onClick={() => {
                        add({ key: nanoid(), })
                        typeof onTableAddRow === "function" && onTableAddRow(childrenIds);
                    }} className="fmy-2">新增一行</Button>}
                </div>
            }}
        </Form.List>
    </div>
}

Table.displayName = "Table"
const TableWrapper = (props) => {
    return (
        <BaseWrapper  {...props} higLabel={true} >
            <Table {...props} />
        </BaseWrapper>
    );
}
TableWrapper.displayName = "Table"
export default TableWrapper;
export { TableCol, TableAction };

export { WithTable } from "./with-table"    
export  { DragHead }