import React, { forwardRef, useEffect } from "react";
import { Form, Row, Col, message } from "antd";
import { ConfigProvider } from 'antd';

import { debounce, isEqual, throttle } from 'lodash';
import { evalFormula } from '../../utils/formula'
import { nanoid } from 'nanoid';
import { eventEmitter } from '../../utils/events'


function batchElements(elements, groupSize, dmap) {
    const groupedElements = [];
    let tempArray = [];

    const fillWithReactElement = (size, array) => {
        const missingElementsCount = size - array.length;
        if (missingElementsCount > 0)
            array.push(...new Array(missingElementsCount).fill(React.createElement('div')));
    };

    for (const element of elements) {
        const { _componentName } = element?.props || {};
        const componentName = element.type?.displayName || _componentName;
        if (componentName && componentName.startsWith('Layout.')) {
            if (tempArray.length > 0) {
                fillWithReactElement(groupSize, tempArray);
                groupedElements.push(tempArray);
                tempArray = [];
            }
            groupedElements.push([element]);
        } else {
            tempArray.push(element);
            const visibleElementsCount = tempArray.filter(el => {
                const identifier = el.props?.componentId || el.props?.__id;
                const isShow = !dmap.has(identifier) || dmap.get(identifier)?.show;
                return isShow && !el?.props?.calcHidden
            }).length;
            if (visibleElementsCount === groupSize) {
                groupedElements.push(tempArray);
                tempArray = [];
            }
        }
    }

    if (tempArray.length > 0) {
        fillWithReactElement(groupSize, tempArray);
        groupedElements.push(tempArray);
    }
    return groupedElements;
}


const FormContainer = forwardRef(({ cols = 1, children, mode = "view" }, ref) => {
    const [form] = Form.useForm();
    const formContentRef = React.useRef(null);
    const [formContent, setFormContent] = React.useState(null);

    const dependencyMap = React.useRef(null);
    const updateFormContent = () => {
        const newFormContent = memoizedRenderChildren();
        if (!isEqual(formContentRef.current, newFormContent)) {
            formContentRef.current = newFormContent;
            setFormContent(newFormContent);
        }
    }
    // 调用setFieldsValue时,进入锁定状态,阻止因字段值变化而触发级联处理
    const lockStatus = React.useRef(0);
    // // 字段变更源跟踪，用于处理双向依赖问题
    // const updateSourceRef = React.useRef(new Map());
    // 更新周期依赖图，用于处理复杂循环依赖 (如 a→b→c→a)
    const dependencyGraphRef = React.useRef({
        // 当前更新周期ID
        currentCycleId: 0,
        // 当前更新周期的依赖路径
        updatePath: [],
        // 最大更新深度限制，避免过深的嵌套引起栈溢出
        MAX_DEPTH: 30,
        // 开始新的更新周期
        startNewCycle: function () {
            this.currentCycleId++;
            this.updatePath = [];
            return this.currentCycleId;
        },
        // 添加依赖路径
        addToPath: function (fieldId) {
            // 限制依赖路径深度，防止栈溢出
            if (this.updatePath.length >= this.MAX_DEPTH) {
                console.warn(`依赖路径深度超过限制(${this.MAX_DEPTH})，可能存在过深嵌套`);
                return;
            }
            if (fieldId) {
                this.updatePath.push(fieldId);
            }
        },
        // 检查是否形成循环
        hasCircularDependency: function (targetFieldId) {
            if (!targetFieldId) return false;
            return this.updatePath.includes(targetFieldId);
        },
        // 获取当前依赖路径
        getCurrentPath: function () {
            return [...this.updatePath];
        },
        // 结束当前字段的处理
        finishField: function () {
            if (this.updatePath.length > 0) {
                this.updatePath.pop();
            }
        }
    });

    React.useImperativeHandle(ref, () => ({
        formRef: form,
        setFieldsValue: (values) => {
            lockStatus.current = 1;
            let formData = { ...values, __id: values?.id }
            for (let key in values) {
                changedFieldsState.current[key] = { name: key, value: values[key] }
                if (dependencyMap.current.has(key) && dependencyMap.current.get(key).componentName == "Field.Table" && Array.isArray(values[key])) {
                    for (let index = 0; index < values[key].length; index++) {
                        const element = values[key][index];
                        for (let elem_key in element) {
                            changedFieldsState.current[[key, index, elem_key]] = { name: [key, index, elem_key], value: element[elem_key] }
                        }
                    }
                }
            }
            form.setFieldsValue(formData)
            debounceHandleFieldsChange()
        },
        initializeFieldVisibility: (reloadFields) => {
            initializeFieldVisibility(reloadFields);
        },
    }), []);

    // 添加节流后的 initializeFormRender
    const throttledInitializeFormRender = React.useCallback(
        throttle(async () => {
            // console.log("throttledInitializeFormRender")
            await initializeDependencyMap();
            console.log("initializeDependencyMap")
            requestAnimationFrame(() => {
                updateFormContent();
            })
            // console.log("throttledInitializeFormRender end")

        }, 300),
        [children, cols]
    );


    useEffect(() => {
        console.log("UseEffect throttledInitializeFormRender")
        throttledInitializeFormRender();
        return () => {
            throttledInitializeFormRender.cancel();
        };
    }, [children, cols]);

    const initializeFormRender = () => {
        throttledInitializeFormRender();
    }

    const lastFormValues = React.useRef(null);
    const getLastFieldValue = (path) => {
        return lastFormValues.current?.[path]
        // let current = lastFormValues.current;
        // for (let i = 0; i < path.length; i++) {
        //     if (current == null) {
        //         return undefined;
        //     }
        //     current = current[path[i]];
        // }
        // return current;
    }
    const initializeDependencyMap = async () => {

        const fields = [];
        function traverse(currentNode, parentNode = null) {
            var componentName = currentNode.type?.displayName || currentNode.props?._componentName;
            const { props } = currentNode;
            if (componentName && (componentName.startsWith('Field.') || componentName.startsWith('Layout.'))) {
                let identifier = props?.componentId || props?.__id;
                let withIds = []
                if (parentNode && parentNode?.props) {
                    identifier = `${parentNode?.props?.componentId || parentNode?.props?.__id}.${identifier}`
                }
                if (props?.withId)
                    withIds.push(props?.withId)
                if (props?.withIds)
                    withIds = [...withIds, ...props?.withIds]

                fields.push({ identifier, withIds, component: currentNode, componentName, fillRules: props?.fillRules });
            }

            if (props?.children) {
                let children = React.Children.toArray(props?.children)
                children.forEach(child => traverse(child, (componentName && (componentName.startsWith('Field.'))) ? currentNode : null));
            }
        }
        dependencyMap.current = new Map();

        console.log(" fields", fields)
        const childrenArray = React.Children.toArray(children);
        for (let index = 0; index < childrenArray.length; index++) {
            const element = childrenArray[index];
            traverse(element)
        }
        fields.forEach(field => {
            const { identifier } = field;
            dependencyMap.current.set(identifier, {
                // children:  childrenArray.filter(item => item.props.withId === identifier || item.props.withIds?.some(item => {
                //     return identifier == item || item.startsWith(identifier + '.')
                // })),
                children: fields.filter(item => item.withIds.some(item => item == identifier)),
                show: true,
                ...field
            });
        });
        // console.log("dependencyMap.current", dependencyMap.current)
        await initializeFieldVisibilityImmediate();
    };
    const initializeFieldVisibilityImmediate = async (reloadFields = false) => {
        console.log("initializeFieldVisibility *************************************")


        const fieldValues = form.getFieldsValue();

        await Promise.all(Array.from(dependencyMap.current.keys()).map(async (key) => {
            await handleFieldsWith(key, fieldValues, true);
        }))
        // for (let key of dependencyMap.current.keys()) {
        //    await handleFieldsWith(key, fieldValues, true);
        // }
        if (reloadFields) updateFormContent();
    };
    // 初始化字段的级联关系
    const initializeFieldVisibility = debounce(async (reloadFields = false) => {
        await initializeFieldVisibilityImmediate(reloadFields);
    }, 100);


    // 计算字段级联关系
    const handleFieldsWith = async (identifier, fieldValues, init = false, fieldId = null) => {
        if (mode == "desgin") {
            console.log("设计模式下不进行字段级联计算")
            return false
        }
        console.log("handleFieldsWith identifier", identifier)
        let needRefresh = false;
        let parentIdentifier = [];
        if (Array.isArray(identifier)) {
            parentIdentifier = [...(identifier.slice(0, -1))]
            identifier = identifier.filter(item => typeof item == "string").join(".")

        }

        // 将标识符标准化为字符串，用于依赖图谱
        const currentFieldId = fieldId || identifier;
        // 检查是否在当前更新链路中已存在，避免循环依赖
        if (dependencyGraphRef.current.hasCircularDependency(currentFieldId)) {
            const currentPath = dependencyGraphRef.current.getCurrentPath();
            console.log(`检测到循环依赖链路: ${[...currentPath, currentFieldId].join(' -> ')}`);
            return false;
        }

        // 将当前字段添加到依赖路径
        dependencyGraphRef.current.addToPath(currentFieldId);

        try {
            console.log("dependencyMap.current", dependencyMap.current)
            console.log("dependencyMap.current identifier", identifier)
            if (dependencyMap.current.has(identifier)) {
                const dependent = dependencyMap.current.get(identifier)
                const dependentChildren = dependent.children;


                if (!init && dependent?.fillRules && Array.isArray(dependent?.fillRules) && dependent?.fillRules.length > 0) {
                    handleFillRules(identifier, parentIdentifier, fieldValues, dependent?.fillRules)
                }
                for (let index = 0; index < dependentChildren.length; index++) {
                    const child = dependentChildren[index];
                    if (child.component.props.withVisibleFunc && typeof child.component.props.withVisibleFunc === 'function') {
                        let needRefresh_ = handleFieldsVisibleFunc(fieldValues, child, parentIdentifier)
                        needRefresh = needRefresh || needRefresh_
                    }

                    if (child.component.props.withVisible) {
                        let needRefresh_ = await handleFieldsVisible(fieldValues, child, parentIdentifier, dependent?.componentName)
                        needRefresh = needRefresh || needRefresh_
                    }


                    if (!init && child.component.props.withFill)
                        await handleFieldsWithFill(fieldValues, child, parentIdentifier, dependent?.componentName, currentFieldId)

                }
            }
        } finally {
            // 在处理完毕后，从依赖路径中移除当前字段
            dependencyGraphRef.current.finishField();
        }

        return needRefresh;
    };
    const removeLastFieldsValues = (name, isTable = false) => {
        if (!lastFormValues.current) return
        if (isTable) {
            const lastFormValuesKeys = Object.keys(lastFormValues.current)
            for (let index = 0; index < lastFormValuesKeys.length; index++) {
                const key = lastFormValuesKeys[index];
                if (key.includes(`${name},`)) {
                    delete lastFormValues.current[key]
                }
            }
        }
        else delete lastFormValues.current[name]
    }

    const recordFieldChange = (fieldName, fieldValue) => {
        if (!changedFieldsState.current) {
            changedFieldsState.current = {};
        }
        changedFieldsState.current[fieldName] = fieldValue;
    }
    const recordFieldsChange = (changedFields, handleChange = false) => {
        console.log("recordFieldsChange", changedFields, handleChange)
        var changedKeys = Object.keys(changedFields)
        if (changedKeys.length > 0) {
            changedKeys.forEach(key => {
                recordFieldChange(key, changedFields[key])

            })
            if (handleChange) debounceHandleFieldsChange();
        }
    }

    // 处理填充规则
    const handleFillRules = (current_identifier, parentIdentifier, fieldValues, fillRules) => {
        // 获取当前变更的字段数据
        let current_value = getParamValue("fieldsValue", current_identifier, fieldValues, null)
        let changedFields = {}

        // 将标识符标准化为字符串
        const sourceFieldId = Array.isArray(current_identifier)
            ? current_identifier.filter(item => typeof item == "string").join(".")
            : current_identifier;

        let idGroups = []
        for (let index = 0; index < fillRules.length; index++) {
            const rule = fillRules[index];
            let { source, target } = rule
            let source_value = current_value?.[source]
            let setValue = source_value

            // 将sourceField格式化为字符串形式，用于依赖跟踪
            const sourceField = Array.isArray(current_identifier)
                ? current_identifier.filter(item => typeof item == "string").join(".")
                : current_identifier;

            // 格式化目标字段
            const targetField = Array.isArray(target)
                ? target.filter(item => typeof item == "string").join(".")
                : target;

            // // 检查循环依赖 - 使用更高级的依赖图检测
            // if (dependencyGraphRef.current.hasCircularDependency(targetField)) {
            //     const currentPath = dependencyGraphRef.current.getCurrentPath();
            //     console.log(`检测到填充规则中的循环依赖链路: ${[...currentPath, targetField].join(' -> ')}`);
            //     continue; // 跳过这条规则
            // }


            //// 添加到依赖路径
            // dependencyGraphRef.current.addToPath(targetField);


            // 子表
            if (rule?.type == 1) {
                if (dependencyMap.current.has(target)) {
                    let tableChildren = dependencyMap.current.get(target)?.component?.props?.children
                    if (Array.isArray(tableChildren) && tableChildren.length > 0) idGroups.push(tableChildren.map(item => `${source}.${item?.props?.componentId || item?.props?.__id}`))

                }
                if (source_value && typeof source_value == "string") {
                    try {
                        source_value = JSON.parse(source_value)
                    } catch (error) {
                        console.error("error end", error)
                    }
                }

                if (!Array.isArray(source_value)) return
                let target_value = source_value.map((item, value_index) => {
                    let target_item_value = {}
                    if (rule?.subRules && Array.isArray(rule?.subRules) && rule?.subRules.length > 0)
                        for (let index = 0; index < rule?.subRules.length; index++) {
                            const { source: subSource, target: subTarget } = rule?.subRules[index];
                            target_item_value[subTarget] = item?.[subSource]
                            let changedField = { name: [target, value_index, subTarget], value: item?.[subSource] }
                            changedFields[changedField.name] = changedField;
                        }
                    return target_item_value
                })
                setValue = target_value
                removeLastFieldsValues(target, true)
                eventEmitter.emit('reloadTable', target);
            }
            // 同级字段
            else {
                let withFillIndex = 0
                let withFillGroup = ""
                if (parentIdentifier && Array.isArray(parentIdentifier) && parentIdentifier.length > 1) {
                    withFillGroup = parentIdentifier.filter(item => typeof item == "string").join(".")
                    withFillIndex = parentIdentifier[parentIdentifier.length - 1]
                    target = [...parentIdentifier, target]
                    if (Array.isArray(current_value) && current_value.length > withFillIndex) {
                        setValue = current_value[withFillIndex]?.[source]
                    }
                }
                changedFields[target] = { name: Array.isArray(target) ? target : [target], value: setValue };
            }
            if (rule?.type == 1) {
                form.setFieldValue(target, undefined);
            }
            form.setFieldValue(target, setValue)

            // 处理完当前字段后从依赖路径中移除
            // dependencyGraphRef.current.finishField();
        }

        setTimeout(() => {
            recordFieldsChange(changedFields, true)
            // console.log("idGroups", idGroups)
            if (idGroups.length > 0) idGroups.forEach(ids => { if (Array.isArray(ids) && ids.length > 0) handleTableAddRow(ids) })
        }, 0)
    }
    // 处理级联显示隐藏 @return {boolean} 是否需要重新渲染表单的字段
    const handleFieldsVisibleFunc = (fieldValues, child, parentIdentifier) => {
        let needRefresh = false;
        const childShouldBeVisible = child?.component?.props?.withVisibleFunc(fieldValues);
        const childIdentifier = child?.identifier;
        if (dependencyMap.current.has(childIdentifier)) {
            const childData = dependencyMap.current.get(childIdentifier);
            if (childData.show !== childShouldBeVisible) {
                childData.show = childShouldBeVisible;
                dependencyMap.current.set(childIdentifier, childData);
                needRefresh = true;
            }
        }
        return needRefresh
    }
    // 处理级联显示隐藏
    const handleFieldsVisible = async (fieldValues, child, parentIdentifier, componentName) => {
        let needRefresh = false;
        const withFill = child?.component?.props.withVisible;
        const withDataFetch = child?.component?.props.withDataFetch;
        let withFillIndex = 0
        let withFillGroup = ""
        let childIdentifier = child.identifier;

        // if (parentIdentifier && Array.isArray(parentIdentifier) && parentIdentifier.length > 1) {
        //     withFillGroup = parentIdentifier.filter(item => typeof item == "string").join(".")
        //     withFillIndex = parentIdentifier[parentIdentifier.length - 1]
        //     if (childIdentifier.startsWith(`${withFillGroup}.`)) {
        //         childIdentifier = [...parentIdentifier, childIdentifier.replace(`${withFillGroup}.`, "")]
        //     }
        // }
        // // 被依赖字段不是子表字段，依赖字段是子表字段的情况
        // else if (childIdentifier.indexOf(".") >= 0) {
        //     childIdentifier = childIdentifier.split(".")
        //     let table_values = getParamValue("fieldsValue", childIdentifier[0], fieldValues, [])
        //     if (Array.isArray(table_values) && table_values.length > 0)
        //         for (let index = 0; index < table_values.length; index++) {
        //             await handleFieldsVisible(fieldValues, child, [childIdentifier[0], index], componentName)
        //         }
        //     return
        // }
        let withDatas = [];
        // 先处理依赖数据
        if (withFill?.withData && withFill?.withData.length > 0 && withDataFetch && typeof withDataFetch === 'function') {
            for (let index = 0; index < withFill?.withData.length; index++) {
                const element = withFill?.withData[index];
                let params = {}
                params.tableName = element.withTable.table_name
                params.filter = {}
                for (let index = 0; index < element.withCondition.length; index++) {
                    const { value: condition_value, column: condition_column } = element.withCondition[index];
                    let filter_value = getParamValue(condition_value.group_key, condition_value.field_key, fieldValues, withDatas)
                    if (Array.isArray(filter_value)) {
                        if (Array.isArray(childIdentifier) && filter_value.length > withFillIndex) {
                            filter_value = filter_value[withFillIndex]
                        }
                    }
                    if (componentName == "Field.WithSingleSelect" && filter_value && filter_value?.value) filter_value = filter_value.value
                    params.filter[condition_column.column_name] = filter_value
                }

                // 访问接口获取数据
                const response = await withDataFetch(params)
                if (response.code === 0 && response.data.list) {
                    withDatas.push({
                        id: element.id,
                        data: response.data.list
                    })
                }
            }
        }

        // 构造计算公式
        let formula;
        if (withFill.value && withFill.value.length > 0) {
            formula = withFill.value.map(item => {
                let result = "";
                const { insert, attributes } = item
                if (typeof insert !== "string") {
                    if (insert?.span && attributes && attributes.tagKey && attributes.id) {
                        result = getParamValue(attributes.tagKey, attributes.id, fieldValues, withDatas)
                        if (Array.isArray(result)) {
                            if (Array.isArray(childIdentifier) && result.length > withFillIndex) {
                                result = result[withFillIndex]
                            }
                            if (typeof result === "object" && result !== null)
                                result = JSON.stringify(result)
                        }
                        else if (typeof result === "object" && result !== null) {
                            result = JSON.stringify(result)
                        }
                        else if (result.length > 0) result = `"${result}"`
                    }
                }
                else result = insert
                return result
            })
        }
        if (dependencyMap.current.has(childIdentifier)) {
            const childData = dependencyMap.current.get(childIdentifier);
            if (formula && formula.length > 0) {
                const formulaResult = (evalFormula(formula) == "true");
                // console.log(`${childIdentifier} 计算公式:`, formula)
                // console.log(`${childIdentifier} 计算结果:`, formulaResult)
                // console.log(`${childIdentifier} childData.show:`, childData.show)
                if (childData.show != formulaResult) {
                    childData.show = formulaResult;
                    dependencyMap.current.set(childIdentifier, childData);
                    needRefresh = true;
                }
            }
        }
        return needRefresh
    }
    // 处理级联数据源
    // 处理级联填充
    const handleFieldsWithFill = async (fieldValues, child, parentIdentifier, componentName, sourceFieldId) => {
        // console.log("handleFieldsWithFill child.identifier", child.identifier)
        // console.log("handleFieldsWithFill parentIdentifier", parentIdentifier)
        console.log("handleFieldsWithFill componentName", componentName)
        const withFill = child?.component?.props.withFill;
        const withDataFetch = child?.component?.props.withDataFetch;
        let withFillIndex = 0
        let withFillGroup = ""
        let childIdentifier = child.identifier;
        if (parentIdentifier && Array.isArray(parentIdentifier) && parentIdentifier.length > 1) {
            withFillGroup = parentIdentifier.filter(item => typeof item == "string").join(".")
            withFillIndex = parentIdentifier[parentIdentifier.length - 1]
            if (childIdentifier.startsWith(`${withFillGroup}.`)) {
                childIdentifier = [...parentIdentifier, childIdentifier.replace(`${withFillGroup}.`, "")]
            }
        }
        // 被依赖字段不是子表字段，依赖字段是子表字段的情况
        else if (childIdentifier.indexOf(".") >= 0) {
            childIdentifier = childIdentifier.split(".")
            let table_values = getParamValue("fieldsValue", childIdentifier[0], fieldValues, [])
            if (Array.isArray(table_values) && table_values.length > 0)
                for (let index = 0; index < table_values.length; index++) {
                    await handleFieldsWithFill(fieldValues, child, [childIdentifier[0], index], componentName, sourceFieldId)
                }

            return
        }

        // 将目标字段ID标准化为字符串
        const targetFieldId = Array.isArray(childIdentifier)
            ? childIdentifier.join(".")
            : childIdentifier;

        // 检查是否在当前依赖链中已存在，避免循环依赖
        if (dependencyGraphRef.current.hasCircularDependency(targetFieldId)) {
            const currentPath = dependencyGraphRef.current.getCurrentPath();
            console.log(`检测到公式计算中的循环依赖链路: ${[...currentPath, targetFieldId].join(' -> ')}`);
            return;
        }

        // // 将当前字段添加到依赖路径
        // dependencyGraphRef.current.addToPath(targetFieldId);

        try {
            let withDatas = [];
            // 先处理依赖数据
            if (withFill?.withData && withFill?.withData.length > 0 && withDataFetch && typeof withDataFetch === 'function') {
                for (let index = 0; index < withFill?.withData.length; index++) {
                    const element = withFill?.withData[index];
                    let params = {}
                    params.tableName = element.withTable.table_name
                    params.filter = {}
                    for (let index = 0; index < element.withCondition.length; index++) {
                        const { value: condition_value, column: condition_column } = element.withCondition[index];
                        let filter_value = getParamValue(condition_value.group_key, condition_value.field_key, fieldValues, withDatas)
                        if (Array.isArray(filter_value)) {
                            if (Array.isArray(childIdentifier) && filter_value.length > withFillIndex) {
                                filter_value = filter_value[withFillIndex]
                            }
                        }
                        if (filter_value && typeof filter_value === "string") {
                            try {
                                filter_value = JSON.parse(filter_value)
                            } catch (error) {
                            }
                        }
                        if (filter_value?.value) filter_value = filter_value.value
                        // if (componentName == "Field.WithSingleSelect" && filter_value) {
                        //     if (typeof filter_value === "string") {
                        //         filter_value = JSON.parse(filter_value)
                        //     }
                        //     filter_value = filter_value?.value
                        // }
                        params.filter[condition_column.column_name] = filter_value
                    }

                    // 访问接口获取数据
                    const response = await withDataFetch(params)
                    if (response.code === 0 && response.data.list) {
                        withDatas.push({
                            id: element.id,
                            data: response.data.list
                        })
                    }
                }
            }

            // 构造计算公式
            let formula;
            if (withFill.value && withFill.value.length > 0) {
                formula = withFill.value.map(item => {
                    let result = "";
                    const { insert, attributes } = item
                    if (typeof insert !== "string") {
                        if (insert?.span && attributes && attributes.tagKey && attributes.id) {
                            result = getParamValue(attributes.tagKey, attributes.id, fieldValues, withDatas)
                            if (Array.isArray(result)) {
                                if (Array.isArray(childIdentifier) && result.length > withFillIndex) {
                                    result = result[withFillIndex]
                                }
                                if (typeof result === "object" && result !== null)
                                    result = JSON.stringify(result)
                            }
                            else if (typeof result === "object" && result !== null) {
                                result = JSON.stringify(result)
                            }
                            else if (result.length > 0) result = `"${result}"`
                        }
                    }
                    else result = insert
                    return result
                })
            }

            if (formula && formula.length > 0) {
                const formulaResult = evalFormula(formula);
                console.log(`${childIdentifier} 计算公式:`, formula)
                console.log(`${childIdentifier} 计算结果:`, formulaResult)


                form.setFieldValue(childIdentifier, formulaResult)
                await handleFieldsWith(childIdentifier, form.getFieldsValue(), false, targetFieldId)
            }
        } finally {
            // 处理完毕后，从依赖路径中删除当前字段
            // dependencyGraphRef.current.finishField();
        }
    }

    const getParamValue = (tagKey, id, fieldValues, withDatas) => {
        let result = "";
        // 从当前表单字段取值
        if (tagKey == "fieldsValue") {
            if (id.indexOf(".") >= 0) {
                const [parentKey, childKey] = id.split(".");
                const parentValue = fieldValues?.[parentKey] || [];
                if (Array.isArray(parentValue))
                    result = parentValue.map(item => {
                        return item?.[childKey] ?? ""
                    })
                else result = parentValue?.[childKey] ?? ""
            }
            else result = fieldValues?.[id] ?? ""
        }
        // 从依赖数据取值
        else {
            let withData = withDatas.find(item => item.id === tagKey)
            if (withData && withData.data && withData.data.length > 0) {
                // 暂时只取一条数据，后续再想 sum 函数等问题
                result = withData.data[0]?.[id] ?? ""
            }
        }

        return result
    }


    const changedFieldsState = React.useRef({});
    const changedValuesState = React.useRef({});
    const timeoutRef = React.useRef(null);

    // 注意:初始化 setFieldsValue 的时候，如果没有更新lastFormValues，或者请求过慢，没有连上500毫秒的防抖，可能会造成数据加载异常（字段变化，填充的连锁反应，造成数据更新成非setFieldsValue的数据）
    const debounceHandleFieldsChange = React.useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        const delay = lockStatus.current === 1 ? 500 : 100;

        timeoutRef.current = setTimeout(async () => {
            // 开始新的更新周期
            dependencyGraphRef.current.startNewCycle();

            // 创建当前变更的快照，避免处理过程中的并发修改
            const currentChanges = {...changedFieldsState.current};
            // 立即重置变更状态，为新的变更做准备
            changedFieldsState.current = {};
            
            const fieldValues = form.getFieldsValue();
            const lockStatus_ = lockStatus.current;
            lockStatus.current = 0
            let needRefresh = false;
            if (!lastFormValues.current) lastFormValues.current = {}

            // 创建已处理字段集合，用于避免多次处理同一字段
            const processedFields = new Set();
            for (let key in currentChanges) {
                try {
                    let field = currentChanges[key];
                    if (!isEqual(field.value || "", getLastFieldValue(field.name) || "")) {
                        if (lockStatus_ != 1) {
                            // 获取字段标识符（字符串形式）
                            const fieldId = Array.isArray(field.name)
                                ? field.name.map(item => typeof item == "string" ? item : item.toString()).join(".")
                                : field.name;

                            // 跳过已处理的字段
                            if (processedFields.has(fieldId)) {
                                console.log("跳过已处理的字段 processedFields", fieldId)
                                continue;
                            }
                            processedFields.add(fieldId);

                            // 处理字段依赖关系，传递字段ID和依赖图谱
                            let needRefresh_ = await handleFieldsWith(field.name, fieldValues, false, fieldId);
                            needRefresh = needRefresh || needRefresh_;
                        }
                        lastFormValues.current[field.name] = field.value;
                    }
                } catch (error) {
                    console.log("debounceHandleFieldsChange error", error)
                }
            }
            if (needRefresh) {
                console.log("needRefresh", needRefresh)
                updateFormContent();
            }
        }, delay);
    }, []);

    const handleFieldsChange = React.useCallback((changedFields) => {
        console.log("handleFieldsChange (disabled, using handleValuesChange instead)", JSON.stringify(changedFields))
        // 已禁用，改为使用 handleValuesChange 来处理字段变更
        // 这样可以避免重复处理和多余的触发
    }, []);
    const handleValuesChange = React.useCallback((changedValues, allValues) => {
        console.log("handleValuesChange changedValues", JSON.stringify(changedValues))
        console.log("handleValuesChange allValues", JSON.stringify(allValues))
        
        // 将 changedValues 转换为类似 handleFieldsChange 的格式
        const convertToFieldsFormat = (values, parentPath = []) => {
            const fieldsArray = [];
            
            for (let fieldName in values) {
                const fieldValue = values[fieldName];
                const currentPath = [...parentPath, fieldName];
                
                // 处理数组类型（如table字段）
                if (Array.isArray(fieldValue)) {
                    fieldValue.forEach((item, index) => {
                        if (typeof item === 'object' && item !== null) {
                            // 递归处理数组中的对象
                            const subFields = convertToFieldsFormat(item, [...currentPath, index]);
                            fieldsArray.push(...subFields);
                        } else {
                            // 数组中的基本类型值
                            fieldsArray.push({
                                touched: true,
                                validating: false,
                                errors: [],
                                warnings: [],
                                name: [...currentPath, index],
                                validated: false,
                                value: item
                            });
                        }
                    });
                } else {
                    // 对于非数组类型（包括对象和基本类型），都作为完整的字段值处理
                    // 这样可以正确处理像 {label: "选项2", value: "2"} 这样的对象值
                    fieldsArray.push({
                        touched: true,
                        validating: false,
                        errors: [],
                        warnings: [],
                        name: currentPath,
                        validated: false,
                        value: fieldValue
                    });
                }
            }
            
            return fieldsArray;
        };
        
        // 转换为类似 handleFieldsChange 的格式
        const convertedFields = convertToFieldsFormat(changedValues);
        console.log("handleValuesChange converted to fields format", JSON.stringify(convertedFields));
        
        // 使用转换后的数据，复用原有的处理逻辑
        if (convertedFields.length > 0) {
            convertedFields.forEach(field => {
                if (field.name && field.name.length > 0) {
                    changedFieldsState.current[field.name] = field;
                }
            });
            
            console.log("handleValuesChange changedFieldsState", changedFieldsState.current);
            debounceHandleFieldsChange();
        }
    }, []);
  
    const getTableWithIds = (ids) => {
        let withAllIds = []
        ids.forEach(id => {
            if (!dependencyMap.current.has(id)) return
            let component = dependencyMap.current.get(id)
            if (component.withIds.length <= 0) return
            withAllIds.push(...(component.withIds.filter(withid => !withAllIds.includes(withid))))
        })
        withAllIds = withAllIds.filter(item => {
            var withValue = form.getFieldValue(item)
            if (typeof withValue != "number" && !withValue) return false
            return true
        })
        return withAllIds
    }
    const handleTableAddRow = (ids) => {
        let withAllIds = getTableWithIds(ids)
        const fieldValues = form.getFieldsValue();
        setTimeout(async () => {
            for (let index = 0; index < withAllIds.length; index++) {
                const withid = withAllIds[index];
                await handleFieldsWith(withid, fieldValues);
            }
        }, 0);
    }
    const handleTableRemoveRow = async (ids) => {
        const fieldValues = form.getFieldsValue();
        setTimeout(async () => {
            for (let index = 0; index < ids.length; index++) {
                const id = ids[index];
                await handleFieldsWith(id, fieldValues);
            }
        }, 0);
    }

    const shouldUpdateKey = React.useRef({})

    const getDependencyMapItem = (identifier) => {
        if (dependencyMap.current && dependencyMap.current.has(identifier)) {
            return dependencyMap.current.get(identifier)
        }
        return null
    }
    const memoizedRenderChildren = () => {
        console.log("memoizedRenderChildren", dependencyMap.current)
        if (dependencyMap.current) {
            return renderChildren();
        }
        return null;
    }
    const renderChildren = () => {
        console.log("renderChildren")
        const renderKey = nanoid()
        const childrenArray = React.Children.toArray(children);
        console.log("childrenArray", childrenArray)
        const groupedChildren = batchElements(
            childrenArray,
            // childrenArray.filter(child => {
            //     const identifier = child.props.componentId || child.props.__id;
            //     return !dependencyMap.current.has(identifier) || dependencyMap.current.get(identifier)?.show;
            // }),
            cols,
            dependencyMap.current
        );







        return groupedChildren.map((group, index) => (
            <Row key={`row-${index}`} gutter={[24, 24]}>
                {group.map((child, index) => {
                    const { componentId, __id, _componentName, ...props } = child.props;
                    const componentName = child.type?.displayName || _componentName;
                    const identifier = componentId || __id;
                    if (!(identifier in shouldUpdateKey.current)) {
                        shouldUpdateKey.current[identifier] = nanoid()
                    }
                    const isLayoutComponent = componentName && componentName.startsWith('Layout.');
                    const isTable = componentName && componentName == 'Field.Table';
                    const isShow = !dependencyMap.current.has(identifier) || dependencyMap.current.get(identifier)?.show;
                    const hidden = (props.calcHidden || !isShow) && mode != "desgin";

                    const rules = []
                    if (props.isRequired)
                        rules.push({ required: true, message: `${props.label}必须填写` });
                    if (props.rules)
                        if (Array.isArray(props.rules)) {
                            const pattern = props.rules.join("|")
                            rules.push({ pattern: new RegExp(pattern), message: props.rulesFailMessage ? props.rulesFailMessage : `${props.label}格式错误` })
                        }
                        else {
                            rules.push({ pattern: new RegExp(props.rules), message: props.rulesFailMessage ? props.rulesFailMessage : `${props.label}格式错误` })
                        }
                    const onCustomChange = (value) => {
                        form.setFieldValue(identifier, value)
                    }

                    let childComponent
                    if (isTable || isLayoutComponent) {
                        childComponent = React.cloneElement(child, { onTableAddRow: handleTableAddRow, getTableWithIds, onTableRemoveRow: handleTableRemoveRow, removeLastFieldsValues, form: form, fieldName: identifier, onCustomChange, initializeFormRender, mode, recordFieldsChange, getDependencyMapItem, renderKey })
                    }
                    else if (componentName === "Field.WithSingleSelect" || componentName === "Field.WithMultipleSelect" || componentName === "Show.WithTable") {
                        childComponent = <Form.Item
                            hidden={hidden}
                            style={{ margin: 0 }}
                            shouldUpdate={(prevValues, curValues) => {
                                let result = false;
                                if (Array.isArray(props.filterRules) &&
                                    props.filterRules.length > 0
                                ) {
                                    // 验证关联表字段是否需要重新渲染
                                    for (let rule of props.filterRules) {
                                        if (rule.valueType != "variable") continue
                                        let prevFieldValue, curFieldValue;

                                        prevFieldValue = prevValues?.[rule.value.field_key];
                                        curFieldValue = curValues?.[rule.value.field_key];
                                        if (prevFieldValue !== curFieldValue) {
                                            result = true;
                                            break;
                                        }
                                    }
                                }
                                if (result) {
                                    shouldUpdateKey.current[identifier] = nanoid()
                                    console.log(identifier, nanoid())
                                }
                                return result;
                            }}
                        >
                            {({ getFieldsValue }) => {
                                const fieldsValue = getFieldsValue();
                                let element = React.cloneElement(child, {
                                    onTableAddRow: handleTableAddRow, onTableRemoveRow: handleTableRemoveRow, removeLastFieldsValues, form: form, fieldName: identifier, onCustomChange, fieldsValue, initializeFormRender, recordFieldsChange,
                                    shouldUpdateKey: shouldUpdateKey.current[identifier],
                                })
                                return componentName === "show.WithTable" ? <>{element}</> : <Form.Item
                                    style={{ marginBottom: 0 }}
                                    label=""
                                    name={identifier}
                                    rules={isShow ? rules : []}
                                >
                                    {element}
                                </Form.Item>
                            }}
                        </Form.Item>
                    } else {
                        childComponent = <Form.Item
                            hidden={hidden}
                            style={{ marginBottom: 0 }}
                            label=""
                            name={identifier}
                            rules={isShow ? rules : []}
                        >
                            {React.cloneElement(child, { form: form, fieldName: identifier, onCustomChange, initializeFormRender, recordFieldsChange, removeLastFieldsValues })}
                        </Form.Item>
                    }
                    return (
                        <Col
                            key={identifier || `col-${index}`}
                            span={hidden ? 0 : (isLayoutComponent ? 24 : 24 / cols)}
                            style={{ marginBottom: 0 }}
                        >
                            {childComponent}
                        </Col>
                    );
                })}
            </Row>
        ));
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        rowHoverBg: '#ebebeb',
                        // "fontSize": 14,
                        // "cellPaddingBlock": 6
                    },
                },
                token: {
                    colorBgContainerDisabled: 'rgba(0, 0, 0, 0.02)', // 设置更浅的灰色背景
                    colorTextDisabled: '#333',
                }
            }}
        >
            <Form form={form} className={"form-container fp-0 fw-full fh-full box-border  fflex fflex-col " + (mode == "desgin" ? " fp-6" : "")} onFieldsChange={handleFieldsChange} onValuesChange={handleValuesChange}>
                <Form.Item name="__id" hidden={true}>
                    <input type="hidden" />
                </Form.Item>
                {formContent}
            </Form>
        </ConfigProvider>
    );
});

export function withWrap(Component) {
    return forwardRef((props, ref) => <Component {...props} ref={ref} forwardedRef={ref} />);
}

export class FormContainerClass extends React.PureComponent {
    render() {
        const { forwardedRef, ...otherProps } = this.props;
        return <FormContainer {...otherProps} ref={forwardedRef} />
    }
}
const FormContainerWrapper = withWrap(({ forwardedRef, ...props }) => <FormContainer {...props} ref={forwardedRef} />);

export { LayoutFormRow, LayoutFormGroupTitle } from './layout';
export { FormContainer, FormContainerWrapper };
