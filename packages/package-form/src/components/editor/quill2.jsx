import React, { useEffect, useRef } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css'; // 引入 Quill 的主题样式

// 定义工具栏选项，可以根据需要自定义
const toolbarOptions = [
  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
  ['bold', 'italic', 'underline', 'strike'], // 粗体、斜体、下划线、删除线
  [{ 'list': 'ordered'}, { 'list': 'bullet' }], // 有序列表、无序列表
  [{ 'script': 'sub'}, { 'script': 'super' }], // 上标、下标
  [{ 'indent': '-1'}, { 'indent': '+1' }], // 缩进
  [{ 'direction': 'rtl' }], // 文字方向
  [{ 'size': ['small', false, 'large', 'huge'] }], // 字号
  [{ 'color': [] }, { 'background': [] }], // 字体颜色、背景颜色
  [{ 'font': [] }], // 字体
  [{ 'align': [] }], // 对齐方式
  ['clean'], // 清除格式
  ['link', 'image', 'video'] // 链接、图片、视频
];

const QuillEditor = ({ value, onChange, placeholder = '请输入内容...', height = '300px' }) => {
  const editorContainerRef = useRef(null); // Ref 指向编辑器的容器 div
  const quillInstance = useRef(null);

  useEffect(() => {
    let quillEditorDiv; // Quill 实际初始化的 div
    if (editorContainerRef.current && !quillInstance.current) {
      // 创建一个 div 给 Quill 初始化，这样 Snow 主题的工具栏会和它关联
      // Snow 主题通常会将工具栏放在编辑器 div 的前面
      quillEditorDiv = document.createElement('div');
      editorContainerRef.current.appendChild(quillEditorDiv);

      quillInstance.current = new Quill(quillEditorDiv, {
        modules: {
          toolbar: toolbarOptions // 直接使用定义的工具栏选项
        },
        theme: 'snow',
        placeholder: placeholder,
      });

      // 设置初始内容
      if (value) {
        quillInstance.current.clipboard.dangerouslyPasteHTML(value);
      }

      // 监听内容变化
      quillInstance.current.on('text-change', (delta, oldDelta, source) => {
        if (source === 'user') {
          const html = quillInstance.current.root.innerHTML;
          onChange?.(html);
        }
      });
    }

    return () => {
      if (quillInstance.current) {
        quillInstance.current.off('text-change');
        quillInstance.current = null;
      }
      // 清理 editorContainerRef 内部所有由 Quill 创建的 DOM
      if (editorContainerRef.current) {
        editorContainerRef.current.innerHTML = '';
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 依赖项为空，确保只在挂载和卸载时运行

  // 当外部传入的 value 变化时，更新编辑器内容
  useEffect(() => {
    if (quillInstance.current && value !== undefined) {
      const currentEditorHTML = quillInstance.current.root.innerHTML;
      if (value !== currentEditorHTML) {
        const selection = quillInstance.current.getSelection();
        quillInstance.current.clipboard.dangerouslyPasteHTML(value);
        if (selection) {
          // 尝试恢复光标位置，如果直接粘贴HTML导致光标丢失
          quillInstance.current.setSelection(selection.index, selection.length);
        }
      }
    }
  }, [value]);

  // editorContainerRef 的高度由 height prop 控制
  // Quill 的 Snow 主题会自动在其内部管理编辑器区域和工具栏的布局
  return <div ref={editorContainerRef} style={{ height: height }} />;
};

export default QuillEditor;
