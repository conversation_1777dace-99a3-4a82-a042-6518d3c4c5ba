import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, FormContainerWrap<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Editor<PERSON><PERSON><PERSON>, Editor<PERSON>ang, EditorWang2, EditorWang3, <PERSON><PERSON><PERSON><PERSON>, WangEditorNext, Show } from './components';
import './App.css';
import { Button, Input, Select } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import Draggable from 'react-draggable';
import { throttle, debounce } from 'lodash';
import { SortList } from "./components"
import { TinyMCEEditor } from './components'
import { PrinterOutlined } from '@ant-design/icons';
const searchSelectOptions = [
  { id: 1, name: "1111" },
  { id: 2, name: "2222" },
  { id: 3, name: "3333" },
  { id: 4, name: "4444" },
  { id: 5, name: "5555" },
  { id: 6, name: "6666" },
  { id: 7, name: "7777" },
  { id: 8, name: "8888" },
  { id: 9, name: "9999" },
]
const searchSelectRequest = async (params) => {
  // console.log("params", params)
  return await new Promise((resolve) => {
    setTimeout(() => {
      if (params && params?.name) {
        resolve({ code: 0, data: searchSelectOptions.filter(item => item.name.includes(params.name)) })
      }
      resolve({ code: 0, data: searchSelectOptions })
    }, 20);
  })
}
const DraggableBtn = () => {
  const [dragging, setDragging] = useState(false);

  const handleStart = (e) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrag = () => {
    setDragging(true);
  };

  const handleStop = (e) => {
    e.preventDefault();
    if (!dragging) {
      alert('Button clicked!');
    }
  };

  return (
    <Draggable
      onStart={handleStart}
      onDrag={handleDrag}
      onStop={handleStop}
    >
      <button className="floating-button">Drag me!</button>
    </Draggable>
  );
}

const treeData = [
  {
    title: '0-1',
    key: '0-1',
    children: [
      { title: '0-1-0-0', key: '0-1-0-0' },
      { title: '0-1-0-1', key: '0-1-0-1' },
      { title: '0-1-0-2', key: '0-1-0-2' },
    ],
  },
  {
    title: '0-2',
    key: '0-2',
  },
];

function App() {
  const formRef = React.createRef();
  const testRef = React.useRef()
  const [testCalcHidden, setTestCalcHidden] = useState(false);
  const [readonly, setReadonly] = useState(true);

  const testdebounce = useCallback(debounce((param1, param2) => {
    console.log("testdebounce param1", param1)
    console.log("testdebounce param2", param2)
  }, 200))



  const [cols, setCols] = React.useState(3);

  const getFormFields = () => {
    console.log("formRef?.current", formRef)
    const formData = formRef?.current?.formRef?.getFieldsValue();
    console.log("formData", JSON.stringify(formData));
  }
  // 验证
  const validateFields = async () => {
    try {
      var values = await formRef?.current?.formRef?.validateFields({
        validateOnly: false,
      })
      console.log("values", values)

    } catch (error) {
      console.log("error", error)
      console.log(error.errorFields[0].errors[0])
    }
  }
  const setFormFields = () => {
    formRef?.current?.setFieldsValue({ "__id": 1, "userselect": "1213131", "remark11": { "label": "选项1", "value": "1", "name": "1111", "table": "[{\"price\":1,\"num\":2},{\"price\":2,\"num\":2},{\"price\":3,\"num\":3},{\"price\":3,\"num\":3}]" }, "table2": [{}], "table": [{ "product_num1": "123", "product_sum1": "", "node_oclxmzswzti": "", "select2": "", "switch_table": false, "remark11": { "label": "选项2", "value": "2" }, "product_price12": "", "tianchong1": { "label": "选项1", "value": "1", "tianchong2": { "label": "选项2", "value": "2" }, "tcinput1": "1111" }, "tianchong2": { "label": "选项2", "value": "2", "tcinput1": "8989", "tcinput2": "2222" }, "tcinput1": "8989", "tcinput2": "2222", "tcinput3": "2222" }, { "product_num1": "213", "product_sum1": "", "node_oclxmzswzti": "", "select2": "", "switch_table": false, "datetime2": "2024-08-22 11:09:07", "product_price13": 1, "product_price14": 2, "product_price12": "", "remark11": { "label": "选项3", "value": "3" }, "product_price11": 3 }], "product_total_price": "0.00", "DeptSelect": ["leaf11"], "PostSelect": ["parent 1-1", "leaf11"], "searchuser": [{ "id": 2, "name": "2222", "label": "2222", "value": 2 }, { "id": 4, "name": "4444", "label": "4444", "value": 4 }], "product_price": "213", "product_num": "21", "product_num_range": [1, 22], "product_sum": "4473", "switch": false, "datetime": "2024-08-25", "datetime2": "2024-08-25", "datetime3": "", "datetime4": "2024-08-22 11:09:04", "remark12": JSON.stringify([{ "label": "选项1", "value": "1" }, { "label": "选项2", "value": "2" }]) })
    // formRef?.current?.setFieldsValue({"tianchong1":{"label":"选项1","value":"1"}, })
  }
  const handleCols = () => {

    setCols(cols == 1 ? 3 : cols - 1)
  }

  const [items, setItems] = useState(['Item 1', 'Item 2', 'Item 3', 'Item 4']);

  const handleUpdateReadonly = () => {
    setReadonly(!readonly)
  }

  const [sortItems, setSortItems] = useState([{ id: 1, content: "sdfsfd" }, { id: 2, content: "sdfsfd2" }, { id: 3, content: "sdfsfd3" }]);
  return (
    <div className='fflex fflex-col fitems-center fh-screen '>
      <div className='fflex fgap-2 fitems-center fjustify-center  fw-full'>
        <Button type="primary" onClick={() => {
          setTestCalcHidden(!testCalcHidden)
        }}>testCalcHidden</Button>
        <Button type="primary" onClick={validateFields}>validateFields</Button>
        <Button type="primary" onClick={getFormFields}>GetValues</Button>
        <Button type="primary" onClick={setFormFields}>SetValues</Button>
        <Button type="primary" onClick={() => {
          console.log("testRef.current", testRef.current)
          testRef.current?.handleChange({ label: '选项1', value: '1', name: "1111", table: "[{\"price\":1,\"num\":2},{\"price\":2,\"num\":2}]" })
        }}>testwithref</Button>
        <Button type="primary" onClick={handleCols}>UpdateCol</Button>
        <Button type="primary" onClick={handleUpdateReadonly}>UpdateReadonly</Button>
      </div>

      <div className=" fflex fflex-col fitems-center fflex-1 foverflow-y-auto">
        <DraggableBtn />
        {/* <MyPureComponentWithRef ref={testRef} />; */}
        {/* <div className=' fp-4 fflex fjustify-center fw-[1336px]'>
          <div className='fflex fflex-col'>
            <SortList
              items={sortItems}
              setItems={setSortItems}
              renderItem={(item, index, isDragging) => (
                <div
                  style={{
                    padding: '8px',
                    margin: '4px',
                    border: '1px solid gray',
                    cursor: 'move',
                    opacity: isDragging ? 0.2 : 1,

                  }}
                >
                  {item.content}
                </div>
              )}
            /></div>
        </div> */}
        {/* <div className=' fp-4 fflex fjustify-center fw-[1336px]'>
        <WangEditorNext />
      </div> */}
        {/* <div className='fw-full fp-4 fflex fjustify-center'>
          <TinyMCEEditor />
        </div> */}
        {/* <div className='fw-full fp-4 fflex fjustify-center'>
        <EditorWang />
      </div> */}
        {/* <div className='fw-full fp-4'>
        <EditorQuill value={"[{\"insert\":\"sdfsd\"}]"} />
      </div>
      <div className='fw-full fp-4'>
        <Select
          mode="tags"
          style={{ width: '100%' }}
          placeholder="Tags Mode"
          options={[{ label: '标签1', value: '1' }, { label: '标签2', value: '2' }]}
        />
        <Setter.OptionSetter />
      </div> */}
        <div className='fw-[960px] frounded  fbg-slate-50 fflex fflex-col fitems-center fpb-10'>


          <FormContainerWrapper cols={cols} key={"formc"} className="" ref={formRef} >

          <Field.Number label="税率" __id="fill_shuilv"
            />
          <Field.Input label="含税单价代理中转" __id="fill_xianghu4"
              withIds={["fill_xianghu2","fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_xianghu2",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "* (1+"
                  },
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />
            <Field.Input label="含税单价" __id="fill_xianghu1"
              withIds={["fill_xianghu6"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_xianghu6",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "* 0.5"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

            <Field.Input label="未税单价" __id="fill_xianghu2"
              withIds={["fill_xianghu1","fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_xianghu1",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ (1+"
                  },
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

            <Field.Input label="客户单价" __id="fill_xianghu3"
              withIds={["fill_xianghu4"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_xianghu4",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ 0.5"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

<Field.Input label="客户单价中转" __id="fill_xianghu6"
              withIds={["fill_xianghu5","fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_xianghu5",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "* (1+"
                  },
                  
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />


  <Field.Input label="客户未税单价" __id="fill_xianghu5"
              withIds={["fill_xianghu3","fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_xianghu3",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ (1+"
                  },
                  
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

            <Field.WithSingleSelect
              ref={testRef}
              request={async (params) => {
                return {
                  code: 0, data: {
                    list:
                      [
                        { label: '选项1', value: '1', routeType: 1 },
                        { label: '选项2', value: '2', routeType: 2 },
                        { label: '选项3', value: '3' }
                      ]
                  }
                }
              }}
              option_label="label"
              option_value="value"
              fillRules={[
                {
                  "id": "636d3924-0298-4e9b-809a-26d4a10d7b19",
                  "type": 0,
                  "source": "routeType",
                  "target": "routeType",
                },

              ]} label="类型填充" __id="fill_routeType" />
            <Field.RadioGroup
              buttonStyle="solid"
              label="菜单类型"
              __id="routeType"
              defaultValue={2}
              options={[{ label: "菜单分组", value: 999 }, { label: "URL", value: 1 }, { label: "绑定表单", value: 2 }]} />


            <Field.Input label="测试 withVisible" __id="withVisible"
              isRequired={true}
              withIds={["routeType"]}
              withVisible={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "routeType",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.开关"
                    }
                  },
                  {
                    "insert": " == 1\n"
                  }
                ],
                "version": 1734400834533,
                "withData": [

                ]
              }}
            />
            <Field.Input label="测试 withVisible2" __id="withVisible2"
              isRequired={true}
              withIds={["routeType"]}
              withVisible={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "routeType",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.开关2"
                    }
                  },
                  {
                    "insert": " == 2\n"
                  }
                ],
                "version": 1734400834533,
                "withData": [

                ]
              }}
            />
            <Field.Input label="测试 withVisible21" __id="withVisible21"
              isRequired={true}
              withIds={["routeType"]}
              withVisible={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "routeType",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.开关2"
                    }
                  },
                  {
                    "insert": " == 2\n"
                  }
                ],
                "version": 1734400834533,
                "withData": [

                ]
              }}
            />
            <Field.Select label="绑定表单"
              option_label={"formTitle"}
              __id="menuFormTemplateId"
              withIds={["routeType"]}
              withVisibleFunc={(fieldsValue) => {
                const result = fieldsValue?.routeType == 2 ? true : false
                // console.log("withVisibleFunc menuFormTemplateId", fieldsValue)
                // console.log("withVisibleFunc menuFormTemplateId result", result)
                return result
              }} />


            <Field.Input label="隐藏字段" __id="hidden1"
              calcHidden={true}
            />

            <Field.Input label="菜单URL" __id="route"
              withIds={["routeType"]}
              withVisibleFunc={(fieldsValue) => {
                const result = fieldsValue?.routeType == 1 ? true : false
                // console.log("withVisibleFunc route", fieldsValue)
                // console.log("withVisibleFunc route result", result)
                return result
              }} />
            <Field.TextArea label="备注" __id="remark"
              withIds={["routeType"]}
              withVisibleFunc={(fieldsValue) => {
                return fieldsValue?.routeType != 999 ? true : false
              }}
            />

            <Field.WithSingleSelect
              ref={testRef}
              request={async (params) => {
                console.log("request params", params)
                return {
                  code: 0, data: {
                    list:
                      [
                        { label: '选项1', value: '1', table_fill: [{ fill_shuilv:0.13, fill_tianchong1: { label: '选项1', value: '1' }, fill_tianchong2: { label: '选项2', value: '2' } },{ fill_shuilv:0.13, fill_tianchong1: { label: '选项1', value: '1' }, fill_tianchong2: { label: '选项2', value: '2' } }] },
                        { label: '选项2', value: '2' },
                        { label: '选项3', value: '3' }
                      ]
                  }
                }
              }}
              option_label="label"
              option_value="value"
              fillRules={[
                {
                  "id": "636d3924-0298-4e9b-809a-26d4a10d7b19",
                  "type": 1,
                  "source": "table_fill",
                  "target": "table_fill",
                  "subRules": [
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b11",
                      "type": 0,
                      "source": "fill_tianchong1",
                      "target": "fill_tianchong1",
                    },
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b11",
                      "type": 0,
                      "source": "fill_shuilv",
                      "target": "fill_shuilv",
                    },
                  ]
                },

              ]} label="子表填充数据源" __id="fill_datasource1" />
            <Layout.FormRow layout={'1'}>
              <Field.Table label="测试主子表填充" __id="table_fill" isAllowAdd={true} isAllowCopy={true} hideEditMode={true}  columnsWidth={[100,200,300,400,500,600,700,800,900,1000]}>
              <Field.Number label="税率" __id="fill_shuilv"
            />
          <Field.Input label="含税单价代理中转" __id="fill_xianghu4"
              withIds={["table_fill.fill_xianghu2","table_fill.fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu2",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "* (1+"
                  },
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />
            <Field.Input label="含税单价" __id="fill_xianghu1"
              withIds={["table_fill.fill_xianghu6"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu6",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "* 0.5"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

            <Field.Input label="未税单价" __id="fill_xianghu2"
              withIds={["table_fill.fill_xianghu1","table_fill.fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu1",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ (1+"
                  },
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

            <Field.Input label="客户单价" __id="fill_xianghu3"
              withIds={["table_fill.fill_xianghu4"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu4",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ 0.5"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />

<Field.Input label="客户单价中转" __id="fill_xianghu6"
              withIds={["table_fill.fill_xianghu5","table_fill.fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu5",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "* (1+"
                  },
                  
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />


  <Field.Input label="客户未税单价" __id="fill_xianghu5"
              withIds={["table_fill.fill_xianghu3","table_fill.fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu3",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ (1+"
                  },
                  
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ")"
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />


            <Field.Number label="数量" __id="fill_shuliang"/>
  <Field.Input label="未税金额" __id="fill_weishu5"
              withIds={["table_fill.fill_xianghu3","table_fill.fill_shuliang","table_fill.fill_shuilv"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_xianghu3",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "/ (1+"
                  },
                  
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_shuilv",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": ") * "
                  },
                  
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table_fill.fill_shuliang",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.测试相互依赖"
                    }
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }}
            />
                <Field.WithSingleSelect
                  ref={testRef}
                  request={async (params) => {
                    console.log("request params", params)
                    return { code: 0, data: { list: [{ label: '选项1', value: '1', fill_tianchong2: { label: '选项2', value: '2' }, fill_tcinput1: "1111", }, { label: '选项2', value: '2' }, { label: '选项3', value: '3' }] } }
                  }}
                  option_label="label"
                  option_value="value"
                  fillRules={[
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b19",
                      "type": 0,
                      "source": "fill_tianchong2",
                      "target": "fill_tianchong2",
                      "subRules": [
                      ]
                    },
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b11",
                      "type": 0,
                      "source": "fill_tcinput1",
                      "target": "fill_tcinput1",
                      "subRules": [
                      ]
                    },

                  ]} label="测试填充1" __id="fill_tianchong1" />
                <Field.WithSingleSelect
                  ref={testRef}
                  request={async (params) => {
                    console.log("测试填充2 reqeust", params)
                    if (!params?.value) return { code: 0, data: { list: [{ label: '选项1', value: '1', }] } }
                    await new Promise(resolve => setTimeout(resolve, 200))
                    return { code: 0, data: { list: [{ label: '选项1', value: '1', }, { label: '选项2', value: '2', fill_tcinput1: "8989", fill_tcinput2: "2222" }, { label: '选项3', value: '3' }] } }
                  }}
                  option_label="label"
                  option_value="value"
                  option_search="label"
                  fillRules={[
                    {
                      "id": "636d3924-0298-4e9b-809a-16d4a10d7b29",
                      "type": 0,
                      "source": "fill_tcinput1",
                      "target": "fill_tcinput1",
                      "subRules": [
                      ]
                    },
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b29",
                      "type": 0,
                      "source": "fill_tcinput2",
                      "target": "fill_tcinput2",
                      "subRules": [
                      ]
                    },

                  ]} label="测试填充2" __id="fill_tianchong2" />
                <Field.Input label="测试被填充1" __id="fill_tcinput1" />
                <Field.Input label="测试被填充2" __id="fill_tcinput2" />
                <Field.Input label="测试被填充计算" __id="fill_tcinput3"
                  withIds={["table_fill.fill_tcinput2"]}
                  withFill={{
                    "value": [
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "table_fill.fill_tcinput2",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.测试被填充2"
                        }
                      },
                      {
                        "insert": "* 0.5"
                      },
                      {
                        "insert": "\n\n"
                      }
                    ],
                    "version": 1719296886283,
                    "withData": [

                    ]
                  }}
                />

              </Field.Table>
            </Layout.FormRow>
                  <Field.Input label="测试计算汇总" __id="huizong1"
                    withIds={["table_fill.fill_tcinput3"]}
                    withFill={{
                      "value": [
                        {
                          "insert": {
                            "span": true
                          },
                          "attributes": {
                            "id": "table_fill.fill_tcinput3",
                            "color": "blue",
                            "tagKey": "fieldsValue",
                            "content": "当前表单.测试被填充计算"
                          }
                        },
                      ]
                    }}
                  />
          </FormContainerWrapper>
          <div className="fgroup">11111
            <div className="fbg-red-500 group-hover:fbg-blue-500">
              Hover over me or my parent!
            </div>
          </div>

        </div>

      </div>
    </div>
  );
}

export default App;

export { FormContainer, Field, FormContainerWrapper } from './components'
