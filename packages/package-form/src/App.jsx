import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, FormContainerWrap<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Editor<PERSON><PERSON><PERSON>, Editor<PERSON>ang, EditorWang2, EditorWang3, <PERSON><PERSON><PERSON><PERSON>, WangEditorNext, Show } from './components';
import './App.css';
import { Button, Input, Select } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import Draggable from 'react-draggable';
import { throttle, debounce } from 'lodash';
import { SortList } from "./components"
import { TinyMCEEditor } from './components'
import { PrinterOutlined } from '@ant-design/icons';
const searchSelectOptions = [
  { id: 1, name: "1111" },
  { id: 2, name: "2222" },
  { id: 3, name: "3333" },
  { id: 4, name: "4444" },
  { id: 5, name: "5555" },
  { id: 6, name: "6666" },
  { id: 7, name: "7777" },
  { id: 8, name: "8888" },
  { id: 9, name: "9999" },
]
const searchSelectRequest = async (params) => {
  // console.log("params", params)
  return await new Promise((resolve) => {
    setTimeout(() => {
      if (params && params?.name) {
        resolve({ code: 0, data: searchSelectOptions.filter(item => item.name.includes(params.name)) })
      }
      resolve({ code: 0, data: searchSelectOptions })
    }, 20);
  })
}
const DraggableBtn = () => {
  const [dragging, setDragging] = useState(false);

  const handleStart = (e) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrag = () => {
    setDragging(true);
  };

  const handleStop = (e) => {
    e.preventDefault();
    if (!dragging) {
      alert('Button clicked!');
    }
  };

  return (
    <Draggable
      onStart={handleStart}
      onDrag={handleDrag}
      onStop={handleStop}
    >
      <button className="floating-button">Drag me!</button>
    </Draggable>
  );
}

const treeData = [
  {
    title: '0-1',
    key: '0-1',
    children: [
      { title: '0-1-0-0', key: '0-1-0-0' },
      { title: '0-1-0-1', key: '0-1-0-1' },
      { title: '0-1-0-2', key: '0-1-0-2' },
    ],
  },
  {
    title: '0-2',
    key: '0-2',
  },
];

function App() {
  const formRef = React.createRef();
  const testRef = React.useRef()
  const [testCalcHidden, setTestCalcHidden] = useState(false);
  const [readonly, setReadonly] = useState(true);

  const testdebounce = useCallback(debounce((param1, param2) => {
    console.log("testdebounce param1", param1)
    console.log("testdebounce param2", param2)
  }, 200))


 
  const [cols, setCols] = React.useState(3);

  const getFormFields = () => {
    console.log("formRef?.current", formRef)
    const formData = formRef?.current?.formRef?.getFieldsValue();
    console.log("formData", JSON.stringify(formData));
  }
  // 验证
  const validateFields = async () => {
    try {
      var values = await formRef?.current?.formRef?.validateFields({
        validateOnly: false,
      })
      console.log("values", values)

    } catch (error) {
      console.log("error", error)
      console.log(error.errorFields[0].errors[0])
    }
  }
  const setFormFields = () => {
    formRef?.current?.setFieldsValue({ "__id": 1, "userselect": "1213131", "remark11": { "label": "选项1", "value": "1", "name": "1111", "table": "[{\"price\":1,\"num\":2},{\"price\":2,\"num\":2},{\"price\":3,\"num\":3},{\"price\":3,\"num\":3}]" }, "table2": [{}], "table": [{ "product_num1": "123", "product_sum1": "", "node_oclxmzswzti": "", "select2": "", "switch_table": false, "remark11": { "label": "选项2", "value": "2" }, "product_price12": "", "tianchong1": { "label": "选项1", "value": "1", "tianchong2": { "label": "选项2", "value": "2" }, "tcinput1": "1111" }, "tianchong2": { "label": "选项2", "value": "2", "tcinput1": "8989", "tcinput2": "2222" }, "tcinput1": "8989", "tcinput2": "2222", "tcinput3": "2222" }, { "product_num1": "213", "product_sum1": "", "node_oclxmzswzti": "", "select2": "", "switch_table": false, "datetime2": "2024-08-22 11:09:07", "product_price13": 1, "product_price14": 2, "product_price12": "", "remark11": { "label": "选项3", "value": "3" }, "product_price11": 3 }], "product_total_price": "0.00", "DeptSelect": ["leaf11"], "PostSelect": ["parent 1-1", "leaf11"], "searchuser": [{ "id": 2, "name": "2222", "label": "2222", "value": 2 }, { "id": 4, "name": "4444", "label": "4444", "value": 4 }], "product_price": "213", "product_num": "21", "product_num_range": [1, 22], "product_sum": "4473", "switch": false, "datetime": "2024-08-25", "datetime2": "2024-08-25", "datetime3": "", "datetime4": "2024-08-22 11:09:04", "remark12": JSON.stringify([{ "label": "选项1", "value": "1" }, { "label": "选项2", "value": "2" }]) })
    // formRef?.current?.setFieldsValue({"tianchong1":{"label":"选项1","value":"1"}, })
  }
  const handleCols = () => {

    setCols(cols == 1 ? 3 : cols - 1)
  }

  const [items, setItems] = useState(['Item 1', 'Item 2', 'Item 3', 'Item 4']);

  const handleUpdateReadonly = () => {
    setReadonly(!readonly)
  }

  const [sortItems, setSortItems] = useState([{ id: 1, content: "sdfsfd" }, { id: 2, content: "sdfsfd2" }, { id: 3, content: "sdfsfd3" }]);
  return (
    <div className='fflex fflex-col fitems-center fh-screen '>
      <div className='fflex fgap-2 fitems-center fjustify-center  fw-full'>
        <Button type="primary" onClick={() => {
          setTestCalcHidden(!testCalcHidden)
        }}>testCalcHidden</Button>
        <Button type="primary" onClick={validateFields}>validateFields</Button>
        <Button type="primary" onClick={getFormFields}>GetValues</Button>
        <Button type="primary" onClick={setFormFields}>SetValues</Button>
        <Button type="primary" onClick={() => {
          console.log("testRef.current", testRef.current)
          testRef.current?.handleChange({ label: '选项1', value: '1', name: "1111", table: "[{\"price\":1,\"num\":2},{\"price\":2,\"num\":2}]" })
        }}>testwithref</Button>
        <Button type="primary" onClick={handleCols}>UpdateCol</Button>
        <Button type="primary" onClick={handleUpdateReadonly}>UpdateReadonly</Button>
      </div>

      <div className=" fflex fflex-col fitems-center fflex-1 foverflow-y-auto">
        <DraggableBtn />
        {/* <MyPureComponentWithRef ref={testRef} />; */}
        {/* <div className=' fp-4 fflex fjustify-center fw-[1336px]'>
          <div className='fflex fflex-col'>
            <SortList
              items={sortItems}
              setItems={setSortItems}
              renderItem={(item, index, isDragging) => (
                <div
                  style={{
                    padding: '8px',
                    margin: '4px',
                    border: '1px solid gray',
                    cursor: 'move',
                    opacity: isDragging ? 0.2 : 1,

                  }}
                >
                  {item.content}
                </div>
              )}
            /></div>
        </div> */}
        {/* <div className=' fp-4 fflex fjustify-center fw-[1336px]'>
        <WangEditorNext />
      </div> */}
        {/* <div className='fw-full fp-4 fflex fjustify-center'>
          <TinyMCEEditor />
        </div> */}
        {/* <div className='fw-full fp-4 fflex fjustify-center'>
        <EditorWang />
      </div> */}
        {/* <div className='fw-full fp-4'>
        <EditorQuill value={"[{\"insert\":\"sdfsd\"}]"} />
      </div>
      <div className='fw-full fp-4'>
        <Select
          mode="tags"
          style={{ width: '100%' }}
          placeholder="Tags Mode"
          options={[{ label: '标签1', value: '1' }, { label: '标签2', value: '2' }]}
        />
        <Setter.OptionSetter />
      </div> */}
        <div className='fw-[960px] frounded  fbg-slate-50 fflex fflex-col fitems-center fpb-10'>


          <FormContainerWrapper cols={cols} key={"formc"} className="" ref={formRef} >

          <Field.WithSingleSelect
              ref={testRef}
              request={async (params) => {
                return {
                  code: 0, data: {
                    list:
                      [
                        { label: '选项1', value: '1',  routeType: 1  },
                        { label: '选项2', value: '2' ,  routeType:2 },
                        { label: '选项3', value: '3' }
                      ]
                  }
                }
              }}
              option_label="label"
              option_value="value"
              fillRules={[
                {
                  "id": "636d3924-0298-4e9b-809a-26d4a10d7b19",
                  "type": 0,
                  "source": "routeType",
                  "target": "routeType",
                },

              ]} label="类型填充" __id="fill_routeType" />
            <Field.RadioGroup
              buttonStyle="solid"
              label="菜单类型"
              __id="routeType"
              defaultValue={2}
              options={[{ label: "菜单分组", value: 999 }, { label: "URL", value: 1 }, { label: "绑定表单", value: 2 }]} />


            <Field.Input label="测试 withVisible" __id="withVisible"
            isRequired={true}
              withIds={["routeType"]}
              withVisible={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "routeType",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.开关"
                    }
                  },
                  {
                    "insert": " == 1\n"
                  }
                ],
                "version": 1734400834533,
                "withData": [

                ]
              }} 
            />
            <Field.Input label="测试 withVisible2" __id="withVisible2"
            isRequired={true}
              withIds={["routeType"]}
              withVisible={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "routeType",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.开关2"
                    }
                  },
                  {
                    "insert": " == 2\n"
                  }
                ],
                "version": 1734400834533,
                "withData": [

                ]
              }} 
            />
            <Field.Input label="测试 withVisible21" __id="withVisible21"
            isRequired={true}
              withIds={["routeType"]}
              withVisible={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "routeType",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.开关2"
                    }
                  },
                  {
                    "insert": " == 2\n"
                  }
                ],
                "version": 1734400834533,
                "withData": [

                ]
              }} 
            />
            <Field.Select label="绑定表单"
              option_label={"formTitle"}
              __id="menuFormTemplateId"
              withIds={["routeType"]}
              withVisibleFunc={(fieldsValue) => {
                const result = fieldsValue?.routeType == 2 ? true : false
                // console.log("withVisibleFunc menuFormTemplateId", fieldsValue)
                // console.log("withVisibleFunc menuFormTemplateId result", result)
                return result
              }} />


            <Field.Input label="隐藏字段" __id="hidden1"
            calcHidden={true}
            />

            <Field.Input label="菜单URL" __id="route"
              withIds={["routeType"]}
              withVisibleFunc={(fieldsValue) => {
                const result = fieldsValue?.routeType == 1 ? true : false
                // console.log("withVisibleFunc route", fieldsValue)
                // console.log("withVisibleFunc route result", result)
                return result
              }} />
            <Field.TextArea label="备注" __id="remark"
              withIds={["routeType"]}
              withVisibleFunc={(fieldsValue) => {
                return fieldsValue?.routeType != 999 ? true : false
              }}
            />

            <Field.WithSingleSelect
              ref={testRef}
              request={async (params) => {
                console.log("request params", params)
                return {
                  code: 0, data: {
                    list:
                      [
                        { label: '选项1', value: '1', table_fill: [{ fill_tianchong1: { label: '选项1', value: '1' }, fill_tianchong2: { label: '选项2', value: '2' } }] },
                        { label: '选项2', value: '2' },
                        { label: '选项3', value: '3' }
                      ]
                  }
                }
              }}
              option_label="label"
              option_value="value"
              fillRules={[
                {
                  "id": "636d3924-0298-4e9b-809a-26d4a10d7b19",
                  "type": 1,
                  "source": "table_fill",
                  "target": "table_fill",
                  "subRules": [
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b11",
                      "type": 0,
                      "source": "fill_tianchong1",
                      "target": "fill_tianchong1",
                    },
                  ]
                },

              ]} label="子表填充数据源" __id="fill_datasource1" />
            <Layout.FormRow layout={'1'}>
              <Field.Table label="测试主子表填充" __id="table_fill" isAllowAdd={true} isAllowCopy={true} >

                <Field.WithSingleSelect
                  ref={testRef}
                  request={async (params) => {
                    console.log("request params", params)
                    return { code: 0, data: { list: [{ label: '选项1', value: '1', fill_tianchong2: { label: '选项2', value: '2' }, fill_tcinput1: "1111", }, { label: '选项2', value: '2' }, { label: '选项3', value: '3' }] } }
                  }}
                  option_label="label"
                  option_value="value"
                  fillRules={[
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b19",
                      "type": 0,
                      "source": "fill_tianchong2",
                      "target": "fill_tianchong2",
                      "subRules": [
                      ]
                    },
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b11",
                      "type": 0,
                      "source": "fill_tcinput1",
                      "target": "fill_tcinput1",
                      "subRules": [
                      ]
                    },

                  ]} label="测试填充1" __id="fill_tianchong1" />
                <Field.WithSingleSelect
                  ref={testRef}
                  request={async (params) => {
                    if (!params?.value) return { code: 0, data: { list: [{ label: '选项1', value: '1', }] } }
                    await new Promise(resolve => setTimeout(resolve, 200))
                    return { code: 0, data: { list: [{ label: '选项1', value: '1', }, { label: '选项2', value: '2', fill_tcinput1: "8989", fill_tcinput2: "2222" }, { label: '选项3', value: '3' }] } }
                  }}
                  option_label="label"
                  option_value="value"
                  option_search="label"
                  fillRules={[
                    {
                      "id": "636d3924-0298-4e9b-809a-16d4a10d7b29",
                      "type": 0,
                      "source": "fill_tcinput1",
                      "target": "fill_tcinput1",
                      "subRules": [
                      ]
                    },
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b29",
                      "type": 0,
                      "source": "fill_tcinput2",
                      "target": "fill_tcinput2",
                      "subRules": [
                      ]
                    },

                  ]} label="测试填充2" __id="fill_tianchong2" />
                <Field.Input label="测试被填充1" __id="fill_tcinput1" />
                <Field.Input label="测试被填充2" __id="fill_tcinput2" />
                <Field.Input label="测试被填充计算" __id="fill_tcinput3"
                  withIds={["table_fill.fill_tcinput2"]}
                  withFill={{
                    "value": [
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "table_fill.fill_tcinput2",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.测试被填充2"
                        }
                      },
                      {
                        "insert": "* 0.5"
                      },
                      {
                        "insert": "\n\n"
                      }
                    ],
                    "version": 1719296886283,
                    "withData": [

                    ]
                  }}
                />

              </Field.Table>
            </Layout.FormRow>
            <Field.MultipleSelect mode="multiple" option_label={"label"} option_value={"value"} label="测试过滤条件" __id="selecta1" request={async (params) => {
              return { code: 0, data: { list: [{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }] } }
            }}></Field.MultipleSelect>
            <Layout.FormRow layout={'1'}>

              <Show.WithTable label="测试关联子表" __id="withtable1"
                filterRules={[
                  {
                    "value": {
                      "parent": "",
                      "field_key": "aa2",
                      "group_key": "fieldsValue",
                      "field_name": "当前表单.测试"
                    },
                    "valueType": "variable",
                    "column": {
                      "label": "库存表.所在仓库",
                      "value": "node_ocm009lpxt2",
                      "column_name": "node_ocm009lpxt2",
                      "column_type": ""
                    }
                  }
                ]} />
            </Layout.FormRow>
            <Field.Number label="测试" __id="aa2" isRequired={true} />
            <Field.Number label="测试" __id="aa3" value={123} readonly={readonly} />
            <Field.Number label="测试" __id="aa1" calcHidden={true} readonly={readonly} />
            <Field.Number label="测试" __id="aa4" readonly={readonly} />
            <Field.UserSelect label="选择用户" __id="userselect" defaultValue={[{ id: 1, username: "十天" }]} readonly={readonly} />
            <Layout.FormGroupTitle title={"基本信息"} />
            <Field.WithSingleSelect
              rightIconRender={({ form, fieldName }) => {
                // console.log("rightIconRender form", form,)
                // console.log("rightIconRender fieldName", fieldName)
                return <><PrinterOutlined /></>
              }}
              ref={testRef} fillRules={[
                {
                  "id": "636d3924-0298-4e9b-809a-26d4a10d7b89",
                  "type": 0,
                  "source": "shuilv",
                  "target": "shuilv",
                  "subRules": [
                  ]
                },

              ]} label="发票类型" options={[{ label: '选项1', value: '1', shuilv: 15, }, { label: '选项2', value: '2', shuilv: 50 }, { label: '选项3', value: '3', shuilv: 2 }]} __id="fapiaoleixing" />

            <Field.Number label="税率（%）" __id="shuilv" />

            <Field.WithSingleSelect ref={testRef} fillRules={[
              {
                "id": "636d3924-0298-4e9b-809a-26d4a10d7b89",
                "type": 0,
                "source": "name",
                "target": "name",
                "subRules": [

                ]
              },
              {
                "id": "93401e38-60a4-4acf-84a6-8958785a4a30",
                "type": 1,
                "source": "table",
                "target": "table",
                "subRules": [
                  {
                    "id": "c4a65ae5-58ff-4d7f-8738-a04de1acab61",
                    "type": 0,
                    "source": "price",
                    "target": "product_price1"
                  },

                  {
                    "id": "c4a65ae5-58ff-4d7f-8738-a04de1acab61",
                    "type": 0,
                    "source": "num",
                    "target": "product_num1"
                  },
                ]
              }
            ]} label="测试关联单选" options={[{ label: '选项1', value: '1', name: "1111", table: "[{\"price\":1,\"num\":2},{\"price\":2,\"num\":2},{\"price\":3,\"num\":3},{\"price\":3,\"num\":3}]" }, { label: '选项2', value: '2' }]} __id="remark11" />
            <Layout.FormRow layout={'1'}>
              <Field.Number label="测试规则" __id="ceshi_rule1" />
            </Layout.FormRow>
            <Layout.FormRow layout={'1'}>
              <Field.Table label="子表格" __id="table2" isAllowCopy={true}  >
                <Field.Number label="测试规则2" __id="ceshi_rule2" />
              </Field.Table>
            </Layout.FormRow>

            <Field.WithSingleSelect
              ref={testRef}
              request={async (params) => {
                console.log("request params", params);
                const { page = 1, pageSize = 10 } = params; // 获取当前页码，默认为1


                // 模拟数据集
                const allData = [
                  { label: '选项1', value: '1' },
                  { label: '选项2', value: '2' },
                  { label: '选项3', value: '3' },
                  { label: '选项4', value: '4' },
                  { label: '选项5', value: '5' },
                  { label: '选项6', value: '6' },
                  { label: '选项7', value: '7' },
                  { label: '选项8', value: '8' },
                  { label: '选项9', value: '9' },
                  { label: '选项10', value: '10' },
                  { label: '选项11', value: '11' },
                  { label: '选项12', value: '12' },
                  { label: '选项13', value: '13' },
                  { label: '选项14', value: '14' },
                  { label: '选项15', value: '15' },
                  { label: '选项16', value: '16' },
                  { label: '选项17', value: '17' },
                  { label: '选项18', value: '18' },
                  { label: '选项19', value: '19' },
                  { label: '选项20', value: '20' },
                  { label: '选项21', value: '21' },
                  { label: '选项22', value: '22' },
                  { label: '选项23', value: '23' },
                  { label: '选项24', value: '24' }
                ];

                // 根据页码和每页数据量计算当前页的数据
                const startIndex = (page - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                let pageData = []
                if (startIndex < allData.length) {
                  pageData = allData.slice(startIndex, endIndex);
                }

                let result = { code: 0, data: { list: pageData } };
                console.log("request result", result)
                await new Promise(resolve => setTimeout(resolve, 200))
                return result
              }}
              option_label="label"
              option_value="value"
              label="测试请求" __id="ceshirequest" />

            <Layout.FormRow layout={'1'}>
              <Field.Table label="子表格" __id="table" isAllowAdd={true} isAllowCopy={true} >

                <Field.Number label="税率（%）" __id="shuilv_table" withIds={[
                  "shuilv"
                ]}
                  withFill={{
                    "value": [
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "shuilv",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.税率（%）"
                        }
                      },

                    ],
                    "version": 1723016911807,
                    "withData": [

                    ]
                  }} />
                <Field.DatePicker defaultNow={true} label="日期时间" prompt="" datetype="date" __id="datetime2" />

                <Field.WithSingleSelect ref={testRef}
                  request={async (params, ruleParams, fieldName) => {
                    console.log("request params", params, ruleParams, fieldName)
                    if (params?.ruleParams?.node_ocm009lpxt2 == 111)
                      return { code: 0, data: { list: [{ label: '选项1', value: '1', product_price11: "1111", product_price12: "2222", product_price1: 111 }, { label: '选项2', value: '2' }, { label: '选项3', value: '3' }] } }
                    else return { code: 0, data: { list: [{ label: '选项1', value: '1', product_price11: "1111", product_price12: "2222", product_price1: 111 }, { label: '选项2', value: '2' },] } }
                  }}
                  option_label="label"
                  option_value="value"
                  filterRules={[
                    {
                      "value": {
                        "parent": "",
                        "field_key": "shuilv",
                        "group_key": "fieldsValue",
                        "field_name": "当前表单.税率"
                      },
                      "valueType": "variable",
                      "column": {
                        "label": "库存表.所在仓库",
                        "value": "node_ocm009lpxt2",
                        "column_name": "node_ocm009lpxt2",
                        "column_type": ""
                      }
                    }
                  ]}

                  fillRules={[
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b89",
                      "type": 0,
                      "source": "product_price11",
                      "target": "product_price11",
                      "subRules": [

                      ]
                    },
                    {
                      "id": "636d3924-0298-4e9b-809a-26d4a10d7b89",
                      "type": 0,
                      "source": "product_price1",
                      "target": "product_price1",
                      "subRules": [

                      ]
                    },

                  ]} label="测试关联单选" __id="remark11" />

                <Field.Switch label="开关" __id="switch_table"></Field.Switch>
                <Field.Input defaultValue={3} label="含税单价" __id="product_price11" />
                <Field.Input label="未税单价" __id="product_price12"
                  withIds={[
                    "table.product_price11",
                    "shuilv"
                  ]}
                  withFill={{
                    "value": [
                      {
                        "insert": "("
                      },
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "table.product_price11",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.产品列表.含税单价"
                        }
                      },
                      {
                        "insert": "/(1+"
                      },
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "shuilv",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.税率（%）"
                        }
                      },
                      {
                        "insert": "/ 100)).toFixed(2)\n\n\n"
                      }
                    ],
                    "version": 1723016911807,
                    "withData": [

                    ]
                  }}
                />
                <Field.Input defaultValue={1} label="商品价格" __id="product_price13" />
                <Field.Input defaultValue={2} label="商品价格" __id="product_price14" />
                <Field.Input label="商品价格" __id="product_price1" />
                <Field.Input label="商品个数" __id="product_num1" />
                <Field.Input disabled={true} label="商品总价" __id="product_sum1"
                  withIds={["table.product_price1", "table.product_num1"]}
                  withFill={{
                    "value": [
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "table.product_price1",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.商品价格"
                        }
                      },
                      {
                        "insert": "*"
                      },
                      {
                        "insert": {
                          "span": true
                        },
                        "attributes": {
                          "id": "table.product_num1",
                          "color": "blue",
                          "tagKey": "fieldsValue",
                          "content": "当前表单.商品个数"
                        }
                      },
                      {
                        "insert": "\n\n"
                      }
                    ],
                    "version": 1719296886283,
                    "withData": [

                    ]
                  }} />
                <Field.Input label="分组名" __id="node_oclxmzswzti" />
                <Field.MultipleSelect mode="multiple" label="测多选" __id="select2" options={[{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }]}></Field.MultipleSelect>

              </Field.Table>
            </Layout.FormRow>
            <Field.Input label="总价" disabled={true} __id="product_total_price" withIds={[
              "table.product_sum1"
            ]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "table.product_sum1",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.标签.小计﻿"
                    }
                  },
                  {
                    "insert": ".reduce((acc, curr) => parseFloat(acc||0) + parseFloat(curr||0), 0).toFixed(2)"
                  },
                  {
                    "insert": "\n"
                  }
                ],
                "version": 1719383786677,
                "withData": [

                ]
              }}
            />
            <Field.UserSelect label="选择用户" __id="userselect" customComponent={Input} />
            <Field.DeptSelect label="DeptSelect" __id="DeptSelect" treeData={[{
              value: 'parent 1-1',
              title: 'parent 1-1',
              children: [
                {
                  value: 'leaf11',
                  title: <b style={{ color: '#08c' }}>leaf11</b>,
                },
              ],
            },]}></Field.DeptSelect>
            <Field.PostSelect multiple={true} label="PostSelect" __id="PostSelect" treeData={[{
              value: 'parent 1-1',
              title: 'parent 1-1',
              children: [
                {
                  value: 'leaf11',
                  title: <b style={{ color: '#08c' }}>leaf11</b>,
                },
              ],
            },]}></Field.PostSelect>
            <Field.SearchSelect mode='multiple' label="搜组件" __id="searchuser" request={searchSelectRequest} option_search={"name"} option_label="name" option_value="id"></Field.SearchSelect>
            <Field.Input label="商品价格" __id="product_price" defaultValue={"12"} readonly={true} />
            <Field.Input label="商品数量" __id="product_num" rules={"^(1[3-9]\\d{9})$"} />
            <Field.NumberRange label="数量范围" __id="product_num_range" />
            <Field.Input rules={["^(1[3-9]\\d{9})$", "^\\d+$"]} label="商品总价" __id="product_sum"
              withIds={["product_price", "product_num"]}
              withFill={{
                "value": [
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "product_price",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.商品价格"
                    }
                  },
                  {
                    "insert": "* "
                  },
                  {
                    "insert": {
                      "span": true
                    },
                    "attributes": {
                      "id": "product_num",
                      "color": "blue",
                      "tagKey": "fieldsValue",
                      "content": "当前表单.商品个数"
                    }
                  },
                  {
                    "insert": "\n\n"
                  }
                ],
                "version": 1719296886283,
                "withData": [

                ]
              }} />

            <Field.SingleSelect mode="single" option_value={"id"} label="测试单选" __id="select1" options={[{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }]}></Field.SingleSelect>
            <Field.MultipleSelect mode="multiple" label="测多选" __id="select2" options={[{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }]}></Field.MultipleSelect>
            <Field.MultipleSelect mode="multiple" option_label={"label"} option_value={"value"} label="测多选2" __id="select2222" request={async (params) => {
              return { code: 0, data: { list: [{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }] } }
            }}></Field.MultipleSelect>
            <Field.TreeSelect label="分组名" __id="title11"></Field.TreeSelect>
            <Field.Switch label="开关" __id="switch"></Field.Switch>
            <Layout.FormGroupTitle title={"嘟嘟嘟嘟嘟"} />
            <Field.CodeMachine label="角色编号" prompt="" __id="code"
              withIds={["switch"]}
              withVisibleFunc={(fieldsValue) => {
                return fieldsValue?.switch ? true : false
              }}
            />

            <div className=' fh-10 fw-full fbg-green-300' __id="div1111"
              _componentName="Field.div"
              withIds={["switch"]}
              withVisibleFunc={(fieldsValue) => {
                return fieldsValue?.switch ? true : false
              }}>1111</div>
            <Field.DatePicker defaultNow={true} label="datetime" prompt="" datetype="month" value='2022-10-22' __id="datetime" />
            <Field.DatePicker label="datetime2" prompt="" datetype="date" __id="datetime2" />
            <Field.DatePicker readonly={true} defaultNow={true} label="datetime3" prompt="" datetype="datetime" value={'2022-10-22'} __id="datetime3" />
            <Field.DatePicker defaultNow={true} label="datetime4" prompt="" datetype="year" value={'2022-10-22'} __id="datetime4" />
            <Field.Input label="角色名称" __id="name" />
            <Layout.FormRow layout={'1,1'}>
              <Field.Input label="角色名称布局" __id="name1" />
              <Field.Input label="角色名称布局2" __id="name2" />
            </Layout.FormRow>
            <Field.CheckboxTree label="角色权限" __id="permissions" addRoot={false} treeData={treeData} />
            <Layout.FormGroupTitle title={"关联信息"} />
            <Field.WithMultipleSelect disabled={true} label="测试关联多选" options={[{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }]} __id="remark12" />
            <Layout.FormRow > <Field.TextArea label="备注" __id="remark" /></Layout.FormRow>

            <Layout.FormRow layout={'1'}>
              <Field.RadioGroup withIds={["remark11"]}
                withFill={{
                  "value": [
                    {
                      "insert": {
                        "span": true
                      },
                      "attributes": {
                        "id": "remark11",
                        "color": "blue",
                        "tagKey": "fieldsValue",
                        "content": "测试关联单选"
                      }
                    },
                  ],
                  "version": 1719296886283,
                  "withData": [

                  ]
                }} options={[{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }]} label="单选框" __id="radio" ></Field.RadioGroup>
            </Layout.FormRow>
            <Layout.FormRow layout={'1'}>
              <Field.CheckboxGroup options={[{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }]} label="多选框" __id="CheckboxGroup" ></Field.CheckboxGroup>
            </Layout.FormRow>
            <Field.UploadFile label="上传文件" __id="UploadFile" ></Field.UploadFile>
            <Field.UploadImage label="上传图片" __id="UploadImage" ></Field.UploadImage>

          </FormContainerWrapper>
          <div className="fgroup">11111
            <div className="fbg-red-500 group-hover:fbg-blue-500">
              Hover over me or my parent!
            </div>
          </div>

        </div>

      </div>
    </div>
  );
}

export default App;

export { FormContainer, Field, FormContainerWrapper } from './components'
