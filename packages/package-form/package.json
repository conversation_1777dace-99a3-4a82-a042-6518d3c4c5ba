{"name": "lu-lowcode-package-form", "version": "0.11.81", "dependencies": {"@ant-design/icons": "^4.8.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^5.1.1", "@wangeditor-next/core": "^1.7.0", "@wangeditor-next/editor": "^5.5.0", "@wangeditor-next/editor-for-react": "^1.0.7", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ahooks": "^3.8.4", "antd": "^5.13.2", "dayjs": "^1.11.11", "decimal.js": "^10.5.0", "eventemitter3": "^5.0.1", "memoizee": "^0.4.17", "nanoid": "^5.0.7", "postcss-modules": "^6.0.0", "quill": "^2.0.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-draggable": "^4.4.6", "react-quill": "^2.0.0", "react-resizable": "^3.0.5", "wangeditor-for-react": "^1.5.6", "web-vitals": "^2.1.4"}, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "scripts": {"start": "vite", "build": "vite build", "test": "react-scripts test", "eject": "react-scripts eject"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^17.0.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@hot-loader/react-dom": "^17.0.2", "@rollup/plugin-babel": "^6.0.4", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "babel-loader": "^9.1.3", "postcss": "^8.4.38", "postcss-prefixer": "^3.0.0", "react": "^18.3.1", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "tailwindcss": "^3.4.4", "vite": "^5.2.13", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}}