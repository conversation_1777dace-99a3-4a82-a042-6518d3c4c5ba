version: '3.8'

services:
  mysql-server:
    image: mysql:8.0.37
    container_name: crm-mysql-server
    ports:
      - "27502:3306"
    volumes:
      - ./mysql-server/conf:/etc/mysql/conf.d
      - ./mysql-server/logs:/var/log/mysql
      - ./mysql-server/data:/var/lib/mysql
      - /etc/localtime:/etc/localtime
    environment:
      MYSQL_ROOT_PASSWORD: Gri42Mvyk3j2PXIiTdn6CCA8JDSST7yv
    restart: always
  redis-server:
    image: redis
    command: redis-server --requirepass lfluYk4reffZDjzzXfeNA2ub9odfJ1Ic
    container_name: crm-redis-server
    ports:
      - "27503:6379"
    logging:
      driver: json-file
      options:
        max-size: 100m
        max-file: '2'
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime
  crm-nats-server:
    image: nats
    container_name: crm-nats-server
    restart: always
    volumes:
      - ./nats-server:/data/nats-server
      - /etc/localtime:/etc/localtime
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2"
    ports:
      - "27504:4222"
      - "8222:8222"
    command: ["-js", "-sd", "/data/nats-server"]
  