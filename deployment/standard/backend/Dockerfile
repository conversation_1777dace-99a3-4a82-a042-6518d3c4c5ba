FROM debian:bullseye-slim

###############################################################################
#                                INSTALLATION
###############################################################################

# Enable contrib repository and install fonts
RUN sed -i 's/main/main contrib/g' /etc/apt/sources.list \
    && apt-get update && apt-get install -y --no-install-recommends \
    wkhtmltopdf \
    fontconfig \
    libfreetype6 \
    libjpeg62-turbo \
    libpng16-16 \
    libx11-6 \
    libxcb1 \
    libxext6 \
    libxrender1 \
    xfonts-75dpi \
    xfonts-base \
    ca-certificates \
    # Basic Chinese fonts
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    # More Chinese fonts
    fonts-arphic-ukai \
    fonts-arphic-uming \
    # Additional Chinese fonts
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    # Microsoft-compatible fonts
    fonts-liberation \
    ttf-mscorefonts-installer \
    # Font utilities
    fontconfig \
    xfonts-utils \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    # Refresh font cache
    && fc-cache -f -v

ENV WORKDIR=/app
ADD ./main $WORKDIR/main
ADD ./config.yaml $WORKDIR/config.yaml
RUN chmod +x $WORKDIR/main

###############################################################################
#                                   START
###############################################################################
WORKDIR $WORKDIR
CMD ["./main"]