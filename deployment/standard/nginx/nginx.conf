server {
    listen 80 default_server;
    listen [::]:80 default_server;

    # 启用 gzip 压缩
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;
    gzip_disable "msie6";

    client_max_body_size 50M;

    # 基础代理设置
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $http_host;
    proxy_set_header X-Forwarded-Port $server_port;
    proxy_set_header X-Forwarded-Server $host;
    proxy_set_header Referer $http_referer;

    # 添加响应头，确保所有请求都使用当前的主机和端口
    add_header X-Forwarded-Host $http_host;

    # 重定向规则
    location = / {
        return 301 $scheme://$http_host/foo/;
    }
    location = /login {
        return 301 $scheme://$http_host/foo/user/login;
    }

    # 后端服务代理 - 拆分为单独的location块
    location /lowcode/ {
        proxy_pass http://backend/lowcode/;
    }

    location /print_pdf/ {
        proxy_pass http://backend/print_pdf/;
    }

    location /export_file/ {
        proxy_pass http://backend/export_file/;
    }

    location /files/ {
        proxy_pass http://backend/files/;
    }

    location /foo/ {
        proxy_pass http://backend/foo/;
    }

    # API请求特殊处理
    location /api/ {
        # 添加跨域头
        add_header Access-Control-Allow-Origin $http_origin;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        add_header Access-Control-Allow-Credentials 'true';

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }

        proxy_pass http://backend/api/;
    }

    # CDN特殊处理
    location /cdn/ {
        proxy_pass http://backend/foo/cdn/;
    }



    # WebSocket代理
    location /ws/ {
        proxy_pass http://backend/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_read_timeout 86400s;
    }

    # Superset相关代理
    location /api/v1/ {
        proxy_pass http://superset:8088/api/v1/;
        proxy_http_version 1.1;
    }

    # Superset默认路由
    location / {
        proxy_pass http://superset:8088/;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        port_in_redirect off;
        proxy_connect_timeout 300;
    }
}