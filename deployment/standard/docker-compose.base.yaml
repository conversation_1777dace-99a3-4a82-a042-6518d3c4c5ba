version: '3.8'

services:
  mysql-server:
    image: mysql:8.0.37
    container_name: ${CONTAINER_PREFIX:-crm}-mysql-server
    ports:
      - "27702:3306"
    volumes:
      - ./mysql-server/conf:/etc/mysql/conf.d
      - ./mysql-server/logs:/var/log/mysql
      - ./mysql-server/data:/var/lib/mysql
      - /etc/localtime:/etc/localtime
    environment:
      MYSQL_ROOT_PASSWORD: Gri42Mvyk3j2PXIiTdn6CCA8JDSST7yv
    restart: always
  redis-server:
    image: redis
    command: redis-server --requirepass lfluYk4reffZDjzzXfeNA2ub9odfJ1Ic
    container_name: ${CONTAINER_PREFIX:-crm}-redis-server
    ports:
      - "27703:6379"
    logging:
      driver: json-file
      options:
        max-size: 100m
        max-file: '2'
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime
  crm-nats-server:
    image: nats
    container_name: ${CONTAINER_PREFIX:-crm}-nats-server
    restart: always
    volumes:
      - ./nats-server:/data/nats-server
      - /etc/localtime:/etc/localtime
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2"
    ports:
      - "27704:4222"
      - "7222:8222"
    command: ["-js", "-sd", "/data/nats-server"]
  nginx:
    image: nginx:alpine
    container_name: ${CONTAINER_PREFIX:-crm}-nginx
    ports:
      - "8889:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - /etc/localtime:/etc/localtime
    restart: always
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2"
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ${CONTAINER_PREFIX:-crm}-backend
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime
      - ./backend/runtime:/app/runtime
    depends_on:
      - mysql-server
      - redis-server
      - crm-nats-server
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2" 