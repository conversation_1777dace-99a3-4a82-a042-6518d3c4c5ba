version: '3.8'

# Superset 相关的定义
x-superset-image: &superset-image lushitian405/superset:0.0.4
x-superset-volumes:
  &superset-volumes # /app/pythonpath_docker will be appended to the PYTHONPATH in the final container
  - ./superset/docker:/app/docker
  - ./superset-home:/app/superset_home

services:
  # 原有服务
  mysql-server:
    image: mysql:8.0.37
    container_name: ${CONTAINER_PREFIX:-crm}-mysql-server
    ports:
      - "27702:3306"
    volumes:
      - ./mysql-server/conf:/etc/mysql/conf.d
      - ./mysql-server/logs:/var/log/mysql
      - ./mysql-server/data:/var/lib/mysql
      - /etc/localtime:/etc/localtime
    environment:
      MYSQL_ROOT_PASSWORD: Gri42Mvyk3j2PXIiTdn6CCA8JDSST7yv
    restart: always
    networks:
      - app-network

  redis-server:
    image: redis
    # command: redis-server --requirepass lfluYk4reffZDjzzXfeNA2ub9odfJ1Ic
    command: redis-server 
    container_name: ${CONTAINER_PREFIX:-crm}-redis-server
    logging:
      driver: json-file
      options:
        max-size: 100m
        max-file: '2'
    restart: always
    networks:
      - app-network
    volumes:
      - /etc/localtime:/etc/localtime
      - ./redis-data:/data

  crm-nats-server:
    image: nats
    container_name: ${CONTAINER_PREFIX:-crm}-nats-server
    restart: always
    volumes:
      - ./nats-server:/data/nats-server
      - /etc/localtime:/etc/localtime
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2"
    networks:
      - app-network
    command: ["-js", "-sd", "/data/nats-server"]

  nginx:
    image: nginx:alpine
    container_name: ${CONTAINER_PREFIX:-crm}-nginx
    ports:
      - "8889:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - /etc/localtime:/etc/localtime
    restart: always
    depends_on:
      - backend
      - superset
    networks:
      - app-network
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2"

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ${CONTAINER_PREFIX:-crm}-backend
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime
      - ./backend/runtime:/app/runtime
    depends_on:
      - mysql-server
      - redis-server
      - crm-nats-server
    networks:
      - app-network
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "2"

  # Superset 相关服务
  superset-db:
    env_file:
      - path: ./superset/docker/.env # default
        required: true
      - path: ./superset/docker/.env-local # optional override
        required: false
    image: postgres:16
    container_name: ${CONTAINER_PREFIX:-crm}-superset-db
    restart: unless-stopped
    volumes:
      - ./superset-db-home:/var/lib/postgresql/data
      - ./superset/docker/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
      - /etc/localtime:/etc/localtime
    networks:
      - app-network
    environment:
      POSTGRES_DB: superset
      POSTGRES_USER: superset
      POSTGRES_PASSWORD: superset

  superset:
    env_file:
      - path: ./superset/docker/.env # default
        required: true
      - path: ./superset/docker/.env-local # optional override
        required: false
    image: *superset-image
    container_name: ${CONTAINER_PREFIX:-crm}-superset-app
    command: ["/app/docker/docker-bootstrap.sh", "app-gunicorn"]
    user: "root"
    restart: unless-stopped
    depends_on:
      - redis-server
      - superset-db
      - superset-init
    volumes: *superset-volumes
    networks:
      - app-network
    environment:
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
      REDIS_HOST: "redis-server"
      REDIS_PORT: "6379"
      DATABASE_HOST: "superset-db"

  superset-init:
    image: *superset-image
    container_name: ${CONTAINER_PREFIX:-crm}-superset-init
    command: ["/app/docker/docker-init.sh"]
    env_file:
      - path: ./superset/docker/.env # default
        required: true
      - path: ./superset/docker/.env-local # optional override
        required: false
    depends_on:
      - superset-db
      - redis-server
    user: "root"
    volumes: *superset-volumes
    networks:
      - app-network
    healthcheck:
      disable: true
    environment:
      SUPERSET_LOAD_EXAMPLES: "no"
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
      PIP_TRUSTED_HOST: "pypi.org pypi.python.org files.pythonhosted.org"
      PIP_DISABLE_PIP_VERSION_CHECK: "1"
      PIP_NO_CACHE_DIR: "1"
      PIP_INDEX_URL: "https://pypi.tuna.tsinghua.edu.cn/simple"
      REDIS_HOST: "redis-server"
      REDIS_PORT: "6379"
      DATABASE_HOST: "superset-db"
    dns:
      - *******
      - *******

  superset-worker:
    image: *superset-image
    container_name: ${CONTAINER_PREFIX:-crm}-superset-worker
    command: ["/app/docker/docker-bootstrap.sh", "worker"]
    env_file:
      - path: ./superset/docker/.env # default
        required: true
      - path: ./superset/docker/.env-local # optional override
        required: false
    restart: unless-stopped
    depends_on:
      - redis-server
      - superset-db
      - superset-init
    user: "root"
    volumes: *superset-volumes
    networks:
      - app-network
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "celery -A superset.tasks.celery_app:app inspect ping -d celery@$$HOSTNAME",
        ]
    environment:
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
      REDIS_HOST: "redis-server"
      REDIS_PORT: "6379"
      DATABASE_HOST: "superset-db"

  superset-worker-beat:
    image: *superset-image
    container_name: ${CONTAINER_PREFIX:-crm}-superset-worker-beat
    command: ["/app/docker/docker-bootstrap.sh", "beat"]
    env_file:
      - path: ./superset/docker/.env # default
        required: true
      - path: ./superset/docker/.env-local # optional override
        required: false
    restart: unless-stopped
    depends_on:
      - redis-server
      - superset-db
      - superset-init
    user: "root"
    volumes: *superset-volumes
    networks:
      - app-network
    healthcheck:
      disable: true
    environment:
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
      REDIS_HOST: "redis-server"
      REDIS_PORT: "6379"
      DATABASE_HOST: "superset-db"

networks:
  app-network:
    driver: bridge
