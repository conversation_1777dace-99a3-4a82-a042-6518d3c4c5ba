#!/bin/bash

# MySQL 配置信息
USER="root"
PASSWORD="Gri42Mvyk3j2PXIiTdn6CCA8JDSST7yv"
DATABASE="crm_v102"
BACKUP_DIR="/var/www/crm/db-bak"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/CRM_205_$DATABASE_$TIMESTAMP.sql"
TAR_FILE="$BACKUP_FILE.tar.gz"
HOST="127.0.0.1"  # 这里添加 MySQL 服务器的 IP 地址
PORT="27502"  # 这里添加 MySQL 服务器的端口号

# 执行 mysqldump 进行备份
mysqldump --single-transaction -h $HOST -P $PORT -u $USER -p$PASSWORD $DATABASE > $BACKUP_FILE

# 检查备份是否成功
if [ $? -eq 0 ]; then
  echo "备份成功：$BACKUP_FILE"

  # 使用 tar 压缩备份文件
  tar -czf $TAR_FILE -C $BACKUP_DIR $(basename $BACKUP_FILE)

  # 检查压缩是否成功
  if [ $? -eq 0 ]; then
    echo "压缩成功：$TAR_FILE"

    # 删除原始的未压缩备份文件
    rm -f $BACKUP_FILE
    # 删除超过90天的备份文件
    find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +90 -exec rm -f {} \;
  else
    echo "压缩失败"
  fi
else
  echo "备份失败"
fi