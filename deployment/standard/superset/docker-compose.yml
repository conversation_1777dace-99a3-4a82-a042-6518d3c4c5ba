#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# -----------------------------------------------------------------------
# We don't support docker compose for production environments.
# If you choose to use this type of deployment make sure to
# create you own docker environment file (docker/.env) with your own
# unique random secure passwords and SECRET_KEY.
# -----------------------------------------------------------------------
x-superset-image: &superset-image apachesuperset.docker.scarf.sh/apache/superset:${TAG:-latest-dev}
x-superset-volumes:
  &superset-volumes # /app/pythonpath_docker will be appended to the PYTHONPATH in the final container
  - ./docker:/app/docker
  - superset_home:/app/superset_home

services:
  redis:
    image: redis:7
    container_name: superset_cache
    restart: unless-stopped
    volumes:
      - redis:/data

  db:
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    image: postgres:16
    container_name: superset_db
    restart: unless-stopped
    volumes:
      - db_home:/var/lib/postgresql/data
      - ./docker/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d

  superset:
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    image: *superset-image
    container_name: superset_app
    command: ["/app/docker/docker-bootstrap.sh", "app-gunicorn"]
    user: "root"
    restart: unless-stopped
    ports:
      - 8088:8088
    depends_on:
      superset-init:
        condition: service_completed_successfully
    volumes: *superset-volumes
    environment:
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"

  superset-init:
    image: *superset-image
    container_name: superset_init
    command: ["/app/docker/docker-init.sh"]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    depends_on:
      db:
        condition: service_started
      redis:
        condition: service_started
    user: "root"
    volumes: *superset-volumes
    healthcheck:
      disable: true
    environment:
      SUPERSET_LOAD_EXAMPLES: "no"
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"
      PIP_TRUSTED_HOST: "pypi.org pypi.python.org files.pythonhosted.org"
      PIP_DISABLE_PIP_VERSION_CHECK: "1"
      PIP_NO_CACHE_DIR: "1"
      PIP_INDEX_URL: "https://pypi.tuna.tsinghua.edu.cn/simple"
    dns:
      - *******
      - *******

  superset-worker:
    image: *superset-image
    container_name: superset_worker
    command: ["/app/docker/docker-bootstrap.sh", "worker"]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    restart: unless-stopped
    depends_on:
      superset-init:
        condition: service_completed_successfully
    user: "root"
    volumes: *superset-volumes
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "celery -A superset.tasks.celery_app:app inspect ping -d celery@$$HOSTNAME",
        ]
    environment:
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"

  superset-worker-beat:
    image: *superset-image
    container_name: superset_worker_beat
    command: ["/app/docker/docker-bootstrap.sh", "beat"]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    restart: unless-stopped
    depends_on:
      superset-init:
        condition: service_completed_successfully
    user: "root"
    volumes: *superset-volumes
    healthcheck:
      disable: true
    environment:
      SUPERSET_LOG_LEVEL: "${SUPERSET_LOG_LEVEL:-info}"

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false
