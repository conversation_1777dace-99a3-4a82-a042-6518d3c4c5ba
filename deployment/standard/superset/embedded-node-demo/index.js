/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// 加载环境变量
require('dotenv').config();
const axios = require('axios');

// 从环境变量中获取配置
const SUPERSET_URL = process.env.SUPERSET_URL;
const SUPERSET_ADMIN_USERNAME = process.env.SUPERSET_ADMIN_USERNAME;
const SUPERSET_ADMIN_PASSWORD = process.env.SUPERSET_ADMIN_PASSWORD;
const DASHBOARD_UUID = process.env.DASHBOARD_UUID;

// 确认环境变量是否设置
if (!SUPERSET_URL || !SUPERSET_ADMIN_USERNAME || !SUPERSET_ADMIN_PASSWORD || !DASHBOARD_UUID) {
  console.error('请确保已设置所有必需的环境变量。请参考.env.example文件');
  process.exit(1);
}

// 创建axios实例以保持会话
const api = axios.create({
  baseURL: SUPERSET_URL,
  withCredentials: true // 启用withCredentials以支持会话cookie
});

/**
 * 从Set-Cookie响应头中提取会话cookie
 * @param {Object} headers - 响应头
 * @param {string} cookieName - cookie名称，默认为'session'
 * @returns {string|null} - 提取的cookie值
 */
function extractSessionCookie(headers, cookieName = 'session') {
  const setCookieHeader = headers['set-cookie'];
  if (!setCookieHeader || !Array.isArray(setCookieHeader)) {
    return null;
  }
  
  for (const cookieStr of setCookieHeader) {
    const match = new RegExp(`${cookieName}=([^;]+)`).exec(cookieStr);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * 获取仪表板列表
 * 此函数展示如何在认证后调用Superset API
 */
async function getDashboardList() {
  try {
    console.log('\n----- 获取仪表板列表 -----');
    
    // 使用最简单的查询参数，不添加任何过滤条件
    const query = {
      page: 0,
      page_size: 25, // 增加页面大小
      select_columns: ["dashboard_title", "url", "id", "changed_on_delta_humanized"]
    };
    
    // 使用URLSearchParams将JSON对象转换为查询字符串参数
    const params = new URLSearchParams();
    params.append('q', JSON.stringify(query));
    
    console.log('请求URL:', '/api/v1/dashboard/');
    console.log('查询参数:', JSON.stringify(query));
    
    const response = await api.get('/api/v1/dashboard/', { params });
    
    console.log('API响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.count === 0) {
      console.log('⚠️ 没有找到仪表板。可能的原因:');
      console.log('1. 该Superset实例中还没有创建仪表板');
      console.log('2. 用户没有查看仪表板的权限');
      console.log('3. API端点路径可能不正确');
      
      // 尝试获取当前用户信息，验证权限
      try {
        console.log('\n尝试获取当前用户信息...');
        const userResponse = await api.get('/api/v1/me/');
        console.log('当前用户:', userResponse.data.result);
        console.log('用户角色:', userResponse.data.result.roles);
      } catch (userError) {
        console.error('获取用户信息失败:', userError.message);
      }
      
      // 尝试直接获取第一个仪表板（如果存在）
      try {
        console.log('\n尝试直接获取ID为1的仪表板...');
        const singleDashResponse = await api.get('/api/v1/dashboard/1');
        console.log('仪表板1存在:', singleDashResponse.data);
      } catch (dashError) {
        if (dashError.response && dashError.response.status === 404) {
          console.log('仪表板ID 1不存在');
        } else {
          console.error('获取单个仪表板失败:', dashError.message);
        }
      }
    } else {
      console.log(`✅ 成功获取仪表板列表，总计: ${response.data.count} 个仪表板`);
      
      // 仅打印前3个仪表板的基本信息
      const dashboards = response.data.result.slice(0, 3).map(dash => ({
        id: dash.id,
        title: dash.dashboard_title,
        url: dash.url,
        changed_on: dash.changed_on_delta_humanized
      }));
      
      console.log('仪表板示例:');
      console.table(dashboards);
    }
    
    console.log('--------------------------\n');
    
    return response.data;
  } catch (error) {
    console.error('❌ 获取仪表板列表失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
      console.error('响应头:', JSON.stringify(error.response.headers, null, 2));
    } else {
      console.error('错误信息:', error.message);
      console.error('请求配置:', error.config);
    }
    throw error;
  }
}

/**
 * 获取Superset Guest Token
 * @returns {Promise<string>} Guest Token
 */
async function getGuestToken() {
  try {
    console.log('1. 登录Superset...');
    const loginResponse = await api.post('/api/v1/security/login', {
      username: SUPERSET_ADMIN_USERNAME,
      password: SUPERSET_ADMIN_PASSWORD,
      provider: 'db'
    });
    
    // 从登录响应中提取访问令牌
    const accessToken = loginResponse.data.access_token;
    if (!accessToken) {
      throw new Error('登录成功但未返回访问令牌');
    }
    
    console.log('✅ 登录成功，获取到访问令牌');
    
    // 设置授权头
    api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
    
    console.log('2. 获取CSRF令牌...');
    const csrfResponse = await api.get('/api/v1/security/csrf_token/');
    const csrfToken = csrfResponse.data.result;
    console.log('✅ 获取到CSRF令牌:', csrfToken);
    
    // 从响应中提取会话cookie
    const sessionCookie = extractSessionCookie(csrfResponse.headers);
    if (sessionCookie) {
      console.log('✅ 获取到会话Cookie');
      // 将会话cookie添加到请求头中
      api.defaults.headers.common['Cookie'] = `session=${sessionCookie}`;
    } else {
      console.warn('⚠️ 未能从响应中提取会话Cookie');
    }
    
    // 设置CSRF令牌头
    api.defaults.headers.common['X-CSRFToken'] = csrfToken;
    
    // 测试API调用 - 获取仪表板列表
    await getDashboardList();
    
    // 如果仪表板列表为空，测试其他端点
    await testOtherEndpoints();
    
    console.log('3. 请求Guest Token...');
    console.log('使用的请求头:', JSON.stringify(api.defaults.headers, null, 2));
    
    const response = await api.post('/api/v1/security/guest_token/', {
      user: {
        username: 'embedded_viewer',
        first_name: '嵌入',
        last_name: '用户'
      },
      resources: [
        {
          type: 'dashboard', 
          id: DASHBOARD_UUID
        }
      ],
      rls: []
    });
    
    console.log('✅ 成功获取Guest Token!');
    return response.data.token;
  } catch (error) {
    console.error('❌ 获取Guest Token失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
      console.error('响应头:', JSON.stringify(error.response.headers, null, 2));
      
      // 更详细的错误信息
      if (error.response.data && error.response.data.errors) {
        console.error('详细错误信息:');
        error.response.data.errors.forEach((err, idx) => {
          console.error(`错误 ${idx + 1}:`, err.message);
          if (err.extra) console.error('额外信息:', err.extra);
        });
      }
    } else {
      console.error(error.message);
    }
    throw error;
  }
}

/**
 * 测试其他API端点以帮助诊断问题
 */
async function testOtherEndpoints() {
  console.log('\n----- 测试其他API端点 -----');
  
  try {
    // 1. 尝试获取数据库列表
    console.log('获取数据库列表...');
    const dbQuery = { page: 0, page_size: 10 };
    const dbParams = new URLSearchParams();
    dbParams.append('q', JSON.stringify(dbQuery));
    
    const dbResponse = await api.get('/api/v1/database/', { params: dbParams });
    console.log(`数据库列表: 找到 ${dbResponse.data.count} 个数据库`);
    if (dbResponse.data.count > 0) {
      console.log(`示例数据库: ${dbResponse.data.result[0].database_name}`);
    }
    
    // 2. 尝试获取图表列表
    console.log('\n获取图表列表...');
    const chartQuery = { page: 0, page_size: 10 };
    const chartParams = new URLSearchParams();
    chartParams.append('q', JSON.stringify(chartQuery));
    
    const chartResponse = await api.get('/api/v1/chart/', { params: chartParams });
    console.log(`图表列表: 找到 ${chartResponse.data.count} 个图表`);
    if (chartResponse.data.count > 0) {
      console.log(`示例图表: ${chartResponse.data.result[0].slice_name}`);
    }
    
    // 3. 尝试获取数据集列表
    console.log('\n获取数据集列表...');
    const datasetQuery = { page: 0, page_size: 10 };
    const datasetParams = new URLSearchParams();
    datasetParams.append('q', JSON.stringify(datasetQuery));
    
    const datasetResponse = await api.get('/api/v1/dataset/', { params: datasetParams });
    console.log(`数据集列表: 找到 ${datasetResponse.data.count} 个数据集`);
    if (datasetResponse.data.count > 0) {
      console.log(`示例数据集: ${datasetResponse.data.result[0].table_name}`);
    }
    
    console.log('\n总结:');
    console.log(`- 数据库: ${dbResponse.data.count}`);
    console.log(`- 图表: ${chartResponse.data.count}`);
    console.log(`- 数据集: ${datasetResponse.data.count}`);
    console.log(`- 仪表板: 0 (从之前的请求)`);
    
    if (dbResponse.data.count > 0 || chartResponse.data.count > 0 || datasetResponse.data.count > 0) {
      console.log('\n✅ 其他API端点返回了数据，这表明可能是仪表板特定的问题');
      console.log('可能的原因:');
      console.log('1. 这个Superset实例中确实没有仪表板');
      console.log('2. 用户对仪表板没有足够的权限');
    } else {
      console.log('\n⚠️ 所有API端点都没有返回数据');
      console.log('可能的原因:');
      console.log('1. 这是一个新的Superset实例，没有任何数据');
      console.log('2. 用户权限问题 - 即使是admin用户也可能配置了有限的权限');
      console.log('3. API配置问题 - 可能API被禁用或有特殊的访问控制');
    }
    
  } catch (error) {
    console.error('测试其他端点时发生错误:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('错误信息:', error.message);
    }
  }
  
  console.log('--------------------------\n');
}

// 主函数
async function main() {
  try {
    const token = await getGuestToken();
    
    console.log('\n------- Guest Token -------');
    console.log(token);
    console.log('---------------------------\n');
    
    console.log('📝 前端嵌入示例代码:');
    console.log(`
import { embedDashboard } from "@superset-ui/embedded-sdk";

embedDashboard({
  id: "${DASHBOARD_UUID}",
  supersetDomain: "${SUPERSET_URL}",
  mountPoint: document.getElementById("dashboard-container"),
  fetchGuestToken: () => {
    // 实际使用中，应从你的后端API获取token
    return Promise.resolve("${token}");
  }
});
`);
  } catch (error) {
    console.error('程序执行失败');
  }
}

main(); 