# Superset Guest Token 获取示例 (Node.js)

这是一个简单的Node.js示例，演示如何从Superset获取Guest Token用于仪表板嵌入。

## 前提条件

- Node.js环境 (v14+)
- 运行中的Superset实例
- 具有`can_grant_guest_token`权限的Superset管理员账户
- 已启用嵌入功能的仪表板（通过仪表板点击"..."菜单中的"嵌入仪表板"进行设置）

## 安装步骤

1. 安装依赖项：

```bash
npm install
```

2. 创建`.env`文件：

```bash
cp .env.example .env
```

3. 编辑`.env`文件，填入您的Superset信息：

```
SUPERSET_URL=http://your-superset-server:8088
SUPERSET_ADMIN_USERNAME=your_admin_username
SUPERSET_ADMIN_PASSWORD=your_admin_password
DASHBOARD_UUID=your-dashboard-embed-uuid
```

> 注意：DASHBOARD_UUID是在仪表板嵌入设置后生成的UUID，不是仪表板的ID。

## 运行示例

```bash
npm start
```

成功执行后，会显示：
- 获取到的Guest Token
- 可用于前端的嵌入代码示例

## 认证流程说明

本示例演示了完整的认证流程：

1. 使用管理员凭据向`/api/v1/security/login`接口进行登录认证
2. 获取JWT访问令牌(`access_token`)并设置到后续请求的`Authorization`头中
3. 获取CSRF令牌，用于防止跨站请求伪造
4. 使用JWT认证和CSRF令牌请求Guest Token

### 关于CSRF令牌和会话Cookie

Superset使用Flask-WTF实现CSRF保护，这需要会话Cookie和CSRF令牌配合工作：

1. 当请求CSRF令牌时，服务器会在响应中设置一个会话Cookie
2. 这个会话Cookie包含一个密钥，用于验证后续请求中的CSRF令牌
3. 后续的POST请求必须同时包含：
   - 原始会话Cookie（在Cookie头中）
   - 对应的CSRF令牌（在X-CSRFToken头中）

如果缺少正确的会话Cookie，即使提供了有效的CSRF令牌，请求也会被拒绝并显示"The CSRF session token is missing"错误。这就是为什么我们需要：

1. 从CSRF令牌响应中提取会话Cookie
2. 将该Cookie包含在后续请求中

本示例通过以下方式处理：
```javascript
// 从响应头提取会话Cookie
const sessionCookie = extractSessionCookie(csrfResponse.headers);
if (sessionCookie) {
  // 将会话Cookie添加到请求头
  api.defaults.headers.common['Cookie'] = `session=${sessionCookie}`;
}
```

## 正式环境使用注意事项

在生产环境中，**不要**：
- 在前端暴露管理员凭据
- 在客户端直接调用guest_token接口

**应该**：
- 创建一个安全的后端API端点
- 在后端维护管理员凭据并调用Superset API
- 前端通过您的API获取token
- 考虑JWT令牌的缓存和刷新策略，避免频繁登录
- 正确处理会话Cookie和CSRF令牌

## 完整的嵌入流程

1. 前端应用加载时，向您的后端API请求Guest Token
2. 您的后端使用管理员凭据请求Superset获取JWT令牌
3. 获取并保存会话Cookie和CSRF令牌
4. 使用JWT令牌、会话Cookie和CSRF令牌请求Guest Token
5. 后端将Guest Token返回给前端
6. 前端使用该Token和Superset嵌入SDK嵌入仪表板

## 排错指南

### CSRF令牌相关错误

如果遇到 `The CSRF session token is missing` 错误，请检查：
- 是否正确从CSRF响应中提取并重用了会话Cookie
- Cookie格式是否正确（通常为`session=<value>`）
- 服务器是否需要额外的Cookie属性（如Path、Domain等）
- 确保Cookie没有过期（某些Superset配置可能设置了较短的会话超时）

### 认证相关错误

如果遇到"Missing Authorization Header"错误，请确保：
- 登录API返回了有效的`access_token`
- 该令牌已正确设置在Authorization头中(`Bearer {token}`)
- 令牌未过期(Superset默认JWT令牌有效期较短)

## 更多资源

- [Superset嵌入SDK文档](https://www.npmjs.com/package/@superset-ui/embedded-sdk)
- [Superset嵌入相关配置](https://superset.apache.org/docs/installation/configuring-superset/#embedding-superset-dashboards)
- [Superset API认证](https://superset.apache.org/docs/rest-api/#section/Authentication)
- [Flask-WTF CSRF保护](https://flask-wtf.readthedocs.io/en/1.0.x/csrf/) 