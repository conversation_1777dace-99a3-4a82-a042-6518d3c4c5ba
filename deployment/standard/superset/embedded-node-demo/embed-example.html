<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Superset仪表板嵌入示例</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    #dashboard-container {
      width: 100%;
      height: 800px;
      border: 1px solid #ddd;
      margin-top: 20px;
    }
    .token-input {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      font-family: monospace;
    }
    button {
      padding: 10px 15px;
      background-color: #1ABC9C;
      border: none;
      color: white;
      cursor: pointer;
      border-radius: 4px;
    }
    button:hover {
      background-color: #16A085;
    }
  </style>
</head>
<body>
  <h1>Superset仪表板嵌入示例</h1>
  
  <div>
    <p>请输入Superset URL:</p>
    <input type="text" id="superset-url" class="token-input" value="http://localhost:8088" />
    
    <p>请输入仪表板嵌入UUID:</p>
    <input type="text" id="dashboard-uuid" class="token-input" placeholder="从仪表板嵌入配置获取的UUID" />
    
    <p>请输入Guest Token (从node-demo获取):</p>
    <input type="text" id="guest-token" class="token-input" placeholder="粘贴Guest Token到这里" />
    
    <button id="embed-btn">嵌入仪表板</button>
  </div>
  
  <div id="dashboard-container"></div>

  <script src="https://unpkg.com/@superset-ui/embedded-sdk"></script>
  <script>
    document.getElementById('embed-btn').addEventListener('click', function() {
      const supersetUrl = document.getElementById('superset-url').value.trim();
      const dashboardUuid = document.getElementById('dashboard-uuid').value.trim();
      const guestToken = document.getElementById('guest-token').value.trim();
      
      if (!supersetUrl || !dashboardUuid || !guestToken) {
        alert('请填写所有必填字段！');
        return;
      }
      
      // 清除容器内容
      const container = document.getElementById('dashboard-container');
      container.innerHTML = '';
      
      // 嵌入仪表板
      supersetEmbeddedSdk.embedDashboard({
        id: dashboardUuid,
        supersetDomain: supersetUrl,
        mountPoint: container,
        fetchGuestToken: () => Promise.resolve(guestToken),
        dashboardUiConfig: {
          hideTitle: false,
          filters: {
            expanded: true
          }
        }
      });
    });
  </script>
</body>
</html> 