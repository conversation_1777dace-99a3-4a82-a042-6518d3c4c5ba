# 关于打包部署

目前使用的方式是在backend目录使用  `make build-all` 来打包（如果lowcode编辑器也有修改，使用 `make build-full`），该命令会把前端文件、lowcode编辑器、后端代码全部打包到一个可执行文件中，再额外复制个配置文件就可以运行起来

存在的问题：
   1. 静态资源和接口返回的数据都没有用gzip，目前的情况是goframe不支持静态文件服务的gzip（gzip后的response有接近10倍的网络开销节约），虽然支持使用中间件对非静态资源进行gzip，但也不知效率几何，因此目前的解决方案是再加一层nginx转发，配置参考：
    ``` 
    server {
        listen 8888 default_server;
        listen [::]:8888 default_server;

        # 启用 gzip 压缩
        gzip on;
        gzip_comp_level 5;
        gzip_min_length 256;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        gzip_vary on;
        gzip_disable "msie6";

        client_max_body_size 50M;
      
        location / {
            # 后端接口和静态资源服务
            proxy_pass http://127.0.0.1:9002/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location /ws/ {
            # websocket 服务
            proxy_pass http://127.0.0.1:9002/ws/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 处理WebSocket超时
            proxy_read_timeout 86400s;
        }
    }
    ```


    ## docker-compose 部署

    在 .standard 目录下有标准的docker-compose相关配置