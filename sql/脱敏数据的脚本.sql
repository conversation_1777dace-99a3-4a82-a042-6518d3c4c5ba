
truncate table form_data_00000_00021 ;  -- 采购订单入库
truncate table form_data_00000_00022;   -- 零散入库单
truncate table form_data_00000_00008;   -- 报销费用
truncate table form_data_00000_00036;   -- 销售退货入库
truncate table form_data_00000_00041;   -- 样品退货入库单
truncate table form_data_00000_00037;   -- 销售订单出库
truncate table form_data_00000_00038;   -- 样品出库单
truncate table form_data_00000_00039;   -- 领料出库单
truncate table form_data_00000_00042;   -- 仓库调拨单
truncate table form_data_00000_00030;   -- 客户报备old
truncate table form_data_00000_00033;   -- 销售报价单
truncate table form_data_00000_00034;   -- 销售订单
truncate table form_data_00000_00035;   -- 样品申请单
truncate table form_data_00000_00012;   -- 采购申请单
truncate table form_data_00000_00013;   -- 采购订单
truncate table form_data_00000_00014;   -- 询价单
truncate table form_data_00000_00048;   -- 询价拆分
truncate table form_data_00000_00020;   -- 采购退货出库单
truncate table form_data_00000_00045;   -- 采购折扣单
truncate table form_data_00000_00046;   -- 采购批次库存退货出库单
truncate table form_data_00000_00050;   -- 借货入库
truncate table form_data_00000_00051;   -- 借货出库
truncate table form_data_00000_00052;   -- 还货入库
truncate table form_data_00000_00053;   -- 还货出库
truncate table form_data_00000_00040;   -- 库存档案
truncate table form_data_00000_00043;   -- 入库明细记录
truncate table form_data_00000_00044;   -- 出库明细记录单
truncate table form_data_00000_00055;   -- 锁定明细
truncate table form_data_00000_00059;   -- 产品批次管理
truncate table form_data_00000_00060;   -- 工作计划
truncate table form_data_00000_00061;  -- 工作日志
truncate table form_data_00000_00062;  -- 客户拜访记录
truncate table form_data_00000_00063;  -- 待报销项
truncate table form_data_00000_00065;  -- 借款
truncate table form_data_00000_00066;  -- 还款
truncate table form_data_00000_00067;  -- 采购出入库明细-对账
truncate table form_data_00000_00068;  -- 采购对账单
truncate table form_data_00000_00069;  -- 采购开票
truncate table form_data_00000_00070;  -- 销售出入库明细-对账
truncate table form_data_00000_00071;  -- 销售对账单
truncate table form_data_00000_00072;  -- 销售开票
truncate table form_data_00000_00073;  -- 工资条
truncate table form_data_00000_00074;  -- 销售订单明细-开票
truncate table form_data_00000_00075;  -- 采购订单明细-开票
truncate table form_data_00000_00076;  -- 应收账款
truncate table form_data_00000_00077;  -- 收款单
truncate table form_data_00000_00078;  -- 请款单
truncate table form_data_00000_00079;  -- 付款单
truncate table form_data_00000_00080;  -- 超付记录
truncate table form_data_00000_00081;  -- 超收记录
truncate table form_data_00000_00082;  -- 请款单明细
truncate table form_data_00000_00083;  -- 应收账款明细
truncate table form_data_00000_00084;  -- 销售开票明细
truncate table form_data_00000_00085;  -- 采购开票明细
truncate table form_data_00000_00086;  -- 客户拜访记录
truncate table form_data_00000_00087;  -- 根据销售订单开票明细记录
truncate table form_data_00000_00088;  -- 根据销售出库开票明细记录
truncate table form_data_00000_00089;  -- 超付抵扣记录
truncate table form_data_00000_00090;  -- 超收抵扣记录
truncate table form_data_00000_00091;  -- 根据采购订单开票明细记录
truncate table form_data_00000_00092;  -- 根据采购入库/对账开票明细记录

truncate table form_data_00000_00093;  -- 销售月报表
truncate table form_data_00000_00094;  -- 报销月报表
truncate table form_data_00000_00095;  -- 费用报销明细
truncate table form_data_00000_00096;  -- 统计用请款明细-根据采购入库/对账/开票
truncate table form_data_00000_00097;  -- 统计用请款明细-根据采购订单
truncate table form_data_00000_00098;  -- 统计用应收明细-根据销售入库/对账/开票
truncate table form_data_00000_00099;  -- 统计用应收明细-根据销售订单
truncate table form_data_00000_00100;  -- 样品跟进记录
truncate table form_data_00000_00101;  -- 样品反馈汇总表
truncate table form_data_00000_00102;  -- 销售报价单明细
truncate table form_data_00000_00103;  -- 采购询价单明细
truncate table form_data_00000_00104;  -- 费用报销调整和打印
truncate table form_data_00000_00105;  -- 个人常用信息管理
truncate table form_data_00000_00106;  -- 信息费报销
truncate table form_data_00000_00107;  -- 样品打印
truncate table form_data_00000_00108;  -- 样品跟进记录-批量

truncate table flow_instance ;             	   -- 流程实例
truncate table flow_instance_history ;          -- 流程节点审核记录
truncate table flow_instance_history_auditor ;  -- 流程节点审批人详细记录
truncate table am_config_history  ;             -- 自动化执行记录











truncate table form_data_00000_00010 ;-- 用户拓展信息
truncate table form_data_00000_00028; -- 问题反馈
truncate table form_data_00000_00047; -- 报备
truncate table form_data_00000_00030;

-- 用户信息脱敏
SET @row_number = 0;
UPDATE sys_user SET 
contact_phone = CASE 
    WHEN contact_phone IS NOT NULL THEN '13800138000' 
    ELSE NULL 
END,
id_number = CASE 
    WHEN id_number IS NOT NULL THEN '440101199001011234' 
    ELSE NULL 
END,
email = CONCAT(
    LEFT(email, 1),
    REPEAT('*', POSITION('@' IN email) - 2),
    SUBSTRING(email, POSITION('@' IN email))
),
last_login_ip = CONCAT(
    SUBSTRING_INDEX(last_login_ip, '.', 2),
    '.0.0'
),
username = CONCAT('用户', (@row_number:=@row_number + 1))
WHERE 1=1
ORDER BY code;

-- 仓库脱敏
SET @row_number = 0;
update form_data_00000_00003 set node_oclz0yiwmh2 = CONCAT('仓库', (@row_number:=@row_number + 1)), node_oclz0yiwmh3= null where 1=1 


-- 客户档案脱敏
SET @row_number1 = 0, @row_number2 = 0, @row_number3 = 0, @row_number4 = 0;

UPDATE form_data_00000_00009 SET 
-- 普通文本字段的姓名脱敏
node_oclz0yjdg93 = CONCAT('法人', (@row_number1:=@row_number1 + 1)),
node_oclzmamyk15u = CONCAT('寄货人', (@row_number2:=@row_number2 + 1)),
node_oclzmamyk15z = CONCAT('收货人', (@row_number3:=@row_number3 + 1)),

-- JSON格式字段直接置为NULL
node_oclz0yjdg9l = NULL,  -- 联系人(json)
node_ocm7kb5ips2 = NULL,  -- 最近联系人(json)

-- 电话号码统一替换为固定值
node_oclzman5su1 = CASE WHEN node_oclzman5su1 IS NOT NULL THEN '020-12345678' ELSE NULL END,
node_oclzmamyk15v = CASE WHEN node_oclzmamyk15v IS NOT NULL THEN '13800138000' ELSE NULL END,
node_oclzmamyk15w = CASE WHEN node_oclzmamyk15w IS NOT NULL THEN '020-12345678' ELSE NULL END,
node_oclzmamyk160 = CASE WHEN node_oclzmamyk160 IS NOT NULL THEN '13800138000' ELSE NULL END,
node_oclzmamyk161 = CASE WHEN node_oclzmamyk161 IS NOT NULL THEN '020-12345678' ELSE NULL END,
node_ocm7lbksgf1 = CASE WHEN node_ocm7lbksgf1 IS NOT NULL THEN '13800138000' ELSE NULL END,
node_ocm7kb5ips3 = CASE WHEN node_ocm7kb5ips3 IS NOT NULL THEN '13800138000' ELSE NULL END,

-- 地址信息脱敏
node_oclz0yjdg9d = CASE WHEN node_oclz0yjdg9d IS NOT NULL THEN '**省**市**区**街道' ELSE NULL END,
node_oclzmamyk15x = CASE WHEN node_oclzmamyk15x IS NOT NULL THEN '**省**市**区**街道' ELSE NULL END,

-- 客户简介脱敏
node_oclz0yjdg9f = CASE WHEN node_oclz0yjdg9f IS NOT NULL THEN '该客户的详细信息已脱敏处理' ELSE NULL END,

-- 公司名称脱敏
node_oclz0yjdg92 = CONCAT('公司', (@row_number4:=@row_number4 + 1))

WHERE 1=1
ORDER BY id;

-- 客户联系人脱敏
SET @row_number1 = 0;

UPDATE form_data_00000_00057 SET 
-- 联系人姓名脱敏
node_ocm4214ete3 = CONCAT('联系人', (@row_number1:=@row_number1 + 1)),

-- 电话号码脱敏
node_ocm4214ete8 = CASE WHEN node_ocm4214ete8 IS NOT NULL THEN '13800138000' ELSE NULL END,
node_ocm4214etea = CASE WHEN node_ocm4214etea IS NOT NULL THEN '020-12345678' ELSE NULL END,

-- 邮箱脱敏
node_ocm4214eteb = CASE 
    WHEN node_ocm4214eteb IS NOT NULL THEN 
        CONCAT(LEFT(node_ocm4214eteb, 1), '***', SUBSTRING(node_ocm4214eteb, POSITION('@' IN node_ocm4214eteb)))
    ELSE NULL 
END,

-- 职务描述脱敏
node_ocm4214ete7 = CASE WHEN node_ocm4214ete7 IS NOT NULL THEN '职务描述信息已脱敏处理' ELSE NULL END,

-- 关联客户json字段脱敏 - 修改label为***
node_ocm4214ete2 = CASE 
    WHEN node_ocm4214ete2 IS NOT NULL THEN 
        JSON_SET(node_ocm4214ete2, '$.label', '***')
    ELSE NULL 
END,

-- 录单人json字段直接置为NULL
node_ocm422ojn72 = NULL

WHERE 1=1
ORDER BY id;


-- 公司脱敏
SET @row1 = 0, @row2 = 0, @row3 = 0;

UPDATE form_data_00000_00002 SET
node_oclzj8ml7g1 = CONCAT('公司简称', (@row1 := @row1 + 1)),
node_oclz0yivei2 = CONCAT('公司全称', (@row2 := @row2 + 1)),
node_ocm0c5ct4g1 = CASE WHEN node_ocm0c5ct4g1 IS NOT NULL THEN '**省**市**区**街道' ELSE NULL END,
node_ocm0c5ct4g2 = CASE WHEN node_ocm0c5ct4g2 IS NOT NULL THEN '020-12345678' ELSE NULL END,
node_ocm0c5ct4g3 = CASE WHEN node_ocm0c5ct4g3 IS NOT NULL THEN '020-12345678' ELSE NULL END,
node_oclz0yivei3 = CASE WHEN node_oclz0yivei3 IS NOT NULL THEN '公司描述已脱敏处理' ELSE NULL END,
node_ocm1n5qlnq1 = CONCAT('收货人', (@row3 := @row3 + 1)),
node_ocm200eyyw1 = CASE WHEN node_ocm200eyyw1 IS NOT NULL THEN '13800138000' ELSE NULL END,
node_ocm200eyyw4 = CASE WHEN node_ocm200eyyw4 IS NOT NULL THEN '**省**市**区**街道' ELSE NULL END
WHERE 1=1
ORDER BY id;

-- 更新客户档案中的所属公司
update   form_data_00000_00009 k
left join form_data_00000_00002 g on g.id = k.node_oclz0yjdg99->>'$.value'
set k.node_oclz0yjdg99 = JSON_SET(k.node_oclz0yjdg99, '$.label', g.node_oclzj8ml7g1)
where 1=1 



-- 脱敏供应商
SET @row1 = 0, @row2 = 0, @row3 = 0, @row4 = 0, @row5 = 0, @row6 = 0;

UPDATE form_data_00000_00004 SET
-- 供应商基本信息脱敏
node_oclz0yixc65 = CONCAT('供应商', (@row1 := @row1 + 1)),
node_oclz0yixc66 = CONCAT('供应商全称', (@row2 := @row2 + 1)),
node_oclz2b8ya61 = CONCAT('法人', (@row3 := @row3 + 1)),
node_oclz2b8x1zq = CASE WHEN node_oclz2b8x1zq IS NOT NULL THEN '**省**市**区**街道' ELSE NULL END,
node_ocm0d51z1j1 = CASE WHEN node_ocm0d51z1j1 IS NOT NULL THEN '020-12345678' ELSE NULL END,

-- JSON字段直接置NULL
node_oclz0yixc61k = NULL,  -- 联系人(json)
node_oclz0yixc63 = NULL,   -- 所属公司(json)

node_ocm7k74tr91 = null,
node_ocm7lbkt961 = '13800138000',

node_ocm7kb5h3s2 = null,
node_ocm7kb5h3s3 = '13800138000', 
-- 联系人类信息脱敏
node_ocm1jzrqcd4 = CONCAT('收货人', (@row4 := @row4 + 1)),
node_ocm1jzrqcd5 = CASE WHEN node_ocm1jzrqcd5 IS NOT NULL THEN '13800138000' ELSE NULL END,
node_ocm1jzrqcd2 = CONCAT('收货人', (@row5 := @row5 + 1)),
node_ocm1jzrqcd3 = CASE WHEN node_ocm1jzrqcd3 IS NOT NULL THEN '13800138000' ELSE NULL END,

-- 描述/备注类字段脱敏
node_oclz0yixc6b = CASE WHEN node_oclz0yixc6b IS NOT NULL THEN '经营范围信息已脱敏' ELSE NULL END,
node_ocm1j0y4ww2j = CASE WHEN node_ocm1j0y4ww2j IS NOT NULL THEN '供应商简介信息已脱敏' ELSE NULL END,
node_ocm1j0y4ww2l = CASE WHEN node_ocm1j0y4ww2l IS NOT NULL THEN '合作现状信息已脱敏' ELSE NULL END,
node_ocm1j0y4ww2k = CASE WHEN node_ocm1j0y4ww2k IS NOT NULL THEN '合作前景信息已脱敏' ELSE NULL END,
node_ocm1j0y4ww2m = CASE WHEN node_ocm1j0y4ww2m IS NOT NULL THEN '跟进策略信息已脱敏' ELSE NULL END,

-- 银行账户信息脱敏
node_ocm1j0ychj10 = CONCAT('账户', (@row6 := @row6 + 1)),
node_ocm1j0ychj11 = CASE WHEN node_ocm1j0ychj11 IS NOT NULL THEN '6200 **** **** 1234' ELSE NULL END,
node_ocm1j0ychj1f = CASE WHEN node_ocm1j0ychj1f IS NOT NULL THEN '账户备注信息已脱敏' ELSE NULL END

WHERE 1=1
ORDER BY id;


-- 脱敏供应商的联系人
SET @row_number1 = 0, @row_number2 = 0;

UPDATE form_data_00000_00058 SET 
-- 联系人姓名脱敏
node_ocm426k11c3 = CONCAT('联系人', (@row_number1 := @row_number1 + 1)),

-- 联系电话脱敏
node_ocm426k11c4 = CASE WHEN node_ocm426k11c4 IS NOT NULL THEN '13800138000' ELSE NULL END,

-- 传真脱敏
node_ocm426k11c7 = CASE WHEN node_ocm426k11c7 IS NOT NULL THEN '020-12345678' ELSE NULL END,

-- 职务脱敏
node_ocm426k11c5 = CONCAT('职务', (@row_number2 := @row_number2 + 1)),

-- 备注脱敏
node_ocm426k11c8 = CASE WHEN node_ocm426k11c8 IS NOT NULL THEN '联系人备注信息已脱敏处理' ELSE NULL END,

-- 关联供应商json字段特殊处理 - 只修改label为*****
node_ocm426k11c2 = CASE 
    WHEN node_ocm426k11c2 IS NOT NULL THEN 
        JSON_SET(node_ocm426k11c2, '$.label', '*****')
    ELSE NULL 
END

WHERE 1=1
ORDER BY id;


-- 银行帐户脱敏 
SET @row_number1 = 0, @row_number2 = 0, @row_number3 = 0;

UPDATE form_data_00000_00001 SET 
-- 银行名称脱敏
node_oclz0yiusl3 = CONCAT('银行', (@row_number1 := @row_number1 + 1)),

-- 银行账号脱敏 - 保留前4位和后4位，中间用星号代替
node_oclz0yiusl4 = CASE 
    WHEN node_oclz0yiusl4 IS NOT NULL AND LENGTH(node_oclz0yiusl4) > 8 THEN 
        CONCAT(LEFT(node_oclz0yiusl4, 4), REPEAT('*', LENGTH(node_oclz0yiusl4) - 8), RIGHT(node_oclz0yiusl4, 4))
    WHEN node_oclz0yiusl4 IS NOT NULL THEN 
        '6200****1234'
    ELSE NULL 
END,

-- 开户行脱敏
node_oclz0yiusl5 = CONCAT('开户行', (@row_number2 := @row_number2 + 1)),

-- 所属公司json字段特殊处理 - 只修改label为*****
node_oclz0yiw701 = CASE 
    WHEN node_oclz0yiw701 IS NOT NULL THEN 
        JSON_SET(node_oclz0yiw701, '$.label', '*****')
    ELSE NULL 
END
WHERE 1=1
ORDER BY id;

