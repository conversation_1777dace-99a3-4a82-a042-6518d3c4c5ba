import ws from 'k6/ws';
import { check } from 'k6';
import { Trend, Counter } from 'k6/metrics';

// 自定义指标
let connectTrend = new Trend('connect_duration');
let messagesSent = new Counter('messages_sent');
let messagesReceived = new Counter('messages_received');

export let options = {
    vus: 20000, // 虚拟用户数量
    duration: '2m', // 测试持续时间
};

export default function () {
    // const url = 'ws://**************:9002/ws/connect';
    const url = 'ws://localhost:9001/ws/connect';
    const params = {};

    let start = new Date(); // 记录连接开始时间

    let response = ws.connect(url, params, function (socket) {
        let connectTime = new Date() - start;
        connectTrend.add(connectTime);

        socket.on('open', function () {
            console.log('连接已打开');
            // 发送消息
            socket.send(JSON.stringify({
                seq: "",
                atc: "test",
                data:"11111",
            }));
            messagesSent.add(1);
        });

        socket.on('message', function (message) {
            // console.log('收到消息:', message);
            messagesReceived.add(1);
            // 根据需求，可以在这里添加断言或其他逻辑
        });

        socket.on('close', function () {
            console.log('连接已关闭');
        });

        socket.on('error', function (e) {
            if (e.error() !== 'websocket: close sent') {
                console.log('发生错误:', e.error());
            }
        });

        // 示例：每隔10秒发送一次消息
        socket.setInterval(function () {
            socket.send(JSON.stringify({
                seq: "",
                atc: "pong", //timer
                data:"11111",
            }));
            messagesSent.add(1);
        }, 2000);
    });

    // 检查连接是否成功
    check(response, { '连接状态码是101': (r) => r && r.status === 101 });
}