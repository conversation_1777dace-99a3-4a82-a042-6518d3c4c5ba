用k6对websocket进行压测
=========================

## 安装k6
安装压测工具 k6 可以通过多种方式，具体取决于你的操作系统。以下是一些常见的安装方法：

### 在 macOS 上安装

1. **使用 Homebrew 安装**：
   ```bash
   brew install k6
   ```

### 在 Windows 上安装

1. **使用 Chocolatey 安装**：
   首先确保你已经安装了 Chocolatey，然后在命令提示符中运行：
   ```bash
   choco install k6
   ```

### 在 Linux 上安装

1. **使用包管理器安装**：
   - **Debian/Ubuntu**：
     ```bash
     sudo apt update
     sudo apt install -y gnupg software-properties-common
     wget -q -O - https://dl.k6.io/key.gpg | sudo apt-key add -
     echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
     sudo apt update
     sudo apt install k6
     ```

   - **CentOS/RHEL**：
     ```bash
     sudo dnf install https://dl.k6.io/rpm/repo.rpm
     sudo dnf install k6
     ```

2. **使用 Docker 安装**：
   如果你有 Docker 环境，可以使用 Docker 运行 k6：
   ```bash
   docker pull grafana/k6
   docker run -i grafana/k6 run - <script.js
   ```

### 手动安装

1. **从 GitHub Releases 下载**：
   访问 [k6 的 GitHub Releases 页面](https://github.com/grafana/k6/releases)，下载适合你操作系统的二进制文件，然后解压并将其移动到你的 PATH 中。

安装完成后，你可以通过运行以下命令来验证 k6 是否安装成功：

```bash
k6 version
```

这将显示 k6 的版本信息，确认安装成功。

## 压测
```bash
k6 run index.js
```