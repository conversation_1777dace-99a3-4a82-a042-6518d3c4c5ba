package websocket

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
)

type Room struct {
	Id          string
	Name        string
	Password    string
	Clients     map[string]*Client
	ClientsLock sync.RWMutex

	Send chan []byte // 待发送的消息
}

func NewRoom(id string, name string, password string) *Room {
	return &Room{
		Id:       id,
		Name:     name,
		Password: password,
		Clients:  make(map[string]*Client),
		Send:     make(chan []byte, 100),
	}
}

// 写入消息
func (r *Room) write() {
	for message := range r.Send {
		for _, client := range r.Clients {
			client.Send <- message
		}
	}
}

// 添加用户
func (r *Room) AddClient(client *Client) {
	r.ClientsLock.Lock()
	defer r.ClientsLock.Unlock()
	r.Clients[client.Id] = client
}

// 移除用户
func (r *Room) RemoveClient(client *Client) {
	r.ClientsLock.Lock()
	defer r.ClientsLock.Unlock()
	delete(r.Clients, client.Id)
}

// 发送消息
func (r *Room) SendMessage(message []byte) {
	if r == nil {
		return
	}
	// 捕获异常
	defer func() {
		if rec := recover(); rec != nil {
			g.Log().Error(context.Background(), fmt.Sprintf("[%s]发送消息错误", r.Id), rec, string(debug.Stack()))
		}
	}()
	r.Send <- message
}
