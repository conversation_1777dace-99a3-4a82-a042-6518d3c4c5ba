package websocket

import (
	"backend/websocket/controllers"
	"backend/websocket/model"
	"backend/websocket/types"
	"context"
	"encoding/json"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

var (
	handlers        = make(map[string]types.HandlerFunc)
	handlersRWMutex sync.RWMutex
)

// 注册路由
func Register(key string, value types.HandlerFunc) {
	handlersRWMutex.Lock()
	defer handlersRWMutex.Unlock()
	handlers[key] = value
}
func getHandlers(key string) (value types.HandlerFunc, ok bool) {
	handlersRWMutex.RLock()
	defer handlersRWMutex.RUnlock()
	value, ok = handlers[key]
	return
}

// 处理消息
func Route(client *Client, message []byte) {
	request := &model.Request{}
	if err := json.Unmarshal(message, request); err != nil {
		g.Log().Error(context.Background(), "websocket 消息解析失败", err)
		return
	}

	disposeFunc, ok := getHandlers(request.Action)
	if !ok {
		g.Log().Error(context.Background(), "websocket 消息未找到对应的路由", request.Action)
		return
	}

	code, msg, data := disposeFunc(client, request.Data)

	response := &model.Response{
		Seq:    request.Seq,
		Action: request.Action,
		Response: &ghttp.DefaultHandlerResponse{
			Code:    code,
			Message: msg,
			Data:    data,
		},
	}
	client.SendMessage(response.Bytes())
}

// 初始化路由时注册控制器函数
func init() {
	Register("test", controllers.Test)
	Register("timer", controllers.Test)
	Register("pong", controllers.Pong)
	Register("login", controllers.Login)
}
