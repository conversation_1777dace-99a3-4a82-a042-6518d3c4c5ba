package websocket

import (
	"backend/internal/model/dto"
	"backend/websocket/model"
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"github.com/aid<PERSON>hanov/nanoid/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gorilla/websocket"
)

type Client struct {
	Id        string          // 客户端ID
	Addr      string          // 客户端地址
	Conn      *websocket.Conn // 客户端连接
	Send      chan []byte     // 待发送的消息
	LastPing  time.Time       // 最后一次心跳时间
	User      *dto.UserClaims // 用户信息
	Done      chan struct{}   // 用于通知退出的通道
	closeOnce sync.Once       // 用于确保关闭通道只执行一次
}

// 创建客户端
func NewClient(addr string, conn *websocket.Conn) *Client {
	id, _ := nanoid.New()
	client := &Client{
		Id:       id,
		Addr:     addr,
		Conn:     conn,
		Send:     make(chan []byte, 100),
		LastPing: time.Now(),
		Done:     make(chan struct{}),
	}
	go client.listen()
	go client.write()
	go client.heartbeat()
	return client
}

// 安全关闭 Done 通道
func (c *Client) CloseDone() {
	c.closeOnce.Do(func() {
		close(c.Done)
	})
}

// 监听客户端发送的消息
func (c *Client) listen() {
	defer func() {
		g.Log().Info(context.Background(), fmt.Sprintf("[%s] listen 结束", c.Addr))
		if r := recover(); r != nil {
			g.Log().Error(context.Background(), fmt.Sprintf("[%s]读取客户端数据错误", c.Addr), r, string(debug.Stack()))
		}
	}()
	defer close(c.Send)
	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
				g.Log().Info(context.Background(), fmt.Sprintf("[%s]客户端断开连接", c.Addr))
			} else {
				// 心跳超时关闭的时候也会触发这个错误，后续想办法更加优雅的解决
				g.Log().Error(context.Background(), fmt.Sprintf("[%s]读取客户端数据错误: %v", c.Addr, err))
			}
			return
		}

		// g.Log().Info(context.Background(), fmt.Sprintf("[%s]读取客户端数据", c.Addr), string(message))
		Route(c, message)
	}
}

// 写入消息
func (c *Client) write() {
	defer func() {
		g.Log().Info(context.Background(), fmt.Sprintf("[%s] write 结束", c.Addr))
		if r := recover(); r != nil {
			g.Log().Error(context.Background(), fmt.Sprintf("[%s]写入消息错误", c.Addr), r, string(debug.Stack()))
		}
	}()
	defer func() {
		GetServer().Unregister <- c
		c.Conn.Close()
		c.CloseDone()
	}()

	for {
		select {
		case <-c.Done:
			return
		case message, ok := <-c.Send:
			if !ok {
				return
			}
			_ = c.Conn.WriteMessage(websocket.TextMessage, message)
		}
	}
}

// 主动发起心跳检测，并且检测超时的客户端
func (c *Client) heartbeat() {
	ticker := time.NewTicker(time.Second * 30)
	defer ticker.Stop()
	defer func() {
		g.Log().Info(context.Background(), fmt.Sprintf("[%s] heartbeat 结束", c.Addr))
		if r := recover(); r != nil {
			g.Log().Info(context.Background(), fmt.Sprintf("[%s]心跳消息错误", c.Addr), r, string(debug.Stack()))
		}
	}()
	for {
		select {
		case <-c.Done:
			return
		case <-ticker.C:
			if time.Since(c.LastPing) > time.Second*60 {
				g.Log().Info(context.Background(), fmt.Sprintf("[%s]心跳检测超时，关闭连接", c.Addr))
				c.CloseDone()
				return
			}
			response := &model.Response{
				Action: "ping",
			}
			c.SendMessage(response.Bytes())
		}
	}

}

// 发送消息
func (c *Client) SendMessage(message []byte) {
	c.Send <- message
}

func (c *Client) GetId() string {
	return c.Id
}

func (c *Client) GetAddr() string {
	return c.Addr
}

func (c *Client) GetUser() *dto.UserClaims {
	return c.User
}

// 更新心跳时间
func (c *Client) UpdateHeartbeat() {
	c.LastPing = time.Now()
}
