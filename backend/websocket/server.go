package websocket

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gorilla/websocket"
)

type Server struct {
	Clients     map[string]*Client
	ClientsLock sync.RWMutex
	Users       map[int64]map[string]*Client
	UsersLock   sync.RWMutex
	Rooms       map[string]*Room
	RoomsLock   sync.RWMutex
	Register    chan *Client
	Unregister  chan *Client
	Broadcast   chan []byte
}

func NewServer() *Server {
	return &Server{
		Clients:    make(map[string]*Client),
		Rooms:      make(map[string]*Room),
		Register:   make(chan *Client, 1000),
		Unregister: make(chan *Client, 1000),
		Broadcast:  make(chan []byte, 1000),
	}
}

// 运行
func (s *Server) Run() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			clientCount := s.GetClientCount()
			g.Log().Info(context.Background(), fmt.Sprintf("[Websocket]当前客户端数量: %d", clientCount))
		case client := <-s.Register:
			s.AddClient(client)
		case client := <-s.Unregister:
			s.RemoveClient(client)
			s.RemoveUser(client)
			s.RemoveRoomClient(client)
		case message := <-s.Broadcast:
			clients := s.GetAllClients()
			for _, client := range clients {
				client.Send <- message
			}
		}
	}
}

// 客户端连接
func (s *Server) Connect(ctx context.Context, r *ghttp.Request) {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // 允许所有来源的连接
		},
	}
	conn, err := upgrader.Upgrade(r.Response.Writer, r.Request, nil)
	if err != nil {
		g.Log().Error(r.Context(), "WebSocket upgrade error", err)
		return
	}
	client := NewClient(r.RemoteAddr, conn)
	s.Register <- client
}

// 添加客户端
func (s *Server) AddClient(client *Client) {
	s.ClientsLock.Lock()
	defer s.ClientsLock.Unlock()
	s.Clients[client.Id] = client
}

// 移除客户端
func (s *Server) RemoveClient(client *Client) {
	s.ClientsLock.Lock()
	defer s.ClientsLock.Unlock()
	delete(s.Clients, client.Id)
}

// 获得当前客户端的数量
func (s *Server) GetClientCount() int {
	s.ClientsLock.RLock()
	defer s.ClientsLock.RUnlock()
	return len(s.Clients)
}

// 获得所有客户端
func (s *Server) GetAllClients() []*Client {
	s.ClientsLock.RLock()
	defer s.ClientsLock.RUnlock()
	clients := make([]*Client, 0, len(s.Clients))
	for _, client := range s.Clients {
		clients = append(clients, client)
	}
	return clients
}

// 添加用户
func (s *Server) AddUser(userId int64, client *Client) {
	s.UsersLock.Lock()
	defer s.UsersLock.Unlock()
	if _, ok := s.Users[userId]; !ok {
		s.Users[userId] = make(map[string]*Client)
	}
	s.Users[userId][client.Id] = client
}

// 移除用户
func (s *Server) RemoveUser(client *Client) {
	s.UsersLock.Lock()
	defer s.UsersLock.Unlock()
	if client.User != nil && client.User.Id > 0 {
		delete(s.Users[client.User.Id], client.Id)
	}
}

// 创建房间
func (s *Server) NewRoom(id string, name string, password string) {
	room := NewRoom(id, name, password)
	go room.write()
	s.AddRoom(room)
}

// 添加房间
func (s *Server) AddRoom(room *Room) {
	s.RoomsLock.Lock()
	defer s.RoomsLock.Unlock()
	s.Rooms[room.Id] = room
}

// 移除房间
func (s *Server) RemoveRoom(room *Room) {
	s.RoomsLock.Lock()
	defer s.RoomsLock.Unlock()
	delete(s.Rooms, room.Id)
}

// 获得某个房间的客户端数量
func (s *Server) GetRoomClientCount(roomId string) int {
	s.RoomsLock.RLock()
	defer s.RoomsLock.RUnlock()
	if room, ok := s.Rooms[roomId]; ok {
		return len(room.Clients)
	}
	return 0
}

// 移除所有房间的某个客户端
func (s *Server) RemoveRoomClient(client *Client) {
	s.RoomsLock.Lock()
	defer s.RoomsLock.Unlock()
	for _, room := range s.Rooms {
		room.RemoveClient(client)
	}
}

// 获得一个用户的所有连接
func (s *Server) GetUserClients(userId int64) []*Client {
	s.UsersLock.RLock()
	defer s.UsersLock.RUnlock()
	if clients, ok := s.Users[userId]; ok {
		clientsList := make([]*Client, 0, len(clients))
		for _, client := range clients {
			clientsList = append(clientsList, client)
		}
		return clientsList
	}
	return nil
}

// 给某个用户发送消息
func (s *Server) SendMessageToUser(userId int64, message []byte) {
	clients := s.GetUserClients(userId)
	for _, client := range clients {
		client.Send <- message
	}
}

// 广播(给所有的客户端发送消息
func (s *Server) SendMessageToAll(message []byte) {
	s.Broadcast <- message
}
