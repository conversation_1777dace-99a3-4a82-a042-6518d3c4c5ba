package project

import (
	model "backend/internal/model/common"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// 项目角色列表
type AdProjectRoleListReq struct {
	g.Meta `path:"/project_role/list" tags:"项目角色相关" summary:"项目角色列表请求"  method:"get"`
	Name   string `p:"name"  summary:"角色名称"`
	model.PageReq
}
type AdProjectRoleListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []*entity.AdProjectRole `json:"list"`
}

// 项目角色详情
type AdProjectRoleDetailReq struct {
	g.Meta `path:"/project_role/detail" tags:"项目角色相关"  summary:"角色详情" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AdProjectRoleDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.AdProjectRole
}

// 删除项目角色
type AdProjectRoleDeleteReq struct {
	g.Meta `path:"/project_role/delete" tags:"项目角色相关" summary:"删除项目角色" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AdProjectRoleDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存项目角色信息
type AdProjectRoleSaveReq struct {
	g.Meta      `path:"/project_role/save" tags:"项目角色相关" summary:"保存项目角色信息" method:"post"`
	Id          int64       `p:"id" summary:"id"`
	Name        interface{} `p:"name"`        // 角色名
	IsDefault   interface{} `p:"isDefault"`   // 是否是默认角色
	Instruction interface{} `p:"instruction"` // 角色说明
}

type AdProjectRoleSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id"`
}
