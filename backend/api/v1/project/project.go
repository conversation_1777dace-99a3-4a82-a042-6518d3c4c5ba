package project

import (
	model "backend/internal/model/common"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

// 项目列表
type AdProjectListReq struct {
	g.Meta `path:"/project/list" tags:"项目相关" summary:"项目列表请求"  method:"get"`
	Name   string `p:"name"  summary:"角色名称"`
	model.PageReq
}
type AdProjectListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []*with.AdProject `json:"list"`
}

// 项目详情
type AdProjectDetailReq struct {
	g.Meta `path:"/project/detail" tags:"项目相关"  summary:"角色详情" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AdProjectDetailRes struct {
	g.Meta `mime:"application/json"`
	*with.AdProject
}

// 删除项目
type AdProjectDeleteReq struct {
	g.Meta `path:"/project/delete" tags:"项目相关" summary:"删除项目" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AdProjectDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存项目信息
type AdProjectSaveReq struct {
	g.Meta      `path:"/project/save" tags:"项目相关" summary:"保存项目信息" method:"post"`
	Id          int64       `p:"id" summary:"id"`
	Name        interface{} `p:"name"`        // 项目名
	Instruction interface{} `p:"instruction"` // 项目说明
	Leader      interface{} `p:"leader"`
	SortOrder   interface{} `p:"sortOrder"`       // 排序字段
	Source      interface{} `p:"source"         ` // 来源
}

type AdProjectSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id"`
}
