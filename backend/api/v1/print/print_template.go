package print

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type ListReq struct {
	g.Meta `path:"/print_template/list"  method:"get" tags:"打印模版相关" summary:"获得打印模版列表"`
	model.PageReq
	Name           string `p:"name"  summary:"名称"`
	FormTemplateId int64  `p:"formTemplateId"  ` // 关联的模板id
}

type ListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.FormPrintTemplate `json:"list"`
}

// 打印模版详情
type FormPrintTemplateDetailReq struct {
	g.Meta `path:"/print_template/detail" tags:"打印模版相关"  summary:"打印模版详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormPrintTemplateDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.FormPrintTemplate
}

// 删除打印模版
type FormPrintTemplateDeleteReq struct {
	g.Meta `path:"/print_template/delete" tags:"打印模版相关" summary:"删除打印模版" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormPrintTemplateDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存打印模版信息
type FormPrintTemplateSaveReq struct {
	g.Meta          `path:"/print_template/save" tags:"打印模版相关" summary:"保存打印模版信息" method:"post"`
	Id              int64                `p:"id" summary:"id"`
	Name            string               `p:"name" v:"required#名称不能为空！"  summary:"名称"`
	FormTemplateId  int64                `p:"formTemplateId"  v:"required#打印模板关联的表单模板不能为空！"  ` // 关联的模板id
	TemplateContent string               `p:"templateContent"  v:"required#打印模板内容不能为空！" `      // 模版内容
	PrintSize       string               `p:"printSize"      v:"required#请选择纸张方向"         `    // 纸张方向
	PrintRotation   string               `p:"printRotation"    v:"required#请选择尺寸大小"   `        // 尺寸大小
	EnterWithData   []*dto.WithTableData `p:"enterWithData"   `                                // 关联表字段设置
	Padding         string               `p:"padding"         `                                // 边距
	Width           int                  `p:"width"         `                                  // 宽度 当PrintRotation=custom时，宽度有效
	Height          int                  `p:"height"         `                                 // 高度 当PrintRotation=custom时，高度有效
	SqlQueries      string               `p:"sqlQueries"     `                                 // sql数据源配置
}

type FormPrintTemplateSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id"`
}

// 修改打印模版的状态
type FormPrintTemplateStatusReq struct {
	g.Meta  `path:"/print_template/status" tags:"打印模版相关" summary:"修改打印模版的状态" method:"post"`
	Id      int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
	Enabled bool  `p:"enabled" v:"required#enabled不能为空！"  summary:"是否启用"`
}
type FormPrintTemplateStatusRes struct {
	g.Meta `mime:"application/json"`
}

// 修改打印模版的是否允许打印前编辑状态
type FormPrintTemplateAllowEditStatusReq struct {
	g.Meta    `path:"/print_template/allow_edit_status" tags:"打印模版相关" summary:"修改打印模版的是否允许打印前编辑状态" method:"post"`
	Id        int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
	AllowEdit bool  `p:"allowEdit" summary:"是否允许打印前编辑"` // 假设前端会传递布尔值，如果不是，需要调整
}
type FormPrintTemplateAllowEditStatusRes struct {
	g.Meta `mime:"application/json"`
}
