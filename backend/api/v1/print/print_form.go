package print

import "github.com/gogf/gf/v2/frame/g"

type PrintFormReq struct {
	g.Meta              `path:"/print/generate" tags:"打印" summary:"打印单据，获得打印的PDF" method:"post"`
	FormPrintTemplateId int64 `p:"formPrintTemplateId" v:"required#参数异常，打印模板id不能为空！"  summary:"打印模板id"`
	FormId              int64 `p:"formId" v:"required#参数异常，表单数据id不能为空！"  summary:"表单数据id"`
}
type PrintFormRes struct {
	g.Meta `mime:"application/json"`
	PdfUrl string `json:"pdfUrl"`
}

// Fetch HTML content for printing
type PrintFormHtmlReq struct {
	g.Meta              `path:"/print/generate-html" tags:"打印" summary:"获取打印单据的HTML内容" method:"post"`
	FormPrintTemplateId int64 `p:"formPrintTemplateId" v:"required#参数异常，打印模板id不能为空！"  summary:"打印模板id"`
	FormId              int64 `p:"formId" v:"required#参数异常，表单数据id不能为空！"  summary:"表单数据id"`
}

type PrintFormHtmlRes struct {
	g.Meta `mime:"application/json"`
	Html   string `json:"html"`
}

// Generate PDF from HTML content
type GeneratePdfFromHtmlReq struct {
	g.Meta              `path:"/print/generate-pdf-from-html" tags:"打印" summary:"根据HTML内容生成PDF" method:"post"`
	FormPrintTemplateId int64  `p:"formPrintTemplateId"  summary:"打印模板id (可选, 用于日志或校验)"`
	FormId              int64  `p:"formId" v:"required#参数异常，表单数据id不能为空！"  summary:"表单数据id (用于日志或文件名)"`
	HtmlContent         string `p:"htmlContent" v:"required#HTML内容不能为空" summary:"HTML内容"`
}

type GeneratePdfFromHtmlRes struct {
	g.Meta `mime:"application/json"`
	PdfUrl string `json:"pdfUrl"`
}
