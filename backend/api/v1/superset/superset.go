package superset

import (
	model "backend/internal/model/common"
	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

// AccessTokenReq 获取Superset的访问令牌请求
type AccessTokenReq struct {
	g.Meta `path:"/superset/access_token" method:"get" tags:"Superset相关" summary:"获取Superset的访问令牌"`
}

// AccessTokenRes 获取Superset的访问令牌响应
type AccessTokenRes struct {
	g.Meta      `mime:"application/json"`
	AccessToken string `json:"accessToken"` // 访问令牌
}

// EmbeddedGuestTokenReq 通过仪表盘ID获取嵌入的guest token请求
type EmbeddedGuestTokenReq struct {
	g.Meta         `path:"/superset/dashboard/guest_token" method:"get" tags:"Superset相关" summary:"通过仪表盘ID获取嵌入的guest token"`
	DashboardID    string   `p:"dashboardId" v:"required#仪表盘ID不能为空" summary:"仪表盘ID"`
	AllowedDomains []string `p:"allowedDomains" summary:"允许的域名，可选，默认为['*']"`
}

// EmbeddedGuestTokenRes 通过仪表盘ID获取嵌入的guest token响应
type EmbeddedGuestTokenRes struct {
	g.Meta    `mime:"application/json"`
	Token     string `json:"token"`     // guest token
	URL       string `json:"url"`       // Superset UI URL
	ExpiresAt int64  `json:"expiresAt"` // 过期时间戳
	UUID      string `json:"uuid"`      // 仪表盘UUID
}

// GuestTokenReq 获取Superset的guest token请求
type GuestTokenReq struct {
	g.Meta `path:"/superset/guest_token" method:"get" tags:"Superset相关" summary:"获取Superset的guest token"`
	// 可以添加其他参数，如需要特定的权限或资源ID等
	ResourceID   string `p:"resourceId" summary:"资源ID，可选"`
	ResourceType string `p:"resourceType" summary:"资源类型，如dashboard，可选"`
}

// GuestTokenRes 获取Superset的guest token响应
type GuestTokenRes struct {
	g.Meta    `mime:"application/json"`
	Token     string `json:"token"`     // guest token
	URL       string `json:"url"`       // Superset UI URL
	ExpiresAt int64  `json:"expiresAt"` // 过期时间戳
}

// DashboardListReq 获取Superset仪表盘列表请求
type DashboardListReq struct {
	g.Meta `path:"/superset/dashboard/list" method:"get" tags:"Superset相关" summary:"获取Superset仪表盘列表"`
	model.PageReq
	OrderColumn    string   `p:"order_column"    summary:"排序列"`
	OrderDirection string   `p:"order_direction" summary:"排序方向"`
	SelectColumns  []string `p:"select_columns"  summary:"选择的列"`
}

// DashboardListRes 获取Superset仪表盘列表响应
type DashboardListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []service.DashboardInfo `json:"list"`
}
