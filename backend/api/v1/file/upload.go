package file

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 图片上传
type ImageUploadReq struct {
	g.Meta `path:"/upload/image"  method:"post" tags:"文件相关" summary:"上传图片"`
	File   []*ghttp.UploadFile `json:"file" v:"required#请选择图片"`
}

type ImageUploadRes struct {
	g.Meta `mime:"application/json"`
	Urls   []string `json:"urls"`
}

// 文件上传
type FileUploadReq struct {
	g.Meta `path:"/upload/file"  method:"post" tags:"文件相关" summary:"上传图片"`
	File   []*ghttp.UploadFile `json:"file" v:"required#请选择文件"`
}

type FileUploadRes struct {
	g.Meta `mime:"application/json"`
	Urls   []string `json:"urls"`
}
