package system

import (
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

type MenuSearchReq struct {
	g.Meta    `path:"/menu/list" tags:"菜单管理" method:"get" summary:"菜单列表"`
	MenuType  int         `p:"menuType" `
	Title     string      `p:"title"`
	ParentId  int64       `p:"parentId"`
	CreatedAt interface{} `p:"createdAt"`
	RouteType int         `p:"routeType"`
}
type MenuTSearchReq struct {
	g.Meta `path:"/menu/tlist" tags:"菜单管理" method:"get" summary:"菜单列表"`
}
type MenuSaveReq struct {
	g.Meta               `path:"/menu/save" tags:"菜单管理" method:"post" summary:"添加菜单"`
	Id                   int64       `p:"id"`
	ParentId             int64       `p:"parentId"  v:"required#父级不能为空"`
	Title                string      `p:"title"  v:"required#菜单名称不能为空"`
	MenuType             int         `p:"menuType" v:"required|min:1#菜单类型不能为空|菜单类型必须大于0"`
	RouteType            int         `p:"routeType" v:"required-if:menuType,2|in:1,2,3,4,5,6,999#路由类型不能为空|路由类型不正确"`
	Route                string      `p:"route" `
	Mark                 string      `p:"mark"  v:"required#菜单权限标识不能为空"`
	Icon                 string      `p:"icon" `
	ApiAssociation       interface{} `p:"apiAssociation" `
	MenuFormTemplateId   int64       `p:"menuFormTemplateId"  v:"required-if:routeType,2#请选择表单"   ` // 绑定的表单模板id
	MenuFormTemplateType int         `p:"menuFormTemplateType"  `                                   // 绑定的表单模板页面类型
	DashboardId          int64       `p:"dashboardId"  `
	DashboardIdMobile    int64       `p:"dashboardIdMobile"  `
	Remark               string      `p:"remark" `
}

type MenuDeleteReq struct {
	g.Meta `path:"/menu/delete" tags:"菜单管理" method:"post" summary:"删除菜单"`
	Id     uint64 `p:"id" v:"required|min:1#id不能为空|id必须大于0"`
}
type MenuInfoReq struct {
	g.Meta `path:"/menu/info" tags:"菜单管理" method:"get" summary:"菜单详情"`
	Id     uint64 `p:"id" v:"required#id不能为空"`
}
type MenuMoveReq struct {
	g.Meta    `path:"/menu/move" tags:"菜单管理" method:"post" summary:"移动菜单顺序"`
	Id        int64  `p:"id" v:"required#id不能为空"`
	Direction string `p:"direction" v:"required|in:up,down#方向不能为空|方向不正确"`
}
type MenuSearchRes struct {
	g.Meta   `mime:"application/json"`
	MenuList []*with.SysMenu `json:"list"`
}

type MenuTSearchRes struct {
	g.Meta   `mime:"application/json"`
	MenuList []*with.SysMenu `json:"list"`
}
type MenuSaveRes struct {
	g.Meta `mime:"application/json"`
}
type MenuDeleteRes struct {
	g.Meta `mime:"application/json"`
}

type MenuInfoRes struct {
	g.Meta `mime:"application/json"`
	*with.SysMenu
}

type MenuMoveRes struct {
	g.Meta `mime:"application/json"`
}
