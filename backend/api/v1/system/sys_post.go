package system

import (
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

type PostSearchReq struct {
	g.Meta    `path:"/post/list" tags:"岗位管理" method:"get" summary:"岗位列表"`
	PostName  string      `p:"postName"`
	PostCode  string      `p:"postCode"`
	CreatedAt interface{} `p:"createdAt"`
}
type PostSaveReq struct {
	g.Meta    `path:"/post/save" tags:"岗位管理" method:"post" summary:"添加岗位"`
	PostID    uint64   `p:"postId"`
	ParentID  uint64   `p:"parentId"  v:"required#父级不能为空"`
	PostName  string   `p:"postName"  v:"required#岗位名称不能为空"`
	ListOrder int      `p:"listOrder" `
	Remark    string   `p:"remark" `
	WithRoles []uint64 `p:"withRoles" v:"foreach|min:1#角色格式不正确|角色格式不正确" `
}

type PostDeleteReq struct {
	g.Meta `path:"/post/delete" tags:"岗位管理" method:"post" summary:"删除岗位"`
	Id     uint64 `p:"id" v:"required|min:1#id不能为空|id必须大于0"`
}
type PostInfoReq struct {
	g.Meta `path:"/post/info" tags:"岗位管理" method:"get" summary:"岗位详情"`
	Id     uint64 `p:"id" v:"required#id不能为空"`
}
type PostSearchRes struct {
	g.Meta   `mime:"application/json"`
	PostList []*with.SysPost `json:"list"`
}

type PostSaveRes struct {
	g.Meta `mime:"application/json"`
}
type PostDeleteRes struct {
	g.Meta `mime:"application/json"`
}

type PostInfoRes struct {
	g.Meta `mime:"application/json"`
	*with.SysPost
}
