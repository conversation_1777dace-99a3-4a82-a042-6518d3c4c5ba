package system

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysRoleListReq 角色列表请求
type SysRoleListReq struct {
	g.Meta `path:"/role/list" tags:"角色管理" summary:"角色列表请求"  method:"get"`
	Name   string `json:"name"    summary:"角色名称"  ` // 角色名称
	Code   string `json:"code"    summary:"角色编码"`   // 角色编码
	model.PageReq
}

// SysRoleListReq 角色列表请求
type SysRoleListAllReq struct {
	g.Meta `path:"/role/list_all" tags:"角色管理" summary:"一次性获得所有角色"  method:"get"`
}

// 编辑角色请求
type SysRoleSaveReq struct {
	g.<PERSON>a      `path:"/role/save" tags:"角色管理" summary:"编辑/新增角色请求"  method:"post"`
	Id          uint                 `json:"id"       `
	Status      uint                 `json:"status"  summary:"状态;0:禁用;1:正常"`
	Permissions []dto.SysPermissions `json:"permissions"  summary:"权限信息"`
	ListOrder   uint                 `json:"listOrder" summary:"排序"`
	Name        string               `json:"name"    summary:"角色名称" v:"required#角色名称不能为空" `
	Remark      string               `json:"remark"   summary:"备注"  `
}

// 删除角色请求
type SysRoleDelReq struct {
	g.Meta `path:"/role/del" tags:"角色管理" summary:"删除角色"  method:"post"`
	Id     uint `json:"id"       ` //
}

// 角色详情请求
type SysRoleDetailReq struct {
	g.Meta `path:"/role/detail" tags:"角色管理"  summary:"角色详情" method:"get"`
	Id     uint `json:"id"       ` //
}

// 设置角色权限
type SysRoleSetPermsReq struct {
	g.Meta      `path:"/role/setPerms" tags:"角色管理"  summary:"设置角色权限" method:"post"`
	Id          uint   `json:"id"     v:"required#id不能为空"   ` //
	Permissions string `json:"permissions"  summary:"权限信息"`   //
}

type SysRoleListRes struct {
	g.Meta `mime:"application/json"`
	List   []*SysRoleListResItem `json:"list"` // 角色列表
	model.ListRes
}
type SysRoleListAllRes struct {
	g.Meta `mime:"application/json"`
	List   []*SysRoleListResItem `json:"list"` // 角色列表
}

type SysRoleListResItem struct {
	Id          uint        `json:"id"       `                        //
	Code        string      `json:"code"     summary:"编码" `           //
	Status      uint        `json:"status"   summary:"状态;0:禁用;1:正常" ` //
	ListOrder   uint        `json:"listOrder"  summary:"排序"`          //
	Name        string      `json:"name"      summary:"角色名称"`         //
	Remark      string      `json:"remark"    summary:"备注"`           //
	CreatedAt   *gtime.Time `json:"createdAt"  summary:"创建时间"`        //
	Users       uint        `json:"users"  summary:"关联用户数"`           //
	Permissions string      `json:"permissions"  summary:"权限"`        //

}
type SysRoleSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     uint `json:"id"       `
}

type SysRoleDelRes struct {
	g.Meta `mime:"application/json"`
}

type SysRoleDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.SysRole
}
type SysRoleSetPermsRes struct {
	g.Meta `mime:"application/json"`
}
