package system

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

// 获得当前用户信息
type CurrentSysUserReq struct {
	g.Meta `path:"/user/current" tags:"用户管理" summary:"当前用户信息请求"  method:"get"`
}

// 检索用户
type SysUserListReq struct {
	g.Meta        `path:"/user/list" tags:"用户管理" summary:"用户列表请求"  method:"get"`
	DeptId        int64  `json:"deptId" summary:"部门ID"  `
	Status        int    `json:"status" summary:"状态"  `
	Code          string `json:"code" summary:"编码"`
	Username      string `json:"username" summary:"用户名"`
	Email         string `json:"email" summary:"邮箱"`
	SystemAccount string `json:"systemAccount" summary:"系统账号"`
	ContactPhone  string `json:"contactPhone" summary:"手机号码"`
	PostId        int64  `json:"postId" summary:"岗位ID"`
	Summary       bool   `json:"summary" summary:"是否显示概要信息"`
	model.PageReq
}

// 创建or修改用户
type SysUserSaveReq struct {
	g.Meta          `path:"/user/save" tags:"用户管理" summary:"创建or修改用户请求"  method:"post"`
	Id              int64                `p:"id" summary:"用户ID"`
	Username        interface{}          `p:"username" v:"required#用户名不能为空" summary:"用户名"  `
	Email           interface{}          `p:"email" v:"email#邮箱格式错误" summary:"邮箱" `
	ContactPhone    interface{}          `p:"contactPhone" v:"required|phone#手机号不能为空|手机号格式错误"  summary:"手机号码"`
	IdNumber        interface{}          `p:"idNumber" v:"resident-id#身份证格式错误"  summary:"身份证"`
	HireDate        interface{}          `p:"hireDate" v:"date#入职日期格式错误"  summary:"入职日期"`
	ResignationDate interface{}          `p:"resignationDate" v:"date#离职日期格式错误"  summary:"离职日期"`
	Remark          interface{}          `p:"remark"`
	Birthday        interface{}          `p:"birthday" v:"date#日期格式错误"  summary:"出生日期"`
	Gender          interface{}          `p:"gender" summary:"性别 0女1男 2保密"`
	Status          interface{}          `p:"status"`
	ContactAddress  interface{}          `p:"contactAddress" summary:"联系地址"`
	Description     interface{}          `p:"description" summary:"描述"`
	WithDepts       []uint64             `p:"withDepts" summary:"所属部门" v:"foreach|min:1#部门格式不正确|部门格式不正确"`     // 所在部门
	WithPosts       []uint64             `p:"withPosts" summary:"所属岗位" v:"foreach|min:1#岗位格式不正确|岗位格式不正确"`     // 所属岗位
	WithRoles       []uint64             `p:"withRoles" summary:"拥有的角色" v:"foreach|min:1#角色格式不正确|角色格式不正确"`    // 拥有的角色
	WithCompanys    []uint64             `p:"withCompanys" summary:"关联的公司" v:"foreach|min:1#公司格式不正确|公司格式不正确"` // 关联的公司
	WithProjects    []*dto.AdProjectUser `p:"withProjects" summary:"关联的项目" v:"foreach#项目格式不正确"`               // 关联的项目
}

// 删除用户
type SysUserDeleteReq struct {
	g.Meta `path:"/user/delete" tags:"用户管理" summary:"删除用户请求"  method:"post"`
	Id     int64 `p:"id" v:"required#请选择要删除的用户" summary:"用户ID"`
}

// 修改状态
type SysUserUpdateStatusReq struct {
	g.Meta `path:"/user/updateStatus" tags:"用户管理" summary:"修改用户状态请求"  method:"post"`
	Id     int64 `p:"id" v:"required#请选择要修改状态的用户" summary:"用户ID"`
	Status int   `p:"status" v:"required#请选择要修改的状态" summary:"状态"`
}

// 重置密码
type SysUserResetPwdReq struct {
	g.Meta `path:"/user/resetPwd" tags:"用户管理" summary:"重置密码请求"  method:"post"`
	Id     int64 `p:"id" v:"required#请选择要重置密码的用户" summary:"用户ID"`
}

// 修改密码
type SysUserUpdatePwdReq struct {
	g.Meta `path:"/user/updatePwd" tags:"用户管理" summary:"修改密码请求"  method:"post"`
	OldPwd string `p:"oldPwd" summary:"旧密码"`
	NewPwd string `p:"newPwd" v:"required#新密码不能为空" summary:"新密码"`
}

// 用户详情
type SysUserDetailReq struct {
	g.Meta `path:"/user/detail" tags:"用户管理" summary:"用户详情请求"  method:"get"`
	Id     int64 `p:"id" v:"required#请选择要查看的用户" summary:"用户ID"`
}

type CurrentSysUserRes struct {
	g.Meta `mime:"application/json"`
	*dto.CurrentUser
}

type SysUserListRes struct {
	g.Meta `mime:"application/json"`
	List   []*with.SysUser `json:"list" summary:"列表"`
	model.ListRes
}
type SysUserSaveRes struct {
	g.Meta `mime:"application/json"`
	UserId int64 `json:"userId" summary:"用户ID"`
}

type SysUserDeleteRes struct {
	g.Meta `mime:"application/json"`
}

type SysUserUpdateStatusRes struct {
	g.Meta `mime:"application/json"`
}

type SysUserResetPwdRes struct {
	g.Meta `mime:"application/json"`
}

type SysUserUpdatePwdRes struct {
	g.Meta `mime:"application/json"`
}

type SysUserDetailRes struct {
	g.Meta `mime:"application/json"`
	*with.SysUser
}
