package system

import (
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

type DeptSearchReq struct {
	g.Meta   `path:"/dept/list" tags:"部门管理" method:"get" summary:"部门列表"`
	DeptName string `p:"deptName"`
	Status   string `p:"status"`
}
type DeptSaveReq struct {
	g.<PERSON>a   `path:"/dept/save" tags:"部门管理" method:"post" summary:"添加部门"`
	DeptID   uint64      `p:"deptId"`
	ParentID uint64      `p:"parentId"  v:"required#父级不能为空"`
	DeptName string      `p:"deptName"  v:"required#部门名称不能为空"`
	OrderNum int         `p:"orderNum" `
	Leader   interface{} `p:"leader"`
	Phone    string      `p:"phone"`
	Email    string      `p:"email"  v:"email#邮箱格式不正确"`
	Status   uint        `p:"status"  `
	Remark   string      `p:"remark"`
}

type DeptDeleteReq struct {
	g.<PERSON>a `path:"/dept/delete" tags:"部门管理" method:"post" summary:"删除部门"`
	Id     uint64 `p:"id" v:"required|min:1#id不能为空|id必须大于0"`
}
type DeptInfoReq struct {
	g.Meta `path:"/dept/info" tags:"部门管理" method:"get" summary:"部门详情"`
	Id     uint64 `p:"id" v:"required#id不能为空"`
}
type DeptSearchRes struct {
	g.Meta   `mime:"application/json"`
	DeptList []*with.SysDept `json:"list"`
}

type DeptSaveRes struct {
	g.Meta `mime:"application/json"`
}
type DeptDeleteRes struct {
	g.Meta `mime:"application/json"`
}

type DeptInfoRes struct {
	g.Meta `mime:"application/json"`
	*with.SysDept
}
