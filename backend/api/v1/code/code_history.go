package code

import (
	model "backend/internal/model/common"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type CodeHistoryListReq struct {
	g.Meta `path:"/code_history/list"  method:"get" tags:"代码库相关" summary:"获得代码库列表"`
	model.PageReq
	KeyWork string `p:"keyWord"  summary:"关键字"`
}

type CodeHistoryListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.CodeHistory `json:"list"`
}

// 代码库详情
type CodeHistoryDetailReq struct {
	g.Meta `path:"/code_history/detail" tags:"代码库相关"  summary:"代码库详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type CodeHistoryDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.CodeHistory
}

// 删除代码库
type CodeHistoryDeleteReq struct {
	g.Meta `path:"/code_history/delete" tags:"代码库相关" summary:"删除代码库" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type CodeHistoryDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存代码库信息
type CodeHistorySaveReq struct {
	g.Meta      `path:"/code_history/save" tags:"代码库相关" summary:"保存代码库信息" method:"post"`
	Id          int64  `p:"id" summary:"id"`
	Title       string `p:"title" v:"required#代码片段标题不能为空"  summary:"代码片段标题"`
	Instruction string `p:"instruction"  summary:"代码片段描述信息"`
	Content     string `p:"content"  v:"required#您还没有输入代码内容" summary:"代码片段内容"`
}

type CodeHistorySaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id"`
}
