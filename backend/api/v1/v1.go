// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package v1

import (
	"context"

	"backend/api/v1/automation"
	"backend/api/v1/code"
	"backend/api/v1/demo"
	"backend/api/v1/file"
	"backend/api/v1/flow"
	"backend/api/v1/form"
	"backend/api/v1/login"
	"backend/api/v1/print"
	"backend/api/v1/project"
	"backend/api/v1/saas"
	"backend/api/v1/superset"
	"backend/api/v1/system"
)

type IV1Automation interface {
	List(ctx context.Context, req *automation.ListReq) (res *automation.ListRes, err error)
	Depend(ctx context.Context, req *automation.DependReq) (res *automation.DependRes, err error)
	AmConfigDetail(ctx context.Context, req *automation.AmConfigDetailReq) (res *automation.AmConfigDetailRes, err error)
	AmConfigDelete(ctx context.Context, req *automation.AmConfigDeleteReq) (res *automation.AmConfigDeleteRes, err error)
	AmConfigSave(ctx context.Context, req *automation.AmConfigSaveReq) (res *automation.AmConfigSaveRes, err error)
	AmConfigStatus(ctx context.Context, req *automation.AmConfigStatusReq) (res *automation.AmConfigStatusRes, err error)
	AmConfigRetry(ctx context.Context, req *automation.AmConfigRetryReq) (res *automation.AmConfigRetryRes, err error)
	AmConfigHistoryList(ctx context.Context, req *automation.AmConfigHistoryListReq) (res *automation.AmConfigHistoryListRes, err error)
	AmConfigHistoryDetail(ctx context.Context, req *automation.AmConfigHistoryDetailReq) (res *automation.AmConfigHistoryDetailRes, err error)
	Metas(ctx context.Context, req *automation.MetasReq) (res *automation.MetasRes, err error)
}

type IV1Code interface {
	CodeHistoryList(ctx context.Context, req *code.CodeHistoryListReq) (res *code.CodeHistoryListRes, err error)
	CodeHistoryDetail(ctx context.Context, req *code.CodeHistoryDetailReq) (res *code.CodeHistoryDetailRes, err error)
	CodeHistoryDelete(ctx context.Context, req *code.CodeHistoryDeleteReq) (res *code.CodeHistoryDeleteRes, err error)
	CodeHistorySave(ctx context.Context, req *code.CodeHistorySaveReq) (res *code.CodeHistorySaveRes, err error)
}

type IV1Demo interface {
	Hello(ctx context.Context, req *demo.HelloReq) (res *demo.HelloRes, err error)
	HelloConfig(ctx context.Context, req *demo.HelloConfigReq) (res *demo.HelloConfigRes, err error)
	HelloCache(ctx context.Context, req *demo.HelloCacheReq) (res *demo.HelloCacheRes, err error)
	HelloNextId(ctx context.Context, req *demo.HelloNextIdReq) (res *demo.HelloNextIdRes, err error)
	StartInstance(ctx context.Context, req *demo.StartInstanceReq) (res *demo.StartInstanceRes, err error)
	HelloDemo(ctx context.Context, req *demo.HelloDemoReq) (res *demo.HelloDemoRes, err error)
	HelloFlowCreatedReload(ctx context.Context, req *demo.HelloFlowCreatedReloadReq) (res *demo.HelloFlowCreatedReloadRes, err error)
	HelloQueueTest(ctx context.Context, req *demo.HelloQueueTestReq) (res *demo.HelloQueueTestRes, err error)
	HelloWebsocketBroadcast(ctx context.Context, req *demo.HelloWebsocketBroadcastReq) (res *demo.HelloWebsocketBroadcastRes, err error)
}

type IV1File interface {
	ImageUpload(ctx context.Context, req *file.ImageUploadReq) (res *file.ImageUploadRes, err error)
	FileUpload(ctx context.Context, req *file.FileUploadReq) (res *file.FileUploadRes, err error)
}

type IV1Flow interface {
	Create(ctx context.Context, req *flow.CreateReq) (res *flow.CreateRes, err error)
	Get(ctx context.Context, req *flow.GetReq) (res *flow.GetRes, err error)
	AuditInstances(ctx context.Context, req *flow.AuditInstancesReq) (res *flow.AuditInstancesRes, err error)
	InstanceInfo(ctx context.Context, req *flow.InstanceInfoReq) (res *flow.InstanceInfoRes, err error)
	Approve(ctx context.Context, req *flow.ApproveReq) (res *flow.ApproveRes, err error)
	MyList(ctx context.Context, req *flow.MyListReq) (res *flow.MyListRes, err error)
	MyCcList(ctx context.Context, req *flow.MyCcListReq) (res *flow.MyCcListRes, err error)
	MyStartList(ctx context.Context, req *flow.MyStartListReq) (res *flow.MyStartListRes, err error)
	MyDoneList(ctx context.Context, req *flow.MyDoneListReq) (res *flow.MyDoneListRes, err error)
	SupportedParams(ctx context.Context, req *flow.SupportedParamsReq) (res *flow.SupportedParamsRes, err error)
}

type IV1Form interface {
	FormCheckRuleList(ctx context.Context, req *form.FormCheckRuleListReq) (res *form.FormCheckRuleListRes, err error)
	FormCheckRuleDetail(ctx context.Context, req *form.FormCheckRuleDetailReq) (res *form.FormCheckRuleDetailRes, err error)
	FormCheckRuleDelete(ctx context.Context, req *form.FormCheckRuleDeleteReq) (res *form.FormCheckRuleDeleteRes, err error)
	FormCheckRuleSave(ctx context.Context, req *form.FormCheckRuleSaveReq) (res *form.FormCheckRuleSaveRes, err error)
	FormCheckRuleStatus(ctx context.Context, req *form.FormCheckRuleStatusReq) (res *form.FormCheckRuleStatusRes, err error)
	FormCountersignLogCreateByGlobalId(ctx context.Context, req *form.FormCountersignLogCreateByGlobalIdReq) (res *form.FormCountersignLogCreateByGlobalIdRes, err error)
	FormCountersignLogCreate(ctx context.Context, req *form.FormCountersignLogCreateReq) (res *form.FormCountersignLogCreateRes, err error)
	FormCountersignLogDelete(ctx context.Context, req *form.FormCountersignLogDeleteReq) (res *form.FormCountersignLogDeleteRes, err error)
	FormCountersignLogList(ctx context.Context, req *form.FormCountersignLogListReq) (res *form.FormCountersignLogListRes, err error)
	FormDataList(ctx context.Context, req *form.FormDataListReq) (res *form.FormDataListRes, err error)
	FormDataListSum(ctx context.Context, req *form.FormDataListSumReq) (res *form.FormDataListSumRes, err error)
	FormDataListCustom(ctx context.Context, req *form.FormDataListCustomReq) (res *form.FormDataListCustomRes, err error)
	FormDataSave(ctx context.Context, req *form.FormDataSaveReq) (res *form.FormDataSaveRes, err error)
	ColumnWithList(ctx context.Context, req *form.ColumnWithListReq) (res *form.ColumnWithListRes, err error)
	ColumnWithInfo(ctx context.Context, req *form.ColumnWithInfoReq) (res *form.ColumnWithInfoRes, err error)
	FormDataDelete(ctx context.Context, req *form.FormDataDeleteReq) (res *form.FormDataDeleteRes, err error)
	FormDataInfo(ctx context.Context, req *form.FormDataInfoReq) (res *form.FormDataInfoRes, err error)
	FormExpandInfo(ctx context.Context, req *form.FormExpandInfoReq) (res *form.FormExpandInfoRes, err error)
	TableDataList(ctx context.Context, req *form.TableDataListReq) (res *form.TableDataListRes, err error)
	FormDataInvalidate(ctx context.Context, req *form.FormDataInvalidateReq) (res *form.FormDataInvalidateRes, err error)
	FormDataUpdateField(ctx context.Context, req *form.FormDataUpdateFieldReq) (res *form.FormDataUpdateFieldRes, err error)
	FormExportHistoryList(ctx context.Context, req *form.FormExportHistoryListReq) (res *form.FormExportHistoryListRes, err error)
	FormExportHistoryDetail(ctx context.Context, req *form.FormExportHistoryDetailReq) (res *form.FormExportHistoryDetailRes, err error)
	FormExportHistoryNew(ctx context.Context, req *form.FormExportHistoryNewReq) (res *form.FormExportHistoryNewRes, err error)
	FormExportTemplateList(ctx context.Context, req *form.FormExportTemplateListReq) (res *form.FormExportTemplateListRes, err error)
	FormExportTemplateDetail(ctx context.Context, req *form.FormExportTemplateDetailReq) (res *form.FormExportTemplateDetailRes, err error)
	FormExportTemplateDelete(ctx context.Context, req *form.FormExportTemplateDeleteReq) (res *form.FormExportTemplateDeleteRes, err error)
	FormExportTemplateSave(ctx context.Context, req *form.FormExportTemplateSaveReq) (res *form.FormExportTemplateSaveRes, err error)
	FormExportTemplateStatus(ctx context.Context, req *form.FormExportTemplateStatusReq) (res *form.FormExportTemplateStatusRes, err error)
	FormImportHistoryList(ctx context.Context, req *form.FormImportHistoryListReq) (res *form.FormImportHistoryListRes, err error)
	FormImportHistoryDetail(ctx context.Context, req *form.FormImportHistoryDetailReq) (res *form.FormImportHistoryDetailRes, err error)
	FormImportHistoryNew(ctx context.Context, req *form.FormImportHistoryNewReq) (res *form.FormImportHistoryNewRes, err error)
	FormImportTemplateList(ctx context.Context, req *form.FormImportTemplateListReq) (res *form.FormImportTemplateListRes, err error)
	FormImportTemplateDetail(ctx context.Context, req *form.FormImportTemplateDetailReq) (res *form.FormImportTemplateDetailRes, err error)
	FormImportTemplateDelete(ctx context.Context, req *form.FormImportTemplateDeleteReq) (res *form.FormImportTemplateDeleteRes, err error)
	FormImportTemplateSave(ctx context.Context, req *form.FormImportTemplateSaveReq) (res *form.FormImportTemplateSaveRes, err error)
	FormImportTemplateStatus(ctx context.Context, req *form.FormImportTemplateStatusReq) (res *form.FormImportTemplateStatusRes, err error)
	FormTemlateSaveFormDesign(ctx context.Context, req *form.FormTemlateSaveFormDesignReq) (res *form.FormTemlateSaveFormDesignRes, err error)
	FormTemlateSaveFormDefault(ctx context.Context, req *form.FormTemlateSaveFormDefaultReq) (res *form.FormTemlateSaveFormDefaultRes, err error)
	FormTemlateList(ctx context.Context, req *form.FormTemlateListReq) (res *form.FormTemlateListRes, err error)
	FormTemlatePermissionList(ctx context.Context, req *form.FormTemlatePermissionListReq) (res *form.FormTemlatePermissionListRes, err error)
	FormTemlateDelete(ctx context.Context, req *form.FormTemlateDeleteReq) (res *form.FormTemlateDeleteRes, err error)
	FormTemlateDetail(ctx context.Context, req *form.FormTemlateDetailReq) (res *form.FormTemlateDetailRes, err error)
	SupportTableList(ctx context.Context, req *form.SupportTableListReq) (res *form.SupportTableListRes, err error)
	SupportFieldsList(ctx context.Context, req *form.SupportFieldsListReq) (res *form.SupportFieldsListRes, err error)
	SupportColumnList(ctx context.Context, req *form.SupportColumnListReq) (res *form.SupportColumnListRes, err error)
	FormTemlateSaveExtKey(ctx context.Context, req *form.FormTemlateSaveExtKeyReq) (res *form.FormTemlateSaveExtKeyRes, err error)
	FormTemlatePermissionSetting(ctx context.Context, req *form.FormTemlatePermissionSettingReq) (res *form.FormTemlatePermissionSettingRes, err error)
	SqlQueryTest(ctx context.Context, req *form.SqlQueryTestReq) (res *form.SqlQueryTestRes, err error)
	SqlQueryExecute(ctx context.Context, req *form.SqlQueryExecuteReq) (res *form.SqlQueryExecuteRes, err error)
	SqlQueryValidate(ctx context.Context, req *form.SqlQueryValidateReq) (res *form.SqlQueryValidateRes, err error)
	SqlQuerySchema(ctx context.Context, req *form.SqlQuerySchemaReq) (res *form.SqlQuerySchemaRes, err error)
}

type IV1Login interface {
	LoginAccount(ctx context.Context, req *login.LoginAccountReq) (res *login.LoginAccountRes, err error)
	RefreshToken(ctx context.Context, req *login.RefreshTokenReq) (res *login.RefreshTokenRes, err error)
}

type IV1Print interface {
	PrintForm(ctx context.Context, req *print.PrintFormReq) (res *print.PrintFormRes, err error)
	PrintFormHtml(ctx context.Context, req *print.PrintFormHtmlReq) (res *print.PrintFormHtmlRes, err error)
	GeneratePdfFromHtml(ctx context.Context, req *print.GeneratePdfFromHtmlReq) (res *print.GeneratePdfFromHtmlRes, err error)
	List(ctx context.Context, req *print.ListReq) (res *print.ListRes, err error)
	FormPrintTemplateDetail(ctx context.Context, req *print.FormPrintTemplateDetailReq) (res *print.FormPrintTemplateDetailRes, err error)
	FormPrintTemplateDelete(ctx context.Context, req *print.FormPrintTemplateDeleteReq) (res *print.FormPrintTemplateDeleteRes, err error)
	FormPrintTemplateSave(ctx context.Context, req *print.FormPrintTemplateSaveReq) (res *print.FormPrintTemplateSaveRes, err error)
	FormPrintTemplateStatus(ctx context.Context, req *print.FormPrintTemplateStatusReq) (res *print.FormPrintTemplateStatusRes, err error)
	FormPrintTemplateAllowEditStatus(ctx context.Context, req *print.FormPrintTemplateAllowEditStatusReq) (res *print.FormPrintTemplateAllowEditStatusRes, err error)
}

type IV1Project interface {
	AdProjectList(ctx context.Context, req *project.AdProjectListReq) (res *project.AdProjectListRes, err error)
	AdProjectDetail(ctx context.Context, req *project.AdProjectDetailReq) (res *project.AdProjectDetailRes, err error)
	AdProjectDelete(ctx context.Context, req *project.AdProjectDeleteReq) (res *project.AdProjectDeleteRes, err error)
	AdProjectSave(ctx context.Context, req *project.AdProjectSaveReq) (res *project.AdProjectSaveRes, err error)
	AdProjectRoleList(ctx context.Context, req *project.AdProjectRoleListReq) (res *project.AdProjectRoleListRes, err error)
	AdProjectRoleDetail(ctx context.Context, req *project.AdProjectRoleDetailReq) (res *project.AdProjectRoleDetailRes, err error)
	AdProjectRoleDelete(ctx context.Context, req *project.AdProjectRoleDeleteReq) (res *project.AdProjectRoleDeleteRes, err error)
	AdProjectRoleSave(ctx context.Context, req *project.AdProjectRoleSaveReq) (res *project.AdProjectRoleSaveRes, err error)
}

type IV1Saas interface {
	Context(ctx context.Context, req *saas.ContextReq) (res *saas.ContextRes, err error)
}

type IV1Superset interface {
	AccessToken(ctx context.Context, req *superset.AccessTokenReq) (res *superset.AccessTokenRes, err error)
	EmbeddedGuestToken(ctx context.Context, req *superset.EmbeddedGuestTokenReq) (res *superset.EmbeddedGuestTokenRes, err error)
	GuestToken(ctx context.Context, req *superset.GuestTokenReq) (res *superset.GuestTokenRes, err error)
	DashboardList(ctx context.Context, req *superset.DashboardListReq) (res *superset.DashboardListRes, err error)
}

type IV1System interface {
	DeptSearch(ctx context.Context, req *system.DeptSearchReq) (res *system.DeptSearchRes, err error)
	DeptSave(ctx context.Context, req *system.DeptSaveReq) (res *system.DeptSaveRes, err error)
	DeptDelete(ctx context.Context, req *system.DeptDeleteReq) (res *system.DeptDeleteRes, err error)
	DeptInfo(ctx context.Context, req *system.DeptInfoReq) (res *system.DeptInfoRes, err error)
	MenuSearch(ctx context.Context, req *system.MenuSearchReq) (res *system.MenuSearchRes, err error)
	MenuTSearch(ctx context.Context, req *system.MenuTSearchReq) (res *system.MenuTSearchRes, err error)
	MenuSave(ctx context.Context, req *system.MenuSaveReq) (res *system.MenuSaveRes, err error)
	MenuDelete(ctx context.Context, req *system.MenuDeleteReq) (res *system.MenuDeleteRes, err error)
	MenuInfo(ctx context.Context, req *system.MenuInfoReq) (res *system.MenuInfoRes, err error)
	MenuMove(ctx context.Context, req *system.MenuMoveReq) (res *system.MenuMoveRes, err error)
	PostSearch(ctx context.Context, req *system.PostSearchReq) (res *system.PostSearchRes, err error)
	PostSave(ctx context.Context, req *system.PostSaveReq) (res *system.PostSaveRes, err error)
	PostDelete(ctx context.Context, req *system.PostDeleteReq) (res *system.PostDeleteRes, err error)
	PostInfo(ctx context.Context, req *system.PostInfoReq) (res *system.PostInfoRes, err error)
	SysRoleList(ctx context.Context, req *system.SysRoleListReq) (res *system.SysRoleListRes, err error)
	SysRoleListAll(ctx context.Context, req *system.SysRoleListAllReq) (res *system.SysRoleListAllRes, err error)
	SysRoleSave(ctx context.Context, req *system.SysRoleSaveReq) (res *system.SysRoleSaveRes, err error)
	SysRoleDel(ctx context.Context, req *system.SysRoleDelReq) (res *system.SysRoleDelRes, err error)
	SysRoleDetail(ctx context.Context, req *system.SysRoleDetailReq) (res *system.SysRoleDetailRes, err error)
	SysRoleSetPerms(ctx context.Context, req *system.SysRoleSetPermsReq) (res *system.SysRoleSetPermsRes, err error)
	CurrentSysUser(ctx context.Context, req *system.CurrentSysUserReq) (res *system.CurrentSysUserRes, err error)
	SysUserList(ctx context.Context, req *system.SysUserListReq) (res *system.SysUserListRes, err error)
	SysUserSave(ctx context.Context, req *system.SysUserSaveReq) (res *system.SysUserSaveRes, err error)
	SysUserDelete(ctx context.Context, req *system.SysUserDeleteReq) (res *system.SysUserDeleteRes, err error)
	SysUserUpdateStatus(ctx context.Context, req *system.SysUserUpdateStatusReq) (res *system.SysUserUpdateStatusRes, err error)
	SysUserResetPwd(ctx context.Context, req *system.SysUserResetPwdReq) (res *system.SysUserResetPwdRes, err error)
	SysUserUpdatePwd(ctx context.Context, req *system.SysUserUpdatePwdReq) (res *system.SysUserUpdatePwdRes, err error)
	SysUserDetail(ctx context.Context, req *system.SysUserDetailReq) (res *system.SysUserDetailRes, err error)
}
