package login

import "github.com/gogf/gf/v2/frame/g"

// 账号登录
type LoginAccountReq struct {
	g.Meta    `path:"/login/account" tags:"登录相关"  summary:"账号密码登录" method:"post"`
	AutoLogin bool   `json:"autoLogin" `
	Password  string `json:"password" v:"required#密码不能为空"`
	Username  string `json:"username" v:"required#用户名不能为空"`
	Type      string `json:"type" v:"required#登录类型不能为空"`
}

type LoginAccountRes struct {
	g.Meta       `mime:"application/json"`
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
}

// 账号登录
type RefreshTokenReq struct {
	g.Meta       `path:"/login/refreshToken" tags:"登录相关"  summary:"刷新 jwttoken" method:"post"`
	RefreshToken string `json:"refreshToken" `
}

type RefreshTokenRes struct {
	g.Meta `mime:"application/json"`
	Token  string `json:"token"`
}
