package automation

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type ListReq struct {
	g.Meta `path:"/am_config/list"  method:"get" tags:"自动化配置相关" summary:"获得自动化配置列表"`
	model.PageReq
	Name string `p:"name"  summary:"名称"`
}

type ListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.AmConfig `json:"list"`
}

type DependReq struct {
	g.Meta `path:"/am_config/depend" method:"get" tags:"自动化配置相关" summary:"获取自动化配置的依赖"`
	Id     int64 `p:"id"  summary:"id"`
}
type DependRes struct {
	g.Meta `mime:"application/json"`
	List   []dto.AMDepend `json:"list"`
}

// 自动化配置详情
type AmConfigDetailReq struct {
	g.Meta `path:"/am_config/detail" tags:"自动化配置相关"  summary:"自动化配置详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AmConfigDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.AmConfig
}

// 删除自动化配置
type AmConfigDeleteReq struct {
	g.Meta `path:"/am_config/delete" tags:"自动化配置相关" summary:"删除自动化配置" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AmConfigDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存自动化配置信息
type AmConfigSaveReq struct {
	g.Meta     `path:"/am_config/save" tags:"自动化配置相关" summary:"保存自动化配置信息" method:"post"`
	Id         int64       `p:"id" summary:"id"`
	Name       interface{} `p:"name" v:"required#名称不能为空！"  summary:"名称"`
	FlowSchema interface{} `p:"flowSchema" v:"required#流程配置不能为空！"  summary:"流程配置"`
	Depend     []int64     `p:"depend"  summary:"依赖的自动化配置id"`
}

type AmConfigSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id"`
}

// 修改自动化配置的状态
type AmConfigStatusReq struct {
	g.Meta  `path:"/am_config/status" tags:"自动化配置相关" summary:"修改自动化配置的状态" method:"post"`
	Id      int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
	Enabled bool  `p:"enabled" v:"required#enabled不能为空！"  summary:"是否启用"`
}
type AmConfigStatusRes struct {
	g.Meta `mime:"application/json"`
}

// 重试执行
type AmConfigRetryReq struct {
	g.Meta    `path:"/am_config/retry" tags:"自动化配置相关" summary:"重试执行" method:"post"`
	HistoryId int64 `p:"historyId" v:"required#历史记录ID不能为空！" summary:"执行历史记录ID"`
}

type AmConfigRetryRes struct {
	g.Meta `mime:"application/json"`
}
