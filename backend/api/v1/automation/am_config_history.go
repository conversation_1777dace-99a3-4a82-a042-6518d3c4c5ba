package automation

import (
	model "backend/internal/model/common"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type AmConfigHistoryListReq struct {
	g.Meta `path:"/am_config_history/list"  method:"get" tags:"自动化执行历史相关" summary:"获得自动化执行历史列表"`
	model.PageReq
	AmConfigId int64  `p:"amConfigId" summary:"自动化配置id"`
	StartTime  string `p:"startTime" summary:"开始时间"`
	EndTime    string `p:"endTime" summary:"结束时间"`
	Status     int    `p:"status" summary:"状态"`
}

type AmConfigHistoryListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.AmConfigHistory `json:"list"`
}

// 自动化执行历史详情
type AmConfigHistoryDetailReq struct {
	g.Meta `path:"/am_config_history/detail" tags:"自动化执行历史相关"  summary:"自动化执行历史详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type AmConfigHistoryDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.AmConfigHistory
}
