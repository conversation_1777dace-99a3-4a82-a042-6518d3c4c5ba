package flow

import (
	"backend/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

// 保存流程模板

// 根据id获取流程模板信息

// 根据单据数据获得流程走向

type SupportedParamsReq struct {
	g.Meta         `path:"/flow_template/get_supported_params"  method:"get" tags:"流程模板相关" summary:"获得流程走向条件支持的参数"`
	FormTemplateId int64 `json:"form_template_id" v:"required#请输入表单模板id"`
}

type SupportedParamsRes struct {
	g.Meta `mime:"application/json"`
	Params []*dto.FLowEdgeParams `json:"params"`
}
