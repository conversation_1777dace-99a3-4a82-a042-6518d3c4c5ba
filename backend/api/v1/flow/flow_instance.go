package flow

import (
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

// 创建流程实例
type CreateReq struct {
	g.Meta         `path:"/flow_instance/create"  method:"post" tags:"流程实例相关" summary:"创建流程实例"`
	FormTemplateId int64                 `p:"form_template_id" v:"required#请输入表单模板id" summary:"表单模板id"`
	FormId         int64                 `p:"form_id" v:"required#请输入表单id" summary:"表单id"`
	ExtraData      dto.InstanceExtraData `p:"extra_data" summary:"附件数据"`
}

type CreateRes struct {
	g.Meta `mime:"application/json"`
}

// 获得流程实例
type GetReq struct {
	g.Meta         `path:"/flow_instance/get"  method:"get" tags:"流程实例相关" summary:"获得流程实例"`
	FormTemplateId int64 `p:"form_template_id" v:"required#请输入表单模板id" summary:"表单模板id"`
	FormId         int64 `p:"form_id" v:"required#请输入表单id" summary:"表单id"`
}

type GetRes struct {
	g.Meta `mime:"application/json"`
	*with.FlowInstance
}

// 获取所有审核实例请求
type AuditInstancesReq struct {
	g.Meta         `path:"/flow_instance/audit_instances" method:"get" tags:"流程实例相关" summary:"获取所有审核实例ID"`
	FormTemplateId int64 `p:"form_template_id" v:"required#请输入表单模板id" summary:"表单模板id"`
	FormId         int64 `p:"form_id" v:"required#请输入表单id" summary:"表单id"`
}

type AuditInstancesRes struct {
	g.Meta `mime:"application/json"`
	List   []*dto.InstanceTypeSummary `json:"list"`
}

// 根据实例ID获取实例信息请求
type InstanceInfoReq struct {
	g.Meta     `path:"/flow_instance/instance_info" method:"get" tags:"流程实例相关" summary:"根据实例ID获取实例信息"`
	InstanceId int64 `p:"instanceId" v:"required#请输入实例ID" summary:"实例ID"`
}

type InstanceInfoRes struct {
	g.Meta `mime:"application/json"`
	*with.FlowInstance
}

// 用户手动审核操作
type ApproveReq struct {
	g.Meta           `path:"/flow_instance/approve"  method:"post" tags:"流程实例相关" summary:"用户手动审核操作"`
	InstanceId       int64               `p:"instanceId" v:"" summary:"流程实例id"`
	HistoryAuditorId int64               `p:"historyAuditorId" v:"required-without:instanceId#请输入操作的审核记录id" summary:"操作的审核记录id"`
	ApprovalStatus   enum.NodeActionType `p:"approvalStatus"        v:"required-without:instanceId#请选择审批状态"   ` // 审批状态
	ApprovalComment  string              `p:"approvalComment"         `                                         // 审批意见
	ReturnNodeId     string              `p:"returnNodeId"            `                                         // 退回节点id
	Attachment       string              `p:"attachment"              `                                         // 附件
	Pics             string              `p:"pics"              `                                               // 图片
	Signature        string              `p:"signature"              `                                          // 签名
}

type ApproveRes struct {
	g.Meta `mime:"application/json"`
}

// 待我处理列表
type MyListReq struct {
	g.Meta `path:"/flow_instance/my_list"  method:"get" tags:"流程实例相关" summary:"待我处理列表"`
	*dto.HistoryAuditorListReq
}

type MyListRes struct {
	g.Meta `mime:"application/json"`
	*dto.HistoryAuditorListRes
}

// 抄送我的
type MyCcListReq struct {
	g.Meta `path:"/flow_instance/my_cc_list"  method:"get" tags:"流程实例相关" summary:"抄送我的"`
	*dto.HistoryAuditorListReq
}

type MyCcListRes struct {
	g.Meta `mime:"application/json"`
	*dto.HistoryAuditorListRes
}

// 我发起的
type MyStartListReq struct {
	g.Meta `path:"/flow_instance/my_start_list"  method:"get" tags:"流程实例相关" summary:"我发起的"`
	*dto.HistoryAuditorListReq
}

type MyStartListRes struct {
	g.Meta `mime:"application/json"`
	*dto.HistoryAuditorListRes
}

// 已处理的
type MyDoneListReq struct {
	g.Meta `path:"/flow_instance/my_done_list"  method:"get" tags:"流程实例相关" summary:"已处理的"`
	*dto.HistoryAuditorListReq
}

type MyDoneListRes struct {
	g.Meta `mime:"application/json"`
	*dto.HistoryAuditorListRes
}
