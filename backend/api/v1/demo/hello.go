package demo

import "github.com/gogf/gf/v2/frame/g"

type HelloReq struct {
	g.Meta `path:"/demo/hello"  method:"get" tags:"测试相关" summary:"测试消息队列"`
}

type HelloRes struct {
	g.Meta `mime:"application/json"`
	Value  string `json:"name"`
}

type HelloConfigReq struct {
	g.Meta `path:"/demo/config"  method:"get" tags:"测试相关" summary:"测试获取配置信息"`
}

type HelloConfigRes struct {
	g.Meta `mime:"application/json"`
	Value  string `json:"name"`
}

type HelloCacheReq struct {
	g.Meta `path:"/demo/cache"  method:"get" tags:"测试相关" summary:"测试缓存"`
}

type HelloCacheRes struct {
	g.Meta   `mime:"application/json"`
	Value    string  `json:"name"`
	Members  []int64 `json:"members"`
	Members2 []int64 `json:"members2"`
}

type HelloNextIdReq struct {
	g.Meta `path:"/demo/nextid"  method:"get" tags:"测试相关" summary:"测试一个测试的自增ID"`
}

type HelloNextIdRes struct {
	g.Meta `mime:"application/json"`
	Value  string `json:"id"`
}

type StartInstanceReq struct {
	g.Meta `path:"/demo/start_instance"  method:"get" tags:"测试相关" summary:"测试流程实例开始"`
}

type StartInstanceRes struct {
	g.Meta `mime:"application/json"`
}

type HelloDemoReq struct {
	g.Meta `path:"/demo/hello_demo"  method:"get" tags:"测试相关" summary:"随机测试"`
}

type HelloDemoRes struct {
	g.Meta `mime:"application/json"`
}

type HelloFlowCreatedReloadReq struct {
	g.Meta `path:"/demo/flow_created_reload"  method:"post" tags:"测试相关" summary:"测试流程实例创建"`
	Id     int64 `v:"min:1#id不能为空"`
}

type HelloFlowCreatedReloadRes struct {
	g.Meta `mime:"application/json"`
}

type HelloQueueTestReq struct {
	g.Meta  `path:"/demo/queue_test"  method:"post" tags:"测试相关" summary:"测试消息队列"`
	Key     string                 `v:"required#key不能为空"`
	Subject string                 `v:"required#主题不能为空"`
	Data    map[string]interface{} `v:"required#数据不能为空"`
}

type HelloQueueTestRes struct {
	g.Meta `mime:"application/json"`
}

type HelloWebsocketBroadcastReq struct {
	g.Meta  `path:"/demo/websocket_broadcast"  method:"post" tags:"测试相关" summary:"测试websocket群发消息"`
	Message string `json:"message" v:"required#消息内容不能为空"`
}

type HelloWebsocketBroadcastRes struct {
	g.Meta `mime:"application/json"`
}
