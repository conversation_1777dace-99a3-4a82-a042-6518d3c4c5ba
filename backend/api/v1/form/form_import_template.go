package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type FormImportTemplateListReq struct {
	g.Meta `path:"/form_import_template/list"  method:"get" tags:"导入模板相关" summary:"获得导入模板列表"`
	model.PageReq
	Name           string `p:"name"  summary:"名称"`
	FormTemplateId int64  `p:"formTemplateId"  `
}

type FormImportTemplateListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.FormImportTemplate `json:"list"`
}

// 导入模板详情
type FormImportTemplateDetailReq struct {
	g.Meta `path:"/form_import_template/detail" tags:"导入模板相关"  summary:"导入模板详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormImportTemplateDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.FormImportTemplate
}

// 删除导入模板
type FormImportTemplateDeleteReq struct {
	g.Meta `path:"/form_import_template/delete" tags:"导入模板相关" summary:"删除导入模板" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormImportTemplateDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存导入模板信息
type FormImportTemplateSaveReq struct {
	g.Meta                  `path:"/form_import_template/save" tags:"导入模板相关" summary:"保存导入模板信息" method:"post"`
	Id                      int64                `p:"id" summary:"id"`
	Name                    string               `p:"name" v:"required#名称不能为空！"  summary:"名称"`
	FormTemplateId          int64                `p:"formTemplateId"  v:"required#关联的表单模板不能为空！"  ` // 关联的模板id
	StartRow                int                  `p:"startRow"                `                    // 开始行数
	FieldMapping            []*dto.ColumnMapRule `p:"fieldMapping"            `                    // 字段映射
	ImportTemplate          string               `p:"importTemplate"          `                    // 导入模板
	DuplicateDataValidation int                  `p:"duplicateDataValidation" `                    // 是否进行数据重复验证
	DuplicateCheckField     []string             `p:"duplicateCheckField"     `                    // 重复验证的标识字段
	DuplicateHandlingMethod int                  `p:"duplicateHandlingMethod" `                    // 数据重复时的处理方式
}

type FormImportTemplateSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `p:"id"`
}

// 修改导入模板的状态
type FormImportTemplateStatusReq struct {
	g.Meta  `path:"/form_import_template/status" tags:"导入模板相关" summary:"修改导入模板的状态" method:"post"`
	Id      int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
	Enabled bool  `p:"enabled" v:"required#enabled不能为空！"  summary:"是否启用"`
}
type FormImportTemplateStatusRes struct {
	g.Meta `mime:"application/json"`
}
