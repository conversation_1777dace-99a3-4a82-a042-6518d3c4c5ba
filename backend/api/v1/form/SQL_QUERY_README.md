# SQL查询功能说明

本功能为打印模板提供了SQL数据源支持，允许在打印模板中动态查询数据库数据。

## 实现的接口

### 1. 测试SQL查询
**接口路径**: `POST /api/form/sql-query/test`

**请求参数**:
```json
{
  "sql": "SELECT * FROM users WHERE id = #{user_id}",
  "parameters": [
    {
      "parameter": "user_id", 
      "fieldPath": "user.id",
      "defaultValue": "1"
    }
  ],
  "type": "table"
}
```

**响应格式**:
```json
{
  "data": [
    {"id": 1, "name": "张三", "age": 25},
    {"id": 2, "name": "李四", "age": 30}
  ],
  "message": "执行成功",
  "executionTime": 45,
  "rowCount": 2
}
```

### 2. 执行SQL查询（实际打印时）
**接口路径**: `POST /api/form/sql-query/execute`

**请求参数**:
```json
{
  "sql": "SELECT * FROM users WHERE id = #{user_id}",
  "parameters": [
    {
      "parameter": "user_id",
      "value": "1"
    }
  ],
  "type": "table",
  "formData": {...}
}
```

### 3. 验证SQL语法
**接口路径**: `POST /api/form/sql-query/validate`

**请求参数**:
```json
{
  "sql": "SELECT * FROM users WHERE"
}
```

**响应格式**:
```json
{
  "valid": false,
  "message": "SQL语法错误：Syntax error near 'WHERE'"
}
```

### 4. 获取数据库结构
**接口路径**: `GET /api/form/sql-query/schema`

**响应格式**:
```json
{
  "tables": [
    {
      "name": "users",
      "comment": "用户表", 
      "columns": [
        {"name": "id", "type": "int", "comment": "用户ID"},
        {"name": "name", "type": "varchar", "comment": "用户姓名"}
      ]
    }
  ]
}
```

## 参数绑定说明

SQL语句中可以使用 `#{参数名}` 格式来定义参数占位符，系统会自动替换为实际值。

### 支持的绑定类型

1. **当前用户信息绑定**
   - 绑定当前登录用户的信息
   - 格式：`currentUser.属性名`
   - 支持的属性：
     - `currentUser.user_id` - 用户ID
     - `currentUser.username` - 用户名
     - `currentUser.dept_id` - 主部门ID
     - `currentUser.dept_ids` - 所有部门IDs
     - `currentUser.post_ids` - 所有岗位IDs
     - `currentUser.role_codes` - 角色代码列表
     - `currentUser.phonenumber` - 手机号
     - `currentUser.email` - 邮箱

2. **当前表单字段绑定**
   - 直接绑定当前表单中的字段值
   - 格式：`current.字段名`
   - 示例：`current.user_name`, `current.order_date`

3. **关联表字段绑定**
   - 支持关联表的字段值，自动查询关联数据
   - 格式：`关联字段.目标字段`
   - 示例：`customer.name`, `product.title`, `user.username`
   - 注意：需要关联字段为JSON格式存储，系统会自动匹配关联表

### 参数值获取优先级

1. `parameters[].value` - 直接传入的值（用于执行时）
2. `formData` 中根据 `fieldPath` 获取的值（支持上述所有绑定类型）
3. `parameters[].defaultValue` - 默认值

## 安全说明

- 系统会对参数值进行基本的SQL注入防护（转义单引号）
- 建议在生产环境中使用预处理语句进一步提升安全性
- 可以根据需要在控制器中添加权限检查

## 文件结构

```
backend/
├── api/v1/form/sql_query.go          # API接口定义
├── internal/
│   ├── service/sql_query.go         # 服务接口定义
│   ├── logic/sql_query/sql_query.go # 业务逻辑实现
│   └── controller/v1/v1_form_sql_query.go # 控制器实现
```

## 使用示例

### 1. 当前用户信息绑定

```javascript
// 查询当前用户创建的订单
const userOrdersResult = await FetchTestSqlQuery({
  sql: "SELECT * FROM orders WHERE created_by = #{current_user_id} AND dept_id = #{current_dept_id}",
  parameters: [
    {
      parameter: "current_user_id",
      fieldPath: "currentUser.user_id",
      defaultValue: ""
    },
    {
      parameter: "current_dept_id", 
      fieldPath: "currentUser.dept_id",
      defaultValue: ""
    }
  ],
  type: "table"
});
```

### 2. 当前表单字段绑定

```javascript
// 根据表单状态查询数据
const statusResult = await FetchTestSqlQuery({
  sql: "SELECT * FROM users WHERE status = #{status}",
  parameters: [
    {
      parameter: "status",
      fieldPath: "current.user_status",
      defaultValue: "active"
    }
  ],
  type: "table"
});
```

### 3. 关联表字段绑定

```javascript
// 根据客户信息查询订单
const customerOrdersResult = await FetchTestSqlQuery({
  sql: "SELECT * FROM orders WHERE customer_name = #{customer_name}",
  parameters: [
    {
      parameter: "customer_name",
      fieldPath: "customer.name", // customer是关联字段，name是目标字段
      defaultValue: ""
    }
  ],
  type: "table",
  formData: {
    customer: {
      value: "123", // 关联表的ID
      label: "张三公司"
    }
  }
});
```

### 4. 执行SQL查询

```javascript
// 执行SQL查询
const executeResult = await FetchExecuteSqlQuery({
  sql: "SELECT COUNT(*) as total FROM orders WHERE user_id = #{user_id}",
  parameters: [
    {
      parameter: "user_id",
      value: "123"
    }
  ],
  type: "value",
  formData: { user: { id: 123 } }
});
``` 