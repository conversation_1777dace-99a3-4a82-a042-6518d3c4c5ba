package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

// FormCountersignLogCreateByGlobalIdReq 通过全局id创建回签记录请求
type FormCountersignLogCreateByGlobalIdReq struct {
	g.Meta   `path:"/form_countersign_logs/create_by_global_id" method:"post" tags:"表单回签相关" summary:"通过全局id创建回签记录"`
	GlobalId string `p:"globalId" summary:"全局唯一标识ID" v:"required#请输入全局唯一标识ID"`
	// Data     map[string]interface{} `p:"data" summary:"回签数据"`
	// Remark   string                 `p:"remark" summary:"备注信息"`
}

// FormCountersignLogCreateByGlobalIdRes 通过全局id创建回签记录响应
type FormCountersignLogCreateByGlobalIdRes struct {
	g.<PERSON>a `mime:"application/json"`
	Id     int64 `json:"id" summary:"回签记录ID"`
}

// FormCountersignLogCreateReq 根据表单模板表单数据id创建回签记录请求
type FormCountersignLogCreateReq struct {
	g.Meta `path:"/form_countersign_logs/create" method:"post" tags:"表单回签相关" summary:"根据表单模板表单数据id创建回签记录"`
	FormId int64 `p:"formId" summary:"表单模板id" v:"required#请输入表单模板ID"`
	DataId int64 `p:"dataId" summary:"表单数据id" v:"required#请输入表单数据ID"`
	// Data   map[string]interface{} `p:"data" summary:"回签数据"`
	// Remark string                 `p:"remark" summary:"备注信息"`
}

// FormCountersignLogCreateRes 根据表单模板表单数据id创建回签记录响应
type FormCountersignLogCreateRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id" summary:"回签记录ID"`
}

// FormCountersignLogDeleteReq 根据表单模板表单数据id删除回签记录请求
type FormCountersignLogDeleteReq struct {
	g.Meta `path:"/form_countersign_logs/delete" method:"post" tags:"表单回签相关" summary:"根据表单模板表单数据id删除回签记录"`
	FormId int64 `p:"formId" summary:"表单模板id" v:"required#请输入表单模板ID"`
	DataId int64 `p:"dataId" summary:"表单数据id" v:"required#请输入表单数据ID"`
	// Id     int64 `p:"id" summary:"回签记录id" v:"required#请输入回签记录ID"`
}

// FormCountersignLogDeleteRes 根据表单模板表单数据id删除回签记录响应
type FormCountersignLogDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// FormCountersignLogListReq 获取回签历史记录列表请求
type FormCountersignLogListReq struct {
	g.Meta `path:"/form_countersign_logs/list" method:"get" tags:"表单回签相关" summary:"获取回签历史记录列表"`
	model.PageReq
}

// FormCountersignLogListRes 获取回签历史记录列表响应
type FormCountersignLogListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []*with.FormCountersignLogs `json:"list"`
}
