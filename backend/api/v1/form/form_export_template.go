package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type FormExportTemplateListReq struct {
	g.Meta `path:"/form_export_template/list"  method:"get" tags:"导出模板相关" summary:"获得导出模板列表"`
	model.PageReq
	Name           string `p:"name"  summary:"名称"`
	FormTemplateId int64  `p:"formTemplateId"  `
}

type FormExportTemplateListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.FormExportTemplate `json:"list"`
}

// 导出模板详情
type FormExportTemplateDetailReq struct {
	g.Meta `path:"/form_export_template/detail" tags:"导出模板相关"  summary:"导出模板详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormExportTemplateDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.FormExportTemplate
}

// 删除导出模板
type FormExportTemplateDeleteReq struct {
	g.Meta `path:"/form_export_template/delete" tags:"导出模板相关" summary:"删除导出模板" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormExportTemplateDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存导出模板信息
type FormExportTemplateSaveReq struct {
	g.Meta         `path:"/form_export_template/save" tags:"导出模板相关" summary:"保存导出模板信息" method:"post"`
	Id             int64                `p:"id" summary:"id"`
	Name           string               `p:"name" v:"required#名称不能为空！"  summary:"名称"`
	FormTemplateId int64                `p:"formTemplateId"  v:"required#关联的表单模板不能为空！"  ` // 关联的模板id
	FieldMapping   []*dto.ColumnMapRule `p:"fieldMapping"            `                    // 字段映射
}

type FormExportTemplateSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `p:"id"`
}

// 修改导出模板的状态
type FormExportTemplateStatusReq struct {
	g.Meta  `path:"/form_export_template/status" tags:"导出模板相关" summary:"修改导出模板的状态" method:"post"`
	Id      int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
	Enabled bool  `p:"enabled" v:"required#enabled不能为空！"  summary:"是否启用"`
}
type FormExportTemplateStatusRes struct {
	g.Meta `mime:"application/json"`
}
