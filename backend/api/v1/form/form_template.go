package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

type FormTemlateSaveFormDesignReq struct {
	g.Meta         `path:"/form_template/save_form_design"  method:"post" tags:"表单模板相关" summary:"保存表单的Lowcode表单设计器相关字段信息"`
	Id             int64                 `p:"id"                  summary:"id" `
	FormTitle      string                `p:"form_title"          summary:"表单名"  v:"required#请输入表单名称" `
	FormSchema     string                `p:"form_schema"         summary:"低代码的原始结构" v:"required#表单设计中没有任何字段，请至少添加一个字段"`
	FormColumns    string                `p:"form_columns"        summary:"低代码获取的字段列表" v:"required#表单设计中没有任何字段，请至少添加一个字段"`
	AddedColumns   string                `p:"added_columns"       summary:"本次保存新增的字段列表" `
	HistoryColumns string                `p:"history_columns"     summary:"历史删除的字段列表" v:"required#表单设计中没有任何字段，请至少添加一个字段"`
	FormType       enum.FormTemplateType `p:"form_type"           summary:"表单类型" `
}

type FormTemlateSaveFormDefaultReq struct {
	g.Meta           `path:"/form_template/save_form_default"  method:"post" tags:"表单模板相关" summary:"保存表单的基础设置字段数据（包含列表设计、表单设置）"`
	Id               int64       `p:"id"                  summary:"id" v:"required|min:1#需先保存表单设计|需先保存表单设计"`
	FormTitle        string      `p:"form_title"          summary:"表单名"  v:"required#请输入表单名称" `
	TableDesign      interface{} `p:"table_design"        summary:"表单列表设计" v:"json#列表设置数据格式错误"`
	OpenFlow         interface{} `p:"open_flow"           summary:"开启审核流程" v:"in:0,1#开启审核流程设置数据格式错误"`
	FlowSchema       interface{} `p:"flow_schema"         summary:"流程设计结构" v:"json#流程设置数据格式错误"`
	OpenLog          interface{} `p:"open_log"            summary:"开启操作日志" v:"in:0,1#开启操作日志设置数据格式错误"`
	AllowCustomAudit interface{} `p:"allow_custom_audit"  summary:"允许增加自定义审核人" v:"in:0,1#允许增加自定义审核人设置数据格式错误"`
	AllowCustomCc    interface{} `p:"allow_custom_cc"     summary:"允许增加自定义抄送人" v:"in:0,1#允许增加自定义抄送人设置数据格式错误"`
	FlowCode         interface{} `p:"flow_code" summary:"流程编码" `
}
type ListReqStruct struct {
	FormTitle string `p:"formTitle"          summary:"表单名" `
	FormCode  string `p:"formCode"           summary:"表单编码"`
	OpenFlow  int    `p:"openFlow"           summary:"开启审核流程"`
	NoInMenu  bool   `p:"noInMenu"           summary:"不在菜单中"`
	model.PageReq
}
type FormTemlateListReq struct {
	g.Meta `path:"/form_template/list"  method:"get" tags:"表单模板相关" summary:"获得所有的表单模板"`
	ListReqStruct
}
type FormTemlatePermissionListReq struct {
	g.Meta `path:"/form_template/permissions"  method:"get" tags:"表单模板相关" summary:"获得所有的表单模板"`
	ListReqStruct
}
type FormTemlateDeleteReq struct {
	g.Meta `path:"/form_template/delete"  method:"post" tags:"表单模板相关" summary:"删除表单模板"`
	Id     int64 `p:"id" v:"required#请输入要删除的表单模板ID"`
}

type FormTemlateDetailReq struct {
	g.Meta `path:"/form_template/detail"  method:"get" tags:"表单模板相关" summary:"获得表单模板详情"`
	Id     int `p:"id" v:"required#请输入要查看的表单模板ID"`
}

type FormTemlateSaveFormDesignRes struct {
	g.Meta `mime:"application/json"`
	entity.FormTemplate
}

type FormTemlateSaveFormDefaultRes struct {
	g.Meta `mime:"application/json"`
	with.FormTemplate
}
type ListResStruct struct {
	List []*with.FormTemplate `json:"list"`
	model.ListRes
}
type FormTemlateListRes struct {
	g.Meta `mime:"application/json"`
	ListResStruct
}
type FormTemlatePermissionListRes struct {
	g.Meta `mime:"application/json"`
	ListResStruct
}
type FormTemlateDeleteRes struct {
	g.Meta `mime:"application/json"`
}

type FormTemlateDetailRes struct {
	g.Meta `mime:"application/json"`
	*with.FormTemplate
}

/* 下方这两个接口比较危险，注意设置权限 */
type SupportTableListReq struct {
	g.Meta `path:"/form_data/support_table_list"  method:"get" tags:"表单模板相关" summary:"获得支持的关联表列表"`
	Label  string `p:"label"          summary:"表显示名"  `
	Type   int    `p:"type"           summary:"表类型 1: 表单模板表 2: 自定义表 " `
}

type SupportTableListRes struct {
	g.Meta `mime:"application/json"`
	List   []*dto.SupportTable `json:"list"`
}

type SupportFieldsListReq struct {
	g.Meta `path:"/form_data/support_fields_list"  method:"get" tags:"表单模板相关" summary:"获得表支持关联的字段列表"`
	Table  string `p:"table"          summary:"表名"  v:"required#请输入表名"`
	Type   int    `p:"type"           summary:"表类型 0: 表单模板表 1: 自定义表 " v:"required|in:0,1#请输入正确的表类型" `
}

type SupportFieldsListRes struct {
	g.Meta `mime:"application/json"`
	List   []*dto.SupportColumn `json:"list"`
}

type SupportColumnListReq struct {
	g.Meta          `path:"/form_data/support_column_list"  method:"get" tags:"表单模板相关" summary:"获得表支持关联的字段列表"`
	Table           string `p:"table"          summary:"表名"  v:"required#请输入表名"`
	Type            int    `p:"type"           summary:"表类型 0: 表单模板表 1: 自定义表 " v:"required|in:0,1#请输入正确的表类型" `
	ShowFixedFields bool   `p:"show_fixed_fields"          summary:"是否显示固定字段"  `
}

type SupportColumnListRes struct {
	g.Meta `mime:"application/json"`
	List   []*dto.FormColumn `json:"list"`
}

// 保存拓展key和模板关联关系
type FormTemlateSaveExtKeyReq struct {
	g.Meta `path:"/form_template/save_ext_key"  method:"post" tags:"表单模板相关" summary:"保存表单的拓展key和模板关联关系"`
	Id     int64  `p:"id"                  summary:"id" v:"required|min:1#请先保存表单设计"`
	ExtKey string `p:"ext_key"             summary:"拓展key" v:"required#请输入拓展key"`
}

type FormTemlateSaveExtKeyRes struct {
	g.Meta `mime:"application/json"`
}

type FormTemlatePermissionSettingReq struct {
	g.Meta      `path:"/form_template/permission_setting"  method:"post" tags:"表单模板相关" summary:"权限设置"`
	Permissions []dto.FormTemplate `json:"permissions"  summary:"权限列表"   `
}

type FormTemlatePermissionSettingRes struct {
	g.Meta `mime:"application/json"`
}
