package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type FormCheckRuleListReq struct {
	g.Meta `path:"/form_template/check_rule/list"  method:"get" tags:"验证规则相关" summary:"获得验证规则列表"`
	model.PageReq
	Name           string `p:"name"  summary:"名称"`
	FormTemplateId int64  `p:"formTemplateId"  ` // 关联的模板id
}

type FormCheckRuleListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.FormCheckRule `json:"list"`
}

// 验证规则详情
type FormCheckRuleDetailReq struct {
	g.Meta `path:"/form_template/check_rule/detail" tags:"验证规则相关"  summary:"验证规则详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormCheckRuleDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.FormCheckRule
}

// 删除验证规则
type FormCheckRuleDeleteReq struct {
	g.Meta `path:"/form_template/check_rule/delete" tags:"验证规则相关" summary:"删除验证规则" method:"post"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormCheckRuleDeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 保存验证规则信息
type FormCheckRuleSaveReq struct {
	g.Meta         `path:"/form_template/check_rule/save" tags:"验证规则相关" summary:"保存验证规则信息" method:"post"`
	Id             int64           `p:"id" summary:"id"`
	Name           string          `p:"name" v:"required#名称不能为空！"  summary:"名称"`
	FormTemplateId int64           `p:"formTemplateId"  v:"required#关联的表单模板不能为空！"  ` // 关联的模板id
	CodeContent    *dto.ScriptCode `p:"codeContent"  v:"required#验证代码内容不能为空！" `      // 代码内容
	FailMsg        string          `p:"failMsg"  v:"required#验证失败提示不能为空！" `          // 失败提示
}

type FormCheckRuleSaveRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `json:"id"`
}

// 修改验证规则的状态
type FormCheckRuleStatusReq struct {
	g.Meta  `path:"/form_template/check_rule/status" tags:"验证规则相关" summary:"修改验证规则的状态" method:"post"`
	Id      int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
	Enabled bool  `p:"enabled" v:"required#enabled不能为空！"  summary:"是否启用"`
}
type FormCheckRuleStatusRes struct {
	g.Meta `mime:"application/json"`
}
