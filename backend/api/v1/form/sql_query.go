package form

import (
	"backend/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

// SQL查询参数结构
type SqlParameter struct {
	Parameter    string          `json:"parameter"`    // 参数名
	FieldPath    string          `json:"fieldPath"`    // 字段路径
	DefaultValue string          `json:"defaultValue"` // 默认值
	Value        string          `json:"value"`        // 实际值（用于执行时）
	FuncCode     *dto.ScriptCode `json:"funcCode"`     // 自定义函数代码
}

// SQL列配置结构
type SqlColumn struct {
	Key      string `json:"key"`      // 列键
	Label    string `json:"label"`    // 列标签
	Alias    string `json:"alias"`    // 列别名
	Show     bool   `json:"show"`     // 是否显示
	DataType string `json:"dataType"` // 数据类型
}

// 测试SQL查询接口
type SqlQueryTestReq struct {
	g.Meta     `path:"/sql-query/test" method:"post" tags:"SQL查询" summary:"测试SQL查询"`
	Sql        string         `json:"sql" v:"required#请输入SQL语句"`
	Parameters []SqlParameter `json:"parameters"` // 参数列表
	Type       string         `json:"type" v:"required|in:table,value#查询类型必须是table或value"`
}

type SqlQueryTestRes struct {
	g.Meta        `mime:"application/json"`
	Data          interface{} `json:"data"`
	Message       string      `json:"message"`
	ExecutionTime int64       `json:"executionTime"` // 毫秒
	RowCount      int         `json:"rowCount"`
}

// 执行SQL查询接口
type SqlQueryExecuteReq struct {
	g.Meta        `path:"/sql-query/execute" method:"post" tags:"SQL查询" summary:"执行SQL查询"`
	Sql           string                            `json:"sql" v:"required#请输入SQL语句"`
	Parameters    []SqlParameter                    `json:"parameters"` // 参数列表
	Type          string                            `json:"type" v:"required|in:table,value#查询类型必须是table或value"`
	FormData      g.Map                             `json:"formData"`      // 表单数据，用于参数绑定
	WithTableData map[string]map[string]interface{} `json:"withTableData"` // 关联表数据
}

type SqlQueryExecuteRes struct {
	g.Meta        `mime:"application/json"`
	Data          interface{} `json:"data"`
	Message       string      `json:"message"`
	ExecutionTime int64       `json:"executionTime"` // 毫秒
	RowCount      int         `json:"rowCount"`
}

// 验证SQL语法接口
type SqlQueryValidateReq struct {
	g.Meta `path:"/sql-query/validate" method:"post" tags:"SQL查询" summary:"验证SQL语法"`
	Sql    string `json:"sql" v:"required#请输入SQL语句"`
}

type SqlQueryValidateRes struct {
	g.Meta  `mime:"application/json"`
	Valid   bool   `json:"valid"`
	Message string `json:"message"`
}

// 获取数据库结构接口
type SqlQuerySchemaReq struct {
	g.Meta `path:"/sql-query/schema" method:"get" tags:"SQL查询" summary:"获取数据库结构"`
}

type DatabaseTable struct {
	Name    string           `json:"name"`
	Comment string           `json:"comment"`
	Columns []DatabaseColumn `json:"columns"`
}

type DatabaseColumn struct {
	Name    string `json:"name"`
	Type    string `json:"type"`
	Comment string `json:"comment"`
}

type SqlQuerySchemaRes struct {
	g.Meta `mime:"application/json"`
	Tables []DatabaseTable `json:"tables"`
}
