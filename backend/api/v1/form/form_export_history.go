package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type FormExportHistoryListReq struct {
	g.Meta `path:"/form_export_history/list"  method:"get" tags:"导出纪录相关" summary:"获得导出纪录列表"`
	model.PageReq
	Name                 string `p:"name"  summary:"名称"`
	FormExportTemplateId int64  `p:"exportTemplateId" summary:"导出模版id"  `
}

type FormExportHistoryListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.FormExportHistory `json:"list"`
}

// 导出纪录详情
type FormExportHistoryDetailReq struct {
	g.Meta `path:"/form_export_history/detail" tags:"导出纪录相关"  summary:"导出纪录详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormExportHistoryDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.FormExportHistory
}

// 新的数据导出
type FormExportHistoryNewReq struct {
	g.Meta               `path:"/form_export_history/new" tags:"导出纪录相关" summary:"新的数据导出" method:"post"`
	FormExportTemplateId int64                  `p:"formExportTemplateId"  v:"required#关联的表单模板不能为空！"  ` // 关联的模板id
	Filter               map[string]interface{} `p:"filter" summary:"搜索类型的表单过滤条件"`
	RuleFilter           map[string]interface{} `p:"ruleFilter" summary:"规则过滤条件"`
}

type FormExportHistoryNewRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `p:"id"`
}
