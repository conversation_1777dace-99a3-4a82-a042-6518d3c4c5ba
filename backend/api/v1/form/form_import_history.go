package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type FormImportHistoryListReq struct {
	g.Meta `path:"/form_import_history/list"  method:"get" tags:"导入纪录相关" summary:"获得导入纪录列表"`
	model.PageReq
	Name                 string `p:"name"  summary:"名称"`
	FormImportTemplateId int64  `p:"importTemplateId" summary:"导入模版id"  `
}

type FormImportHistoryListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []entity.FormImportHistory `json:"list"`
}

// 导入纪录详情
type FormImportHistoryDetailReq struct {
	g.Meta `path:"/form_import_history/detail" tags:"导入纪录相关"  summary:"导入纪录详细信息" method:"get"`
	Id     int64 `p:"id" v:"required#id不能为空！"  summary:"id"`
}
type FormImportHistoryDetailRes struct {
	g.Meta `mime:"application/json"`
	*entity.FormImportHistory
}

// 新的数据导入
type FormImportHistoryNewReq struct {
	g.Meta               `path:"/form_import_history/new" tags:"导入纪录相关" summary:"新的数据导入" method:"post"`
	FormImportTemplateId int64  `p:"formImportTemplateId"  v:"required#关联的表单模板不能为空！"  ` // 关联的模板id
	ImportFile           string `p:"importFile"                `                        // 数据文件地址
}

type FormImportHistoryNewRes struct {
	g.Meta `mime:"application/json"`
	Id     int64 `p:"id"`
}
