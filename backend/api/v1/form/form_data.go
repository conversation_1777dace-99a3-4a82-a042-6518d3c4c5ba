package form

import (
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

type CommonFormDataReq struct {
	FormId            int64                  `p:"formId" summary:"表单模板id" v:"required#请输入表单模板ID"`
	Filter            map[string]interface{} `p:"filter" summary:"搜索类型的表单过滤条件"`
	RuleFilter        map[string]interface{} `p:"ruleFilter" summary:"规则过滤条件"`
	ShowInvalid       int                    `p:"showInvalid" summary:"是否显示已经作废的单据 0全部 1显示除作废外的单据 2只显示作废的单据"`
	EnabledLinkedData bool                   `p:"enabledLinkedData" summary:"是否禁用已经被关联的数据"`
	SortByUsedCount   bool                   `p:"sortByUsedCount" summary:"是否根据关联数据量排序"`
	LinkedData        *dto.LinkedData        `p:"linkedData" summary:"关联数据"`
}

type FormDataListReq struct {
	g.Meta `path:"/form_data/list" method:"get" tags:"表单数据相关" summary:"获取所在表单的所有数据"`
	model.PageReq
	CommonFormDataReq
}

type FormDataListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []map[string]interface{} `json:"list"`
}

type FormDataListSumReq struct {
	g.Meta `path:"/form_data/list_sum" method:"get" tags:"表单数据相关" summary:"获取所在表单的所有数据的总和"`
	CommonFormDataReq
	FieldName string `p:"fieldName" summary:"字段名" v:"required#请输入字段名"`
}
type FormDataListSumRes struct {
	g.Meta `mime:"application/json"`
	Total  float64 `json:"total"`
}

type FormDataListCustomReq struct {
	g.Meta    `path:"/form_data/list_custom"  method:"get" tags:"表单数据相关" summary:"获取自定义的表单数据"`
	TableName string                 `p:"tableName" summary:"表名" v:"required#请输入表名"`
	Filter    map[string]interface{} `p:"filter"    summary:"表单过滤条件"`
}

type FormDataListCustomRes struct {
	g.Meta `mime:"application/json"`
	List   []map[string]interface{} `json:"list"`
}
type FormDataSaveReq struct {
	g.Meta            `path:"/form_data/save"  method:"post" tags:"表单数据相关" summary:"保存表单数据"`
	FormId            int64                  `p:"formId"        summary:"表单模板id"  v:"required#请输入表单模板ID"`
	Id                int64                  `p:"id"            summary:"表单id" `
	Data              map[string]interface{} `p:"data"          summary:"表单数据"`
	ExtraData         *dto.InstanceExtraData `p:"extraData" summary:"附加数据"`
	ExpandDataId      int64                  `p:"expandDataId" summary:"扩展数据id"`
	AllowUpdatePartly bool                   `p:"allowUpdatePartly" summary:"是否允许只更新部分字段"`
}

type FormDataSaveRes struct {
	g.Meta   `mime:"application/json"`
	Template *with.FormTemplate     `json:"template" summary:"表单模板信息"`
	Info     map[string]interface{} `json:"info" summary:"表单数据"`
}

type ColumnWithListReq struct {
	g.Meta `path:"/form_data/column_with_list"  method:"get" tags:"表单数据相关" summary:"获得某个表单的某个字段关联数据列表"`
	model.PageReq
	FormId            int64                  `p:"formId"          summary:"表单模板id"  v:"required#请输入表单模板ID"`
	ColumnName        string                 `p:"column"          summary:"字段名"  v:"required#请输入字段名"`
	Filter            map[string]interface{} `p:"filter" summary:"过滤条件"`
	RuleFilter        map[string]interface{} `p:"ruleFilter" summary:"规则过滤条件"`
	EnabledLinkedData bool                   `p:"enabledLinkedData" summary:"是否禁用已经被关联的数据"`
	SortByUsedCount   bool                   `p:"sortByUsedCount" summary:"是否根据关联数据量排序"`
}
type ColumnWithListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []map[string]interface{} `json:"list"`
}
type ColumnWithInfoReq struct {
	g.Meta `path:"/form_data/column_with_info"  method:"get" tags:"表单数据相关" summary:"获得某个表单的某个字段关联表单信息"`
	model.PageReq
	FormId     int64  `p:"formId"          summary:"表单模板id"  v:"required#请输入表单模板ID"`
	ColumnName string `p:"column"          summary:"字段名"  v:"required#请输入字段名"`
	TableName  string `p:"tableName"       summary:"表名"`
}
type ColumnWithInfoRes struct {
	g.Meta `mime:"application/json"`
	*dto.SupportTable
}
type FormDataDeleteReq struct {
	g.Meta `path:"/form_data/delete"  method:"post" tags:"表单数据相关" summary:"删除表单数据"`
	FormId int64 `p:"formId"          summary:"表单模板id"  v:"required#请输入表单模板ID"`
	Id     int64 `p:"id"          summary:"表单id" `
}

type FormDataDeleteRes struct {
	g.Meta `mime:"application/json"`
}

type FormDataInfoReq struct {
	g.Meta `path:"/form_data/info"  method:"get" tags:"表单数据相关" summary:"获得表单详细信息"`
	FormId int64 `p:"formId"          summary:"表单模板id"  v:"required#请输入表单模板ID"`
	Id     int64 `p:"id"          summary:"表单id" `
}

type FormDataInfoRes struct {
	g.Meta   `mime:"application/json"`
	Template *with.FormTemplate     `json:"template" summary:"表单模板信息"`
	Info     map[string]interface{} `json:"info" summary:"表单数据"`
}

type FormExpandInfoReq struct {
	g.Meta       `path:"/form_data/get_expand_info"  method:"get" tags:"表单数据相关" summary:"获得表单扩展信息"`
	ExpandKey    string `p:"expandKey"          summary:"表单的拓展标识key"  v:"required#请输入表单的拓展标识key"`
	ExpandDataId int64  `p:"expandDataId" summary:"扩展数据id"`
}

type FormExpandInfoRes struct {
	g.Meta `mime:"application/json"`
	*dto.ExpandInfo
}

type TableDataListReq struct {
	g.Meta `path:"/table_data/list" method:"get" tags:"表单数据相关" summary:"获得任意数据表数据列表"`
	model.PageReq
	TableName  string                 `p:"tableName" summary:"表名" v:"required#请输入表名"`
	TableType  int                    `p:"tableType" summary:"表类型" v:"required#请输入表表类型"`
	Filter     map[string]interface{} `p:"filter" summary:"搜索类型的表单过滤条件"`
	RuleFilter map[string]interface{} `p:"ruleFilter" summary:"规则过滤条件"`
}

type TableDataListRes struct {
	g.Meta `mime:"application/json"`
	model.ListRes
	List []map[string]interface{} `json:"list"`
}

type FormDataInvalidateReq struct {
	g.Meta    `path:"/form_data/invalidate" method:"post" tags:"表单数据相关" summary:"作废表单数据"`
	FormId    int64                  `p:"formId" summary:"表单模板id" v:"required#请输入表单模板ID"`
	Id        int64                  `p:"id" summary:"表单id" v:"required#请输入表单ID"`
	ExtraData *dto.InstanceExtraData `p:"extraData" summary:"附加数据"`
}

type FormDataInvalidateRes struct {
	g.Meta `mime:"application/json"`
}

type FormDataUpdateFieldReq struct {
	g.Meta     `path:"/form_data/update_field" method:"post" tags:"表单数据相关" summary:"更新表单单个字段"`
	FormId     int64       `p:"formId" summary:"表单模板id" v:"required#请输入表单模板ID"`
	Id         int64       `p:"id" summary:"表单数据id" v:"required#请输入表单数据ID"`
	FieldName  string      `p:"fieldName" summary:"字段名" v:"required#请输入字段名"`
	FieldValue interface{} `p:"fieldValue" summary:"字段值"`
}

type FormDataUpdateFieldRes struct {
	g.Meta `mime:"application/json"`
}
