# SQL数据源在打印模板中的使用说明

## 功能概述

SQL数据源功能允许在打印模板中动态执行SQL查询并将结果插入到打印内容中。支持两种类型的查询：
- **表格查询** (`table`): 返回多行数据，显示为HTML表格
- **单值查询** (`value`): 返回单个值，直接显示在模板中

## 前端使用方式

### 1. 在打印模板编辑器中插入SQL查询

```javascript
// 前端插入SQL查询占位符的格式
const sqlConfig = {
    type: "sql_query",
    sqlType: "table", // 或 "value"
    sqlId: "query_001", // 查询的唯一标识
    sqlName: "订单统计", // 查询的显示名称
    sql: "SELECT * FROM orders WHERE user_id = #{user_id}",
    parameters: [
        {
            parameter: "user_id",
            fieldPath: "user.id", 
            defaultValue: "0"
        }
    ]
};
```

### 2. 参数绑定

支持从表单数据中绑定参数到SQL查询：
- `parameter`: SQL中的参数名（使用 `#{parameter_name}` 格式）
- `fieldPath`: 表单字段路径，支持点号分隔（如 `user.id`, `order.create_time`）
- `defaultValue`: 当无法获取字段值时的默认值

## 后端实现

### 1. SQL查询执行流程

```go
// 在打印时执行SQL查询
sqlQueryResults, err := s.ExecuteSqlQueries(ctx, printTemplate.SqlQueries, formData)

// 将结果传递给模板处理器
templateContent, err := HandleTemplateContent(ctx, 
    printTemplate.TemplateContent, 
    withFormColumns, 
    withTableData, 
    withTableDataMaps, 
    withFormTableInfo, 
    sqlQueryResults)
```

**重构说明**: 实际实现中复用了现有的 `service.SqlQuery()` 服务，避免重复代码：

```go
// 使用现有的SQL查询服务
req := &form.SqlQueryExecuteReq{
    Sql:        sql,
    Type:       queryType,
    Parameters: parameters,
    FormData:   g.Map(formData),
}

result, err := service.SqlQuery().ExecuteQuery(ctx, req)
```

### 2. 占位符替换

在`HandleHrefContent`函数中处理`sql_query`类型的占位符：

```go
} else if varType == "sql_query" {
    // 处理SQL查询占位符
    res = HandleSqlQueryContent(ctx, hrefMap, sqlQueryResults)
}
```

### 3. 结果格式化

- **表格查询**: 自动生成HTML表格，包含表头和数据行
- **单值查询**: 直接转换为字符串显示

## 安全性

### 1. SQL注入防护

```go
// 参数值转义处理
escapedValue := strings.ReplaceAll(value, "'", "''")
result = strings.ReplaceAll(result, paramPlaceholder, fmt.Sprintf("'%s'", escapedValue))
```

### 2. 错误处理

- 单个SQL查询失败不会阻断整个打印流程
- 查询失败时显示默认占位符内容
- 详细错误日志记录

## 使用示例

### 示例1: 订单明细表格

```sql
-- SQL查询
SELECT 
    product_name as '商品名称',
    quantity as '数量', 
    unit_price as '单价',
    (quantity * unit_price) as '小计'
FROM order_items 
WHERE order_id = #{order_id}
ORDER BY id
```

**参数配置:**
```json
{
    "parameter": "order_id",
    "fieldPath": "id",
    "defaultValue": "0"
}
```

**生成的HTML:**
```html
<table border='1' style='width: 100%; border-collapse: collapse;'>
<tr>
    <th style='font-weight: 400; padding: 3px 5px; text-align: center;'>商品名称</th>
    <th style='font-weight: 400; padding: 3px 5px; text-align: center;'>数量</th>
    <th style='font-weight: 400; padding: 3px 5px; text-align: center;'>单价</th>
    <th style='font-weight: 400; padding: 3px 5px; text-align: center;'>小计</th>
</tr>
<!-- 数据行 -->
</table>
```

### 示例2: 订单总金额

```sql
-- SQL查询
SELECT SUM(quantity * unit_price) as total_amount
FROM order_items 
WHERE order_id = #{order_id}
```

**参数配置:**
```json
{
    "parameter": "order_id", 
    "fieldPath": "id",
    "defaultValue": "0"
}
```

**生成结果:** 直接显示总金额数值

## 数据库支持

支持所有GoFrame框架兼容的数据库：
- MySQL
- PostgreSQL
- SQLite
- SQL Server
- Oracle

## 错误排查

### 1. SQL查询不执行
- 检查`printTemplate.SqlQueries`是否正确保存
- 确认JSON格式正确
- 查看日志中的错误信息

### 2. 参数替换失败
- 检查`fieldPath`是否正确
- 确认表单数据中包含对应字段
- 验证默认值设置

### 3. 结果显示异常
- 检查SQL查询结果格式
- 确认查询类型（table/value）设置正确
- 查看HTML生成是否正常

## 注意事项

1. **性能考虑**: 避免在打印模板中使用复杂或耗时的SQL查询
2. **数据权限**: 确保SQL查询不会泄露敏感数据
3. **数据量限制**: 表格查询结果过多可能影响打印效果
4. **参数验证**: 建议对关键参数进行额外验证
5. **缓存策略**: 对于静态数据可考虑缓存机制

## 版本信息

- 支持版本: v1.0+
- 最后更新: 2024年12月
- 功能状态: 稳定可用 