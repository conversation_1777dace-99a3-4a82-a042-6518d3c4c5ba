package nodes

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"fmt"

	"github.com/gogf/gf/v2/os/gctx"
)

type OutPrint struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *OutPrint) Execute() {
	fmt.Printf("OutPrint inputs: %v\n", n.Inputs)
	fmt.Printf("OutPrint params: %v\n", n.Params)
	n.Outputs["data"] = n.Params["data"]
}

func init() {
	manager.RegisterNode("outprint", func() types.INode {
		return &OutPrint{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "打印节点",
					Description: "测试用节点",
					Group:       "test",
					Type:        "test_print",
					Width:       500,
					InputTypes:  make([]*types.MetaColumns, 0),
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
