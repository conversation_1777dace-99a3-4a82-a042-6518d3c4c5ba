package caculate

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"fmt"
	"regexp"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

type CalculateSubtraction struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *CalculateSubtraction) Execute() {
	n.Log("CalculateSubtraction Execute")

	input1, err := n.GetInputValueFromRefInputNumberSetter("input1")
	if err != nil {
		n.LogError("获取输入数值1失败")
		return
	}

	input2, err := n.GetInputValueFromRefInputNumberSetter("input2")
	if err != nil {
		n.LogError("获取输入数值2失败")
		return
	}

	isInteger := regexp.MustCompile(`^-?\d+$`)

	var result interface{}
	if isInteger.MatchString(fmt.Sprint(input1)) && isInteger.MatchString(fmt.Sprint(input2)) {
		result = gconv.Int64(input1) - gconv.Int64(input2)
	} else {
		input1Decimal, err := decimal.NewFromString(fmt.Sprint(input1))
		if err != nil {
			n.LogError("减法计算失败")
			return
		}
		input2Decimal, err := decimal.NewFromString(fmt.Sprint(input2))
		if err != nil {
			n.LogError("减法计算失败")
			return
		}
		resultDecimal := input1Decimal.Sub(input2Decimal)
		result = resultDecimal.String()
	}
	n.Outputs[n.Meta.Id] = map[string]interface{}{
		"result": gvar.New(result),
	}
}

func init() {
	manager.RegisterNode("calculate_subtraction", func() types.INode {
		return &CalculateSubtraction{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "减法计算器",
					Description: "传入两个数值，进行减法运算",
					Group:       "calculate",
					Type:        "calculate_subtraction",
					Width:       400,
					Height:      214,
					InputTypes: []*types.MetaColumns{
						{Id: "input1", ComponentName: "InputNumber", Label: "输入数值1", ColumnType: "number", Required: true, Children: make([]*types.MetaColumns, 0), Setter: &types.MetaSetter{
							Name: "RefInputNumberSetter",
						}},
						{Id: "input2", ComponentName: "InputNumber", Label: "输入数值2", ColumnType: "number", Required: true, Children: make([]*types.MetaColumns, 0), Setter: &types.MetaSetter{
							Name: "RefInputNumberSetter",
						}},
					},
					OutputTypes: []*types.MetaColumns{
						{Id: "result", ComponentName: "InputNumber", Label: "计算结果", ColumnType: "number", Required: true, Children: make([]*types.MetaColumns, 0)},
					},
				},
			},
		}
	})
}
