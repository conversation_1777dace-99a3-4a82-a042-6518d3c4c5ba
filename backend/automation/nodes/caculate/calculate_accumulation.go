package caculate

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/service"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type CalculateAccumulation struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *CalculateAccumulation) Execute() {
	n.Log("CalculateAccumulation Execute")

	currentNumber, err := n.GetInputValueFromRefInputNumberSetter("current_number")
	if err != nil {
		n.LogError("获取本次数值失败")
		return
	}
	accumulationKeys, err := n.GetInputValueFromAccumulationKeySetter("accumulation_key")
	if err != nil {
		n.LogError("获取累加KEY失败")
		return
	}
	operationType, err := n.GetInputValueFromSelectSetter("operation_type")
	if err != nil || operationType == "" {
		operationType = "add"
	}
	jsonStr, err := json.Marshal(accumulationKeys)
	if err != nil {
		n.LogError("accumulationKeys 序列化失败")
		return
	}
	incrKey := gmd5.MustEncrypt("accumulation_" + string(jsonStr))
	result, err := service.Cache().IncrByFloat(n.Ctx, incrKey, lo.Ternary(operationType == "add", gconv.Float64(currentNumber), -gconv.Float64(currentNumber)))
	if err != nil {
		n.LogError(err.Error())
		return
	}
	n.Log(fmt.Sprintf("incrKey: %s, incrJson: %s, result: %v", incrKey, string(jsonStr), result))
	n.Outputs[n.Meta.Id] = map[string]interface{}{
		"result": gvar.New(result),
	}
}

func init() {
	manager.RegisterNode("calculate_accumulation", func() types.INode {
		return &CalculateAccumulation{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "累加计算器（浮点）",
					Description: "用于计算某种类型的值经过此节点的累加结果",
					Group:       "calculate",
					Type:        "calculate_accumulation",
					Width:       400,
					Height:      214,
					InputTypes: []*types.MetaColumns{
						{Id: "operation_type", ComponentName: "InputSelect", Label: "运算类型", ColumnType: "String", Required: true, Children: make([]*types.MetaColumns, 0), Setter: &types.MetaSetter{
							Name: "SelectSetter",
							Props: map[string]interface{}{
								"options": []map[string]string{
									{"label": "加", "value": "add"},
									{"label": "减", "value": "subtract"},
								},
								"defaultValue": "add",
							},
						}},
						{Id: "accumulation_key", ComponentName: "InputNumber", Label: "累加KEY(用于标识累加)", ColumnType: "array", Required: true, Children: make([]*types.MetaColumns, 0), Setter: &types.MetaSetter{
							Name: "AccumulationKeySetter",
						}},
						{Id: "current_number", ComponentName: "InputNumber", Label: "本次数值", ColumnType: "Float", Required: true, Children: make([]*types.MetaColumns, 0), Setter: &types.MetaSetter{
							Name: "RefInputNumberSetter",
						}},
					},
					OutputTypes: []*types.MetaColumns{
						{Id: "result", ComponentName: "InputNumber", Label: "累加结果", ColumnType: "Float", Required: true, Children: make([]*types.MetaColumns, 0)},
					},
				},
			},
		}
	})
}
