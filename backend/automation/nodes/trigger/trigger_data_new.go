package trigger

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/consts"
	"backend/internal/service"
	"backend/library"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type TriggerDataNew struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *TriggerDataNew) Execute() {
	g.Log().Info(n.Ctx, library.SDump("TriggerDataNew Execute", n.Inputs))
	// 获得 form_data_id
	form_data_id_input, exists := n.Inputs["form_data_id"]
	if !exists {
		n.StopError("获取数据ID失败")
		return
	}
	form_data_id := gconv.Int64(form_data_id_input)

	source_table, err := n.GetInputValueFromTableNameSetter("source_table")
	if err != nil {
		n.StopError("获取触发器的表单名失败")
		return
	}

	form_data, err := service.FormData().GetFormData(n.Ctx, source_table, form_data_id)
	if err != nil {
		n.StopError("获取表单数据失败")
		return
	}
	n.Outputs[n.Meta.Id] = form_data
}

// 获取触发器参数
func (n *TriggerDataNew) GetTriggerParam(meta types.Meta) (source_table string, am_config_id int, version int64, err error) {
	source_table_settle, exists := meta.InputSettles["source_table"]
	if !exists {
		err = fmt.Errorf("TriggerFormInvalid source_table not exists")
		return
	}
	source_table = gconv.String(source_table_settle)
	am_config_id_input, exists := n.Inputs["am_config_id"]
	if !exists {
		err = fmt.Errorf("TriggerFormInvalid am_config_id not exists")
		return
	}
	am_config_id = gconv.Int(am_config_id_input)
	version_input, exists := n.Inputs["version"]
	if !exists {
		err = fmt.Errorf("TriggerFormInvalid version not exists")
		return
	}
	version = gconv.Int64(version_input)
	return
}

// 创建触发器时的逻辑
func (n *TriggerDataNew) TriggerCreate(meta *types.Meta) {
	source_table, _, _, err := n.GetTriggerParam(*meta)
	if err != nil {
		g.Log().Info(n.Ctx, "TriggerCreate")
		g.Log().Error(n.Ctx, err)
		return
	}
	g.Log().Info(n.Ctx, "TriggerCreate source_table", source_table)
	trigger_key := fmt.Sprintf("%s_%s", consts.TriggerFormDataNew, source_table)
	service.Cache().SAdd(n.Ctx, trigger_key, n.Inputs)
}

// 删除触发器时的逻辑
func (n *TriggerDataNew) TriggerRemove(meta *types.Meta) {
	source_table, _, _, err := n.GetTriggerParam(*meta)
	if err != nil {
		g.Log().Info(n.Ctx, "TriggerRemove")
		g.Log().Error(n.Ctx, err)
		return
	}
	trigger_key := fmt.Sprintf("%s_%s", consts.TriggerFormDataNew, source_table)
	service.Cache().SRem(n.Ctx, trigger_key, n.Inputs)
}
func init() {
	funcNew := func() *TriggerDataNew {
		return &TriggerDataNew{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "表单数据新增触发器",
					Description: "当选择的表单发生数据新增时，触发此流程",
					Group:       "trigger",
					Type:        "trigger_form_data_new",
					Width:       400,
					Height:      214,
					InputTypes: []*types.MetaColumns{
						{Id: "source_table", ComponentName: "", Label: "表单名称", ColumnType: "FormTable", Required: true, Setter: &types.MetaSetter{
							Name: "FormTableSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	}
	manager.RegisterNode("trigger_form_data_new", func() types.INode {
		return funcNew()
	})
	manager.RegisterNodeTrigger("trigger_form_data_new", func() types.INodeTrigger {
		return funcNew()
	})
}
