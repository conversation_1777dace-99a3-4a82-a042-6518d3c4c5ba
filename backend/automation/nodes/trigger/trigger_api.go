package trigger

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/consts"
	"backend/internal/service"
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type APITrigger struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *APITrigger) Execute() {
	fmt.Printf("APITrigger inputs: %v\n", n.Inputs)
	fmt.Printf("APITrigger params: %v\n", n.Params)

	// 获得n.Inputs中传入的token，与元数据中的token对比
	metaToken, _ := n.GetInputValueFromInputTextSetter("token")
	// if err != nil {
	// 	n.Log("没有获取到设置的Token")
	// }
	if !g.<PERSON>Empty(metaToken) {
		token, exists := n.Inputs["token"]
		if !exists || token != metaToken {
			n.StopError("token不匹配或未传入token")
			return
		}
	}

	metaFieldSettles, err := n.GetInputValueFromFieldListSetter("params")
	if err != nil {
		n.StopError("获取字段设置失败")
		return
	}

	fieldData, exists := n.Inputs["params"]
	if !exists {
		n.StopError("获取字段数据失败")
		return
	}

	fieldDataMap, ok := fieldData.(map[string]interface{})
	if !ok {
		n.StopError("字段数据格式错误")
		return
	}

	outputValues := make(map[string]interface{})
	for _, metaFieldSettle := range metaFieldSettles {
		fieldValue, exists := fieldDataMap[metaFieldSettle.Id]
		if !exists {
			if metaFieldSettle.Required {
				n.StopError(fmt.Sprintf("字段数据%s必须传入", metaFieldSettle.Id))
				return
			}
			outputValues[metaFieldSettle.Id] = ""
			continue
		}
		outputValues[metaFieldSettle.Id] = fieldValue
	}

	fmt.Printf("outputValues: %v\n", outputValues)

	n.Outputs[n.Meta.Id] = outputValues
}

func (n *APITrigger) GetTriggerParam(meta types.Meta) (route string, am_config_id int, version int64, err error) {
	route_settle, exists := meta.InputSettles["route"]
	if !exists {
		err = fmt.Errorf("TriggerFormInvalid route not exists")
		return
	}
	route = gconv.String(route_settle)
	am_config_id_input, exists := n.Inputs["am_config_id"]
	if !exists {
		err = fmt.Errorf("TriggerFormInvalid am_config_id not exists")
		return
	}
	am_config_id = gconv.Int(am_config_id_input)
	version_input, exists := n.Inputs["version"]
	if !exists {
		err = fmt.Errorf("TriggerFormInvalid version not exists")
		return
	}
	version = gconv.Int64(version_input)
	return
}

func (n *APITrigger) handleTrigger(meta *types.Meta, action func(ctx context.Context, key string, values interface{}) error) {
	route, _, _, err := n.GetTriggerParam(*meta)
	if err != nil {
		g.Log().Info(n.Ctx, "HandleTrigger")
		g.Log().Error(n.Ctx, err)
		return
	}
	route = strings.TrimPrefix(route, "/")

	trigger_key := fmt.Sprintf("%s_%s", consts.TriggerAPI, route)
	action(n.Ctx, trigger_key, n.Inputs)
}

// 创建触发器时的逻辑(创建一个路由缓存，方便被API触发)
func (n *APITrigger) TriggerCreate(meta *types.Meta) {
	n.handleTrigger(meta, func(ctx context.Context, key string, values interface{}) error {
		service.Cache().SAdd(ctx, key, values)
		return nil
	})
}

// 删除触发器时的逻辑（删除一个路由缓存）
func (n *APITrigger) TriggerRemove(meta *types.Meta) {
	n.handleTrigger(meta, func(ctx context.Context, key string, values interface{}) error {
		service.Cache().SRem(ctx, key, values)
		return nil
	})
}

func init() {
	funcNew := func() *APITrigger {
		return &APITrigger{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "API触发器",
					Description: "对外开放一个接口，接收token和参数列表",
					Group:       "trigger",
					Type:        "trigger_api",
					Width:       600,
					Height:      214,
					InputTypes: []*types.MetaColumns{
						{Id: "route", ComponentName: "InputText", Label: "触发的路由(仅支持POST)(访问格式：/api/v1/automation/trigger/自定义路由内容)", ColumnType: "String", Required: true, Setter: &types.MetaSetter{
							Name: "InputTextSetter",
						}},
						{Id: "token", ComponentName: "InputText", Label: "Token(用于安全验证，传入header中的token)", ColumnType: "String", Required: false, Setter: &types.MetaSetter{
							Name: "InputTextRandomText",
						}},
						{Id: "params", ComponentName: "FieldList", Label: "参数列表", ColumnType: "Map", Required: true, Setter: &types.MetaSetter{
							Name: "FieldListSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	}
	manager.RegisterNode("trigger_api", func() types.INode {
		return funcNew()
	})
	manager.RegisterNodeTrigger("trigger_api", func() types.INodeTrigger {
		return funcNew()
	})
}
