package trigger

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/consts"
	"backend/internal/service"
	"backend/library"
	"context"

	"fmt"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/samber/lo"

	"github.com/gogf/gf/v2/util/gconv"
)

// 表单的流程结束或者退回到了开始节点时触发
type TriggerFormFlowRejectOrCancel struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *TriggerFormFlowRejectOrCancel) Execute() {
	g.Log().Info(n.Ctx, library.SDump("TriggerFormFlowRejectOrCancel Execute", n.Inputs))
	// 获得 form_data_id
	form_data_id_input, exists := n.Inputs["form_data_id"]
	if !exists {
		n.StopError("获取数据ID失败")
		return
	}
	form_data_id := gconv.Int64(form_data_id_input)

	source_table, err := n.GetInputValueFromTableNameSetter("source_table")
	if err != nil {
		n.StopError("获取触发器的表单名失败")
		return
	}

	form_data, err := service.FormData().GetFormData(n.Ctx, source_table, form_data_id)
	if err != nil {
		n.StopError("获取表单数据失败")
		return
	}
	n.Outputs[n.Meta.Id] = form_data
}

// 获取触发器参数
func (n *TriggerFormFlowRejectOrCancel) GetTriggerParam(meta types.Meta) (source_table string, approval_type string, am_config_id int, version int64, err error) {
	source_table_settle, exists := meta.InputSettles["source_table"]
	if !exists {
		err = fmt.Errorf("TriggerFormFlowRejectOrCancel source_table not exists")
		return
	}
	source_table = gconv.String(source_table_settle)

	approval_type_settle, exists := meta.InputSettles["approval_type"]
	if exists {
		approval_type = gconv.String(approval_type_settle)
	}

	am_config_id_input, exists := n.Inputs["am_config_id"]
	if !exists {
		err = fmt.Errorf("TriggerFormFlowRejectOrCancel am_config_id not exists")
		return
	}
	am_config_id = gconv.Int(am_config_id_input)
	version_input, exists := n.Inputs["version"]
	if !exists {
		err = fmt.Errorf("TriggerFormFlowRejectOrCancel version not exists")
		return
	}
	version = gconv.Int64(version_input)
	return
}
func (n *TriggerFormFlowRejectOrCancel) handleTrigger(meta *types.Meta, action func(ctx context.Context, key string, values interface{}) error) {
	source_table, approval_type, _, _, err := n.GetTriggerParam(*meta)
	if err != nil {
		g.Log().Info(n.Ctx, "HandleTrigger")
		g.Log().Error(n.Ctx, err)
		return
	}
	approval_list := []string{"create", "invalidate"}
	if lo.Contains(approval_list, approval_type) {
		approval_list = []string{approval_type}
	} else if approval_type != "all" {
		// 设定一个默认值
		approval_list = []string{"create"}
	}
	for _, approval_type := range approval_list {
		trigger_key := fmt.Sprintf("%s_%s_%s", consts.TriggerFormFlowRejectOrCancel, source_table, approval_type)
		action(n.Ctx, trigger_key, n.Inputs)
	}
}

// 创建触发器时的逻辑
func (n *TriggerFormFlowRejectOrCancel) TriggerCreate(meta *types.Meta) {
	n.handleTrigger(meta, func(ctx context.Context, key string, values interface{}) error {
		return service.Cache().SAdd(ctx, key, values)
	})
}

// 删除触发器时的逻辑
func (n *TriggerFormFlowRejectOrCancel) TriggerRemove(meta *types.Meta) {
	n.handleTrigger(meta, service.Cache().SRem)
}
func init() {
	funcNew := func() *TriggerFormFlowRejectOrCancel {
		return &TriggerFormFlowRejectOrCancel{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "驳回或撤销触发器",
					Description: "当表单审批流驳回或撤销时，触发此流程",
					Group:       "trigger",
					Type:        "trigger_form_flow_reject_or_cancel",
					Width:       400,
					Height:      214,
					InputTypes: []*types.MetaColumns{
						{Id: "source_table", ComponentName: "", Label: "表单名称", ColumnType: "FormTable", Required: true, Setter: &types.MetaSetter{
							Name: "FormTableSetter",
						}},
						{Id: "approval_type", ComponentName: "InputSelect", Label: "审批类型", ColumnType: "String", Required: true, Children: make([]*types.MetaColumns, 0), Setter: &types.MetaSetter{
							Name: "SelectSetter",
							Props: map[string]interface{}{
								"options": []map[string]string{
									{"label": "所有", "value": "all"},
									{"label": "创建表单", "value": "create"},
									{"label": "作废表单", "value": "invalidate"},
								},
								"defaultValue": "create",
							},
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	}
	manager.RegisterNode("trigger_form_flow_reject_or_cancel", func() types.INode {
		return funcNew()
	})
	manager.RegisterNodeTrigger("trigger_form_flow_reject_or_cancel", func() types.INodeTrigger {
		return funcNew()
	})
}
