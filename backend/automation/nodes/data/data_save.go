package data

import (
	"backend/api/v1/form"
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"fmt"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type DataSave struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *DataSave) Execute() {
	n.Log("DataSave Execute")

	targetTable, err := n.GetInputValueFromTableNameSetter("target_table")
	if err != nil {
		n.StopError("获取保存数据的表名失败")
		return
	}

	columnMaps, err := n.GetInputValueFromColumnMapSetter("column_map")
	if err != nil {
		n.StopError("获取字段对应关系失败")
		return
	}

	uniqueID, err := n.GetInputValueFromUniqueIDSetter("unique_id")
	if err != nil {
		n.StopError("获取唯一标识字段失败")
		return
	}

	g.Log().Info(n.Ctx, library.SDump("target_table", targetTable))
	g.Log().Info(n.Ctx, library.SDump("column_map", columnMaps))

	// 获取表单模板
	formTemplate, err := service.FormTemplate().GetFormTemplateByTableName(n.Ctx, targetTable)
	if err != nil || formTemplate == nil {
		n.StopError(fmt.Sprintf("获取表单模板失败:%s", err.Error()))
		return
	}

	splitDatas, err := n.SplitInputData(columnMaps)
	if err != nil {
		n.StopError(err.Error())
		return
	}
	var outputDatas []map[string]interface{}

	for _, splitData := range splitDatas {
		if err := n.saveFormDataAndOutput(formTemplate.Id, splitData, &outputDatas, uniqueID); err != nil {
			n.StopError(err.Error())
			break
		}
	}

	// 设置输出（暂时设置为最后一个）
	n.Outputs[n.Meta.Id] = lo.Ternary(len(outputDatas) > 0, outputDatas[len(outputDatas)-1], nil)

}

// 保存表单数据
func (n *DataSave) saveFormDataAndOutput(formTemplateId int64, formData map[string]interface{}, outputDatas *[]map[string]interface{}, uniqueID []string) (err error) {

	var id int64 = 0
	if len(uniqueID) > 0 {
		filter := make(map[string]interface{})
		for _, id := range uniqueID {
			if _, exists := formData[id]; !exists {
				continue
			}
			filter[id] = formData[id]
		}
		if len(filter) > 0 {
			oldFormData, err := service.FormData().GetDetailByFilter(n.Ctx, formTemplateId, filter)
			if err != nil {
				return fmt.Errorf("根据唯一字段获取表单数据失败: %s", err.Error())
			}
			if oldFormData != nil {
				id = gconv.Int64(oldFormData["id"])
			}
			// for key, value := range formData {
			// 	oldFormData[key] = value
			// }
			// formData = oldFormData
		}
	}

	input := &form.FormDataSaveReq{
		FormId:            formTemplateId,
		Id:                id,
		Data:              formData,
		AllowUpdatePartly: true,
	}

	// 添加自动化来源标记，用于防止循环触发
	automationId, hasAutomationId := n.Inputs["automation_id"]
	if hasAutomationId && !g.IsEmpty(automationId) {
		input.ExtraData = &dto.InstanceExtraData{
			TriggerType:     "automation",
			TriggerSourceId: gconv.String(automationId),
		}
	}

	var newFormData *form.FormDataSaveRes
	createdBy, hasCreatedBy := formData["created_by"].(int64)
	if hasCreatedBy && createdBy > 0 {
		newFormData, err = service.FormData().Save(n.Ctx, input, nil, createdBy)
	} else {
		newFormData, err = service.FormData().Save(n.Ctx, input, nil)
	}
	if err != nil {
		return fmt.Errorf("保存数据失败: %s", err.Error())
	}

	*outputDatas = append(*outputDatas, newFormData.Info)
	return
}

func init() {
	manager.RegisterNode("data_save", func() types.INode {
		return &DataSave{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "保存数据",
					Description: "根据指定的字段对应关系，以及唯一标识字段，将节点引用的数据或者输入数据保存到新表",
					Group:       "data",
					Type:        "data_save",
					Width:       600,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{Id: "target_table", ComponentName: "", Label: "目标表单", ColumnType: "FormTable", Required: false, Setter: &types.MetaSetter{
							Name: "FormTableSetter",
						}},
						{Id: "unique_id", ComponentName: "", Label: "唯一标识字段", ColumnType: "FormTable", Required: false, Setter: &types.MetaSetter{
							Name: "UniqueIDSetter",
						}},
						{Id: "column_map", ComponentName: "", Label: "字段对应关系", ColumnType: "ColumnMap", Required: false, Setter: &types.MetaSetter{
							Name: "ColumnMapSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
