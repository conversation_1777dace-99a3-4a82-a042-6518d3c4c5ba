package data

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/service"
	"backend/library"
	"fmt"

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

// ... existing code ...
type DataFindUser struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *DataFindUser) Execute() {
	n.Log("UserInfoFindone Execute")

	userId, err := n.GetInputValueFromRefInputNumberSetter("user_id")
	if err != nil {
		n.StopError("获取用户ID失败：" + err.Error())
		return
	}
	userIdInt := gconv.Int64(userId)
	if userIdInt == 0 {
		n.StopError(fmt.Sprintf("用户ID错误：%v", userId))
		return
	}

	n.Log(fmt.Sprintf("userIdInt: %v", userIdInt))
	user, err := service.SysUser().GetInfoByID(n.Ctx, userIdInt)
	if err != nil {
		n.StopError("查询用户信息失败")
		return
	}
	// n.Log(fmt.Sprintf("user: %v", user))

	// 设置输出
	userMap, err := library.StructToMap(user)
	if err != nil {
		n.StopError("转换用户信息失败")
		return
	}
	// n.Log(fmt.Sprintf("userMap: %v", userMap))
	n.Outputs[n.Meta.Id] = userMap
}

// 新增方法：注册节点
func init() {
	manager.RegisterNode("data_finduser", func() types.INode {
		return &DataFindUser{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "查询用户信息",
					Description: "根据用户ID查询用户信息",
					Group:       "data",
					Type:        "data_finduser",
					Width:       500,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{
							Id:            "user_id",
							ComponentName: "",
							Label:         "用户ID",
							ColumnType:    "RefInputNumber",
							Required:      true,
							Setter: &types.MetaSetter{
								Name: "RefInputNumberSetter",
							},
						},
					},
					OutputTypes: []*types.MetaColumns{
						{
							Id:            "id",
							ComponentName: "",
							Label:         "用户ID",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "code",
							ComponentName: "",
							Label:         "员工编号",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "systemAccount",
							ComponentName: "",
							Label:         "系统账号",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "username",
							ComponentName: "",
							Label:         "用户名",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "email",
							ComponentName: "",
							Label:         "邮箱",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "contactPhone",
							ComponentName: "",
							Label:         "联系电话",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "idNumber",
							ComponentName: "",
							Label:         "身份证号码",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "hireDate",
							ComponentName: "",
							Label:         "入职日期",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "resignationDate",
							ComponentName: "",
							Label:         "离职日期",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "status",
							ComponentName: "",
							Label:         "状态",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "withDepts",
							ComponentName: "",
							Label:         "所在部门",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "withPosts",
							ComponentName: "",
							Label:         "所属岗位",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "withRoles",
							ComponentName: "",
							Label:         "拥有的角色",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "withCompanys",
							ComponentName: "",
							Label:         "关联的公司",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "withProjects",
							ComponentName: "",
							Label:         "关联的项目",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "gender",
							ComponentName: "",
							Label:         "性别",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "usersale",
							ComponentName: "",
							Label:         "用户盐",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "password",
							ComponentName: "",
							Label:         "密码",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "birthday",
							ComponentName: "",
							Label:         "生日",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "contactAddress",
							ComponentName: "",
							Label:         "联系地址",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "description",
							ComponentName: "",
							Label:         "描述信息",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "lastLoginIp",
							ComponentName: "",
							Label:         "最后登录IP",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "lastLoginTime",
							ComponentName: "",
							Label:         "最后登录时间",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "createdBy",
							ComponentName: "",
							Label:         "创建人",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "createdAt",
							ComponentName: "",
							Label:         "创建时间",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "updatedAt",
							ComponentName: "",
							Label:         "更新时间",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "deletedAt",
							ComponentName: "",
							Label:         "删除时间",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "avatar",
							ComponentName: "",
							Label:         "用户头像",
							ColumnType:    "string",
							Required:      false,
						},
						{
							Id:            "lastUpdatePwdTime",
							ComponentName: "",
							Label:         "最后一次修改密码的时间",
							ColumnType:    "date",
							Required:      false,
						},
						{
							Id:            "passwordChanged",
							ComponentName: "",
							Label:         "密码是否已修改",
							ColumnType:    "bool",
							Required:      false,
						},
						{
							Id:            "depts",
							ComponentName: "",
							Label:         "部门",
							ColumnType:    "array",
							Required:      false,
							Children: []*types.MetaColumns{
								{
									Id:            "id",
									ComponentName: "",
									Label:         "关联id",
									ColumnType:    "number",
									Required:      false,
								},
								{
									Id:            "dept",
									ComponentName: "",
									Label:         "部门信息",
									ColumnType:    "object",
									Required:      false,
									Children: []*types.MetaColumns{
										{
											Id:            "id",
											ComponentName: "",
											Label:         "部门ID",
											ColumnType:    "number",
											Required:      false,
										},
										{
											Id:            "dept_name",
											ComponentName: "",
											Label:         "部门名称",
											ColumnType:    "string",
											Required:      false,
										},
									},
								},
							},
						},
					},
				},
			},
		}
	})
}
