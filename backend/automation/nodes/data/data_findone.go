package data

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/service"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type DataFindone struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *DataFindone) Execute() {
	n.Log("DataFindone Execute")

	targetTable, err := n.GetInputValueFromTableNameSetter("target_table")
	if err != nil {
		n.StopError("获取查询数据的表名失败")
		return
	}

	queryConditions, err := n.GetInputValueFromQueryConditionSetter("query_condition")
	if err != nil {
		n.StopError("获取查询条件失败：" + err.Error())
		return
	}

	m := g.DB().Model(gconv.String(targetTable)).Safe().Ctx(n.Ctx)

	// 获得表的字段信息
	formColumns, _ := service.FormTemplate().GetRelatedColumnsByTableName(n.Ctx, gconv.String(targetTable), 0, true)

	// 使用新方法构造查询条件
	build := n.buildQueryConditions(m, queryConditions, formColumns)

	// 新增排序参数处理：尝试获取排序参数并添加到查询中
	sortConditions, sortErr := n.GetInputValueFromSortConditionSetter("sort_condition")
	if sortErr == nil && len(sortConditions) > 0 {
		for _, sortCondition := range sortConditions {
			m = m.Order(sortCondition)
		}
	}

	record, err := m.Where(build).One()
	if err != nil {
		n.StopError("查询数据失败")
		return
	}
	// 设置输出
	n.Outputs[n.Meta.Id] = record.Map()
}

// 新增方法：构造查询条件
func (n *DataFindone) buildQueryConditions(m *gdb.Model, queryConditions []*types.QueryConditionSetter, formColumns []*dto.FormColumn) *gdb.WhereBuilder {
	build := m.Builder()
	for _, condition := range queryConditions {
		var conditonValue interface{}
		if condition.TargetColumn.ValueType == "fixed" {
			conditonValue = condition.TargetColumn.FixedValue
		} else {
			conditonValue = n.GetInputRefWrapperValue(condition.TargetColumn, true)
		}

		sourceColumn, _ := lo.Find(formColumns, func(item *dto.FormColumn) bool {
			return item.Id == condition.SourceColumn
		})
		if condition.Relation == "OR" {
			if condition.Type == 1 {
				build = n.applyCondition(build, build.WhereOr, build.WhereOrNot, build.WhereOrIn, build.WhereOrLike, condition, conditonValue, sourceColumn)
			} else {
				subBuild := n.buildQueryConditions(m, condition.SubCondition, formColumns)
				build = build.WhereOr(subBuild)
			}
		} else {
			if condition.Type == 1 {
				build = n.applyCondition(build, build.Where, build.WhereNot, build.WhereIn, func(column string, like interface{}) *gdb.WhereBuilder {
					return build.WhereLike(column, gconv.String(like))
				}, condition, conditonValue, sourceColumn)
			} else {
				subBuild := n.buildQueryConditions(m, condition.SubCondition, formColumns)
				build = build.Where(subBuild)
			}
		}
	}
	return build
}

// 新增方法：应用具体的查询条件
func (n *DataFindone) applyCondition(
	build *gdb.WhereBuilder,
	whereFunc func(where interface{}, args ...interface{}) *gdb.WhereBuilder,
	whereNotFunc func(column string, value interface{}) *gdb.WhereBuilder,
	whereInFunc func(column string, in interface{}) *gdb.WhereBuilder,
	whereLikeFunc func(column string, like interface{}) *gdb.WhereBuilder,
	condition *types.QueryConditionSetter,
	conditonValue interface{},
	sourceColumn *dto.FormColumn,
) *gdb.WhereBuilder {
	var anyFunc func(column string, value interface{}) *gdb.WhereBuilder
	switch condition.Operator {
	case enum.Operator_Equal:
		anyFunc = func(column string, value interface{}) *gdb.WhereBuilder {
			return whereFunc(column, conditonValue)
		}
	case enum.Operator_NotEqual:
		anyFunc = whereNotFunc
	case enum.Operator_GreaterThan:
		anyFunc = func(column string, value interface{}) *gdb.WhereBuilder {
			return whereFunc(column+" > ", conditonValue)
		}
	case enum.Operator_GreaterThanOrEqual:
		anyFunc = func(column string, value interface{}) *gdb.WhereBuilder {
			return whereFunc(column+" >=", conditonValue)
		}
	case enum.Operator_LessThan:
		anyFunc = func(column string, value interface{}) *gdb.WhereBuilder {
			return whereFunc(column+" < ", conditonValue)
		}
	case enum.Operator_LessThanOrEqual:
		anyFunc = func(column string, value interface{}) *gdb.WhereBuilder {
			return whereFunc(column+" <=", conditonValue)
		}
	case enum.Operator_In:
		anyFunc = whereInFunc
	case enum.Operator_ContainedIn:
		anyFunc = whereLikeFunc
	}

	if sourceColumn != nil && sourceColumn.ColumnType == "json" {
		switch value := conditonValue.(type) {
		case string:
			var jsonValue map[string]interface{}
			unmarshalErr := json.Unmarshal([]byte(value), &jsonValue)
			if unmarshalErr == nil {
				conditonValue = jsonValue["value"]
			}
		case map[string]interface{}:
			conditonValue = value["value"]
		}
		switch sourceColumn.ComponentName {
		case "Field.WithSingleSelect":
			build = anyFunc(fmt.Sprintf("JSON_EXTRACT(%s, '$.value') = ?", sourceColumn.Id), gconv.Int64(conditonValue))
		case "Field.WithMultipleSelect":
			build = anyFunc(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"value": ', ?, '}'), '$')`, sourceColumn.Id), gconv.Int64(conditonValue))
		case "Field.UserSelect", "Field.DeptSelect", "Field.PostSelect":
			if sourceColumn.Id == "created_by" {
				build = anyFunc(sourceColumn.Id, gconv.Int64(conditonValue))
			} else {
				build = anyFunc(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"id": ', ?, '}'), '$')`, sourceColumn.Id), gconv.Int64(conditonValue))
			}
		default:
			switch condition.Operator {
			case enum.Operator_Equal:
				build = anyFunc(sourceColumn.Id, conditonValue)
			default:
				build = anyFunc(sourceColumn.Id+" "+string(condition.Operator)+" ?", conditonValue)
			}
		}
	} else {
		build = anyFunc(condition.SourceColumn, conditonValue)
	}
	return build
}

func init() {
	manager.RegisterNode("data_findone", func() types.INode {
		return &DataFindone{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "查询单条数据",
					Description: "根据查询条件，查询单条数据",
					Group:       "data",
					Type:        "data_findone",
					Width:       750,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{
							Id:            "target_table",
							ComponentName: "",
							Label:         "目标表单",
							ColumnType:    "FormTable",
							Required:      true,
							Setter: &types.MetaSetter{
								Name: "FormTableSetter",
							},
						},
						{
							Id:            "query_condition",
							ComponentName: "",
							Label:         "查询条件",
							ColumnType:    "QueryCondition",
							Required:      false,
							Setter: &types.MetaSetter{
								Name: "QueryConditionSetter",
							},
						},
						{
							Id:            "sort_condition",
							ComponentName: "",
							Label:         "排序参数",
							ColumnType:    "SortCondition",
							Required:      false,
							Setter: &types.MetaSetter{
								Name: "SortConditionSetter",
							},
						},
						{
							Id:            "result_type",
							ComponentName: "",
							Label:         "输出结果类型",
							ColumnType:    "Select",
							Required:      true,
							Setter: &types.MetaSetter{
								Name: "ResultTypeSetter",
							},
						},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
