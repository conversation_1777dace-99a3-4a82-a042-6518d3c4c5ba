package data

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"fmt"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type DataInvalidate struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *DataInvalidate) Execute() {
	n.Log("DataInvalidate Execute")

	targetTable, err := n.GetInputValueFromTableNameSetter("target_table")
	if err != nil {
		n.StopError("获取保存数据的表名失败")
		return
	}

	columnMaps, err := n.GetInputValueFromColumnMapSetter("column_map")
	if err != nil {
		n.StopError("获取字段对应关系失败")
		return
	}

	uniqueID, err := n.GetInputValueFromUniqueIDSetter("unique_id")
	if err != nil {
		n.StopError("获取唯一标识字段失败")
		return
	}

	g.Log().Info(n.Ctx, library.SDump("target_table", targetTable))
	g.Log().Info(n.Ctx, library.SDump("column_map", columnMaps))

	// 获取表单模板
	formTemplate, err := service.FormTemplate().GetFormTemplateByTableName(n.Ctx, targetTable)
	if err != nil || formTemplate == nil {
		n.StopError(fmt.Sprintf("获取表单模板失败:%s", err.Error()))
		return
	}

	splitDatas, err := n.SplitInputData(columnMaps)
	if err != nil {
		n.StopError(err.Error())
		return
	}
	var outputDatas []map[string]interface{}

	for _, splitData := range splitDatas {
		if err := n.invalidateFormData(formTemplate.Id, splitData, &outputDatas, uniqueID); err != nil {
			n.StopError(err.Error())
			break
		}
	}

	// 设置输出
	n.Outputs[n.Meta.Id] = outputDatas

}

// 保存表单数据
func (n *DataInvalidate) invalidateFormData(formTemplateId int64, formData map[string]interface{}, outputDatas *[]map[string]interface{}, uniqueID []string) error {

	var ids []int64
	if len(uniqueID) > 0 {
		filter := make(map[string]interface{})
		for _, id := range uniqueID {
			if _, exists := formData[id]; !exists {
				continue
			}
			filter[id] = formData[id]
		}
		if len(filter) > 0 {
			templateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(n.Ctx, formTemplateId)
			if err != nil {
				return fmt.Errorf("获取表单模板失败: %s", err.Error())
			}
			list, err := service.FormData().GetListByRules(n.Ctx, 0, templateInfo.FormTableName, formTemplateId, formColumns, filter)
			if err != nil {
				return fmt.Errorf("根据唯一字段获取表单数据失败: %s", err.Error())
			}
			if len(list) > 0 {
				ids = lo.Map(list, func(s map[string]interface{}, _ int) int64 {
					return gconv.Int64(s["id"])
				})
			}
		}
	}

	if len(ids) > 0 {
		for _, id := range ids {
			err := service.FormData().Invalidate(n.Ctx, &dto.InstanceExtraData{}, formTemplateId, id)
			if err != nil {
				// return fmt.Errorf("作废数据失败: %s", err.Error())
				n.LogError(fmt.Sprintf("作废数据失败: %s , id: %d", err.Error(), id))
			}
		}
	}
	return nil
}

func init() {
	manager.RegisterNode("data_invalidate", func() types.INode {
		return &DataInvalidate{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "作废数据",
					Description: "根据指定的字段对应关系，以及唯一标识字段，将节点引用的数据或者输入数据作废",
					Group:       "data",
					Type:        "data_invalidate",
					Width:       600,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{Id: "target_table", ComponentName: "", Label: "目标表单", ColumnType: "FormTable", Required: false, Setter: &types.MetaSetter{
							Name: "FormTableSetter",
						}},
						{Id: "unique_id", ComponentName: "", Label: "唯一标识字段", ColumnType: "FormTable", Required: false, Setter: &types.MetaSetter{
							Name: "UniqueIDSetter",
						}},
						{Id: "column_map", ComponentName: "", Label: "字段对应关系", ColumnType: "ColumnMap", Required: false, Setter: &types.MetaSetter{
							Name: "ColumnMapSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
