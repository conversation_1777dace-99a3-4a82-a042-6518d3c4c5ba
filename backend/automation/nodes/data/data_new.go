package data

import (
	"backend/api/v1/form"
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/model/dto"
	"backend/internal/service"
	"fmt"

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type DataNew struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *DataNew) Execute() {
	n.Log("DataNew Execute")

	targetTable, err := n.GetInputValueFromTableNameSetter("target_table")
	if err != nil {
		n.StopError("获取新增数据的表名失败")
		return
	}

	columnMaps, err := n.GetInputValueFromColumnMapSetter("column_map")
	if err != nil {
		n.StopError("获取字段对应关系失败")
		return
	}

	// 获取表单模板
	formTemplate, err := service.FormTemplate().GetFormTemplateByTableName(n.Ctx, targetTable)
	if err != nil || formTemplate == nil {
		n.StopError(fmt.Sprintf("获取表单模板失败:%s", err.Error()))
		return
	}

	splitDatas, err := n.SplitInputData(columnMaps)
	if err != nil {
		n.StopError(err.Error())
		return
	}

	var outputDatas []map[string]interface{}
	for _, splitData := range splitDatas {
		if err := n.saveFormDataAndOutput(formTemplate.Id, splitData, &outputDatas); err != nil {
			n.StopError(err.Error())
			break
		}
	}
	// 设置输出
	n.Outputs[n.Meta.Id] = outputDatas

}

// 保存表单数据
func (n *DataNew) saveFormDataAndOutput(formTemplateId int64, inputData map[string]interface{}, outputDatas *[]map[string]interface{}) (err error) {

	input := &form.FormDataSaveReq{
		FormId: formTemplateId,
		Data:   inputData,
	}

	// 添加自动化来源标记，用于防止循环触发
	automationId, hasAutomationId := n.Inputs["automation_id"]
	if hasAutomationId && automationId != nil {
		input.ExtraData = &dto.InstanceExtraData{
			TriggerType:     "automation",
			TriggerSourceId: gconv.String(automationId),
		}
	}

	var newFormData *form.FormDataSaveRes
	// 判断inputData 中是否包含字段 created_by 并且大于零
	createdBy, hasCreatedBy := inputData["created_by"].(int64)
	if hasCreatedBy && createdBy > 0 {
		newFormData, err = service.FormData().Save(n.Ctx, input, nil, createdBy)
	} else {
		newFormData, err = service.FormData().Save(n.Ctx, input, nil)
	}
	if err != nil {
		return fmt.Errorf("新增数据失败: %s", err.Error())
	}
	*outputDatas = append(*outputDatas, newFormData.Info)
	return
}

func init() {
	manager.RegisterNode("data_new", func() types.INode {
		return &DataNew{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "新增数据",
					Description: "将节点引用的数据或者输入数据添加到新表",
					Group:       "data",
					Type:        "data_new",
					Width:       600,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{Id: "target_table", ComponentName: "", Label: "目标表单", ColumnType: "FormTable", Required: false, Setter: &types.MetaSetter{
							Name: "FormTableSetter",
						}},
						{Id: "column_map", ComponentName: "", Label: "字段对应关系", ColumnType: "ColumnMap", Required: false, Setter: &types.MetaSetter{
							Name: "ColumnMapSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
