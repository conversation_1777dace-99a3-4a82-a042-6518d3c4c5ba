package data

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"

	"github.com/gogf/gf/v2/os/gctx"
)

type DataFactory struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *DataFactory) Execute() {
	n.Log("DataFactory Execute")

	dataMap, err := n.GetInputValueFromDataMapSetter("data_definition")
	if err != nil {
		n.StopError("获取数据定义失败")
		return
	}

	// 设置输出
	n.Outputs[n.Meta.Id] = dataMap

}

func init() {
	manager.RegisterNode("data_factory", func() types.INode {
		return &DataFactory{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "数据工厂",
					Description: "接收输入数据，处理后输出给后面的节点",
					Group:       "data",
					Type:        "data_factory",
					Width:       700,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{Id: "data_definition", ComponentName: "", Label: "数据定义", ColumnType: "ColumnMap", Required: false, Setter: &types.MetaSetter{
							Name: "DataMapSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
