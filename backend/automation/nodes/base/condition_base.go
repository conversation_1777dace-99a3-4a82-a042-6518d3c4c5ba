package base

import (
	"backend/automation/manager"
	"backend/automation/types"
	"backend/internal/model/enum"
	"backend/library"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

type ConditionBase struct {
	NodeBase
}

// 节点的执行逻辑
func (n *ConditionBase) Execute() {
	// 执行条件判断逻辑
	result := n.evaluateCondition()
	n.Outputs["success"] = result
	n.Outputs["fail"] = !result
	n.Log(fmt.Sprintf("ConditionBase Execute %v", result))
}

// IsConditionMet 检查条件是否满足
func (n *ConditionBase) IsConditionMet(condition string) bool {
	if condition == "success" {
		if result, ok := n.Outputs["success"].(bool); ok {
			return result
		}
	}
	if condition == "fail" {
		if result, ok := n.Outputs["fail"].(bool); ok {
			return result
		}
	}
	return true
}

// evaluateCondition 评估条件
func (n *ConditionBase) evaluateCondition() bool {
	conditions, err := n.GetInputValueFromConditionSetter("condition")
	if err != nil {
		n.StopError(fmt.Sprintf("获取条件失败: %v", err))
	}
	singleEvaluateCondition_ := func(ctx context.Context, condition interface{}) (result bool, err error) {
		var condition_ types.SettleCondition
		if err := library.ToStruct(condition, &condition_); err != nil {
			return false, err
		}
		return n.singleEvaluateCondition(ctx, &condition_)
	}
	result, err := conditions.EvaluateFlowConditionDetail(n.Ctx, singleEvaluateCondition_)
	if err != nil {
		n.StopError(fmt.Sprintf("评估条件失败: %v", err))
	}
	return result

}

func (n *ConditionBase) singleEvaluateCondition(ctx context.Context, condition *types.SettleCondition) (result bool, err error) {
	var rightValue interface{}
	var leftValue interface{}
	leftValue, _ = n.GetInputRefWrapperVaariableValue(*condition.LeftParam, true)

	if condition.ValueType != "variable" {
		rightValue = condition.RightParam
		// 特殊处理值为空的情况（当条件为等于时，如果右值为空，则判断左值是否也为空）
		if g.IsEmpty(rightValue) && condition.Operator == enum.Operator_Equal {
			result = g.IsEmpty(leftValue)
			return
		}
		// 特殊处理值为空的情况（当条件为不等于时，如果右值为空，则判断左值是否不为空）
		if g.IsEmpty(rightValue) && condition.Operator == enum.Operator_NotEqual {
			result = !g.IsEmpty(leftValue)
			return
		}
	} else {
		var refWrapperVaariable types.RefWrapperVaariable
		if err := library.ToStruct(condition.RightParam, &refWrapperVaariable); err != nil {
			return false, err
		}
		rightValue, _ = n.GetInputRefWrapperVaariableValue(refWrapperVaariable, true)
	}
	result = library.CompareValues(condition.Operator, leftValue, rightValue)

	return
}

func init() {
	manager.RegisterNode("condition_base", func() types.INode {
		return &ConditionBase{
			NodeBase: NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "条件设置器",
					Description: "从选择的表单中，增加字段数据判断，输出判断结果，根据判断结果选择分支节点进行下一步操作",
					Group:       "condition",
					Type:        "condition_base",
					Width:       700,
					Height:      342,
					InputTypes: []*types.MetaColumns{
						{Id: "condition", ComponentName: "", Label: "条件", ColumnType: "Array<Object>", Required: true, Setter: &types.MetaSetter{
							Name: "ConditionSetter",
						}},
					},
					OutputTypes: []*types.MetaColumns{
						{Id: "success", ComponentName: "", Label: "成功", ColumnType: "Bool", Required: true, Children: make([]*types.MetaColumns, 0), IsPoint: true},
						{Id: "fail", ComponentName: "", Label: "失败", ColumnType: "Bool", Required: true, Children: make([]*types.MetaColumns, 0), IsPoint: true},
					},
				},
			},
		}
	})
}
