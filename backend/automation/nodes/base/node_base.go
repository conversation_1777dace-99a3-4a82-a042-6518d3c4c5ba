package base

import (
	"backend/automation/types"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"backend/internal/model/dto"
	"backend/internal/service"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type NodeBase struct {
	Inputs  map[string]interface{}
	Outputs map[string]interface{}
	Params  map[string]interface{}
	Logs    []types.Log
	Meta    types.Meta
	Ctx     context.Context
}

func (n *NodeBase) SetCtx(ctx context.Context) {
	n.Ctx = ctx
}

func (n *NodeBase) GetMeta() types.Meta {
	return n.Meta
}

func (n *NodeBase) SetMeta(meta *types.Meta) {
	n.Meta = *meta
}

func (n *NodeBase) SetInput(key string, value interface{}) {
	n.Inputs[key] = value
}

func (n *NodeBase) SetInputs(inputs map[string]interface{}) {
	n.Inputs = inputs
}

func (n *NodeBase) GetOutputs() map[string]interface{} {
	return n.Outputs
}

func (n *NodeBase) SetOutputs(outputs map[string]interface{}) {
	n.Outputs = outputs
}

func (n *NodeBase) Log(msg string) {
	label := n.Meta.Label
	n.Logs = append(n.Logs, types.Log{Type: "info", Time: time.Now().Format("2006-01-02 15:04:05.000"), Tag: label, Msg: msg})
	g.Log().Info(n.Ctx, msg)
}
func (n *NodeBase) LogError(msg string) {
	label := n.Meta.Label
	n.Logs = append(n.Logs, types.Log{Type: "error", Time: time.Now().Format("2006-01-02 15:04:05.000"), Tag: label, Msg: msg})
	g.Log().Error(n.Ctx, msg)
}
func (n *NodeBase) StopError(msg string) {
	label := n.Meta.Label
	n.Logs = append(n.Logs, types.Log{Type: "error", Time: time.Now().Format("2006-01-02 15:04:05.000"), Tag: label, Msg: msg})
	g.Log().Error(n.Ctx, msg)
	library.Fail(n.Ctx, msg)
}
func (n *NodeBase) GetLogs() []types.Log {
	return n.Logs
}

func (n *NodeBase) GetInputRefWrapperValue(refWrapper *types.RefWrapper, getCurrent bool) (result interface{}) {
	var currentRecord interface{}
	if refWrapper.ValueType == "fixed" {
		result = refWrapper.FixedValue
	} else {
		variable_value := refWrapper.VariableValue
		if variable_value == nil {
			return nil
		}
		// 从inputs中获取变量的值
		result, currentRecord = n.GetInputRefWrapperVaariableValue(*variable_value, true)
	}

	if refWrapper.FuncCode != nil {
		result = n.GetInputRefWrapperFuncCodeValue(refWrapper.FuncCode, result, currentRecord, -1)
	}
	return result
}
func (n *NodeBase) GetInputRefWrapperFuncCodeValue(funcCode interface{}, result interface{}, currentRecord interface{}, recordIndex ...int) interface{} {
	// 执行脚本
	var funcCodeScript dto.ScriptCode
	err := library.ToStruct(funcCode, &funcCodeScript)
	if err != nil {
		g.Log().Error(n.Ctx, fmt.Sprintf("脚本格式错误:%s", err.Error()))
		return nil
	}
	if funcCodeScript.CodeContent == "" {
		return result
	}

	vm, err := service.Javascript().GetVM(nil)
	if err != nil {
		g.Log().Error(n.Ctx, fmt.Sprintf("获取vm失败:%s", err.Error()))
		return nil
	}
	vm.Set("__current", result)
	vm.Set("__currentRecord", currentRecord)
	if len(recordIndex) > 0 {
		vm.Set("__recordIndex", recordIndex[0])
	}
	runResult, err := service.Javascript().Run(n.Ctx, funcCodeScript, 10*time.Second, vm)
	if err != nil {
		g.Log().Error(n.Ctx, fmt.Sprintf("脚本执行失败:%s", err.Error()))
		n.StopError(fmt.Sprintf("脚本执行失败:%s", err.Error()))
		return nil
	}
	return runResult.Export()
}
func (n *NodeBase) GetInputRefWrapperVaariableValue(ref types.RefWrapperVaariable, getCurrent bool) (result interface{}, nodeData interface{}) {
	if g.IsEmpty(ref) || ref.Id == "" || ref.ColumnId == "" {
		g.Log().Error(n.Ctx, fmt.Sprintf("配置参数异常:%s", ref.Id))
		return nil, nil
	}
	nodeData, exists := n.Inputs[ref.Id]
	if !exists {
		g.Log().Error(n.Ctx, fmt.Sprintf("获取节点输入数据失败:%s", ref.Id))
		return nil, nil
	}
	nodeDataMap, ok := nodeData.(map[string]interface{})
	if !ok {
		g.Log().Error(n.Ctx, fmt.Sprintf("节点输入数据格式错误:%s", ref.Id))
		return nil, nil
	}
	ref.ColumnId = lo.Ternary(g.IsEmpty(ref.ColumnPId), ref.ColumnId, fmt.Sprintf("%s.%s", ref.ColumnPId, ref.ColumnId))
	columnid := lo.Ternary(getCurrent, ref.ColumnId, ref.ColumnPId)
	columnData, exists := nodeDataMap[columnid]
	if !exists {
		g.Log().Error(n.Ctx, fmt.Sprintf("获取节点输入数据失败:%s", columnid))
		return nil, nil
	}

	gvarValue, ok := columnData.(*gvar.Var)
	if !ok {
		// g.Log().Error(n.Ctx, fmt.Sprintf("节点输入数据格式错误:%s", columnid))
		// return nil, nil
		return columnData, nodeData
	}
	// gvarValue2, ok2 := columnData.(gvar.Var)
	// if !ok2 {
	// 	g.Log().Error(n.Ctx, fmt.Sprintf("节点输入数据格式错误:%s", columnid))
	// 	return nil
	// }
	// g.Log().Info(n.Ctx, fmt.Sprintf("获取节点输入数据成功:%s", gvarValue2))
	return gvarValue.Val(), nodeData
}

// 获取字段映射类型的输入参数
func (n *NodeBase) GetInputValueFromColumnMapSetter(field_name string) ([]types.ColumnMapSetter, error) {
	settle, exists := n.Meta.InputSettles[field_name]
	if !exists {
		return nil, fmt.Errorf("column map settle not found")
	}

	var columnMaps []types.ColumnMapSetter
	for _, iface := range settle.([]interface{}) {
		var columnMap types.ColumnMapSetter
		if err := library.ToStruct(iface, &columnMap); err != nil {
			return nil, err
		}
		columnMaps = append(columnMaps, columnMap)
	}
	return columnMaps, nil
}

func (n *NodeBase) GetInputValueFromConditionSetter(field_name string) (flowCondition *dto.SettleFlowConditionDetail, err error) {
	settle, exists := n.Meta.InputSettles[field_name]
	if !exists {
		return nil, fmt.Errorf("query condition settle not found")
	}
	var flowCondition_ dto.SettleFlowConditionDetail
	if err := library.ToStruct(settle, &flowCondition_); err != nil {
		return nil, err
	}
	return &flowCondition_, nil
}

// 获取查询条件类型的输入参数
func (n *NodeBase) GetInputValueFromQueryConditionSetter(field_name string) ([]*types.QueryConditionSetter, error) {
	settle, exists := n.Meta.InputSettles[field_name]
	if !exists {
		return nil, fmt.Errorf("query condition settle not found")
	}

	var queryConditions []*types.QueryConditionSetter
	for _, iface := range settle.([]interface{}) {
		var queryCondition types.QueryConditionSetter
		if err := library.ToStruct(iface, &queryCondition); err != nil {
			return nil, err
		}
		queryConditions = append(queryConditions, &queryCondition)
	}
	return queryConditions, nil
}
func (n *NodeBase) GetInputValueFromFieldListSetter(field_name string) ([]types.FieldVar, error) {
	settle, exists := n.Meta.InputSettles[field_name]
	if !exists {
		return nil, fmt.Errorf("unique id settle not found")
	}
	var fieldVars []types.FieldVar
	library.ToAny(settle, &fieldVars)
	return fieldVars, nil
}

func (n *NodeBase) GetInputValueFromInputJavasciprtCodeSetter(field_name string) (*dto.ScriptCode, error) {
	settle, exists := n.Meta.InputSettles[field_name]
	if !exists {
		return nil, fmt.Errorf("unique id settle not found")
	}
	var funcCode dto.ScriptCode
	if err := library.ToStruct(settle, &funcCode); err != nil {
		return nil, err
	}
	return &funcCode, nil
}

func (n *NodeBase) GetInputValueFromUniqueIDSetter(field_name string) ([]string, error) {
	settle, exists := n.Meta.InputSettles[field_name]
	if !exists {
		return nil, fmt.Errorf("unique id settle not found")
	}

	uniqueIds := gconv.Strings(settle)
	return uniqueIds, nil
}

func (n *NodeBase) GetInputValueFromTableNameSetter(key string) (string, error) {
	settle, exists := n.Meta.InputSettles[key]
	if !exists {
		return "", fmt.Errorf("settle not found for key: %s", key)
	}
	return gconv.String(settle), nil
}

func (n *NodeBase) GetInputValueFromInputTextSetter(key string) (string, error) {
	settle, exists := n.Meta.InputSettles[key]
	if !exists {
		return "", fmt.Errorf("settle not found for key: %s", key)
	}
	return gconv.String(settle), nil
}

func (n *NodeBase) GetInputValueFromSelectSetter(key string) (string, error) {
	settle, exists := n.Meta.InputSettles[key]
	if !exists {
		return "", fmt.Errorf("settle not found for key: %s", key)
	}
	return gconv.String(settle), nil
}

func (n *NodeBase) GetInputValueFromRefInputNumberSetter(key string) (interface{}, error) {
	iface, exists := n.Meta.InputSettles[key]
	if !exists {
		return nil, fmt.Errorf("settle not found for key: %s", key)
	}
	var ref types.RefWrapper
	err := library.ToStruct(iface, &ref)
	if err != nil {
		return nil, err
	}

	return n.GetInputRefWrapperValue(&ref, true), nil
}

func (n *NodeBase) GetInputValueFromAccumulationKeySetter(key string) ([]interface{}, error) {
	ifaces, exists := n.Meta.InputSettles[key]
	if !exists {
		return nil, fmt.Errorf("settle not found for key: %s", key)
	}
	var refs []*types.RefWrapper
	for _, iface := range ifaces.([]interface{}) {
		var item types.RefWrapper
		if err := library.ToStruct(iface, &item); err != nil {
			return nil, err
		}
		refs = append(refs, &item)
	}
	result := make([]interface{}, 0)
	for _, accumulationKey := range refs {
		refValue := n.GetInputRefWrapperValue(accumulationKey, true)
		if g.IsEmpty(refValue) {
			continue
		}
		var mapRefValu map[string]interface{}
		mapUnErr := json.Unmarshal(gconv.Bytes(refValue), &mapRefValu)
		if mapUnErr == nil {
			jsonValue, isExists := mapRefValu["value"]
			if isExists {
				result = append(result, jsonValue)
				continue
			}
		}
		result = append(result, n.GetInputRefWrapperValue(accumulationKey, true))

	}

	return result, nil
}

func (n *NodeBase) GetInputValueFromDataMapSetter(key string) (map[string]interface{}, error) {
	dataMap, exists := n.Meta.InputSettles[key]
	if !exists {
		return nil, fmt.Errorf("settle not found for key: %s", key)
	}
	var fields []*types.FieldVar
	for _, iface := range dataMap.([]interface{}) {
		var item types.FieldVar
		if err := library.ToStruct(iface, &item); err != nil {
			return nil, err
		}
		fields = append(fields, &item)
	}
	result := make(map[string]interface{}, 0)
	for _, field := range fields {
		result[field.Id] = n.GetInputRefWrapperValue(field.TargetColumn, true)
	}
	return result, nil
}

func (n *NodeBase) SplitInputData(columnMaps []types.ColumnMapSetter) (result []map[string]interface{}, err error) {

	inputData := make(map[string]interface{})
	for _, columnMap := range columnMaps {
		if !g.IsEmpty(columnMap.SourceColumn) && columnMap.TargetColumn != nil {
			inputData[columnMap.SourceColumn] = n.GetInputRefWrapperValue(columnMap.TargetColumn, true)
		}
	}
	parentColumn := make(map[string][]types.ColumnMapSetter)

	for _, columnMap := range columnMaps {
		if columnMap.TargetColumn != nil && columnMap.TargetColumn.VariableValue != nil && columnMap.TargetColumn.VariableValue.ColumnPId != "" {
			if _, has := parentColumn[columnMap.TargetColumn.VariableValue.ColumnPId]; !has {
				parentColumn[columnMap.TargetColumn.VariableValue.ColumnPId] = make([]types.ColumnMapSetter, 0)
			}
			parentColumn[columnMap.TargetColumn.VariableValue.ColumnPId] = append(parentColumn[columnMap.TargetColumn.VariableValue.ColumnPId], columnMap)
		}
	}

	if len(parentColumn) > 0 {
		for _, childColumnMaps := range parentColumn {
			if len(childColumnMaps) == 0 {
				continue
			}

			parentColumnValue, currentRecord := n.GetInputRefWrapperVaariableValue(*childColumnMaps[0].TargetColumn.VariableValue, false)
			if g.IsEmpty(parentColumnValue) {
				continue
			}
			var tableValues []map[string]interface{}
			if err := json.Unmarshal([]byte(gconv.String(parentColumnValue)), &tableValues); err != nil {
				return nil, fmt.Errorf("获取父表数据失败: %s", err.Error())
			}

			for tableRowIndex, tableRow := range tableValues {
				newData := make(map[string]interface{})
				for key, value := range inputData {
					newData[key] = value
				}
				for _, childColumnMap := range childColumnMaps {
					childColumnValue, isExists := tableRow[childColumnMap.TargetColumn.VariableValue.ColumnId]
					if isExists {
						newData[childColumnMap.SourceColumn] = childColumnValue
					}
					if childColumnMap.TargetColumn.FuncCode != nil {
						newData[childColumnMap.SourceColumn] = n.GetInputRefWrapperFuncCodeValue(childColumnMap.TargetColumn.FuncCode, newData[childColumnMap.SourceColumn], currentRecord, tableRowIndex)
					}
				}
				result = append(result, newData)
			}
		}
	}

	if len(result) <= 0 {
		result = append(result, inputData)
	}
	return result, nil
}

// GetInputValueFromSortConditionSetter 从节点配置中获取排序条件，返回值为字符串切片，每个元素格式为 "字段 排序方式"
func (n *NodeBase) GetInputValueFromSortConditionSetter(key string) ([]string, error) {
	settle, exists := n.Meta.InputSettles[key]
	if !exists {
		return nil, fmt.Errorf("sort condition settle not found for key: %s", key)
	}

	var conditions []interface{}
	// 如果 settle 为切片，则直接赋值，否则封装成切片处理
	if condSlice, ok := settle.([]interface{}); ok {
		conditions = condSlice
	} else {
		conditions = []interface{}{settle}
	}

	var result []string
	for _, cond := range conditions {
		condMap := gconv.Map(cond)
		field := gconv.String(condMap["field"])
		order := gconv.String(condMap["order"])
		if field == "" {
			return nil, fmt.Errorf("排序参数错误: 排序字段为空")
		}
		if order == "" {
			return nil, fmt.Errorf("排序参数错误: 排序方式为空")
		}
		orderLower := strings.ToLower(order)
		if orderLower != "asc" && orderLower != "desc" {
			return nil, fmt.Errorf("排序参数错误: 排序方式应为 asc 或 desc")
		}
		result = append(result, fmt.Sprintf("%s %s", field, orderLower))
	}
	return result, nil
}
