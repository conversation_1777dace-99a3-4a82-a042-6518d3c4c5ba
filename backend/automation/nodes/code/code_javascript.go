package code

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"backend/internal/service"
	"time"

	"github.com/gogf/gf/v2/os/gctx"
)

type CodeJavascript struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *CodeJavascript) Execute() {
	n.Log("CodeJavascript Execute")

	params, err := n.GetInputValueFromFieldListSetter("params")
	if err != nil {
		n.StopError("获取字段设置失败")
		return
	}

	code, err := n.GetInputValueFromInputJavasciprtCodeSetter("code")
	if err != nil {
		n.StopError("获取代码内容失败")
		return
	}

	inputData := make(map[string]interface{})

	for _, param := range params {
		if param.TargetColumn.ValueType == "fixed" {
			inputData[param.Id] = param.TargetColumn.FixedValue
		} else {
			inputData[param.Id] = n.GetInputRefWrapperValue(param.TargetColumn, true)
		}
	}

	vm, err := service.Javascript().GetVM(map[string]interface{}{
		"__current": inputData,
	})
	if err != nil {
		n.StopError("创建Javascript运行时失败")
		return
	}

	result, err := service.Javascript().Run(n.Ctx, *code, 5*time.Second, vm)
	if err != nil {
		n.StopError("执行代码失败")
		return
	}

	n.Outputs[n.Meta.Id] = result
}

func init() {
	manager.RegisterNode("code_javascript", func() types.INode {
		return &CodeJavascript{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "自定义代码-Javascript(开发中)",
					Description: "使用自定义代码处理输入,获得输出结果",
					Group:       "code",
					Type:        "javascript",
					Width:       750,
					Height:      214,
					InputTypes: []*types.MetaColumns{
						{Id: "params", ComponentName: "FieldList", Label: "参数列表", ColumnType: "Map", Required: true, Setter: &types.MetaSetter{
							Name: "RefFieldListSetter",
						}},
						{Id: "code", ComponentName: "InputJavasciprtCode", Label: "代码内容", ColumnType: "Object", Required: true, Setter: &types.MetaSetter{
							Name: "InputJavasciprtCodeSetter",
						}},
						{Id: "result", ComponentName: "FieldList", Label: "输出结果", ColumnType: "Map", Required: true, Setter: &types.MetaSetter{
							Name: "FieldListSetter",
						}},
					},
					OutputTypes: make([]*types.MetaColumns, 0),
				},
			},
		}
	})
}
