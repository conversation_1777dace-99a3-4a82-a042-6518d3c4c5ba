package webhook

import (
	"backend/automation/manager"
	"backend/automation/nodes/base"
	"backend/automation/types"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type Webhook struct {
	base.NodeBase
}

// 节点的执行逻辑
func (n *Webhook) Execute() {
	n.Log("Webhook Execute")

	// 获取请求URL
	url, err := n.GetInputValueFromInputTextSetter("url")
	if err != nil {
		n.StopError("获取请求URL失败")
		return
	}

	// 获取请求方法
	method, err := n.GetInputValueFromSelectSetter("method")
	if err != nil {
		n.StopError("获取请求方法失败")
		return
	}

	// 获取请求参数
	params, err := n.GetInputValueFromDataMapSetter("params")
	if err != nil {
		n.StopError("获取请求参数失败")
		return
	}

	// 获取请求头
	headers, err := n.GetInputValueFromDataMapSetter("headers")
	if err != nil {
		n.StopError("获取请求头失败")
		return
	}

	// 发送请求
	response, err := n.sendRequest(method, url, params, headers)
	if err != nil {
		n.StopError(fmt.Sprintf("请求失败: %s", err.Error()))
		return
	}

	// 解析响应数据
	var responseData interface{}
	if err := json.Unmarshal(response, &responseData); err != nil {
		n.StopError(fmt.Sprintf("解析响应数据失败: %s", err.Error()))
		return
	}

	// 设置输出
	n.Outputs[n.Meta.Id] = responseData
}

func (n *Webhook) sendRequest(method, url string, params map[string]interface{}, headers map[string]interface{}) ([]byte, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	var req *http.Request
	var err error

	if method == "GET" {
		// 构建GET请求URL
		if len(params) > 0 {
			queryParams := make([]string, 0)
			for key, value := range params {
				queryParams = append(queryParams, fmt.Sprintf("%s=%v", key, value))
			}
			if strings.Contains(url, "?") {
				url += "&" + strings.Join(queryParams, "&")
			} else {
				url += "?" + strings.Join(queryParams, "&")
			}
		}
		req, err = http.NewRequest("GET", url, nil)
	} else {
		// 构建POST请求体
		jsonData, err := json.Marshal(params)
		if err != nil {
			return nil, err
		}
		req, err = http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
		if err != nil {
			return nil, err
		}
		req.Header.Set("Content-Type", "application/json")
	}

	if err != nil {
		return nil, err
	}

	// 添加请求头
	for key, value := range headers {
		req.Header.Set(key, gconv.String(value))
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("请求失败，状态码: %d, 响应内容: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

func init() {
	manager.RegisterNode("webhook", func() types.INode {
		return &Webhook{
			NodeBase: base.NodeBase{
				Inputs:  make(map[string]interface{}),
				Outputs: make(map[string]interface{}),
				Params:  make(map[string]interface{}),
				Ctx:     gctx.New(),
				Meta: types.Meta{
					Shape:       "AutoNode",
					Label:       "Webhook(开发中)",
					Description: "发送HTTP请求到指定URL，支持GET和POST方法",
					Group:       "webhook",
					Type:        "webhook",
					Width:       800,
					Height:      278,
					InputTypes: []*types.MetaColumns{
						{Id: "url", ComponentName: "InputText", Label: "请求URL", ColumnType: "String", Required: true, Setter: &types.MetaSetter{
							Name: "InputTextSetter",
						}},
						{Id: "method", ComponentName: "InputSelect", Label: "请求方法", ColumnType: "String", Required: true, Setter: &types.MetaSetter{
							Name: "SelectSetter",
							Props: map[string]interface{}{
								"options": []map[string]string{
									{"label": "GET", "value": "GET"},
									{"label": "POST", "value": "POST"},
								},
								"defaultValue": "POST",
							},
						}},
						{Id: "headers", ComponentName: "DataMapSetter", Label: "请求头", ColumnType: "Map", Required: false, Setter: &types.MetaSetter{
							Name: "DataMapSetter",
							Props: map[string]interface{}{
								"autoUpdateRespon": false,
							},
						}},
						{Id: "params", ComponentName: "DataMapSetter", Label: "请求参数", ColumnType: "Map", Required: false, Setter: &types.MetaSetter{
							Name: "DataMapSetter",
							Props: map[string]interface{}{
								"autoUpdateRespon": false,
							},
						}},
						{Id: "result", ComponentName: "FieldList", Label: "Webhook预期结果", ColumnType: "Map", Required: true, Setter: &types.MetaSetter{
							Name: "FieldListSetter",
						}},
					},
					OutputTypes: []*types.MetaColumns{
						{Id: "response", ComponentName: "", Label: "响应数据", ColumnType: "Map", Required: false},
					},
				},
			},
		}
	})
}
