package types

import (
	"backend/internal/model/enum"
)

type ISetter interface {
}

// 字段映射设置器的数据结构
type ColumnMapSetter struct {
	Id           string      `json:"id"`
	Type         int         `json:"type"`
	SourceColumn string      `json:"source_column"`
	TargetColumn *RefWrapper `json:"target_column"`
}

type RefWrapper struct {
	ValueType     string               `json:"valueType"`
	FixedValue    interface{}          `json:"fixedValue"`
	VariableValue *RefWrapperVaariable `json:"variableValue"`
	FuncCode      interface{}          `json:"funcCode"`
}

type RefWrapperVaariable struct {
	Id        string `json:"id"`
	Label     string `json:"label"`
	ColumnId  string `json:"columnId"`
	ColumnPId string `json:"columnPId"`
}

type QueryConditionSetter struct {
	Id           string                  `json:"id"`
	Type         int                     `json:"type"`          // 条件类型 1 单个条件 2 多个条件
	Relation     string                  `json:"relation"`      // 关系 OR 和 AND
	Operator     enum.Operator           `json:"operator"`      // 操作符
	SourceColumn string                  `json:"source_column"` // 源字段
	TargetColumn *RefWrapper             `json:"target_column"` // 目标字段
	SubCondition []*QueryConditionSetter `json:"sub_condition"` // 子条件
}

type FieldVar struct {
	Id            string         `json:"id"`             // 字段Id，唯一标识
	ComponentName string         `json:"component_name"` // 组件名称
	Label         string         `json:"label"`          // 字段标题
	ColumnType    string         `json:"column_type"`    // 字段类型
	Required      bool           `json:"required"`       // 是否必填
	Children      []*MetaColumns `json:"children"`       // 子字段
	DefaultValue  interface{}    `json:"default_value"`  // 默认值
	TargetColumn  *RefWrapper    `json:"target_column"`  // 目标字段
}

type SettleCondition struct {
	ID         string               `json:"id"`
	Operator   enum.Operator        `json:"operator"`
	LeftParam  *RefWrapperVaariable `json:"leftParam"`
	ValueType  string               `json:"valueType"`
	RightParam interface{}          `json:"rightParam"`
}
