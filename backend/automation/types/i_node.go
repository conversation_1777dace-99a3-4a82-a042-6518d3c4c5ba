package types

import "context"

type INode interface {
	Execute()
	SetCtx(ctx context.Context)
	SetInputs(inputs map[string]interface{})
	SetInput(key string, value interface{})
	GetOutputs() map[string]interface{}
	SetOutputs(outputs map[string]interface{})
	GetMeta() Meta
	SetMeta(meta *Meta)
	GetLogs() []Log
}

type INodeTrigger interface {
	Execute()
	SetCtx(ctx context.Context)
	SetInputs(inputs map[string]interface{})
	SetInput(key string, value interface{})
	GetOutputs() map[string]interface{}
	SetOutputs(outputs map[string]interface{})
	GetMeta() Meta
	SetMeta(meta *Meta)
	GetLogs() []Log
	TriggerCreate(meta *Meta)
	TriggerRemove(meta *Meta)
}
