package types

type Meta struct {
	Id           string                 `json:"id"`
	IsDrag       bool                   `json:"isDrag"`
	IsCanvas     bool                   `json:"isCanvas"`
	Selected     bool                   `json:"selected"`
	Width        int                    `json:"width"`        // 默认的节点宽度
	Height       int                    `json:"height"`       // 默认的节点高度
	Group        string                 `json:"group"`        // 节点分组
	Shape        string                 `json:"shape"`        // 节点形状
	Type         string                 `json:"type"`         // 节点类型
	Label        string                 `json:"label"`        // 节点标签
	Description  string                 `json:"description"`  // 节点描述
	OutputTypes  []*MetaColumns         `json:"outputTypes"`  // 输出字段
	InputTypes   []*MetaColumns         `json:"inputTypes"`   // 输入字段
	InputSettles map[string]interface{} `json:"inputSettles"` // 输入设置
	RefInputs    []*Meta                `json:"refInputs"`    // 可应用变量
	Scale        map[string]interface{} `json:"scale"`        // 节点缩放
}

type MetaColumns struct {
	Id            string         `json:"id"`             // 字段Id，唯一标识
	ComponentName string         `json:"component_name"` // 组件名称
	Label         string         `json:"label"`          // 字段标题
	ColumnType    string         `json:"column_type"`    // 字段类型
	Required      bool           `json:"required"`       // 是否必填
	Children      []*MetaColumns `json:"children"`       // 子字段
	DefaultValue  interface{}    `json:"default_value"`  // 默认值
	Setter        *MetaSetter    `json:"setter"`         // 设置器
	IsPoint       bool           `json:"is_point"`       // 是否是一个点位
	TargetColumn  *RefWrapper    `json:"target_column"`  // 目标字段
}

type MetaSetter struct {
	Name  string                 `json:"name"`  // 设置器名称
	Props map[string]interface{} `json:"props"` // 传入设置器的值
}

type Log struct {
	Type string `json:"type"` // 日志类型
	Time string `json:"time"` // 日志时间
	Tag  string `json:"tag"`  // 日志标签
	Msg  string `json:"msg"`  // 日志内容
}
