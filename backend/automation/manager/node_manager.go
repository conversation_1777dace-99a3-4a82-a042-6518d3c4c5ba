package manager

import (
	"backend/automation/types"
	"fmt"
)

var automationNodes = make(map[string]func() types.INode)
var automationNodeTriggers = make(map[string]func() types.INodeTrigger)

// var setters

func RegisterNode(nodeType string, constructor func() types.INode) {
	automationNodes[nodeType] = constructor
}

func CreateNode(nodeType string) (types.INode, error) {
	constructor, exists := automationNodes[nodeType]
	if !exists {
		return nil, fmt.Errorf("node type %s not registered", nodeType)
	}
	return constructor(), nil
}

func RegisterNodeTrigger(nodeType string, constructor func() types.INodeTrigger) {
	automationNodeTriggers[nodeType] = constructor
}

func CreateNodeTrigger(nodeType string) (types.INodeTrigger, error) {
	constructor, exists := automationNodeTriggers[nodeType]
	if !exists {
		return nil, fmt.Errorf("node type %s not registered", nodeType)
	}
	return constructor(), nil
}

func GetNodeMetas() []types.Meta {
	result := make([]types.Meta, 0)
	for _, constructor := range automationNodes {
		node := constructor()
		meta := node.GetMeta()
		result = append(result, meta)
	}
	return result
}
