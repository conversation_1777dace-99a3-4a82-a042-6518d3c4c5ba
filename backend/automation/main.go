package main

import (
	"backend/automation/manager"
	_ "backend/automation/nodes"
	"backend/automation/workflow"
	"encoding/json"
	"fmt"
)

func main() {
	jsonMeta, _ := json.Marshal(manager.GetNodeMetas())
	fmt.Printf("metas: %v\n", string(jsonMeta))

	workflow := workflow.NewWorkflow()

	// 添加节点
	workflow.AddNode("A", "trigger_data", map[string]interface{}{
		"data": "new customer data",
	}, nil)

	workflow.AddNode("B", "calculate_addition", nil, nil)

	workflow.AddNode("D", "outprint", nil, nil)
	workflow.AddNode("E", "outprint", nil, nil)

	// 添加数据流
	workflow.AddDataFlow("A", "data", "B", "data", "")
	workflow.AddDataFlow("B", "data", "D", "data", "")
	workflow.AddDataFlow("B", "data", "E", "data", "")

	// 执行工作流
	workflow.Execute()
}
