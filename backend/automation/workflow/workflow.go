package workflow

import (
	"backend/automation/manager"
	"backend/automation/types"

	"github.com/samber/lo"
)

type NodeStatus struct {
	Executed bool
	Outputs  map[string]interface{}
}

type DataFlow struct {
	FromNodeID string
	FromOutput string
	ToNodeID   string
	ToInput    string
	Condition  string // "success" 或 "fail" 或空字符串
}

type Workflow struct {
	Nodes       map[string]types.INode
	DataFlows   []DataFlow
	NodeStatus  map[string]*NodeStatus
	PreNode     string
	Logs        []types.Log
	Queue       []string // 当前执行队列
	LastNodeID  string   // 最后一次执行的节点ID
	TriggerData map[string]interface{}
}

type WorkflowState struct {
	NodeStatuses map[string]*NodeStatus // 节点执行状态
	PendingQueue []string               // 待执行的节点队列
	TriggerData  map[string]interface{} // 触发数据
}

func NewWorkflow() *Workflow {
	return &Workflow{
		Nodes:      make(map[string]types.INode),
		DataFlows:  make([]DataFlow, 0),
		NodeStatus: make(map[string]*NodeStatus),
		Queue:      make([]string, 0),
	}
}

func (wf *Workflow) SetTriggerData(triggerData map[string]interface{}) {
	wf.TriggerData = triggerData
}

func (wf *Workflow) GetLogs() []types.Log {
	return wf.Logs
}

// 添加节点
func (wf *Workflow) AddNode(id, nodeType string, params map[string]interface{}, meta *types.Meta) error {
	node, err := manager.CreateNode(nodeType)
	if err != nil {
		return err
	}
	if params != nil {
		node.SetInputs(params)
	}
	node.SetMeta(meta)
	wf.Nodes[id] = node
	return nil
}

// AddDataFlow 添加数据流向
func (wf *Workflow) AddDataFlow(fromNodeID, fromOutput, toNodeID, toInput string, condition string) {
	wf.DataFlows = append(wf.DataFlows, DataFlow{
		FromNodeID: fromNodeID,
		FromOutput: fromOutput,
		ToNodeID:   toNodeID,
		ToInput:    toInput,
		Condition:  condition,
	})
}

// Execute 执行工作流
func (wf *Workflow) Execute() {
	// 初始化节点状态
	wf.NodeStatus = make(map[string]*NodeStatus)
	for nodeID := range wf.Nodes {
		wf.NodeStatus[nodeID] = &NodeStatus{
			Executed: false,
			Outputs:  make(map[string]interface{}),
		}
	}

	// 初始化执行队列
	wf.Queue = make([]string, 0)
	wf.LastNodeID = ""

	// 获取第一批可执行的节点
	wf.Queue = append(wf.Queue, wf.findExecutableNodes(wf.Queue)...)

	// 执行节点
	for len(wf.Queue) > 0 {
		nodeID := wf.Queue[0]
		wf.Queue = wf.Queue[1:]

		// 记录当前执行的节点ID
		wf.LastNodeID = nodeID

		// 执行节点
		wf.executeNode(nodeID)
		wf.Queue = append(wf.Queue, wf.findExecutableNodes(wf.Queue)...)
	}
}

// findExecutableNodes 找到所有可执行的节点
func (wf *Workflow) findExecutableNodes(inQueue []string) []string {
	var executableNodes []string
	seen := make(map[string]bool)

	for nodeID := range wf.Nodes {

		// 跳过已执行的节点
		if wf.NodeStatus[nodeID].Executed {
			continue
		}

		// 跳过已经在结果列表中的节点
		if seen[nodeID] {
			continue
		}
		if lo.Contains(inQueue, nodeID) {
			continue
		}
		// 检查是否可以执行
		if wf.canExecuteNode(nodeID) {
			executableNodes = append(executableNodes, nodeID)
			seen[nodeID] = true
		}
	}

	return executableNodes
}

// canExecuteNode 检查节点是否可以执行
func (wf *Workflow) canExecuteNode(nodeID string) bool {
	// 查找所有指向当前节点的数据流
	for _, flow := range wf.DataFlows {
		if flow.ToNodeID == nodeID {
			// 检查前置节点是否已执行
			if !wf.NodeStatus[flow.FromNodeID].Executed {
				return false
			}

			// 如果有条件，检查条件是否满足
			if flow.Condition != "" {
				if node, ok := wf.Nodes[flow.FromNodeID].(types.IConditionNode); ok {

					if !node.IsConditionMet(flow.Condition) {
						return false
					}
				}
			}
		}
	}

	return true
}

// executeNode 执行单个节点
func (wf *Workflow) executeNode(nodeID string) {
	node, exists := wf.Nodes[nodeID]
	if !exists {
		return
	}

	// 设置输入
	wf.setNodeInputs(nodeID)

	// 执行节点
	node.Execute()

	// 记录状态和输出
	wf.NodeStatus[nodeID].Executed = true
	wf.NodeStatus[nodeID].Outputs = node.GetOutputs()

	// 记录日志
	wf.Logs = append(wf.Logs, node.GetLogs()...)
	wf.PreNode = nodeID
}

// setNodeInputs 设置节点的输入
func (wf *Workflow) setNodeInputs(nodeID string) {
	node := wf.Nodes[nodeID]
	for _, flow := range wf.DataFlows {
		if flow.ToNodeID == nodeID {
			fromNodeStatus := wf.NodeStatus[flow.FromNodeID]
			if fromNodeStatus.Executed {
				node.SetInput(flow.ToInput, fromNodeStatus.Outputs[flow.FromOutput])
			}
		}
	}
}

// SaveState 保存当前工作流状态
func (wf *Workflow) SaveState() *WorkflowState {
	// 如果有最后执行的节点，且执行失败（未标记为已执行），将其加入待执行队列
	pendingQueue := make([]string, len(wf.Queue))
	copy(pendingQueue, wf.Queue)

	if wf.LastNodeID != "" {
		if status := wf.NodeStatus[wf.LastNodeID]; status == nil || !status.Executed {
			// 将失败节点放在队列最前面
			pendingQueue = append([]string{wf.LastNodeID}, pendingQueue...)
		}
	}

	return &WorkflowState{
		NodeStatuses: wf.NodeStatus,
		PendingQueue: pendingQueue,
		TriggerData:  wf.TriggerData,
	}
}

// RecoverExecution 从保存的状态恢复执行工作流
func (wf *Workflow) RecoverExecution(state *WorkflowState) {
	// 恢复节点状态
	wf.NodeStatus = state.NodeStatuses

	// 注入每个节点的Outputs
	for nodeID, status := range wf.NodeStatus {
		node, isExists := wf.Nodes[nodeID]
		if !isExists {
			continue
		}
		node.SetOutputs(status.Outputs)
	}

	// 恢复执行队列
	wf.Queue = make([]string, 0)
	for _, nodeID := range state.PendingQueue {
		if wf.canExecuteNode(nodeID) {
			wf.Queue = append(wf.Queue, nodeID)
		}
	}
	// wf.Queue = make([]string, len(state.PendingQueue))
	// copy(wf.Queue, state.PendingQueue)
	wf.LastNodeID = ""

	// 执行节点
	for len(wf.Queue) > 0 {
		nodeID := wf.Queue[0]
		wf.Queue = wf.Queue[1:]

		wf.LastNodeID = nodeID
		wf.executeNode(nodeID)

		// 获取新的可执行节点
		wf.Queue = append(wf.Queue, wf.findExecutableNodes(wf.Queue)...)
	}
}
