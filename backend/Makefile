ROOT_DIR    = $(shell pwd)
NAMESPACE   = "default"
DEPLOY_NAME = "template-single"
DOCKER_NAME = "template-single"

include ./hack/hack.mk
include ./hack/db-migrate.mk
include ./hack/db-sync.mk
include ./hack/build.mk

###############################
# 帮助命令
###############################
.PHONY: help
help:
	@echo "CRM 项目管理命令集"
	@echo
	@echo "构建命令:"
	@echo "  make build-all        构建前端和后端"
	@echo "  make build-frontend   仅构建前端"
	@echo "  make build-backend    仅构建后端"
	@echo "  make build-lowcode    构建低代码编辑器"
	@echo "  make build-full       完整构建（前端+低代码+后端）"
	@echo
	@echo "数据库迁移相关命令:"
	@echo -e "  \033[33m[前置要求]\033[0m需要mysql-client、mysqldump、redis-cli、atlas"
	@echo -e "  \033[33m[mysql安装命令]\033[0msudo apt-get install mysql-client-core-8.0  # 包含mysql和mysqldump"
	@echo -e "  \033[33m[redis安装命令]\033[0msudo apt-get install redis-tools  # 包含redis-cli"
	@echo -e "  \033[33m[atlas安装命令]\033[0mcurl -sSf https://atlasgo.sh | sh"
	@echo -e "  \033[33m[atlas项目地址]\033[0mhttps://github.com/ariga/atlas"
	@echo "  make db-diff desc=\"描述\"   生成数据库差异脚本"
	@echo "  make db-migrate v=版本号   执行指定版本迁移"
	@echo "  make db-diff-list          列出所有迁移版本"
	@echo "  make db-rm-diff v=版本号   删除指定迁移版本"
	@echo "  make restart-prod          重启生产服务"
	@echo "  make redis-flush           清除Redis缓存数据库"
	@echo
	@echo "数据库同步命令:"
	@echo -e "  \033[33m[前置要求]\033[0m需要mysql-client、mysqldump"
	@echo -e "  \033[33m[安装命令]\033[0msudo apt-get install mysql-client-core-8.0  # 包含mysql和mysqldump"
	@echo "  make backup-from      备份源数据库（配置见db-sync.mk）"
	@echo "  make restore-to       恢复备份到目标数据库"
	@echo "  make sync-from-to     直接同步源到目标数据库（危险操作）"
	@echo "  make sync-clean       清理同步备份文件"
	@echo
	@echo "部署命令:"
	@echo "  make publish-pre      发布到预发布环境"
	@echo "  make publish          发布到生产环境"
	@echo "  make publish-jmt      发布到嘉明特环境"
	@echo
	@echo "其他命令:"
	@echo "  make help             显示本帮助信息"
	@echo
	@echo "使用示例:"
	@echo "  生成迁移脚本: make db-diff desc=\"添加用户表\""
	@echo "  执行版本迁移: make db-migrate v=v1"
	@echo "  完整发布流程: make build-full && make publish"
