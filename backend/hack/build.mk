# 构建前端和后端
.PHONY: build-all
build-all: build-frontend build-backend

# 构建嘉明特的前后端
.PHONY: build-all-jmt
build-all-jmt: build-frontend-jmt build-backend

# 构建前端、后端、LowCode编辑器
.PHONY: build-full
build-full: build-frontend build-lowcode build-backend


# 构建前端
.PHONY: build-frontend
build-frontend:
	cd ../frontend && npm run build
	rm -rf resource/frontend/*
	cp -r ../frontend/dist/* resource/frontend/

# 构建前端-嘉明特
.PHONY: build-frontend-jmt
build-frontend-jmt:
	cd ../frontend && npm run build:jmt
	rm -rf resource/frontend/*
	cp -r ../frontend/dist/* resource/frontend/

# 构建后端
.PHONY: build-backend
build-backend:
	gf build -y

# 构建LowCode编辑器
.PHONY: build-lowcode
build-lowcode:
	cd ../packages/lu-lowcode-materials && \
	npm run clear && \
	npm run build && \
	npm run lowcode:build && \
	cd $(ROOT_DIR) && \
	rm -rf resource/lowcode/* && \
	cp -r ../packages/lu-lowcode-materials/build/lowcode/* ./resource/lowcode/



PRE_SSH := ssh
PRE_SCP := scp
PRE_HOST := shitian@**************

# 发布到pre 
# 使用 ssh-keygen -t rsa -b 4096 -C "<EMAIL>" 生成密钥，然后 ssh-copy-id shitian@************** 后，用ssh就不用密码了，如果不行可能还需要改服务器的ssh配置
# 手动复制公钥  echo "您的公钥内容" >> ~/.ssh/authorized_keys 
# 还需要修改一下ssh配置文件 sudo vim /etc/ssh/sshd_config  , 修改完之后 sudo systemctl restart sshd
#     PubkeyAuthentication yes
#     AuthorizedKeysFile .ssh/authorized_keys
# sudo systemctl ... 等命令可能也要输入密码，可以在服务器sudo visudo修改一下配置，让这几个命令不需要输入密码就行（加一行：shitian ALL=(ALL) NOPASSWD: /bin/systemctl stop crm-pre, /bin/systemctl start crm-pre）
.PHONY: publish-pre
publish-pre:
	scp -r bin/v1.0.0/linux_amd64/main shitian@**************:/var/www/crm_pre/backend && \
	ssh shitian@************** 'cd /var/www/crm_pre && sudo docker-compose up -d --build && sudo docker-compose restart backend'

.PHONY: publish-test
publish-test:
	ssh shitian@************** 'sudo systemctl stop crm-prod-test' && \
	scp -r bin/v1.0.0/linux_amd64/main shitian@**************:/var/www/crm_prod_test/backend && \
	ssh shitian@************** 'sudo systemctl start crm-prod-test'

.PHONY: publish-prod
publish-prod:
	scp -r bin/v1.0.0/linux_amd64/main shitian@**************:/var/www/crm/backend && \
	ssh shitian@************** 'cd /var/www/crm && sudo docker-compose up -d --build && sudo docker-compose restart backend'
	
# JMT_SSH := ssh -p 27601
# JMT_SCP := scp -P 27601
# JMT_HOST := jmt@**************

JMT_SSH := ssh -p 22
JMT_SCP := scp -P 22
JMT_HOST := jmt02@*************

# 单独发布 backend
.PHONY: publish-jmt
publish-jmt:
	@echo "正在发布 backend 到 jmt 服务器..."
	$(JMT_SSH) $(JMT_HOST) 'sudo systemctl stop crm' && \
	$(JMT_SCP) -r bin/v1.0.0/linux_amd64/main $(JMT_HOST):/var/www/crm/backend && \
	$(JMT_SSH) $(JMT_HOST) 'sudo systemctl start crm'
	@echo "backend 发布完成." 

# 单独发布 console
.PHONY: publish-jmt-console
publish-jmt-console:
	@echo "正在发布 console 到 jmt 服务器..."
	$(JMT_SSH) $(JMT_HOST) 'rm -rf /var/www/crm/console' && \
	$(JMT_SCP) -r ../deployment/jmt/dist $(JMT_HOST):/var/www/crm/console
	@echo "console 发布完成."

# 单独发布 console-api
.PHONY: publish-jmt-console-api
publish-jmt-console-api:
	@echo "正在发布 console-api 到 jmt 服务器..."
	$(JMT_SCP) -r ../deployment/jmt/api/* $(JMT_HOST):/var/www/crm/console_api/ && \
	$(JMT_SSH) $(JMT_HOST) 'cd /var/www/crm && sudo docker-compose up --build -d'
	@echo "console-api 发布完成."

# 合并发布：在执行publish-jmt-all时，将依次执行 backend、console 以及 console-api 的发布命令
.PHONY: publish-jmt-all
publish-jmt-all: publish-jmt publish-jmt-console publish-jmt-console-api
	@echo "所有 jmt 发布任务已完成."