SHELL := /bin/bash
-include .env
export $(shell sed 's/=.*//' .env 2>/dev/null)

# 同步源环境配置
SYNC_FROM_MYSQL_USER  ?= root
SYNC_FROM_MYSQL_PASSWORD ?= 111
SYNC_FROM_MYSQL_HOST  ?= **************
SYNC_FROM_MYSQL_PORT  ?= 27502
SYNC_FROM_DB          ?= crm_v102

# 同步目标环境配置
SYNC_TO_MYSQL_USER  ?= root
SYNC_TO_MYSQL_PASSWORD ?= 111
SYNC_TO_MYSQL_HOST  ?= **************
SYNC_TO_MYSQL_PORT  ?= 29502
SYNC_TO_DB          ?= adg_crm_pre7

# 备份目录
BACKUP_DIR ?= backups

# 通用连接参数
MYSQL_CMD = mysql -h $(1) -P $(2) -u $(3) -p$(4)
DUMP_CMD = mysqldump -h $(1) -P $(2) -u $(3) -p$(4) --add-drop-table --routines --triggers --single-transaction

.PHONY: help backup-from restore-to sync-from-to clean


backup-from:
	@echo -e "\033[33m警告：即将备份同步源数据库 $(SYNC_FROM_DB)（主机：$(SYNC_FROM_MYSQL_HOST):$(SYNC_FROM_MYSQL_PORT)）\033[0m"
	@read -p "确认要执行备份操作吗？(y/N) " confirm; \
	if [ "$${confirm^^}" != "Y" ]; then \
		echo "操作已取消"; \
		exit 1; \
	fi
	@echo -e "\033[33m!!! 源数据库名称核对 !!!\033[0m"
	@read -p "请输入要备份的数据库名称（必须完全匹配）: " db_name; \
	if [ "$$db_name" != "$(SYNC_FROM_DB)" ]; then \
		echo -e "\033[31m错误：数据库名称不匹配！预期: \033[36m$(SYNC_FROM_DB)\033[31m 实际输入: \033[36m$$db_name\033[0m"; \
		exit 1; \
	fi
	@mkdir -p $(BACKUP_DIR)
	@$(call DUMP_CMD,$(SYNC_FROM_MYSQL_HOST),$(SYNC_FROM_MYSQL_PORT),$(SYNC_FROM_MYSQL_USER),$(SYNC_FROM_MYSQL_PASSWORD)) $(SYNC_FROM_DB) > $(BACKUP_DIR)/sync_$(shell date +%Y%m%d%H%M%S).sql
	@echo "同步源数据库备份完成: $(BACKUP_DIR)/sync_*.sql"

restore-to:
	@echo -e "\033[31m危险操作：即将用同步备份覆盖目标数据库 $(SYNC_TO_DB)（主机：$(SYNC_TO_MYSQL_HOST):$(SYNC_TO_MYSQL_PORT)）\033[0m"
	@read -p "确认要执行恢复操作吗？(y/N) " confirm; \
	if [ "$${confirm^^}" != "Y" ]; then \
		echo "操作已取消"; \
		exit 1; \
	fi
	@echo -e "\033[33m!!! 目标数据库名称核对 !!!\033[0m"; \
	read -p "请输入要恢复的数据库名称（必须完全匹配）: " db_name; \
	if [ "$$db_name" != "$(SYNC_TO_DB)" ]; then \
		echo -e "\033[31m错误：数据库名称不匹配！预期: \033[36m$(SYNC_TO_DB)\033[31m 实际输入: \033[36m$$db_name\033[0m"; \
		exit 1; \
	fi; 
	@$(call MYSQL_CMD,$(SYNC_TO_MYSQL_HOST),$(SYNC_TO_MYSQL_PORT),$(SYNC_TO_MYSQL_USER),$(SYNC_TO_MYSQL_PASSWORD)) -e "DROP DATABASE IF EXISTS $(SYNC_TO_DB); CREATE DATABASE $(SYNC_TO_DB) default character set utf8mb4 collate utf8mb4_general_ci;"
	@test -n "$(wildcard $(BACKUP_DIR)/sync_*.sql)" || (echo "找不到同步备份文件"; exit 1)
	@$(call MYSQL_CMD,$(SYNC_TO_MYSQL_HOST),$(SYNC_TO_MYSQL_PORT),$(SYNC_TO_MYSQL_USER),$(SYNC_TO_MYSQL_PASSWORD)) $(SYNC_TO_DB) < $(shell ls -t $(BACKUP_DIR)/sync_*.sql | head -n 1)
	@echo "目标数据库已从最新同步备份恢复"

sync-from-to:
	@echo -e "\033[31m危险操作：即将直接同步源数据库 $(SYNC_FROM_DB) 到目标环境 $(SYNC_TO_DB)\033[0m"
	@echo -e "\033[31m源库：$(SYNC_FROM_MYSQL_HOST):$(SYNC_FROM_MYSQL_PORT)\033[0m"
	@echo -e "\033[31m目标库：$(SYNC_TO_MYSQL_HOST):$(SYNC_TO_MYSQL_PORT)\033[0m"
	@read -p "确认要执行直接同步吗？(y/N) " confirm; \
	if [ "$${confirm^^}" != "Y" ]; then \
		echo "操作已取消"; \
		exit 1; \
	fi
	@echo -e "\033[33m!!! 目标数据库名称核对 !!!\033[0m"; \
	read -p "请输入要同步的目标数据库名称（必须完全匹配）: " db_name; \
	if [ "$$db_name" != "$(SYNC_TO_DB)" ]; then \
		echo -e "\033[31m错误：数据库名称不匹配！预期: \033[36m$(SYNC_TO_DB)\033[31m 实际输入: \033[36m$$db_name\033[0m"; \
		exit 1; \
	fi; 
	@$(call MYSQL_CMD,$(SYNC_TO_MYSQL_HOST),$(SYNC_TO_MYSQL_PORT),$(SYNC_TO_MYSQL_USER),$(SYNC_TO_MYSQL_PASSWORD)) -e "DROP DATABASE IF EXISTS $(SYNC_TO_DB); CREATE DATABASE $(SYNC_TO_DB) default character set utf8mb4 collate utf8mb4_general_ci;"
	@$(call DUMP_CMD,$(SYNC_FROM_MYSQL_HOST),$(SYNC_FROM_MYSQL_PORT),$(SYNC_FROM_MYSQL_USER),$(SYNC_FROM_MYSQL_PASSWORD)) $(SYNC_FROM_DB) | $(call MYSQL_CMD,$(SYNC_TO_MYSQL_HOST),$(SYNC_TO_MYSQL_PORT),$(SYNC_TO_MYSQL_USER),$(SYNC_TO_MYSQL_PASSWORD)) $(SYNC_TO_DB)
	@echo "源数据库已直接同步到目标环境"

sync-clean:
	@rm -f $(BACKUP_DIR)/sync_*.sql
	@echo "已清理所有同步备份文件"