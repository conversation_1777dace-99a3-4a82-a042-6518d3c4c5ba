SHELL := /bin/bash

# 尝试加载 .env 文件中的环境变量（若 .env 文件不存在，则忽略）
-include .env
export $(shell sed 's/=.*//' .env)

# 迁移脚本存放目录
MIGRATIONS_DIR = migrations

###############################
# 测试库配置（可通过 .env 文件设置敏感信息）
###############################
TEST_MYSQL_USER  ?= root
TEST_MYSQL_PASSWORD ?= 111
TEST_MYSQL_HOST  ?= **************
TEST_MYSQL_PORT  ?= 29502
TEST_DB          ?= adg_crm_pre5

###############################
# 生产库配置（可通过 .env 文件设置敏感信息）
###############################
PROD_MYSQL_USER  ?= root
PROD_MYSQL_PASSWORD ?= 111
PROD_MYSQL_HOST  ?= **************
PROD_MYSQL_PORT  ?= 29502
PROD_DB          ?= adg_crm_pre6

###############################
# 添加 URL 编码函数
###############################
urlencode = $(shell python3 -c "import urllib.parse; print(urllib.parse.quote('$(1)'))")

###############################
# 拼接 DSN（Atlas CLI 使用），并对密码进行编码
###############################
TEST_MYSQL_PASSWORD_ENCODED := $(call urlencode,$(TEST_MYSQL_PASSWORD))
PROD_MYSQL_PASSWORD_ENCODED := $(call urlencode,$(PROD_MYSQL_PASSWORD))

# 修改 DSN 格式：去掉 @tcp(...) 的写法，使用标准 URL 风格
TEST_DSN_ATLAS = mysql://$(TEST_MYSQL_USER):$(TEST_MYSQL_PASSWORD_ENCODED)@$(TEST_MYSQL_HOST):$(TEST_MYSQL_PORT)/$(TEST_DB)?parseTime=true
PROD_DSN_ATLAS = mysql://$(PROD_MYSQL_USER):$(PROD_MYSQL_PASSWORD_ENCODED)@$(PROD_MYSQL_HOST):$(PROD_MYSQL_PORT)/$(PROD_DB)?parseTime=true

###############################
# 要导出的表（多个表用空格分隔）
# 自动化配置表、代码库、流程模板表、表单验证规则表、表单导出模板表、表单导入模板表、表单打印模板表、表单模板表、系统菜单表
###############################
TABLES := am_config code_history flow_template form_check_rule form_export_template form_import_template form_print_template form_template sys_menu

###############################
# 自定义查询配置（使用单引号包裹，避免双引号冲突）
###############################
CUSTOM_QUERIES = SELECT CONCAT('UPDATE sys_config SET config_value = ''', config_value, ''' WHERE config_key = ''NextId_form_name_00000'';') AS sql_statement FROM sys_config WHERE config_key = 'NextId_form_name_00000';

###############################
# db-diff 命令：生成迁移脚本（版本命名格式 v{序号}_{描述}）
###############################
# 使用说明：
#   此命令用于比较测试库和生产库的差异，并生成迁移脚本。
#   它会自动创建一个新的版本目录，包含：
#   - 数据库结构变更脚本 (001_schema_changes.sql)
#   - 指定表的数据导出脚本 (002_table_data.sql)
#   - 版本说明文件 (README.md)
#
# 使用示例：
#   make db-diff desc="添加用户表"
#   make db-diff desc="更新菜单权限"
db-diff:
	@if [ -z "$(desc)" ]; then \
		echo -e '请指定版本描述，例如: make db-diff desc="添加用户表"'; \
		exit 1; \
	fi; \
	LAST_VERSION=$$(ls -1d $(MIGRATIONS_DIR)/v* 2>/dev/null | grep -Eo 'v[0-9]+' | sed 's/v//' | sort -n | tail -1); \
	if [ -z "$$LAST_VERSION" ]; then \
		NEXT_VERSION=1; \
	else \
		NEXT_VERSION=$$((LAST_VERSION + 1)); \
	fi; \
	VERSION="v$${NEXT_VERSION}"; \
	echo "开始生成数据库差异脚本 ($$VERSION)..."; \
	mkdir -p "$(MIGRATIONS_DIR)/$$VERSION"; \
	\
	echo "1. 生成 Atlas Schema 差异脚本..."; \
	atlas schema diff \
		--from '$(PROD_DSN_ATLAS)' \
		--to '$(TEST_DSN_ATLAS)' \
		> "$(MIGRATIONS_DIR)/$$VERSION/001_schema_changes.sql"; \
	if [ ! -s "$(MIGRATIONS_DIR)/$$VERSION/001_schema_changes.sql" ]; then \
		rm "$(MIGRATIONS_DIR)/$$VERSION/001_schema_changes.sql"; \
		echo "无 Schema 变更."; \
	else \
		echo "Schema 差异已保存到: $(MIGRATIONS_DIR)/$$VERSION/001_schema_changes.sql"; \
	fi; \
	\
	echo "2. 导出指定表的结构和数据..."; \
	mysqldump -h $(TEST_MYSQL_HOST) -P $(TEST_MYSQL_PORT) -u $(TEST_MYSQL_USER) -p$(TEST_MYSQL_PASSWORD) $(TEST_DB) $(TABLES) > "$(MIGRATIONS_DIR)/$$VERSION/002_table_data.sql"; \
	echo "表数据已导出到: $(MIGRATIONS_DIR)/$$VERSION/002_table_data.sql"; \
	\
	echo "3. 生成自定义更新语句..."; \
	echo "执行的查询语句: $(CUSTOM_QUERIES)"; \
	mysql -h $(TEST_MYSQL_HOST) -P $(TEST_MYSQL_PORT) -u $(TEST_MYSQL_USER) -p$(TEST_MYSQL_PASSWORD) $(TEST_DB) -N -e "$(CUSTOM_QUERIES)" > "$(MIGRATIONS_DIR)/$$VERSION/003_custom_updates.sql"; \
	if [ ! -s "$(MIGRATIONS_DIR)/$$VERSION/003_custom_updates.sql" ]; then \
		rm "$(MIGRATIONS_DIR)/$$VERSION/003_custom_updates.sql"; \
		echo "无自定义更新."; \
	else \
		echo "自定义更新已保存到: $(MIGRATIONS_DIR)/$$VERSION/003_custom_updates.sql"; \
	fi; \
	echo "4. 生成版本说明文件..."; \
	echo "Version: $$VERSION" > "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "Description: $(desc)" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "Generated at: $$(date '+%Y-%m-%d %H:%M:%S')" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "Tables: $(TABLES)" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	\
	echo "" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "数据库对比信息：" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "源数据库：" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "- Host: $(PROD_MYSQL_HOST):$(PROD_MYSQL_PORT)" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "- 名称: $(PROD_DB)" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	\
	echo "" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "目标数据库：" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "- Host: $(TEST_MYSQL_HOST):$(TEST_MYSQL_PORT)" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	echo "- 名称: $(TEST_DB)" >> "$(MIGRATIONS_DIR)/$$VERSION/README.md"; \
	\
	echo "迁移脚本已生成在目录: $(MIGRATIONS_DIR)/$$VERSION"; \
	if [ ! -f "$(MIGRATIONS_DIR)/$$VERSION/001_schema_changes.sql" ] && [ ! -s "$(MIGRATIONS_DIR)/$$VERSION/002_table_data.sql" ]; then \
		echo '警告：未检测到任何变更！'; \
		rm -rf "$(MIGRATIONS_DIR)/$$VERSION"; \
	fi

###############################
# db-migrate 命令：执行指定版本的迁移（作用于生产库）
###############################
# 使用说明：
#   此命令用于将指定版本的迁移脚本应用到生产数据库。
#   它会按顺序执行版本目录下的所有 SQL 文件。
#   请谨慎执行，建议先备份生产数据库。
#
# 使用示例：
#   make db-migrate v=v1
#   make db-migrate v=v2
db-migrate:
	@if [ -z "$(v)" ]; then \
		echo "请指定要执行的版本，例如:"; \
		echo "  make db-migrate v=v1"; \
		exit 1; \
	fi
	@if [ ! -d "$(MIGRATIONS_DIR)/$(v)" ]; then \
		echo "版本 $(v) 不存在！"; \
		exit 1; \
	fi
	@echo -e "\033[36m====================================================\033[0m"; \
	echo -e "\033[33m即将执行数据库迁移操作，请确认以下信息：\033[0m"; \
	echo -e "目标数据库: \033[36m$(PROD_MYSQL_HOST):$(PROD_MYSQL_PORT)\033[0m"; \
	echo -e "数据库名称: \033[36m$(PROD_DB)\033[0m"; \
	echo -e "迁移版本号: \033[36m$(v)\033[0m"; \
	echo -e "\033[36m====================================================\033[0m"; \
	read -p "确定要执行迁移吗？(y/N) " confirm; \
	if [ "$$confirm" != "y" ] && [ "$$confirm" != "Y" ]; then \
		echo -e "\033[31m操作已取消\033[0m"; \
		exit 0; \
	fi; \
	\
	echo -e "\033[33m!!! 迁移的目标数据库核对 !!!\033[0m"; \
	read -p "请输入要迁移的目标数据库名称（必须完全匹配）: " db_name; \
	if [ "$$db_name" != "$(PROD_DB)" ]; then \
		echo -e "\033[31m错误：数据库名称不匹配！预期: \033[36m$(PROD_DB)\033[31m 实际输入: \033[36m$$db_name\033[0m"; \
		exit 1; \
	fi; \
	echo -e "\033[32m开始执行版本 \033[36m$(v)\033[32m 的迁移...\033[0m"; \
	BACKUP_DIR=backups; \
    TIMESTAMP=$$(date +%Y%m%d_%H%M%S); \
    BACKUP_FILE=$${BACKUP_DIR}/backup_$${TIMESTAMP}_$(v).sql.gz; \
    mkdir -p $$BACKUP_DIR; \
    echo "正在备份生产数据库到: $$BACKUP_FILE"; \
    mysqldump -h $(PROD_MYSQL_HOST) -P $(PROD_MYSQL_PORT) -u $(PROD_MYSQL_USER) -p$(PROD_MYSQL_PASSWORD) $(PROD_DB) | gzip > $$BACKUP_FILE || exit 1; \
    \
	for sql in $(MIGRATIONS_DIR)/$(v)/*.sql; do \
		if [ -f "$$sql" ]; then \
			echo "执行: $$sql"; \
			mysql -h $(PROD_MYSQL_HOST) -P $(PROD_MYSQL_PORT) -u $(PROD_MYSQL_USER) -p$(PROD_MYSQL_PASSWORD) $(PROD_DB) < $$sql || exit 1; \
		fi; \
	done; \
	echo "版本 $(v) 迁移完成"\
	\
	echo -e "\n\033[33m=== 执行Redis清理 ===\033[0m"; \
	$(MAKE) redis-flush; \
	\
	echo -e "\n\033[33m=== 重启生产服务 ===\033[0m"; \
	$(MAKE) restart-prod\

###############################
# db-diff-list 命令：列出所有可用的迁移版本
###############################
# 使用说明：
#   此命令用于显示所有已生成的迁移版本及其详细信息。
#   它会列出每个版本的：
#   - 版本号和描述
#   - README.md 中的详细信息
#
# 使用示例：
#   make db-diff-list
db-diff-list:
	@echo "可用的迁移版本:"; \
	if [ ! -d "$(MIGRATIONS_DIR)" ] || [ -z "$$(ls -A $(MIGRATIONS_DIR))" ]; then \
		echo "  没有可用的迁移版本"; \
	else \
		printf "%-6s | %-24s | %-7s | %-30s | %s\n" "版本" "时间" "表数量" "描述" "数据库对比"; \
		echo "---------------------------------------------------------------------------------------------------"; \
		for dir in $(MIGRATIONS_DIR)/v*; do \
			if [ -d "$$dir" ]; then \
				VERSION=$$(basename $$dir); \
				DESC=$$(grep 'Description:' $$dir/README.md | cut -d' ' -f2- | sed 's/\(.\{30\}\).*/\1.../'); \
				TABLES_COUNT=$$(grep 'Tables:' $$dir/README.md | cut -d' ' -f2- | wc -w); \
				DATE=$$(grep 'Generated at:' $$dir/README.md | cut -d' ' -f3-); \
				SRC_HOST=$$(grep -A2 '源数据库：' $$dir/README.md | grep 'Host:' | cut -d' ' -f3); \
				SRC_DB=$$(grep -A2 '源数据库：' $$dir/README.md | grep '名称:' | cut -d' ' -f3); \
				TGT_HOST=$$(grep -A2 '目标数据库：' $$dir/README.md | grep 'Host:' | cut -d' ' -f3); \
				TGT_DB=$$(grep -A2 '目标数据库：' $$dir/README.md | grep '名称:' | cut -d' ' -f3); \
				printf "%-6s | %-24s | %-7s | %-30s | %s → %s\n" \
					"$$VERSION" \
					"$$DATE" \
					"$$TABLES_COUNT表" \
					"$$DESC" \
					"$${SRC_HOST%:*}($$SRC_DB)" \
					"$${TGT_HOST%:*}($$TGT_DB)"; \
			fi; \
		done; \
	fi

###############################
# db-rm-diff 命令：清理指定版本的迁移脚本
###############################
# 使用说明：
#   此命令用于删除指定版本的迁移脚本目录。
#   请谨慎使用，删除后无法恢复。
#
# 使用示例：
#   make db-rm-diff v=v1_添加用户表
#   make db-rm-diff v=v2_更新菜单权限
db-rm-diff:
	@if [ -z "$(v)" ]; then \
		echo "请指定要清理的版本，例如:"; \
		echo "  make db-rm-diff v=v1_添加用户表"; \
		exit 1; \
	fi
	@if [ -d "$(MIGRATIONS_DIR)/$(v)" ]; then \
		rm -rf "$(MIGRATIONS_DIR)/$(v)"; \
		echo "已清理版本 $(v)"; \
	else \
		echo "版本 $(v) 不存在"; \
	fi

# ###############################
# # db-clear-diff 命令：清理所有生成的迁移脚本
# ###############################
# # 使用说明：
# #   此命令用于删除所有迁移脚本，完全清空 migrations 目录。
# #   请特别谨慎使用，删除后无法恢复。
# #
# # 使用示例：
# #   make db-clear-diff
# db-clear-diff:
# 	@echo "清理 $(MIGRATIONS_DIR) 目录..."; \
# 	rm -rf $(MIGRATIONS_DIR)


###############################
# Redis 配置（可通过 .env 文件设置）
###############################
PROD_REDIS_HOST     ?= 127.0.0.1
PROD_REDIS_PORT     ?= 6379
PROD_REDIS_PASSWORD ?= 
PROD_REDIS_DB       ?= 11

###############################
# redis-flush 命令：清空指定Redis数据库
###############################
redis-flush:
	@echo -e "\033[31m====================================================\033[0m"; \
	echo -e "\033[31m危险操作！即将清空 Redis 数据库:\033[0m"; \
	echo -e "Host:     \033[33m$(PROD_REDIS_HOST):$(PROD_REDIS_PORT)\033[0m"; \
	echo -e "Database: \033[33m$(PROD_REDIS_DB)\033[0m"; \
	[ -n "$(PROD_REDIS_PASSWORD)" ] && echo -e "认证:     \033[33m使用密码\033[0m" || true; \
	echo -e "\033[31m====================================================\033[0m"; \
	read -p "确认要清空数据库吗？(y/N) " confirm; \
	if [ "$$confirm" != "y" ] && [ "$$confirm" != "Y" ]; then \
		echo -e "\033[32m操作已取消\033[0m"; \
		exit 0; \
	fi; \
	if [ -z "$(PROD_REDIS_PASSWORD)" ]; then \
		redis-cli -h $(PROD_REDIS_HOST) -p $(PROD_REDIS_PORT) -n $(PROD_REDIS_DB) FLUSHDB; \
	else \
		redis-cli -h $(PROD_REDIS_HOST) -p $(PROD_REDIS_PORT) -a $(PROD_REDIS_PASSWORD) -n $(PROD_REDIS_DB) FLUSHDB; \
	fi; \
	echo -e "\033[32m数据库 $(PROD_REDIS_DB) 已清空\033[0m"

###############################
# restart-prod 命令：重启生产服务
###############################
PROD_BACKEND_SYSTEM_NAME ?= crm
restart-prod:
	ssh shitian@************** 'sudo systemctl stop $(PROD_BACKEND_SYSTEM_NAME)' && \
	ssh shitian@************** 'sudo systemctl start $(PROD_BACKEND_SYSTEM_NAME)'

