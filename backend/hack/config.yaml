
# CLI tool, only in development environment.
# https://goframe.org/pages/viewpage.action?pageId=3673173
gfcli:
  build:
    name:     "main"
    arch:     "amd64" # 386,amd64,arm
    system:   "linux,windows" # linux,darwin,windows
    packSrc:  "resource/lib,resource/frontend,resource/lowcode"
    version:  "v1.0.0"
    path:   "./bin"
    extra:    ""
  docker:
    build: "-a amd64 -s linux -p temp -ew"
    tagPrefixes:
      - my.image.pub/my-app
  gen:
    dao:
    - link: "mysql:root:123456@tcp(127.0.0.1:3306)/adg_crm_dev?charset=utf8mb4" # &allowPublicKeyRetrieval=true
      tplDaoInternalPath: "./tpl/dao-internal.tpl" # dao internal 模板文件，可以对模板进行修改
      # path: "./app" # 保存dao和model文件的目录地址, 默认从根目录开始找 ./ 代表的就是根目录
      # 需要生成dao和model的表名，多个表名用逗号分隔, 如果为空则生成全部表的dao和model 可以使用查询语句获得（SELECT GROUP_CONCAT(TABLE_NAME SEPARATOR ', ') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'adg_crm_dev' AND TABLE_NAME NOT LIKE 'form_data%'）
      tables: "form_print_logs,form_countersign_logs,form_export_template,form_export_history,form_import_history,form_import_template,code_history, form_check_rule,form_print_template,am_config,am_config_history,form_template, flow_template,flow_instance, flow_instance_history,flow_instance_history_auditor,  sys_config, sys_dept, sys_menu, sys_post, sys_post_role, sys_role, sys_user, sys_user_dept, sys_user_role, sys_user_post, ad_project, ad_project_role, sys_user_project"  # 因为数据库里可能很多form_data开头的表，gf dao又不支持通配符，所以只能放个列表