# ------------------------ 同步数据库的设置（一般是用于从生产库复制一个库出来测试）--------------------------------- #
# 来源库（一般是生产库）
SYNC_FROM_MYSQL_HOST=**************
SYNC_FROM_MYSQL_PORT=27502
SYNC_FROM_MYSQL_USER=root
SYNC_FROM_MYSQL_PASSWORD=111
SYNC_FROM_DB=crm_v102

# 目标库（一般是测试库）
SYNC_TO_MYSQL_HOST=**************
SYNC_TO_MYSQL_PORT=21502
SYNC_TO_MYSQL_USER=root
SYNC_TO_MYSQL_PASSWORD=111
SYNC_TO_DB=adg_crm_prod_test1

# ------------------------ 迁移数据库的设置（一般是在测试库做了变更之后，迁移变更到生产库，例如测试库增加了某个表单、流程、自动化等）--------------------------------- #
# 测试库
TEST_MYSQL_USER=root
TEST_MYSQL_PASSWORD=111
TEST_MYSQL_HOST=**************
TEST_MYSQL_PORT=29502
TEST_DB=adg_crm_pre5

# 生产库
PROD_MYSQL_USER=root
PROD_MYSQL_PASSWORD=111
PROD_MYSQL_HOST=**************
PROD_MYSQL_PORT=21502
PROD_DB=adg_crm_prod_test1

# 生产的redis库（这个是迁移数据后，需要清除redis的数据，然后重启后台应用，重新从库的配置文件中同步缓存键值，因为迁移数据后，缓存键值可能出现了变化）
PROD_REDIS_HOST=**************
PROD_REDIS_PORT=21503
PROD_REDIS_PASSWORD=lfluYk4reffZDjzzXfeNA2ub9odfJ1Ic
PROD_REDIS_DB=3

# 生产后台应用名称
PROD_BACKEND_SYSTEM_NAME=crm-prod-test
