package main

import (
	"backend/internal/cmd"
	_ "backend/internal/logic"  // 注册 业务逻辑
	_ "backend/internal/packed" // 注册 资源

	_ "backend/queue/nats"       // 注册 nats 的队列管理器 （可以实现 kafka 等其他队列管理器，然后在此注册）
	_ "backend/queue/z_consumer" // 注册 消费者 （为了确保编辑器不会自动把这样注册格式化到注册队列管理器前面，在它前面加个 z_ 前缀）

	_ "backend/automation/manager" // 注册自动化管理器
	_ "backend/automation/nodes"   // 注册自动化支持的节点

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2" // 注册 mysql 驱动
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"   // 注册 redis 驱动

	"github.com/gogf/gf/v2/os/gctx"
	// "fmt" 
	// "time"
	// "github.com/shirou/gopsutil/cpu"
	// "github.com/shirou/gopsutil/disk"
	// "github.com/shirou/gopsutil/mem"
)

func main() {

	// for {
	// 	// 获取CPU信息
	// 	cpuPercents, err := cpu.Percent(0, false)
	// 	if err != nil {
	// 		fmt.Printf("获取CPU信息失败: %v\n", err)
	// 		return
	// 	}

	// 	// 获取内存信息
	// 	memInfo, err := mem.VirtualMemory()
	// 	if err != nil {
	// 		fmt.Printf("获取内存信息失败: %v\n", err)
	// 		return
	// 	}

	// 	// 获取磁盘信息
	// 	diskInfo, err := disk.Usage("/")
	// 	if err != nil {
	// 		fmt.Printf("获取磁盘信息失败: %v\n", err)
	// 		return
	// 	}

	// 	// 打印信息
	// 	fmt.Printf("CPU使用率: %.2f%%\n", cpuPercents[0])
	// 	fmt.Printf("内存: 总计: %.2f GB, 使用: %.2f GB, 可用: %.2f GB, 使用率: %.2f%%\n",
	// 		float64(memInfo.Total)/1e9, float64(memInfo.Used)/1e9, float64(memInfo.Available)/1e9, memInfo.UsedPercent)
	// 	fmt.Printf("磁盘: 总计: %.2f GB, 使用: %.2f GB, 可用: %.2f GB, 使用率: %.2f%%\n",
	// 		float64(diskInfo.Total)/1e9, float64(diskInfo.Used)/1e9, float64(diskInfo.Free)/1e9, diskInfo.UsedPercent)

	// 	// 每秒刷新一次
	// 	time.Sleep(1 * time.Second)
	// }

	cmd.Main.Run(gctx.GetInitCtx())
}
