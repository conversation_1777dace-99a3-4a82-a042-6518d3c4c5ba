package library

import (
	"backend/internal/consts"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

func GetTenantID(ctx context.Context) uint64 {
	tenant_id := ctx.Value(consts.CTX_TENANT_ID)
	if tenant_id != nil {
		return gconv.Uint64(tenant_id)
	}
	return 0
}

func GetUserId(ctx context.Context) uint64 {
	user_id := ctx.Value(consts.CTX_USER_ID)
	if user_id != nil {
		return gconv.Uint64(user_id)
	}
	return 0
}

func GetTenantCacheKey(ctx context.Context, cacheKey string) string {
	tenant_id := GetTenantID(ctx)
	result := cacheKey + "_" + gconv.String(tenant_id)
	return result
}

func GetJWTKey(ctx context.Context) string {
	cfg := g.Cfg()
	jwtKey := cfg.MustGet(ctx, "jwt.key").String()
	if g.<PERSON>y(jwtKey) {
		jwtKey = consts.JWTKEY
	}
	return jwtKey
}

func GetDomain(ctx context.Context) string {
	domain := ""
	r := g.RequestFromCtx(ctx)
	if r != nil {
		domain = r.Host
	}
	return domain
}
