package library

import (
	"context"
	"strconv"
	"time"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

// 将Excel内容转成map切片
func ExcelToMapSlice(ctx context.Context, filePath string, startRow int) (result []map[string]string, err error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows(f.GetSheetName(0))
	if err != nil {
		return nil, err
	}

	if len(rows) < startRow {
		return nil, nil // 没有足够的数据
	}

	headers := rows[startRow-1]
	for _, row := range rows[startRow:] {
		rowMap := make(map[string]string)
		for i, cell := range row {
			if i < len(headers) {
				rowMap[headers[i]] = cell
			}
		}
		result = append(result, rowMap)
	}

	return result, nil
}

// ColumnConfig 定义列配置
type ColumnConfig struct {
	ColumnName string
	MapKey     string
	Width      float64
	IsCentered bool
	SubColumns []ColumnConfig
}

// 获取列的最大深度，用于确定表头的行数
func getMaxDepth(columns []ColumnConfig, currentDepth int) int {
	maxDepth := currentDepth
	for _, col := range columns {
		if len(col.SubColumns) > 0 {
			depth := getMaxDepth(col.SubColumns, currentDepth+1)
			if depth > maxDepth {
				maxDepth = depth
			}
		}
	}
	return maxDepth
}

// 递归生成表头，并处理单元格的合并
func buildHeaders(f *excelize.File, sheetName string, columns []ColumnConfig, startCol, startRow, level int, hasBorder bool, maxLevel int, publicBorder []excelize.Border) (endCol int, err error) {
	colIndex := startCol
	for _, col := range columns {
		cell, _ := excelize.CoordinatesToCellName(colIndex, startRow)
		if len(col.SubColumns) > 0 {
			// 有子列，递归处理
			subStartCol := colIndex
			var subEndCol int
			subEndCol, err = buildHeaders(f, sheetName, col.SubColumns, subStartCol, startRow+1, level+1, hasBorder, maxLevel, publicBorder)
			if err != nil {
				return
			}
			// 合并当前列的单元格
			startCell, _ := excelize.CoordinatesToCellName(subStartCol, startRow)
			endCell, _ := excelize.CoordinatesToCellName(subEndCol-1, startRow)
			if startCell != endCell {
				if err = f.MergeCell(sheetName, startCell, endCell); err != nil {
					return
				}
			}
			// 设置当前列的值
			if err = f.SetCellValue(sheetName, startCell, col.ColumnName); err != nil {
				return
			}
			// 设置样式
			headerStyle, err := f.NewStyle(&excelize.Style{
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
				Border: lo.Ternary(hasBorder, publicBorder, nil),
				Font: &excelize.Font{
					Bold: true,
					Size: 12,
				},
			})
			if err != nil {
				return endCol, err
			}
			f.SetCellStyle(sheetName, startCell, endCell, headerStyle)

			colIndex = subEndCol
		} else {
			// 没有子列，填写单元格，并根据需要合并行
			if maxLevel > level {
				// 需要合并行
				startCell, _ := excelize.CoordinatesToCellName(colIndex, startRow)
				endCell, _ := excelize.CoordinatesToCellName(colIndex, startRow+maxLevel-level)
				if err = f.MergeCell(sheetName, startCell, endCell); err != nil {
					return
				}
				// 设置值
				if err = f.SetCellValue(sheetName, startCell, col.ColumnName); err != nil {
					return
				}
				// 设置样式
				headerStyle, err := f.NewStyle(&excelize.Style{
					Alignment: &excelize.Alignment{
						Horizontal: lo.Ternary(col.IsCentered, "center", ""),
						Vertical:   "center",
					},
					Border: lo.Ternary(hasBorder, publicBorder, nil),
					Font: &excelize.Font{
						Bold: true,
						Size: 12,
					},
				})
				if err != nil {
					return endCol, err
				}
				f.SetCellStyle(sheetName, startCell, endCell, headerStyle)

			} else {
				// 不需要合并行
				if err = f.SetCellValue(sheetName, cell, col.ColumnName); err != nil {
					return
				}
				// 设置样式
				headerStyle, err := f.NewStyle(&excelize.Style{
					Alignment: &excelize.Alignment{
						Horizontal: lo.Ternary(col.IsCentered, "center", ""),
						Vertical:   "center",
					},
					Border: lo.Ternary(hasBorder, publicBorder, nil),
					Font: &excelize.Font{
						Bold: true,
						Size: 12,
					},
				})
				if err != nil {
					return endCol, err
				}
				f.SetCellStyle(sheetName, cell, cell, headerStyle)
			}
			colIndex++

			// 设置列宽
			colLetter, _ := excelize.ColumnNumberToName(colIndex - 1)
			f.SetColWidth(sheetName, colLetter, colLetter, lo.Ternary(col.Width > 0, col.Width, 10))
		}
	}
	endCol = colIndex
	return
}

// 填充数据，并处理单元格的合并
func fillData(f *excelize.File, sheetName string, data []map[string]interface{}, columns []ColumnConfig, startRow int, hasBorder bool, publicBorder []excelize.Border) (endRow int, err error) {
	rowIndex := startRow
	for _, row := range data {
		// 计算当前行需要占用的行数
		subTableRows := getSubTableRows(row, columns)
		mergeSize := lo.Ternary(subTableRows > 0, subTableRows, 1)

		// 填充主列数据并进行行合并
		colIndex := 1
		if err = fillRowData(f, sheetName, row, columns, &colIndex, rowIndex, mergeSize, hasBorder, publicBorder, false); err != nil {
			return
		}

		// 填充子表数据
		if subTableRows > 0 {
			// 展开子表数据
			if err = fillSubTableData(f, sheetName, row, columns, rowIndex, hasBorder, publicBorder); err != nil {
				return
			}
		}

		rowIndex += mergeSize
	}
	endRow = rowIndex
	return
}

// 获取子表的最大行数
func getSubTableRows(row map[string]interface{}, columns []ColumnConfig) (maxRows int) {
	for _, col := range columns {
		if len(col.SubColumns) > 0 {
			if subData, ok := row[col.MapKey]; ok {
				if subSlice, ok := subData.([]map[string]interface{}); ok {
					if len(subSlice) > maxRows {
						maxRows = len(subSlice)
					}
				}
			}
		}
	}
	return
}

// 填充主列数据并处理行合并
func fillRowData(f *excelize.File, sheetName string, rowData map[string]interface{}, columns []ColumnConfig, colIndex *int, rowIndex, mergeSize int, hasBorder bool, publicBorder []excelize.Border, isSubColumn bool) error {
	for _, col := range columns {
		cellStart, _ := excelize.CoordinatesToCellName(*colIndex, rowIndex)
		cellEnd, _ := excelize.CoordinatesToCellName(*colIndex, rowIndex+mergeSize-1)
		if len(col.SubColumns) > 0 {
			// 有子列，递归处理，标识当前是子表列
			if err := fillRowData(f, sheetName, rowData, col.SubColumns, colIndex, rowIndex, mergeSize, hasBorder, publicBorder, true); err != nil {
				return err
			}
		} else {
			// 填充数据
			value := rowData[col.MapKey]
			// 合并单元格，仅在处理主表列时进行
			if mergeSize > 1 && !isSubColumn {
				if err := f.MergeCell(sheetName, cellStart, cellEnd); err != nil {
					return err
				}
			}
			// 设置值
			if err := f.SetCellValue(sheetName, cellStart, value); err != nil {
				return err
			}
			// 设置样式
			cellStyle, err := f.NewStyle(&excelize.Style{
				Alignment: &excelize.Alignment{
					Horizontal: lo.Ternary(col.IsCentered, "center", ""),
					Vertical:   "center",
				},
				Border: lo.Ternary(hasBorder, publicBorder, nil),
			})
			if err != nil {
				return err
			}
			f.SetCellStyle(sheetName, cellStart, cellEnd, cellStyle)
			*colIndex++
		}
	}
	return nil
}

// 填充子表数据
func fillSubTableData(f *excelize.File, sheetName string, rowData map[string]interface{}, columns []ColumnConfig, rowIndex int, hasBorder bool, publicBorder []excelize.Border) error {
	colIndex := 1
	for _, col := range columns {
		if len(col.SubColumns) > 0 {
			// 获取子表数据
			subData := []map[string]interface{}{}
			if sd, ok := rowData[col.MapKey]; ok {
				if s, ok := sd.([]map[string]interface{}); ok {
					subData = s
				}
			}
			// 填充子表数据
			for i := 0; i < len(subData); i++ {
				subRow := subData[i]
				subColIndex := colIndex
				for _, subCol := range col.SubColumns {
					cell, _ := excelize.CoordinatesToCellName(subColIndex, rowIndex+i)
					value := subRow[subCol.MapKey]
					if err := f.SetCellValue(sheetName, cell, value); err != nil {
						return err
					}
					// 设置样式
					cellStyle, err := f.NewStyle(&excelize.Style{
						Alignment: &excelize.Alignment{
							Horizontal: lo.Ternary(subCol.IsCentered, "center", ""),
							Vertical:   "center",
						},
						Border: lo.Ternary(hasBorder, publicBorder, nil),
					})
					if err != nil {
						return err
					}
					f.SetCellStyle(sheetName, cell, cell, cellStyle)
					subColIndex++
				}
			}
			colIndex += len(col.SubColumns)
		} else {
			colIndex++
		}
	}
	return nil
}

// 将[]map[string]interface{} 转为excel文件并保存
func MapSliceToExcelFile(ctx context.Context, data []map[string]interface{}, columns []ColumnConfig, hasBorder bool, title string, exportPath string) (filePath string, err error) {

	// 新建一个日期格式的文件夹
	dateFolder := time.Now().Format("20060102")
	exportPath = exportPath + "/" + dateFolder
	if err := IsNotExistMkdirPath(ctx, exportPath); err != nil {
		return "", err
	}

	// 创建一个新的Excel文件
	f := excelize.NewFile()
	sheetName := "Sheet1"
	f.NewSheet(sheetName)

	publicBorder := []excelize.Border{
		{Type: "left", Color: "000000", Style: 1},
		{Type: "right", Color: "000000", Style: 1},
		{Type: "top", Color: "000000", Style: 1},
		{Type: "bottom", Color: "000000", Style: 1},
	}

	// 如果有传入标题，设置标题并合并单元格
	startRow := 1
	if title != "" {
		titleCell := "A1"
		// 计算总列数
		totalCols := getTotalCols(columns)
		lastCol, _ := excelize.ColumnNumberToName(totalCols)
		f.MergeCell(sheetName, titleCell, lastCol+"1")
		f.SetCellValue(sheetName, titleCell, title)

		// 设置标题样式
		titleStyle, err := f.NewStyle(&excelize.Style{
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
			Font: &excelize.Font{
				Bold: true,
				Size: 14,
			},
			Border: lo.Ternary(hasBorder, publicBorder, nil),
		})
		if err != nil {
			return "", err
		}
		f.SetCellStyle(sheetName, titleCell, lastCol+"1", titleStyle)
		startRow = 2
		f.SetRowHeight(sheetName, 1, 30)
	}

	// 获取表头的最大层级
	maxLevel := getMaxDepth(columns, 1)

	// 生成表头
	_, err = buildHeaders(f, sheetName, columns, 1, startRow, 1, hasBorder, maxLevel, publicBorder)
	if err != nil {
		return "", err
	}
	// 填充数据
	_, err = fillData(f, sheetName, data, columns, startRow+maxLevel, hasBorder, publicBorder)
	if err != nil {
		return "", err
	}

	// 保存文件
	filePath = exportPath + "/" + strconv.FormatInt(time.Now().UnixNano(), 10) + "_exported_file.xlsx"
	if err = f.SaveAs(filePath); err != nil {
		return "", err
	}

	return filePath, nil
}

// 计算总列数
func getTotalCols(columns []ColumnConfig) int {
	total := 0
	for _, col := range columns {
		if len(col.SubColumns) > 0 {
			total += getTotalCols(col.SubColumns)
		} else {
			total++
		}
	}
	return total
}
