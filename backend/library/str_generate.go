package library

import (
	"fmt"
	"math"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

// RandSeq 生成指定长度的随机字母
func RandSeq(n int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	b := make([]rune, n)

	for i := range b {

		b[i] = letters[r.Intn(len(letters))] // Use r for random generation

	}

	return string(b)
}

// RandNum 生成指定长度的随机数字
func RandNum(n int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano())) // Use local random generator
	format := fmt.Sprintf("%%0%dd", n)
	return fmt.Sprintf(format, r.Intn(int(math.Pow10(n))))
}

// 自动生成指定长度的自增ID，并返回
func AutoIncrement(n int, maxid int64) string {
	maxid += 1
	format := fmt.Sprintf("%%0%dd", n)
	fmt.Println("format:" + format)
	return fmt.Sprintf(format, maxid%int64(math.Pow10(n)))
}

// 在数字前面添加指定位数的0
func AddZero(num uint64, n int) string {
	format := fmt.Sprintf("%%0%dd", n)
	return fmt.Sprintf(format, num)
}

// 生成指定格式的ID ，例如
// format = "R{20060102150406}{RanNum(5)}{RanStr(5)}{AUTO_INCREMENT(5)}" , 或者 "DD{20060102}{AUTO_INCREMENT(5)}"
// maxid 是当前表中最大的ID，用于自动生成AUTO_INCREMENT ID
func GenerateID(format string, maxid int64) (newid string) {
	randNumFormat := regexp.MustCompile(`\{RanNum\((\d+)\)\}`).FindStringSubmatch(format)
	if randNumFormat != nil {
		n, _ := strconv.Atoi(randNumFormat[1])
		format = strings.Replace(format, randNumFormat[0], RandNum(n), 1)
	}

	randStrFormat := regexp.MustCompile(`\{RanStr\((\d+)\)\}`).FindStringSubmatch(format)
	if randStrFormat != nil {
		n, _ := strconv.Atoi(randStrFormat[1])
		format = strings.Replace(format, randStrFormat[0], RandSeq(n), 1)
	}
	// // 支持 {AUTO_INCREMENT({20060102},6)} 规则
	// autoIncrementFormatWithDate := regexp.MustCompile(`\{AUTO_INCREMENT\(\{(\d+)\},(\d+)\)\}`).FindStringSubmatch(format)
	// if autoIncrementFormatWithDate != nil {
	// 	dateFormat := autoIncrementFormatWithDate[1]
	// 	n, _ := strconv.Atoi(autoIncrementFormatWithDate[2])
	// 	now := time.Now()
	// 	dateStr := now.Format(dateFormat)
	// 	service.Cache().GetNextId()
	// 	incrementStr := AutoIncrement(n, maxid)
	// 	format = strings.Replace(format, autoIncrementFormatWithDate[0], dateStr+incrementStr, 1)
	// }

	autoIncrementFormat := regexp.MustCompile(`\{AUTO_INCREMENT\((\d+)\)\}`).FindStringSubmatch(format)
	if autoIncrementFormat != nil {
		n, _ := strconv.Atoi(autoIncrementFormat[1])
		format = strings.Replace(format, autoIncrementFormat[0], AutoIncrement(n, maxid), 1)
	}

	now := time.Now()
	datePattern := regexp.MustCompile(`\{[\d]+}`) // Match patterns like "{20060102}" or "{20060102150406}"
	dateFormatMatches := datePattern.FindAllStringSubmatch(format, -1)

	for _, match := range dateFormatMatches {
		dateFormat := strings.Trim(match[0], "{}")
		format = strings.Replace(format, match[0], now.Format(dateFormat), 1)
	}

	return format
}
