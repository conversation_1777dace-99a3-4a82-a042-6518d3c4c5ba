package library

// 定义泛型切片类型
type MySlice[T any] []T

// FilterFunc 表示过滤函数
type FilterFunc[T any] func(T) bool

// FilterList 过滤切片
func (s *MySlice[T]) FilterList(filters []FilterFunc[T]) MySlice[T] {
	return FilterList(*s, filters)
}

// FindOne 查找第一个满足条件的元素
func (s *MySlice[T]) FindOne(filters []FilterFunc[T]) T {
	return FindOne(*s, filters)
}

// FindIndex 查找第一个满足条件的元素的索引
func (s *MySlice[T]) FindIndex(filters []FilterFunc[T]) int {
	return FindIndex(*s, filters)
}

func Contains[T comparable](slice []T, item T) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

// FilterList 实现过滤逻辑
func FilterList[T any](input []T, filters []FilterFunc[T]) MySlice[T] {
	var output MySlice[T]

	for _, item := range input {
		if matchFilters(item, filters) {
			output = append(output, item)
		}
	}

	return output
}

// FindOne 实现查找逻辑
func FindOne[T any](input []T, filters []FilterFunc[T]) T {
	var zero T
	for _, item := range input {
		if matchFilters(item, filters) {
			return item
		}
	}
	return zero
}

// FindIndex 实现查找索引逻辑
func FindIndex[T any](input []T, filters []FilterFunc[T]) int {
	for i, item := range input {
		if matchFilters(item, filters) {
			return i
		}
	}
	return -1
}

// matchFilters 判断元素是否满足所有过滤条件
func matchFilters[T any](item T, filters []FilterFunc[T]) bool {
	for _, filter := range filters {
		if !filter(item) {
			return false
		}
	}
	return true
}
