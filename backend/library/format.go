package library

import (
	"bytes"
	"fmt"
	"io"

	"github.com/gogf/gf/v2/util/gutil"
)

type DumpWriter struct {
	Content string
}

func (d *DumpWriter) Write(p []byte) (n int, err error) {
	buffer := bytes.NewBuffer(nil)
	buffer.WriteString(string(p))
	d.Content = buffer.String()
	return buffer.Len(), nil
}

func SDump(data ...interface{}) string {
	var dw io.Writer = &DumpWriter{}

	// Check if the first element is a string and set it as the title
	var title string
	if len(data) > 0 {
		if t, ok := data[0].(string); ok {
			title = t
			data = data[1:]
		} else {
			data = data[0:]
		}
	}

	gutil.DumpTo(dw, data, gutil.DumpOption{})

	// Prepend the title if it exists
	if title != "" {
		return fmt.Sprintf("%s:\n%s", title, dw.(*DumpWriter).Content)
	}
	return dw.(*DumpWriter).Content
}
