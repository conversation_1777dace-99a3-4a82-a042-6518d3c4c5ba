package library

import (
	"backend/internal/model/enum"
	"fmt"
	"reflect"
	"regexp"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

func ToSlice(slice interface{}) (ret []interface{}, ok bool) {
	s := reflect.ValueOf(slice)
	ok = false
	if s.Kind() != reflect.Slice {
		return
	}
	ret = make([]interface{}, s.Len())

	for i := 0; i < s.Len(); i++ {
		ret[i] = s.Index(i).Interface()
	}
	ok = true
	return

}

func ToSliceInt64(slice interface{}) (ret []int64, ok bool) {
	s := reflect.ValueOf(slice)
	ok = false
	if s.Kind() != reflect.Slice {
		return
	}
	ret = make([]int64, s.Len())

	for i := 0; i < s.Len(); i++ {
		ret[i] = gconv.Int64(s.Index(i).Interface())
	}
	ok = true
	return

}

func ConvertAndValidate(value interface{}) interface{} {
	strValue := fmt.Sprintf("%v", value)

	// 检查是否是浮点数
	if matched, _ := regexp.MatchString(`^-?\d+$|^-?\d+\.\d+$`, strValue); matched {
		g.Log().Info(gctx.New(), fmt.Sprintf("is float %v", value))
		return gconv.Float64(strValue)
	}

	// 检查是否是时间
	if timeValue, err := time.Parse(time.RFC3339, strValue); err == nil {
		return timeValue
	}

	return strValue
}

func ConvertAndValidateN(value interface{}) interface{} {
	strValue := fmt.Sprintf("%v", value)

	// 检查是否是浮点数
	if matched, _ := regexp.MatchString(`^-?\d+$|^-?\d+\.\d+$`, strValue); matched {
		g.Log().Info(gctx.New(), fmt.Sprintf("is float %v", value))
		return gconv.Float64(strValue)
	}

	// 检查是否是时间
	if timeValue, err := time.Parse(time.RFC3339, strValue); err == nil {
		return timeValue
	}

	return value
}

func compare(left, right interface{}, op enum.Operator) bool {
	switch left := left.(type) {
	case float64:
		right := right.(float64)
		switch op {
		case enum.Operator_Equal:
			return left == right
		case enum.Operator_NotEqual:
			return left != right
		case enum.Operator_GreaterThan:
			return left > right
		case enum.Operator_GreaterThanOrEqual:
			return left >= right
		case enum.Operator_LessThan:
			return left < right
		case enum.Operator_LessThanOrEqual:
			return left <= right
		}
	case time.Time:
		right := right.(time.Time)
		switch op {
		case enum.Operator_Equal:
			return left.Equal(right)
		case enum.Operator_NotEqual:
			return !left.Equal(right)
		case enum.Operator_GreaterThan:
			return left.After(right)
		case enum.Operator_GreaterThanOrEqual:
			return !left.Before(right)
		case enum.Operator_LessThan:
			return left.Before(right)
		case enum.Operator_LessThanOrEqual:
			return !left.After(right)
		}
	default:
		switch op {
		case enum.Operator_Equal:
			return reflect.DeepEqual(left, right)
		case enum.Operator_NotEqual:
			return !reflect.DeepEqual(left, right)
		case enum.Operator_GreaterThan:
			return fmt.Sprintf("%v", left) > fmt.Sprintf("%v", right)
		case enum.Operator_GreaterThanOrEqual:
			return fmt.Sprintf("%v", left) >= fmt.Sprintf("%v", right)
		case enum.Operator_LessThan:
			return fmt.Sprintf("%v", left) < fmt.Sprintf("%v", right)
		case enum.Operator_LessThanOrEqual:
			return fmt.Sprintf("%v", left) <= fmt.Sprintf("%v", right)
		}
	}
	return false
}
func DeepEqual(left, right interface{}) bool {
	conv1 := ConvertAndValidateN(left)
	conv2 := ConvertAndValidateN(right)
	return reflect.DeepEqual(conv1, conv2)
}
func CompareValues(op enum.Operator, leftValue interface{}, rightValue interface{}) (result bool) {
	ctx := gctx.New()

	switch op {
	case enum.Operator_In:
		rightValues, ok := ToSlice(rightValue)
		if ok {
			for _, item := range rightValues {
				if DeepEqual(item, leftValue) {
					result = true
					break
				}
			}
		}
	case enum.Operator_ContainedIn:
		leftValues, ok := ToSlice(leftValue)
		if ok {
			for _, item := range leftValues {
				if DeepEqual(item, rightValue) {
					result = true
					break
				}
			}
		}
	case enum.Operator_CrossContained:
		leftValues, ok1 := ToSlice(leftValue)
		rightValues, ok2 := ToSlice(rightValue)
		if ok1 && ok2 {
			for _, item1 := range leftValues {
				for _, item2 := range rightValues {
					if DeepEqual(item1, item2) {
						result = true
						break
					}
				}
			}
		}
	default:
		ConvleftValue := ConvertAndValidate(leftValue)
		ConvrightValue := ConvertAndValidate(rightValue)
		result = compare(ConvleftValue, ConvrightValue, op)
	}
	g.Log().Info(ctx, SDump("op", op))
	g.Log().Info(ctx, SDump("leftValue", leftValue))
	g.Log().Info(ctx, SDump("rightValue", rightValue))
	g.Log().Info(ctx, SDump("result", result))
	return
}
