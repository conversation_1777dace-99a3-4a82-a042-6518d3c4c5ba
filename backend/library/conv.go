package library

import (
	"encoding/json"
	"fmt"
	"math"
	"reflect"
	"strings"

	"github.com/gogf/gf/v2/util/gutil"
)

func ToSliceString(i interface{}) ([]string, error) {
	// Convert the interface to JSON
	jsonBytes, err := json.Marshal(i)
	if err != nil {
		return nil, fmt.Errorf("error marshalling to JSON: %w", err)
	}

	// Now unmarshal the JSON into a []string
	var strs []string
	err = json.Unmarshal(jsonBytes, &strs)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling from JSON: %w", err)
	}

	return strs, nil
}

func ToStruct(i interface{}, obj interface{}) error {
	// Check if obj is pointer to struct
	rv := reflect.ValueOf(obj)
	if rv.Kind() != reflect.Ptr || rv.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("obj is not a pointer to struct")
	}

	// Convert the interface to JSON
	jsonBytes, err := json.Marshal(i)
	if err != nil {
		return fmt.Errorf("error marshalling to JSON: %w", err)
	}

	// Now unmarshal the JSON into the struct obj
	err = json.Unmarshal(jsonBytes, obj)
	if err != nil {
		return fmt.Errorf("error unmarshalling from JSON: %w", err)
	}

	return nil
}

func ToAny(i interface{}, obj interface{}) error {
	// Convert the interface to JSON
	jsonBytes, err := json.Marshal(i)
	if err != nil {
		return fmt.Errorf("error marshalling to JSON: %w", err)
	}

	// Now unmarshal the JSON into the struct obj
	err = json.Unmarshal(jsonBytes, &obj)
	if err != nil {
		return fmt.Errorf("error unmarshalling from JSON: %w", err)
	}

	return nil
}

// IsTrulyEmptyValue 判断一个值是否为真正的空值，数值类型的0不被视为空值
func IsTrulyEmptyValue(value interface{}) bool {
	if gutil.IsEmpty(value) {
		// 检查是否为数值类型的0，不视为真正的空值
		switch v := value.(type) {
		case int:
			return v != 0
		case int8:
			return v != 0
		case int16:
			return v != 0
		case int32:
			return v != 0
		case int64:
			return v != 0
		case uint:
			return v != 0
		case uint8:
			return v != 0
		case uint16:
			return v != 0
		case uint32:
			return v != 0
		case uint64:
			return v != 0
		case float32:
			return v != 0.0
		case float64:
			return v != 0.0
		}
		// 其他类型视为真正的空值
		return true
	}
	// 非空值
	return false
}

const Base62Charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz"

// DecimalToBase62 将十进制数字转换为 62 进制字符串，并确保返回 maxLength 位长度
func DecimalToBase62(n int, maxLength int) string {
	if n == 0 {
		return "AAAA" // 使用 'A' 作为填充字符
	}

	var result []byte
	base := len(Base62Charset)

	// 将十进制数字转换为62进制
	for n > 0 {
		remainder := n % base
		result = append([]byte{Base62Charset[remainder]}, result...)
		n = n / base
	}

	// 前面补'A'
	for len(result) < maxLength {
		result = append([]byte{'A'}, result...)
	}

	// 截断前面的部分
	if len(result) > maxLength {
		result = result[len(result)-4:]
	}

	return string(result)
}

// Base62ToDecimal 将 62 进制字符串转换为十进制数字
func Base62ToDecimal(s string) int {
	base := len(Base62Charset)
	result := 0

	// 去掉前面的填充字符 'A'
	s = strings.TrimLeft(s, "A")

	for i, c := range s {
		result += strings.IndexRune(Base62Charset, c) * int(math.Pow(float64(base), float64(len(s)-i-1)))
	}

	return result
}

// 将表单id和类型枚举转为四位字符串（id：0-3694083，enum：0-3）
func CodePrefixEncode(id int32, enum int32) string {
	packid := PackIDAndEnum(id, enum)
	return DecimalToBase62(int(packid), 4)
}

// 将四位字符串解码为表单id和类型枚举
func CodePrefixDecode(code string) (id int32, enum int32, err error) {
	packid := Base62ToDecimal(code)
	id, enum = UnpackIDAndEnum(int32(packid))
	return

}

func PackIDAndEnum(id int32, enum int32) int32 {
	// 确保id在30位内，enum在2位内
	if id >= (1<<30) || enum >= (1<<2) {
		panic("ID或Enum超出范围")
	}
	// 将枚举值放在最后两位，ID放在前30位
	return (id << 2) | enum
}

func UnpackIDAndEnum(packedID int32) (id int32, enum int32) {
	// 枚举值在最后两位
	enum = packedID & 0x3 // 0x3相当于二进制的11，表示只取最后两位
	// ID在前30位
	id = packedID >> 2
	return id, enum
}
func MergeMaps(map1, map2 map[string]interface{}, retainFromMap ...int) map[string]interface{} {
	mergedMap := make(map[string]interface{})

	// 合并 map1 和 map2
	for key, value := range map1 {
		mergedMap[key] = value
	}
	for key, value := range map2 {
		mergedMap[key] = value
	}

	if len(retainFromMap) == 1 {
		targetMap := map1
		if retainFromMap[0] == 2 {
			targetMap = map2
		}

		for key := range mergedMap {
			if _, exists := targetMap[key]; !exists {
				delete(mergedMap, key)
			}
		}
	}

	return mergedMap
}

func StructToMap(i interface{}) (map[string]interface{}, error) {
	// 将结构体编码为 JSON
	data, err := json.Marshal(i)
	if err != nil {
		return nil, err
	}

	// 将 JSON 解码为 map
	var result map[string]interface{}
	err = json.Unmarshal(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
