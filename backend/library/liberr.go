package library

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func ErrIsNil(ctx context.Context, err error, msg ...string) {
	if !g.IsNil(err) {
		if len(msg) > 0 {
			g.Log().<PERSON>rror(ctx, err.<PERSON><PERSON>r())
			panic(msg[0])
		} else {
			panic(err.<PERSON>rror())
		}
	}
}

func Fail(ctx context.Context, msg string) {
	err := fmt.Errorf(msg)
	g.Log().Error(ctx, err.Error())
	panic(err.Error())
}

func ValueIsNil(value interface{}, msg string) {
	// g.Log().Info(gctx.New(), fmt.Sprintf("%s is errormessage", msg))
	if g.<PERSON>(value) {
		panic(msg)
	}
}

func CheckFail(ctx context.Context, checkResult bool, msg string) {
	if !checkResult {
		err := fmt.Errorf(msg)
		g.Log().Error(ctx, err.<PERSON>rror())
		panic(err.<PERSON>rror())
	}
}
