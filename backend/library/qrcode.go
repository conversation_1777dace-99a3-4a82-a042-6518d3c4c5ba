package library

import (
	"bytes"
	"encoding/base64"
	"image/png"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/code128"
	"github.com/boombuler/barcode/qr"
)

func BarcodeBase64(content string, width int, height int) (barcodeBase64 string, err error) {
	code, err := code128.Encode(content)
	if err != nil {
		return
	}

	// scode, err := barcode.Scale(code, width, height)
	// if err != nil {
	// 	return
	// }
	// 将条码保存到缓冲区
	var buf bytes.Buffer
	err = png.Encode(&buf, code)
	if err != nil {
		return
	}

	// 将缓冲区内容转换为Base64
	barcodeBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return
}

// func BarcodeBase64(content string, width int, height int) (barcodeBase64 string, err error) {
// 	code, err := code128.Encode(content)
// 	if err != nil {
// 		return
// 	}

// 	// 获取条码的实际宽度和高度
// 	originalWidth := code.Bounds().Dx()
// 	originalHeight := code.Bounds().Dy()

// 	g.Log().Info(nil, SDump("originalWidth", originalWidth))
// 	g.Log().Info(nil, SDump("originalHeight", originalHeight))
// 	// 计算缩放比例，以适应指定的宽度
// 	scaleFactor := float64(width) / float64(originalWidth)

// 	// 计算缩放后的高度
// 	newHeight := int(float64(originalHeight) * scaleFactor)

// 	// 缩放条码到指定宽度和调整后的高度
// 	scode, err := barcode.Scale(code, width, newHeight)
// 	if err != nil {
// 		return
// 	}

// 	// 创建一个新的图像以容纳条码（包括可能的居中留白）
// 	finalImage := image.NewRGBA(image.Rect(0, 0, width, height))

// 	// 设置背景色为白色
// 	white := color.RGBA{255, 255, 255, 255}
// 	draw.Draw(finalImage, finalImage.Bounds(), &image.Uniform{white}, image.Point{}, draw.Src)

// 	// 如果 newHeight 小于 height，则计算偏移量使条码垂直居中
// 	offsetY := 0
// 	if newHeight < height {
// 		offsetY = (height - newHeight) / 2
// 	}

// 	// 将条码绘制到新图像上，居中对齐
// 	draw.Draw(finalImage, image.Rect(0, offsetY, width, offsetY+newHeight), scode, image.Point{0, 0}, draw.Over)

// 	// 将条码图像保存到缓冲区
// 	var buf bytes.Buffer
// 	err = png.Encode(&buf, finalImage)
// 	if err != nil {
// 		return
// 	}

// 	// 将缓冲区内容转换为 Base64
// 	barcodeBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

// 	return
// }

// QRCodeBase64 生成二维码的 Base64 编码
func QRCodeBase64(content string, size int) (qrcodeBase64 string, err error) {
	qrCode, err := qr.Encode(content, qr.M, qr.Auto)
	if err != nil {
		return
	}
	scode, err := barcode.Scale(qrCode, size, size)
	if err != nil {
		return
	}
	// 将条码保存到缓冲区
	var buf bytes.Buffer
	err = png.Encode(&buf, scode)
	if err != nil {
		return
	}

	// 将缓冲区内容转换为Base64
	qrcodeBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	// var png []byte
	// png, err = qrcode.Encode(content, qrcode.Medium, size)

	// if err != nil {
	// 	return
	// }

	// // 将缓冲区内容转换为Base64
	// qrcodeBase64 = base64.StdEncoding.EncodeToString(png)

	return
}
