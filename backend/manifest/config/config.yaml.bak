server:
  address:     ":9001"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  clientMaxBodySize: "50MB"
  # Logging配置
  logPath: "runtime/log/server"                 # 日志文件存储目录路径，建议使用绝对路径。默认为空，表示关闭
  logStdout: true                               # 日志是否输出到终端。默认为true
  errorStack: true               # 当Server捕获到异常时是否记录堆栈信息到日志中。默认为true
  errorLogEnabled: true                # 是否记录异常日志信息到日志中。默认为true
  errorLogPattern: "error-{Ymd}.log"   # 异常错误日志文件格式。默认为"error-{Ymd}.log"
  accessLogEnabled: true               # 是否记录访问日志。默认为false
  accessLogPattern: "access-{Ymd}.log" # 访问日志文件格式。默认为"access-{Ymd}.log"

jwt:
  key: "adg_crm_dev"
  
logger:
  path: "runtime/log/run"
  file: "{Y-m-d}.log"
  level: "all"
  stdout: true

upload:
  path: "runtime/upload"
  mappath: "/files"
  maxSize: 50 #MB
  allowTypes: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/tiff",
    "image/webp",
    "image/svg+xml",
    "application/pdf",
    "application/msword",
    "application/vnd.ms-excel",
    "application/vnd.ms-powerpoint",
    "application/vnd.apple.keynote",
    "application/vnd.apple.pages",
    "application/vnd.apple.numbers",
    "application/vnd.oasis.opendocument.text",
    "application/vnd.oasis.opendocument.spreadsheet",
    "application/vnd.oasis.opendocument.presentation",
    "text/plain",
    "application/zip",
    "application/x-rar-compressed",
    "text/markdown",
    "audio/mpeg",
    "audio/wav",
    "audio/ogg",
    "video/mp4",
    "video/x-msvideo",
    "video/quicktime",
    "video/x-matroska",
    "text/csv",
    "application/xml",
    "application/json",
    "application/sql",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document" 
]


print:
  path: "runtime/print"
  mappath: "/print_pdf"

export:
  path: "runtime/export"
  mappath: "/export_file"

# 持久化数据库配置 mysql
database:
  logger:
    level:   "all"
    stdout:  true
    Path: "runtime/log/sql"

  default:
    link:   "mysql:root:123456@tcp(127.0.0.1:3306)/adg_crm_dev?charset=utf8mb4&parseTime=true&loc=Local"
    debug:  true
    charset: "utf8mb4" #数据库编码
    dryRun: false #空跑
    maxIdle: 10 #连接池最大闲置的连接数
    maxOpen: 100 #连接池最大打开的连接数
    maxLifetime: "30s" #(单位秒)连接对象可重复使用的时间长度

# Redis 配置 (可用于缓存、分布式锁)
redis:
  default:
    address: 127.0.0.1:6379
    pass: 123456
    db: 2 
    idleTimeout: "60s" #连接最大空闲时间，使用时间字符串例如30s/1m/1d
    maxConnLifetime: "90s" #连接最长存活时间，使用时间字符串例如30s/1m/1d
    waitTimeout: "10s" #等待连接池连接的超时时间，使用时间字符串例如30s/1m/1d
    dialTimeout: "10s" #TCP连接的超时时间，使用时间字符串例如30s/1m/1d
    readTimeout: "10s" #TCP的Read操作超时时间，使用时间字符串例如30s/1m/1d
    writeTimeout: "10s" #TCP的Write操作超时时间，使用时间字符串例如30s/1m/1d
    maxActive: 100

# nats 配置 (可用于消息队列)
nats:
  default:
    url: nats://127.0.0.1:4222
    
system:
  cache:
    model: "redis"  #缓存模式 memory redis