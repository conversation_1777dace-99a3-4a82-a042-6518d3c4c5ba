package flow

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectFlowAudit, &FlowAudioConsumer{})
}

type FlowAudioConsumer struct {
}

func (f *FlowAudioConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		info := &dto.NextApproval{}
		err = json.Unmarshal(queueMsg.Data, info)
		library.ErrIsNil(ctx, err)

		// 审核后的下一步处理
		err = service.FlowInstance().StartNextApproval(ctx, info.HistoryAuditor, info.Instance)
		library.ErrIsNil(ctx, err)

	})
	return
}
