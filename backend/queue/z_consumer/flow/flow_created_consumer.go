package flow

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectFlowCreated, &FlowStartConsumer{})
}

type FlowStartConsumer struct {
}

// 启动流程实例
func (f *FlowStartConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		instance := &dto.FlowInstance{}
		err = json.Unmarshal(queueMsg.Data, instance)
		library.ErrIsNil(ctx, err)

		instanceInfo, err := service.FlowInstance().GetInstance(ctx, instance.Id)
		library.ErrIsNil(ctx, err)

		err = service.FlowInstance().StartNextApproval(ctx, nil, instanceInfo)
		library.ErrIsNil(ctx, err)
	})
	return
}
