package flow

import (
	"backend/internal/consts"
	"backend/library"
	"backend/queue"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectFlowFinish, &FlowEndConsumer{})
}

type FlowEndConsumer struct {
}

func (f *FlowEndConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		// instance := &dto.FlowInstance{}
		// err = json.Unmarshal(queueMsg.Data, instance)
		// library.ErrIsNil(ctx, err)

		// // 更新表单状态
		// err = service.FormData().UpdateFlowStatus(ctx, instance.FormTableName, instance.FormTableId, enum.FlowStatus(instance.InstanceState))
		// library.ErrIsNil(ctx, err)
	})
	return
}
