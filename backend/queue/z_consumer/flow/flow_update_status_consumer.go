package flow

import (
	"backend/internal/consts"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectFlowUpdateStatus, &FlowUpdateStatusConsumer{})
}

type FlowUpdateStatusConsumer struct {
}

func (f *FlowUpdateStatusConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		do_instance := &do.FlowInstance{}

		err = json.Unmarshal(queueMsg.Data, do_instance)
		library.ErrIsNil(ctx, err)
		// 查询流程实例
		instance, err := service.FlowInstance().GetInfoCache(ctx, gconv.Int64(do_instance.Id))
		library.ErrIsNil(ctx, err)

		// 使用 queueMsg.Sequence 作为秒数伪造一个时间 flow_status_version(因为flow_status_version精度只到秒，速度太快可能会导致更新失败，但修改字段类型涉及过多，因此用此替代方案)
		flow_status_version := do_instance.UpdatedAt.Add(time.Duration(queueMsg.Sequence) * time.Second)

		// 更新表单状态
		affected, err := service.FormData().SyncFlowStatus(ctx, instance.FormTableName, instance.FormTableId, enum.FlowStatus(instance.InstanceState), flow_status_version, enum.FlowInstanceType(instance.FlowInstanceType))
		library.ErrIsNil(ctx, err)
		// 更新作废状态和作废理由信息
		if affected > 0 && instance.InstanceState == int(enum.FlowStatus_Invalid) {
			extraData := &dto.InstanceExtraData{}
			err = json.Unmarshal([]byte(instance.FormExtraData), extraData)
			library.ErrIsNil(ctx, err)
			_ = service.FormData().UpdateInvalidStatus(ctx, instance.FormTableName, instance.FormTableId, extraData.Attachment)
		}
	})
	return
}
