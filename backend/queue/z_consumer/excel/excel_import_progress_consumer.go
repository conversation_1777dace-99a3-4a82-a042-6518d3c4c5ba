package excel

import (
	"backend/internal/consts"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectExcelImportProgress, &ExcelImportProgressConsumer{})
}

type ExcelImportProgressConsumer struct {
}

func (f *ExcelImportProgressConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		info := &entity.FormImportHistory{}
		err = json.Unmarshal(queueMsg.Data, info)
		library.ErrIsNil(ctx, err)

		// 更新导入数据文件的历史记录
		err = service.FormImportHistory().Update(ctx, info)
		library.ErrIsNil(ctx, err)

	})
	return
}
