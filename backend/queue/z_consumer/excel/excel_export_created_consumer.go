// ExcelExportCreatedConsumer 消费者用于处理导入创建消息。
// 它会监听 Excel 导出创建的消息队列，当接收到消息时，
// 会解析消息并触发对应的导入文件处理逻辑。

package excel

import (
	"backend/internal/consts"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectExcelExportCreated, &ExcelExportCreatedConsumer{})
}

type ExcelExportCreatedConsumer struct {
}

func (f *ExcelExportCreatedConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		info := &entity.FormExportHistory{}
		err = json.Unmarshal(queueMsg.Data, info)
		library.ErrIsNil(ctx, err)

		// 触发处理导出数据文件
		err = service.FormExportHistory().TriggerExportFile(ctx, info.Id)
		library.ErrIsNil(ctx, err)
	})
	return
}
