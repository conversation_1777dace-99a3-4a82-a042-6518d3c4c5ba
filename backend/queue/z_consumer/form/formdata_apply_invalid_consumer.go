package form

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamFormDataApplyInvalid, &FormDataApplyInvalidConsumer{})
}

type FormDataApplyInvalidConsumer struct {
}

// 启动流程实例
func (f *FormDataApplyInvalidConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		// g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))

		invalidData := &dto.FormDataShort{}
		err = json.Unmarshal(queueMsg.Data, invalidData)
		library.ErrIsNil(ctx, err)

		// 创建流程实例
		_, err = service.FlowInstance().Create(ctx, invalidData.FormTemplateId, invalidData.FormId, invalidData.ExtraData, true, enum.FlowInstanceType_Invalid, invalidData.CreatedBy)
		library.ErrIsNil(ctx, err)
	})
	return
}
