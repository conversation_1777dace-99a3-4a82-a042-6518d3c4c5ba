package form

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamFormFlowFinish, &FormFlowFinishConsumer{})
}

type FormFlowFinishConsumer struct {
}

// 表单审核通过
func (f *FormFlowFinishConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		finishData := &dto.FormFlowFinish{}
		err = json.Unmarshal(queueMsg.Data, finishData)
		library.ErrIsNil(ctx, err)

		// 触发相关业务逻辑
		smemberKey := fmt.Sprintf("%s_%s", consts.TriggerFormFlowFinish, finishData.TableName)
		service.AmConfig().TriggerBySmemberKey(ctx, smemberKey, map[string]interface{}{"form_data_id": finishData.FormDataId}, int64(queueMsg.Sequence))
		// if err != nil {
		// 	g.Log().Error(ctx, err)
		// 	library.ErrIsNil(ctx, err)
		// }

	})
	return
}
