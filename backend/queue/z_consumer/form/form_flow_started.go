package form

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamFormFlowStarted, &FormFlowStartedConsumer{})
}

type FormFlowStartedConsumer struct {
}

// 表单审批流经过了开始节点
func (f *FormFlowStartedConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		invalidData := &dto.FormStreamInfo{}
		err = json.Unmarshal(queueMsg.Data, invalidData)
		library.ErrIsNil(ctx, err)

		smemberKey := fmt.Sprintf("%s_%s_%s", consts.TriggerFormFlowStarted, invalidData.TableName, invalidData.ApprovalType)
		service.AmConfig().TriggerBySmemberKey(ctx, smemberKey, map[string]interface{}{"form_data_id": invalidData.FormDataId}, int64(queueMsg.Sequence))
		// if err != nil {
		// 	g.Log().Error(ctx, err)
		// 	library.ErrIsNil(ctx, err)
		// }

	})
	return
}
