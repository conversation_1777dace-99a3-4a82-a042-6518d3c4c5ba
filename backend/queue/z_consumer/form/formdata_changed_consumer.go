package form

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamFormDataChanged, &FormDataChangedConsumer{})
}

type FormDataChangedConsumer struct {
}

// 表单的创建
func (f *FormDataChangedConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		changedData := &dto.FormDataShort{}
		err = json.Unmarshal(queueMsg.Data, changedData)
		library.ErrIsNil(ctx, err)

		// 触发自动化
		smemberKey := fmt.Sprintf("%s_%s", consts.TriggerFormDataChange, changedData.TableName)
		triggerData := map[string]interface{}{
			"form_data_id": changedData.FormId,
		}
		if changedData.ExtraData != nil && changedData.ExtraData.TriggerType != "" {
			triggerData["trigger_type"] = changedData.ExtraData.TriggerType
		}
		if changedData.ExtraData != nil && changedData.ExtraData.TriggerSourceId != "" {
			triggerData["trigger_source_id"] = changedData.ExtraData.TriggerSourceId
		}
		service.AmConfig().TriggerBySmemberKey(ctx, smemberKey, triggerData, int64(queueMsg.Sequence))
	})
	return
}
