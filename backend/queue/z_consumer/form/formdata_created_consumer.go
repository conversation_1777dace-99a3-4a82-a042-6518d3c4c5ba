package form

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamFormDataCreated, &FormDataCreatedConsumer{})
}

type FormDataCreatedConsumer struct {
}

// 表单的创建
func (f *FormDataCreatedConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		createdData := &dto.FormDataShort{}
		err = json.Unmarshal(queueMsg.Data, createdData)
		library.ErrIsNil(ctx, err)

		// 创建流程实例
		_, err = service.FlowInstance().Create(ctx, createdData.FormTemplateId, createdData.FormId, createdData.ExtraData, true, enum.FlowInstanceType_Create, createdData.CreatedBy)
		library.ErrIsNil(ctx, err)

		// 触发自动化
		smemberKey := fmt.Sprintf("%s_%s", consts.TriggerFormDataNew, createdData.TableName)
		triggerData := map[string]interface{}{
			"form_data_id": createdData.FormId,
		}
		if createdData.ExtraData != nil && createdData.ExtraData.TriggerType != "" {
			triggerData["trigger_type"] = createdData.ExtraData.TriggerType
		}
		if createdData.ExtraData != nil && createdData.ExtraData.TriggerSourceId != "" {
			triggerData["trigger_source_id"] = createdData.ExtraData.TriggerSourceId
		}
		service.AmConfig().TriggerBySmemberKey(ctx, smemberKey, triggerData, int64(queueMsg.Sequence))
	})
	return
}
