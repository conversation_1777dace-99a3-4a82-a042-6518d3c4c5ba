package other

import (
	"backend/internal/consts"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectConfigUpdate, &ConfigUpdatedConsumer{})
}

type ConfigUpdatedConsumer struct {
}

func (f *ConfigUpdatedConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 增加一个版本初始值，防止历史版本不更新
		const INITIAL_VERSION int64 = 4000000
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))
		config := &entity.SysConfig{}
		err := json.Unmarshal(queueMsg.Data, config)
		library.ErrIsNil(ctx, err)
		config.Version = INITIAL_VERSION + int64(queueMsg.Sequence)
		// 更新配置
		err = service.SysConfig().SetValue(ctx, config.ConfigKey, config.ConfigValue, config.Version)
		library.ErrIsNil(ctx, err)
	})
	return
}
