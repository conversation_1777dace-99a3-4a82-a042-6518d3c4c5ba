package other

import (
	"backend/internal/consts"
	"backend/library"
	"backend/queue"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectOrderPayed, &OrderPayedConsumer{})
}

type OrderPayedConsumer struct {
}

func (f *OrderPayedConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))
	})
	return
}
