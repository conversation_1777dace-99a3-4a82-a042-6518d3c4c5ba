package other

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	queue.QueueManager().RegisterConsumer(consts.StreamSubjectLoginLog, &LoginLogConsumer{})
}

type LoginLogConsumer struct {
}

func (f *LoginLogConsumer) HandleMessage(ctx context.Context, queueMsg *queue.QueueMsg) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(queueMsg, "queueMsg is nil")
		g.Log().Info(ctx, fmt.Sprintf("[%s]接收到消息: %s", queueMsg.Subject, string(queueMsg.Data)))
		log := &dto.QueueUserLoginLog{}
		err = json.Unmarshal(queueMsg.Data, log)
		library.ErrIsNil(ctx, err)

		// 处理登录日志
		if log.Success {
			// 成功登录
			err = service.SysUser().UpdateUserLoginInfo(ctx, log.UserId, log.IP, log.LoginTime)

		} else {
			// 登录失败
		}
		library.ErrIsNil(ctx, err)
	})
	return
}
