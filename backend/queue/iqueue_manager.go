package queue

import (
	"context"
)

type IQueueManager interface {
	Publish(subject string, data interface{}) (err error)
	RegisterConsumer(subject string, consumer IConsumer) (err error)
	Start() (err error)
	Close() (err error)
}

var localQueueManager IQueueManager

func QueueManager() IQueueManager {
	if localQueueManager == nil {
		panic("queue manager not initialized")
	}
	return localQueueManager
}

// 注册队列管理器
func RegisterQueueManager(i IQueueManager) {
	localQueueManager = i
}

type QueueMsg struct {
	Subject  string // 主题
	Reply    string //
	Data     []byte // 消息数据
	Sequence uint64 // 消息序列号(对顺序敏感的消息可使用此字段进行版本验证)
}

type IConsumer interface {
	HandleMessage(ctx context.Context, msg *QueueMsg) (err error)
}
