package nats

import (
	"backend/internal/consts"
	"backend/queue"
	"encoding/json"
	"fmt"
	"runtime"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/nats-io/nats.go"
)

func init() {
	queue.RegisterQueueManager(NewNatsManager())
}

type NatsManager struct {
	consumers           map[string]queue.IConsumer
	conn                *nats.Conn
	jetStream           nats.JetStreamContext
	subscriptions       []*nats.Subscription
	concurrentConsumers int
}

func NewNatsManager() *NatsManager {
	connUrl := g.Cfg().MustGet(gctx.New(), "nats.default.url").String()
	conn, err := nats.Connect(connUrl)
	if err != nil {
		panic(fmt.Sprintf("connect to nats error: %s", err.<PERSON>rror()))
	}

	manager := &NatsManager{
		consumers:           make(map[string]queue.IConsumer),
		conn:                conn,
		concurrentConsumers: runtime.NumCPU(),
	}
	return manager
}

func (n *NatsManager) Publish(subject string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	_, err = n.jetStream.Publish(subject, jsonData)
	// g.Log().Info(gctx.New(), fmt.Sprintf("发送主题 [%s] 数据 [%s] ", subject, string(data)))
	return err
}

func (n *NatsManager) RegisterConsumer(subject string, consumer queue.IConsumer) error {
	n.consumers[subject] = consumer
	g.Log().Info(gctx.New(), fmt.Sprintf("注册主题处理器 [%s] 成功", subject))
	return nil
}

func (n *NatsManager) Start() error {
	defer n.Close()
	ctx := gctx.New()
	g.Log().Info(ctx, fmt.Sprintf("正在启动nats管理器, 主题处理器数量: %d", len(n.consumers)))

	// 设置 JetStream
	jetStream, err := n.conn.JetStream(nats.PublishAsyncMaxPending(256))
	if err != nil {
		return fmt.Errorf("JetStream 初始化错误: %w", err)
	}
	n.jetStream = jetStream

	// 创建 JetStream 流
	jetStreamInfo, err := jetStream.StreamInfo(consts.StreamName)
	if err != nil && err != nats.ErrStreamNotFound {
		return fmt.Errorf("获取 Stream 信息错误: %w", err)
	}

	if jetStreamInfo == nil {

		_, err = jetStream.AddStream(&nats.StreamConfig{
			Name:     consts.StreamName,
			Subjects: []string{consts.StreamSubjects},
			Replicas: 1,
			Storage:  nats.FileStorage,
		})
		if err != nil {
			return fmt.Errorf("创建 Stream 错误: %w", err)
		}
		g.Log().Info(ctx, fmt.Sprintf("创建 Stream 成功: %s", consts.StreamName))
	}

	version := "v1_1_3"
	startTime, _ := time.Parse("2006-01-02 15:04:05", "2025-03-03 19:00:00")
	// 订阅 JetStream （目前是一条条处理，因为没有处理消息先后顺序的问题，后续可改成使用ChanQueueSubscribe消费，然后使用channel进行批量处理）
	subscription, err := jetStream.QueueSubscribe(
		consts.StreamSubjects,
		"queue-group-"+version,
		n.handleMessage,
		nats.Durable("durable-name-"+version),
		nats.AckExplicit(),            // 每条消息都要显示确认
		nats.AckWait(120*time.Second), // 未确认消息等待时间
		nats.MaxAckPending(10),        // 最大未确认消息数量
		nats.StartTime(startTime),     // 只接受指定时间之后的消息
		// nats.DeliverNew(),          // 只接收新消息
	)
	if err != nil {
		return fmt.Errorf("启动消费者订阅主题 [%s] 错误: %w", consts.StreamSubjects, err)
	}

	n.subscriptions = append(n.subscriptions, subscription) // 保存订阅
	g.Log().Info(ctx, fmt.Sprintf("启动消费者订阅主题 [%s] 成功", consts.StreamSubjects))
	select {}
}

func (n *NatsManager) handleMessage(msg *nats.Msg) {
	ctx := gctx.New()
	if consumer, ok := n.consumers[msg.Subject]; ok {

		// 获取消息元数据
		meta, err := msg.Metadata()
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("获取消息元数据错误: %s", err))
			return
		}
		g.Log().Info(ctx, fmt.Sprintf("获取消息元数据: %+v", meta))

		sequence := meta.Sequence.Stream

		err = consumer.HandleMessage(ctx, &queue.QueueMsg{
			Data:     msg.Data,
			Subject:  msg.Subject,
			Reply:    msg.Reply,
			Sequence: sequence,
		})
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("消费主题 [%s] 错误: %s", msg.Subject, err))
			return
		}
		msg.Ack()
	} else {
		g.Log().Error(ctx, fmt.Sprintf("主题 [%s] 没有主题处理器注册", msg.Subject))
	}
}

func (n *NatsManager) Close() (err error) {
	ctx := gctx.New()
	g.Log().Info(ctx, "关闭 Nats Manager")

	for _, sub := range n.subscriptions {
		if err := sub.Unsubscribe(); err != nil {
			g.Log().Error(ctx, fmt.Sprintf("取消订阅失败: %s", err))
		}
	}

	if n.conn != nil {
		n.conn.Close()
	}

	return
}
