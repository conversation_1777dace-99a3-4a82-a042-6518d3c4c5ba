package test

import (
	"backend/internal/model/enum"
	"backend/library"
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/stretchr/testify/assert"
)

func TestSubTablesSliceToExcelFile(t *testing.T) {
	ctx := context.Background()
	// 从配置文件中获取保存路径
	exportPath := g.Cfg().MustGet(ctx, "export.path").String()
	columns := []library.ColumnConfig{
		{ColumnName: "姓名", MapKey: "name", Width: 20, IsCentered: true},
		{ColumnName: "年龄", MapKey: "age", Width: 15, IsCentered: false},
		{ColumnName: "子表1", MapKey: "table1", SubColumns: []library.ColumnConfig{
			{ColumnName: "子表1-1", MapKey: "table1-1", Width: 10, IsCentered: true},
			{ColumnName: "子表1-2", MapKey: "table1-2", Width: 10, IsCentered: true},
			{ColumnName: "子表1-3", MapKey: "table1-3", Width: 10, IsCentered: true},
		}},
		{ColumnName: "子表2", MapKey: "table2", SubColumns: []library.ColumnConfig{
			{ColumnName: "子表2-1", MapKey: "table2-1", Width: 10, IsCentered: true},
			{ColumnName: "子表2-2", MapKey: "table2-2", Width: 10, IsCentered: true},
			{ColumnName: "子表2-3", MapKey: "table2-3", Width: 10, IsCentered: true},
		}},
		{ColumnName: "年龄2", MapKey: "age", IsCentered: false},
		{ColumnName: "年龄3", MapKey: "age", IsCentered: true},
	}

	data := []map[string]interface{}{
		{"name": "Alice", "age": 30},
		{"name": "Bob", "age": 25},
		{"name": "Charlie", "age": 20, "table1": []map[string]interface{}{
			{"table1-1": "Value1", "table1-2": "Value2"},
			{"table1-1": "Value3", "table1-2": "Value4"},
		}, "table2": []map[string]interface{}{
			{"table2-1": "Value1", "table2-2": "Value2"},
			{"table2-1": "Value3", "table2-2": "Value4", "table2-3": "Value2"},
			{"table2-1": "Value5", "table2-2": "Value6"},
		}},
	}

	filePath, err := library.MapSliceToExcelFile(ctx, data, columns, true, "测试导出表格", exportPath)

	// 输出日志
	t.Logf("Generated file path: %s", filePath)

	// 检查是否生成Excel文件
	assert.NoError(t, err, "Failed to generate the Excel file")
	assert.FileExists(t, filePath, "Excel file should exist")
}

func TestMapSliceToExcelFile(t *testing.T) {
	ctx := context.Background()
	exportPath := g.Cfg().MustGet(ctx, "export.path").String()
	columns := []library.ColumnConfig{
		{ColumnName: "姓名", MapKey: "name", Width: 20, IsCentered: true},
		{ColumnName: "年龄", MapKey: "age", Width: 15, IsCentered: false},
		{ColumnName: "年龄2", MapKey: "age", IsCentered: false},
	}

	data := []map[string]interface{}{
		{"name": "Alice", "age": 30},
		{"name": "Bob", "age": 25},
	}

	filePath, err := library.MapSliceToExcelFile(ctx, data, columns, true, "测试导出表格", exportPath)

	// 输出日志
	t.Logf("Generated file path: %s", filePath)

	// 检查是否生成Excel文件
	assert.NoError(t, err, "Failed to generate the Excel file")
	assert.FileExists(t, filePath, "Excel file should exist")
}
func TestExcelToMapSlice(t *testing.T) {
	ctx := context.Background()
	// columns := []library.ColumnConfig{
	// 	{ColumnName: "姓名", MapKey: "name", Width: 20, IsCentered: true},
	// 	{ColumnName: "年龄", MapKey: "age", Width: 15, IsCentered: false},
	// 	{ColumnName: "年龄2", MapKey: "age2", IsCentered: false},
	// }

	// data := []map[string]interface{}{
	// 	{"name": "Alice", "age": 30, "age2": 1},
	// 	{"name": "Bob", "age": 25, "age2": 222},
	// }

	// filePath, err := library.MapSliceToExcelFile(ctx, data, columns, true, "测试导出表格")

	// // 输出日志
	// t.Logf("Generated file path: %s", filePath)

	// // 检查文件是否正确保存
	// assert.NoError(t, err, "Failed to save the Excel file")

	filePath := "runtime/export/20240914/1.xlsx"
	result, err := library.ExcelToMapSlice(ctx, filePath, 2)

	// 输出日志
	t.Logf("Result: %+v", result[1:3])

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 2, len(result), "Result length should be 1")
	assert.Equal(t, "Value1", result[0]["Header1"])
	assert.Equal(t, "Value2", result[0]["Header2"])
}

// func TestExcelToMapSlice(t *testing.T) {
// 	ctx := context.Background()
// 	columns := []library.ColumnConfig{
// 		{ColumnName: "姓名", MapKey: "name", Width: 20, IsCentered: true},
// 		{ColumnName: "年龄", MapKey: "age", Width: 15, IsCentered: false},
// 		{ColumnName: "年龄2", MapKey: "age2", IsCentered: false},
// 	}

// 	data := []map[string]interface{}{
// 		{"name": "Alice", "age": 30, "age2": 1},
// 		{"name": "Bob", "age": 25, "age2": 222},
// 	}

// 	filePath, err := library.MapSliceToExcelFile(ctx, data, columns, true, "测试导出表格")

// 	// 输出日志
// 	t.Logf("Generated file path: %s", filePath)

// 	// 检查文件是否正确保存
// 	assert.NoError(t, err, "Failed to save the Excel file")

// 	result, err := library.ExcelToMapSlice(ctx, filePath, 2)

// 	// 输出日志
// 	t.Logf("Result: %+v", result)

// 	assert.NoError(t, err)
// 	assert.NotNil(t, result)
// 	assert.Equal(t, 2, len(result), "Result length should be 1")
// 	assert.Equal(t, "Value1", result[0]["Header1"])
// 	assert.Equal(t, "Value2", result[0]["Header2"])
// }

func TestLibrary_CompareTime(t *testing.T) {
	leftValue := time.Now()
	rightValue := time.Now()
	t.Logf("leftValue is %v", leftValue)
	t.Logf("rightValue is %v", rightValue)
	result := library.CompareValues(enum.Operator_LessThan, leftValue, rightValue)
	t.Logf("result is %v", result)
}

func TestLibrary_CompareSlice(t *testing.T) {
	leftValue := "2021-01-05"

	rightValue := "2021-05-03 11:11:11"
	// var num1 float64 = 21555.00
	// var num2 float64 = 3000

	// num3 := gconv.Float64(leftValue)
	// num4 := gconv.Float64(rightValue)

	// t.Logf("TestLibrary_CompareSlice is  num2%v", num1 >= num2)
	// t.Logf("TestLibrary_CompareSlice is  num1%v", num3 >= num4)
	t.Logf("TestLibrary_CompareSlice is %v", gconv.Float64(leftValue) >= gconv.Float64(rightValue))
	t.Logf("TestLibrary_CompareSlice is %v", fmt.Sprintf("%v", gconv.Float64(leftValue)) >= fmt.Sprintf("%v", gconv.Float64(rightValue)))

	t.Logf("leftValue is %v", leftValue)
	t.Logf("rightValue is %v", rightValue)
	result := library.CompareValues(enum.Operator_GreaterThanOrEqual, leftValue, rightValue)
	t.Logf("result is %v", result)
}
func toInterfaceSlice(slice interface{}) []interface{} {
	s := reflect.ValueOf(slice)

	if s.Kind() != reflect.Slice {
		panic("InterfaceSlice() given a non-slice type")
	}

	ret := make([]interface{}, s.Len())

	for i := 0; i < s.Len(); i++ {
		ret[i] = s.Index(i).Interface()
	}

	return ret
}
