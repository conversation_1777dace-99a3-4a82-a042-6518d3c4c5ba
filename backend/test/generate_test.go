package test

import (
	_ "backend/internal/logic"  // 注册 业务逻辑
	_ "backend/internal/packed" // 注册 资源
	"backend/internal/service"
	"backend/queue"
	_ "backend/queue/nats"       // 注册 nats 的队列管理器 （可以实现 kafka 等其他队列管理器，然后在此注册）
	_ "backend/queue/z_consumer" // 注册 消费者 （为了确保编辑器不会自动把这样注册格式化到注册队列管理器前面，在它前面加个 z_ 前缀）
	"testing"
	"time"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2" // 注册 mysql 驱动
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"   // 注册 redis 驱动
	"github.com/gogf/gf/frame/g"

	"github.com/gogf/gf/v2/os/gctx"
)

// 验证编号器
func TestGenerateID(t *testing.T) {
	ctx := gctx.New()
	// 启动队列管理器
	go func() {
		_err := queue.QueueManager().Start()
		if _err != nil {
			g.Log().Error(ctx, "queue manager start error", _err)
		}
	}()
	rule := "dsfsd20060102150406"
	now := time.Now()
	dateStr := now.Format(rule)
	t.Log(dateStr)
	rule = "R{RanStr(5)}{20060102150406}{RanNum(5)}{AUTO_INCREMENT({2006},5)}"
	newid := service.Generate().GenerateID(ctx, rule)
	t.Log(newid)
}
