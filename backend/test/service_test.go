package test

import (
	_ "backend/internal/logic" // 注册 业务逻辑
	"backend/internal/service"

	// _ "backend/internal/packed"  // 注册 资源
	// _ "backend/queue/nats"       // 注册 nats 的队列管理器 （可以实现 kafka 等其他队列管理器，然后在此注册）
	// _ "backend/queue/z_consumer" // 注册 消费者 （为了确保编辑器不会自动把这样注册格式化到注册队列管理器前面，在它前面加个 z_ 前缀）
	"context"
	"testing"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2" // 注册 mysql 驱动
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"   // 注册 redis 驱动
	"github.com/gogf/gf/v2/frame/g"
)

// 测试验证规则的方法
func TestVRust(t *testing.T) {

	type BizReq struct {
		Age1 int         `v:"min:100"`
		Id   interface{} `v:"in:0,1#开启审核流程设置数据格式错误"`
	}

	var (
		ctx = context.Background()
		req = BizReq{
			Age1: 101,
			Id:   "01",
		}
	)
	t.Log("sdfsdfsdfs")
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		t.Logf("%s", err.Strings())
	}

}

func TestGetDetailByFilter(t *testing.T) {
	var (
		ctx = context.Background()
	)
	t.Log("TestGetDetailByFilter")
	service.FormData().GetDetailByFilter(ctx, 39, map[string]interface{}{
		"node_ocm009lpxt6": nil,
	})
}
