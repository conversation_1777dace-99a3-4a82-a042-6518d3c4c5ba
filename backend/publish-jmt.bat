@echo off
chcp 936 >nul
setlocal

:: 设置 SSH 和 SCP 的基本参数
set "SSH_PORT=27601"
set "HOST=jmt@**************"

echo ======================================
echo 开始发布流程，请确保已配置好 SSH 密钥认证
echo ======================================

:: 发布 console
echo.
echo [步骤1] 正在发布 console 到 jmt 服务器...
ssh -p %SSH_PORT% %HOST% "rm -rf /var/www/crm/console"
if errorlevel 1 (
    echo [错误] SSH 命令执行失败
    pause
    exit /b 1
)

scp -P %SSH_PORT% -r ../deployment/jmt/dist %HOST%:/var/www/crm/console
if errorlevel 1 (
    echo [错误] SCP 传输失败
    pause
    exit /b 1
)
echo [成功] console 发布完成.

:: 发布 console-api
echo.
echo [步骤2] 正在发布 console-api 到 jmt 服务器...
scp -P %SSH_PORT% -r ../deployment/jmt/api/* %HOST%:/var/www/crm/console_api/
if errorlevel 1 (
    echo [错误] SCP 传输失败
    pause
    exit /b 1
)

ssh -p %SSH_PORT% %HOST% "cd /var/www/crm && sudo docker-compose up --build -d"
if errorlevel 1 (
    echo [错误] Docker 命令执行失败
    pause
    exit /b 1
)
echo [成功] console-api 发布完成.

echo.
echo ======================================
echo 所有发布任务已完成！
echo ======================================
echo.
pause
endlocal 