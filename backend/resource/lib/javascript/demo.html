<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="./dayjs.min.js" type="text/javascript"></script>
    <script src="./lodash.min.js" type="text/javascript"></script>
    <script src="./nzh.min.js" type="text/javascript"></script>
    <script src="./crypto-js.min.js" type="text/javascript"></script>
    <script>

        console.log(CryptoJS.MD5("123456").toString())
        console.log(dayjs('2018-05-05').locale('zh-cn').format())
        console.log(dayjs().add(8, 'hour').format('YYYY-MM-DD HH:mm:ss'))
        console.log(dayjs('2018-05-05').format('YYYY-MM'))

        console.log(_.chunk(['a', 'b', 'c', 'd'], 2))
        console.log(_.filter(['a', 'b', 'c', 'd'], item => item == "a"))


        console.log(Nzh.cn.encodeB(100111));
        console.log(Nzh.cn.toMoney("1.222", { outSymbol: false }));
        console.log(Nzh.cn.toMoney("100111", { outSymbol: false }));
        let orderProductList = [
            { node_oclzmgoa7m17: { value: "node_oclzmgoa7m17_value_1" }, node_oclzmgoa7m4j: "10" },
            { node_oclzmgoa7m17: { value: "node_oclzmgoa7m17_value_1" }, node_oclzmgoa7m4j: "15" },
            { node_oclzmgoa7m17: { value: "node_oclzmgoa7m17_value_2" }, node_oclzmgoa7m4j: "12" }
        ]
        // 按 productId 分组
        let groupedProducts = _.groupBy(orderProductList, orderProduct => orderProduct?.node_oclzmgoa7m17?.value)


        console.log("groupedProducts", groupedProducts);
        let result = _.reduce(groupedProducts, (acc, products, productId) => {
            let totalOrderAmount = _.sumBy(products, product => parseFloat(product?.node_oclzmgoa7m4j))
            let outedSumAmount = 24
            let unOutAmount = totalOrderAmount - outedSumAmount
            if (unOutAmount >= 0) return acc + unOutAmount
            return acc
        }, 0)
        console.log("result", result);


        let productionDate = "2024-12-06 00:00:00";
        let shelfLifeMonths = "12"; // 确保是数字

        if (productionDate && shelfLifeMonths) {
            let expirationDate = dayjs(productionDate).add(shelfLifeMonths, 'month').format('YYYY-MM-DD');
            console.log(expirationDate);
        } else {
            console.log('2099-01-01');
        }

    </script>
</head>

<body>

</body>

</html>