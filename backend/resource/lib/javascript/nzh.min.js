/*!
 * nzh v1.0.13
 * Homepage http://cnwhy.github.io/nzh
 * License BSD-2-Clause
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Nzh=e()}(this,function(){"use strict";function t(t,e){var r=h.getNumbResult(t);if(!r)return t;e=e||{};var n=this.ch,i=this.ch_u,c=this.ch_f||"",u=this.ch_d||".",a=n.charAt(0),s=r.int,o=r.decimal,l=r.minus,f="",g="",p=l?c:"";if(o){o=h.clearZero(o,"0","$");for(var d=0;d<o.length;d++)g+=n.charAt(+o.charAt(d));g=g?u+g:""}if(f=function t(r,c,u){r=h.getNumbResult(r).int;var s="",o=arguments.length>1?arguments[1]:e.tenMin,l=r.length;if(1==l)return n.charAt(+r);if(l<=4)for(var f=0,g=l;g--;){var p=+r.charAt(f);s+=o&&2==l&&0==f&&1==p?"":n.charAt(p),s+=p&&g?i.charAt(g):"",f++}else{for(var d=r.length/4>>0,_=r.length%4;0==_||!i.charAt(3+d);)_+=4,d--;var m=r.substr(0,_),A=r.substr(_);s=t(m,o)+i.charAt(3+d)+("0"==A.charAt(0)?a:"")+t(A,A.length>4&&o)}return s=h.clearZero(s,a)}(s),e.ww&&i.length>5){var _=i.charAt(4),m=i.charAt(5),A=f.lastIndexOf(m);~A&&(f=f.substring(0,A).replace(new RegExp(m,"g"),_+_)+f.substring(A))}return p+f+g}function e(t){t=t.toString();var e=t.split(this.ch_d),r=e[0].replace(this.ch_f,""),n=e[1],i=!!~e[0].indexOf(this.ch_f),c=this.ch_u.charAt(1),u=this.ch_u.charAt(4),a=this.ch_u.charAt(5);r=r.replace(new RegExp(u+"{2}","g"),a);for(var s=r.split(""),o=0,l=0,f=[],g=[],p=[],d=0;d<s.length;d++){var _=s[d],m=0,A=0;if(~(m=this.ch.indexOf(_)))m>0&&p.unshift(m);else if(~(A=this.ch_u.indexOf(_))){var v=h.getDigit(A);o>A?(h.unshiftZero(p,v),h.centerArray(g,p)):A>=l?(0==d&&(p=[1]),h.centerArray(f,g,p),f.length>0&&h.unshiftZero(f,v),l=A):(0==p.length&&c==_&&(p=[1]),h.centerArray(g,p),h.unshiftZero(g,h.getDigit(A)),o=A)}}h.centerArray(f,g,p).reverse(),0==f.length&&f.push(0);var y=0;if(n){f.push("."),y="0.";for(var d=0;d<n.length;d++)y+=this.ch.indexOf(n.charAt(d)),f.push(this.ch.indexOf(n.charAt(d)));y=+y}return i&&f.unshift("-"),parseFloat(f.join(""))}function r(e,r){var n={ww:!0,complete:!1,outSymbol:!0,unOmitYuan:!1,forceZheng:!1},i=h.getNumbResult(e),c=this.ch.charAt(0);if(r="object"==typeof r?r:{},!i)return e;r=h.extend(n,r);var u=i.int,a=i.decimal||"",s=r.outSymbol?this.m_t:"",o=i.minus?this.ch_f:"",l="";if(r.complete){for(var f=1;f<this.m_u.length;f++)l+=t.call(this,a.charAt(f-1)||"0")+this.m_u.charAt(f);o+=t.call(this,u,r)+this.m_u.charAt(0)}else{var g=r.unOmitYuan||"0"!==u;if(a=a.substr(0,this.m_u.length-1),a=h.clearZero(a,"0","$"))for(var p,f=0;f<this.m_u.length-1;f++)a.charAt(f)&&"0"!=a.charAt(f)&&(l+=t.call(this,a.charAt(f))+this.m_u.charAt(f+1),p=!1),"0"!==a.charAt(f)||p||(0==f&&"0"===u||(l+=c),p=!0);!g&&l||(o+=t.call(this,u,r)+this.m_u.charAt(0)),r.forceZheng?""!=l&&l.charAt(l.length-1)===this.m_u[2]||(l+=this.m_z):o+=i.decimal?"":this.m_z,r.forceZheng}return s+o+l}function n(t,e){return{encodeS:function(e,r){return r=h.extend({ww:!0,tenMin:!0},r),i.CL.call(t,e,r)},encodeB:function(t,r){return r=h.extend({ww:!0},r),i.CL.call(e,t,r)},decodeS:function(){return i.unCL.apply(t,arguments)},decodeB:function(){return i.unCL.apply(e,arguments)},toMoney:function(t,r){return r=h.extend({ww:!0},r),i.toMoney.call(e,t,r)}}}var h=function(t,e){return e={exports:{}},t(e,e.exports),e.exports}(function(t,e){var r=/^([+-])?0*(\d+)(\.(\d+))?$/,n=/^([+-])?0*(\d+)(\.(\d+))?e(([+-])?(\d+))$/i,h=e.e2ten=function(t){var e=n.exec(t.toString());if(!e)return t;var r=e[2],h=e[4]||"",i=e[5]?+e[5]:0;if(i>0){var c=h.substr(0,i);c=c.length<i?c+new Array(i-c.length+1).join("0"):c,h=h.substr(i),r+=c}else{i=-i;var u=r.length-i;u=u<0?0:u;var a=r.substr(u,i);a=a.length<i?new Array(i-a.length+1).join("0")+a:a,r=r.substring(0,u),h=a+h}return r=""==r?"0":r,("-"==e[1]?"-":"")+r+(h?"."+h:"")};e.getNumbResult=function(t){var e=r.exec(t.toString());if(!e&&n.test(t.toString())&&(e=r.exec(h(t.toString()))),e)return{int:e[2],decimal:e[4],minus:"-"==e[1],num:e.slice(1,3).join("")}},e.centerArray=function t(e,r){if(e.splice.apply(e,[0,r.length].concat(r.splice(0,r.length))),arguments.length>2){var n=[].slice.call(arguments,2);n.unshift(e),t.apply(null,n)}return e};var i=e.hasAttr=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};e.extend=function(t){for(var e,r=arguments[0]||{},n=Array.prototype.slice.call(arguments,1),h=0;h<n.length;h++){var c=n[h];for(e in c)i(c,e)&&(r[e]=c[e])}return r},e.getDigit=function(t){return t>=5?4*(t-4)+4:t},e.unshiftZero=function(t,e){if(null==e&&(e=1),!(e<=0))for(;e--;)t.unshift(0)},e.clearZero=function(t,e,r){if(null==t)return"";var n=~"*.?+$^[](){}|\\/".indexOf(e)?"\\"+e:e,h=new RegExp("^"+n+"+"),i=new RegExp(n+"+$"),c=new RegExp(n+"{2}","g");return t=t.toString(),"^"==r&&(t=t.replace(h,"")),r&&"$"!=r||(t=t.replace(i,"")),r&&"nto1"!=r||(t=t.replace(c,e)),t}}),i=(h.e2ten,h.getNumbResult,h.centerArray,h.hasAttr,h.extend,h.getDigit,h.unshiftZero,h.clearZero,{CL:t,unCL:e,toMoney:r}),c=n,u={ch:"零一二三四五六七八九",ch_u:"个十百千万亿",ch_f:"负",ch_d:"点"},a={ch:"零壹贰叁肆伍陆柒捌玖",ch_u:"个拾佰仟万亿",ch_f:"负",ch_d:"点",m_t:"人民币",m_z:"整",m_u:"元角分"},s={ch:"零一二三四五六七八九",ch_u:"個十百千萬億",ch_f:"負",ch_d:"點"},o={ch:"零壹貳參肆伍陸柒捌玖",ch_u:"個拾佰仟萬億",ch_f:"負",ch_d:"點",m_t:"$",m_z:"整",m_u:"圓角分"},l={s:u,b:a,hk_s:s,hk_b:o},f=function(t){this.lang=t,this.encode=function(){return i.CL.apply(t,arguments)},this.decode=function(){return i.unCL.apply(t,arguments)},this.toMoney=function(){return i.toMoney.apply(t,arguments)}};return f.langs=l,f.cn=c(l.s,l.b),f.hk=c(l.hk_s,l.hk_b),f});