// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalCodeHistoryDao is internal type for wrapping internal DAO implements.
type internalCodeHistoryDao = *internal.CodeHistoryDao

// codeHistoryDao is the data access object for table code_history.
// You can define custom methods on it to extend its functionality as you wish.
type codeHistoryDao struct {
	internalCodeHistoryDao
}

var (
	// CodeHistory is globally public accessible object for table code_history operations.
	CodeHistory = codeHistoryDao{
		internal.NewCodeHistoryDao(),
	}
)

// Fill with you ideas below.
