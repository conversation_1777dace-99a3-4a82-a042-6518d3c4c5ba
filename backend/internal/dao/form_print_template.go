// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormPrintTemplateDao is internal type for wrapping internal DAO implements.
type internalFormPrintTemplateDao = *internal.FormPrintTemplateDao

// formPrintTemplateDao is the data access object for table form_print_template.
// You can define custom methods on it to extend its functionality as you wish.
type formPrintTemplateDao struct {
	internalFormPrintTemplateDao
}

var (
	// FormPrintTemplate is globally public accessible object for table form_print_template operations.
	FormPrintTemplate = formPrintTemplateDao{
		internal.NewFormPrintTemplateDao(),
	}
)

// Fill with you ideas below.
