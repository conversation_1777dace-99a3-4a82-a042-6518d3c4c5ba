// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormPrintLogsDao is internal type for wrapping internal DAO implements.
type internalFormPrintLogsDao = *internal.FormPrintLogsDao

// formPrintLogsDao is the data access object for table form_print_logs.
// You can define custom methods on it to extend its functionality as you wish.
type formPrintLogsDao struct {
	internalFormPrintLogsDao
}

var (
	// FormPrintLogs is globally public accessible object for table form_print_logs operations.
	FormPrintLogs = formPrintLogsDao{
		internal.NewFormPrintLogsDao(),
	}
)

// Fill with you ideas below.
