// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalSysUserRoleDao is internal type for wrapping internal DAO implements.
type internalSysUserRoleDao = *internal.SysUserRoleDao

// sysUserRoleDao is the data access object for table sys_user_role.
// You can define custom methods on it to extend its functionality as you wish.
type sysUserRoleDao struct {
	internalSysUserRoleDao
}

var (
	// SysUserRole is globally public accessible object for table sys_user_role operations.
	SysUserRole = sysUserRoleDao{
		internal.NewSysUserRoleDao(),
	}
)

// Fill with you ideas below.
