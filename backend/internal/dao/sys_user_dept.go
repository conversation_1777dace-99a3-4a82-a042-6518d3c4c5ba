// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalSysUserDeptDao is internal type for wrapping internal DAO implements.
type internalSysUserDeptDao = *internal.SysUserDeptDao

// sysUserDeptDao is the data access object for table sys_user_dept.
// You can define custom methods on it to extend its functionality as you wish.
type sysUserDeptDao struct {
	internalSysUserDeptDao
}

var (
	// SysUserDept is globally public accessible object for table sys_user_dept operations.
	SysUserDept = sysUserDeptDao{
		internal.NewSysUserDeptDao(),
	}
)

// Fill with you ideas below.
