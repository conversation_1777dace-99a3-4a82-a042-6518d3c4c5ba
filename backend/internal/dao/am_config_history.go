// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalAmConfigHistoryDao is internal type for wrapping internal DAO implements.
type internalAmConfigHistoryDao = *internal.AmConfigHistoryDao

// amConfigHistoryDao is the data access object for table am_config_history.
// You can define custom methods on it to extend its functionality as you wish.
type amConfigHistoryDao struct {
	internalAmConfigHistoryDao
}

var (
	// AmConfigHistory is globally public accessible object for table am_config_history operations.
	AmConfigHistory = amConfigHistoryDao{
		internal.NewAmConfigHistoryDao(),
	}
)

// Fill with you ideas below.
