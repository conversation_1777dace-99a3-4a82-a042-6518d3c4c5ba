// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormCountersignLogsDao is internal type for wrapping internal DAO implements.
type internalFormCountersignLogsDao = *internal.FormCountersignLogsDao

// formCountersignLogsDao is the data access object for table form_countersign_logs.
// You can define custom methods on it to extend its functionality as you wish.
type formCountersignLogsDao struct {
	internalFormCountersignLogsDao
}

var (
	// FormCountersignLogs is globally public accessible object for table form_countersign_logs operations.
	FormCountersignLogs = formCountersignLogsDao{
		internal.NewFormCountersignLogsDao(),
	}
)

// Fill with you ideas below.
