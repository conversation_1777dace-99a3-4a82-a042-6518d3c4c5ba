// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFlowInstanceHistoryDao is internal type for wrapping internal DAO implements.
type internalFlowInstanceHistoryDao = *internal.FlowInstanceHistoryDao

// flowInstanceHistoryDao is the data access object for table flow_instance_history.
// You can define custom methods on it to extend its functionality as you wish.
type flowInstanceHistoryDao struct {
	internalFlowInstanceHistoryDao
}

var (
	// FlowInstanceHistory is globally public accessible object for table flow_instance_history operations.
	FlowInstanceHistory = flowInstanceHistoryDao{
		internal.NewFlowInstanceHistoryDao(),
	}
)

// Fill with you ideas below.
