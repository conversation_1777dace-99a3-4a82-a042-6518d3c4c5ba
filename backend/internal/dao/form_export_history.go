// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormExportHistoryDao is internal type for wrapping internal DAO implements.
type internalFormExportHistoryDao = *internal.FormExportHistoryDao

// formExportHistoryDao is the data access object for table form_export_history.
// You can define custom methods on it to extend its functionality as you wish.
type formExportHistoryDao struct {
	internalFormExportHistoryDao
}

var (
	// FormExportHistory is globally public accessible object for table form_export_history operations.
	FormExportHistory = formExportHistoryDao{
		internal.NewFormExportHistoryDao(),
	}
)

// Fill with you ideas below.
