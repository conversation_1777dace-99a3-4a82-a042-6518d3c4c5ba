// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormCheckRuleDao is internal type for wrapping internal DAO implements.
type internalFormCheckRuleDao = *internal.FormCheckRuleDao

// formCheckRuleDao is the data access object for table form_check_rule.
// You can define custom methods on it to extend its functionality as you wish.
type formCheckRuleDao struct {
	internalFormCheckRuleDao
}

var (
	// FormCheckRule is globally public accessible object for table form_check_rule operations.
	FormCheckRule = formCheckRuleDao{
		internal.NewFormCheckRuleDao(),
	}
)

// Fill with you ideas below.
