// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormTemplateDao is internal type for wrapping internal DAO implements.
type internalFormTemplateDao = *internal.FormTemplateDao

// formTemplateDao is the data access object for table form_template.
// You can define custom methods on it to extend its functionality as you wish.
type formTemplateDao struct {
	internalFormTemplateDao
}

var (
	// FormTemplate is globally public accessible object for table form_template operations.
	FormTemplate = formTemplateDao{
		internal.NewFormTemplateDao(),
	}
)

// Fill with you ideas below.
