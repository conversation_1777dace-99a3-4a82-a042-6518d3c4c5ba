// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalSysUserProjectDao is internal type for wrapping internal DAO implements.
type internalSysUserProjectDao = *internal.SysUserProjectDao

// sysUserProjectDao is the data access object for table sys_user_project.
// You can define custom methods on it to extend its functionality as you wish.
type sysUserProjectDao struct {
	internalSysUserProjectDao
}

var (
	// SysUserProject is globally public accessible object for table sys_user_project operations.
	SysUserProject = sysUserProjectDao{
		internal.NewSysUserProjectDao(),
	}
)

// Fill with you ideas below.
