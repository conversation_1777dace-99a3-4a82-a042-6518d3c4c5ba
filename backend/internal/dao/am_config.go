// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalAmConfigDao is internal type for wrapping internal DAO implements.
type internalAmConfigDao = *internal.AmConfigDao

// amConfigDao is the data access object for table am_config.
// You can define custom methods on it to extend its functionality as you wish.
type amConfigDao struct {
	internalAmConfigDao
}

var (
	// AmConfig is globally public accessible object for table am_config operations.
	AmConfig = amConfigDao{
		internal.NewAmConfigDao(),
	}
)

// Fill with you ideas below.
