// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalAdProjectDao is internal type for wrapping internal DAO implements.
type internalAdProjectDao = *internal.AdProjectDao

// adProjectDao is the data access object for table ad_project.
// You can define custom methods on it to extend its functionality as you wish.
type adProjectDao struct {
	internalAdProjectDao
}

var (
	// AdProject is globally public accessible object for table ad_project operations.
	AdProject = adProjectDao{
		internal.NewAdProjectDao(),
	}
)

// Fill with you ideas below.
