// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFlowTemplateDao is internal type for wrapping internal DAO implements.
type internalFlowTemplateDao = *internal.FlowTemplateDao

// flowTemplateDao is the data access object for table flow_template.
// You can define custom methods on it to extend its functionality as you wish.
type flowTemplateDao struct {
	internalFlowTemplateDao
}

var (
	// FlowTemplate is globally public accessible object for table flow_template operations.
	FlowTemplate = flowTemplateDao{
		internal.NewFlowTemplateDao(),
	}
)

// Fill with you ideas below.
