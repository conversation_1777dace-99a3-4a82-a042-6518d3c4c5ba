// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalSysPostRoleDao is internal type for wrapping internal DAO implements.
type internalSysPostRoleDao = *internal.SysPostRoleDao

// sysPostRoleDao is the data access object for table sys_post_role.
// You can define custom methods on it to extend its functionality as you wish.
type sysPostRoleDao struct {
	internalSysPostRoleDao
}

var (
	// SysPostRole is globally public accessible object for table sys_post_role operations.
	SysPostRole = sysPostRoleDao{
		internal.NewSysPostRoleDao(),
	}
)

// Fill with you ideas below.
