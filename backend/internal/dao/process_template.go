// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalProcessTemplateDao is internal type for wrapping internal DAO implements.
type internalProcessTemplateDao = *internal.ProcessTemplateDao

// processTemplateDao is the data access object for table process_template.
// You can define custom methods on it to extend its functionality as you wish.
type processTemplateDao struct {
	internalProcessTemplateDao
}

var (
	// ProcessTemplate is globally public accessible object for table process_template operations.
	ProcessTemplate = processTemplateDao{
		internal.NewProcessTemplateDao(),
	}
)

// Fill with you ideas below.
