// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFlowInstanceHistoryAuditorDao is internal type for wrapping internal DAO implements.
type internalFlowInstanceHistoryAuditorDao = *internal.FlowInstanceHistoryAuditorDao

// flowInstanceHistoryAuditorDao is the data access object for table flow_instance_history_auditor.
// You can define custom methods on it to extend its functionality as you wish.
type flowInstanceHistoryAuditorDao struct {
	internalFlowInstanceHistoryAuditorDao
}

var (
	// FlowInstanceHistoryAuditor is globally public accessible object for table flow_instance_history_auditor operations.
	FlowInstanceHistoryAuditor = flowInstanceHistoryAuditorDao{
		internal.NewFlowInstanceHistoryAuditorDao(),
	}
)

// Fill with you ideas below.
