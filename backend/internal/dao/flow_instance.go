// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFlowInstanceDao is internal type for wrapping internal DAO implements.
type internalFlowInstanceDao = *internal.FlowInstanceDao

// flowInstanceDao is the data access object for table flow_instance.
// You can define custom methods on it to extend its functionality as you wish.
type flowInstanceDao struct {
	internalFlowInstanceDao
}

var (
	// FlowInstance is globally public accessible object for table flow_instance operations.
	FlowInstance = flowInstanceDao{
		internal.NewFlowInstanceDao(),
	}
)

// Fill with you ideas below.
