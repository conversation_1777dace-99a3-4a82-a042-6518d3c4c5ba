// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalAdProjectRoleDao is internal type for wrapping internal DAO implements.
type internalAdProjectRoleDao = *internal.AdProjectRoleDao

// adProjectRoleDao is the data access object for table ad_project_role.
// You can define custom methods on it to extend its functionality as you wish.
type adProjectRoleDao struct {
	internalAdProjectRoleDao
}

var (
	// AdProjectRole is globally public accessible object for table ad_project_role operations.
	AdProjectRole = adProjectRoleDao{
		internal.NewAdProjectRoleDao(),
	}
)

// Fill with you ideas below.
