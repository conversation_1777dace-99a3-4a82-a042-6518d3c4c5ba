package dao

import (
	"backend/internal/consts"
	"backend/library"
	"context"
	"database/sql"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

type ITenantDao interface {
	Table() string
}

/**
 * 通过实现一个工具函数的方式修改ctx，使其支持多租户模式
 */
func TenantCtx(d ITenantDao, ctx context.Context) *gdb.Model {
	return TenantTableCtx(d.Table(), ctx)
}

func TenantTableCtx(tableName string, ctx context.Context) *gdb.Model {
	// // 或者可以通过这种方式修改租户的数据库，让某个租户拥有独立数据库
	// tenantDB := ctx.Value("tenantdb")
	// if tenantDB != nil {
	// 	return g.DB(tenantDB.(string)).Model(d.Table()).Safe().Ctx(ctx)
	// }
	tenant_id := library.GetTenantID(ctx)
	// Where能兼容多租户的变更和修改，但是不兼容新增，新增使用Hook实现
	hasTenantID := IsFieldExist(ctx, tableName, consts.TENANT_ID)
	// g.Log().Info(ctx, "hasTenantID******************************** ", hasTenantID)
	if tenant_id > 0 && hasTenantID {
		return g.DB().Model(tableName).Safe().Ctx(ctx).Where(fmt.Sprintf("`%s` = ?", consts.TENANT_ID), tenant_id).Hook(gdb.HookHandler{
			Insert: func(ctx context.Context, in *gdb.HookInsertInput) (result sql.Result, err error) {
				var newDatas []map[string]interface{}
				for _, row := range in.Data {
					if _, ok := row[consts.TENANT_ID]; !ok {
						row[consts.TENANT_ID] = tenant_id
					}
					newDatas = append(newDatas, row)
				}
				in.Data = newDatas
				result, err = in.Next(ctx)
				return
			},
		})
	}
	return g.DB().Model(tableName).Safe().Ctx(ctx)
}
func IsFieldExist(ctx context.Context, tableName string, fieldName string) bool {
	tableFields, err := g.DB().TableFields(ctx, tableName)
	if err != nil {
		return false
	}
	for _, field := range tableFields {
		if field.Name == fieldName {
			return true
		}
	}
	return false
}
