// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormPrintLogsDao is the data access object for table form_print_logs.1111111111111111111111111111111111111111
type FormPrintLogsDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of current DAO.
	columns FormPrintLogsColumns // columns contains all the column names of Table for convenient usage.
}

// FormPrintLogsColumns defines and stores column names for table form_print_logs.
type FormPrintLogsColumns struct {
	Id                  string // id
	FormTemplateId      string // 表单模板id
	FormDataId          string // 表单数据id
	FormPrintTemplateId string // 打印模板id
	PrintCount          string // 打印次数
	LastPrintedAt       string // 最后打印时间
	PrintDetails        string // 打印明细记录
	CreatedBy           string // 创建人
	CreatedAt           string // 创建时间
	UpdatedAt           string // 更新时间
	DeletedAt           string // 删除时间
	TenantId            string // 数据租户id
}

// formPrintLogsColumns holds the columns for table form_print_logs.
var formPrintLogsColumns = FormPrintLogsColumns{
	Id:                  "id",
	FormTemplateId:      "form_template_id",
	FormDataId:          "form_data_id",
	FormPrintTemplateId: "form_print_template_id",
	PrintCount:          "print_count",
	LastPrintedAt:       "last_printed_at",
	PrintDetails:        "print_details",
	CreatedBy:           "created_by",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	DeletedAt:           "deleted_at",
	TenantId:            "tenant_id",
}

// NewFormPrintLogsDao creates and returns a new DAO object for table data access.
func NewFormPrintLogsDao() *FormPrintLogsDao {
	return &FormPrintLogsDao{
		group:   "default",
		table:   "form_print_logs",
		columns: formPrintLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormPrintLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormPrintLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormPrintLogsDao) Columns() FormPrintLogsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormPrintLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormPrintLogsDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormPrintLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
