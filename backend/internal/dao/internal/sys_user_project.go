// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysUserProjectDao is the data access object for table sys_user_project.1111111111111111111111111111111111111111
type SysUserProjectDao struct {
	table   string                // table is the underlying table name of the DAO.
	group   string                // group is the database configuration group name of current DAO.
	columns SysUserProjectColumns // columns contains all the column names of Table for convenient usage.
}

// SysUserProjectColumns defines and stores column names for table sys_user_project.
type SysUserProjectColumns struct {
	Id            string // 关联ID
	UserId        string // 用户ID
	ProjectId     string // 项目id
	ProjectRoleId string // 项目角色id
}

// sysUserProjectColumns holds the columns for table sys_user_project.
var sysUserProjectColumns = SysUserProjectColumns{
	Id:            "id",
	UserId:        "user_id",
	ProjectId:     "project_id",
	ProjectRoleId: "project_role_id",
}

// NewSysUserProjectDao creates and returns a new DAO object for table data access.
func NewSysUserProjectDao() *SysUserProjectDao {
	return &SysUserProjectDao{
		group:   "default",
		table:   "sys_user_project",
		columns: sysUserProjectColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysUserProjectDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysUserProjectDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysUserProjectDao) Columns() SysUserProjectColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysUserProjectDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysUserProjectDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysUserProjectDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
