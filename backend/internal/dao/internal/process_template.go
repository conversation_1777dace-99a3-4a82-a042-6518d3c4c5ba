// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ProcessTemplateDao is the data access object for table process_template.1111111111111111111111111111111111111111
type ProcessTemplateDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns ProcessTemplateColumns // columns contains all the column names of Table for convenient usage.
}

// ProcessTemplateColumns defines and stores column names for table process_template.
type ProcessTemplateColumns struct {
	Id          string // id
	Name        string // 模版名
	Description string // 流程说明
	Settleinfo  string // 流程设置（JSON）
	Nodes       string // 流程节点设置（JSON）
	CreatedBy   string // 创建人
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
	DeletedAt   string // 删除时间
	TenantId    string // 数据租户id
}

// processTemplateColumns holds the columns for table process_template.
var processTemplateColumns = ProcessTemplateColumns{
	Id:          "id",
	Name:        "name",
	Description: "description",
	Settleinfo:  "settleinfo",
	Nodes:       "nodes",
	CreatedBy:   "created_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	TenantId:    "tenant_id",
}

// NewProcessTemplateDao creates and returns a new DAO object for table data access.
func NewProcessTemplateDao() *ProcessTemplateDao {
	return &ProcessTemplateDao{
		group:   "default",
		table:   "process_template",
		columns: processTemplateColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ProcessTemplateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ProcessTemplateDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ProcessTemplateDao) Columns() ProcessTemplateColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ProcessTemplateDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ProcessTemplateDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *ProcessTemplateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
