// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AmConfigDao is the data access object for table am_config.1111111111111111111111111111111111111111
type AmConfigDao struct {
	table   string          // table is the underlying table name of the DAO.
	group   string          // group is the database configuration group name of current DAO.
	columns AmConfigColumns // columns contains all the column names of Table for convenient usage.
}

// AmConfigColumns defines and stores column names for table am_config.
type AmConfigColumns struct {
	Id          string // id
	Name        string // 自动化名称
	Description string // 自动化说明
	FormId      string // 关联的表单id
	TriggerData string // 触发器数据
	FlowSchema  string // 自动化流程设计整体结构
	Status      string // 状态;1 正常 999 停止
	CreatedBy   string // 创建人
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
	DeletedAt   string // 删除时间
	TenantId    string // 数据租户id
	Version     string // 当前版本
	Depend      string // 依赖的其他流程id（定义执行顺序）
}

// amConfigColumns holds the columns for table am_config.
var amConfigColumns = AmConfigColumns{
	Id:          "id",
	Name:        "name",
	Description: "description",
	FormId:      "form_id",
	TriggerData: "trigger_data",
	FlowSchema:  "flow_schema",
	Status:      "status",
	CreatedBy:   "created_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	TenantId:    "tenant_id",
	Version:     "version",
	Depend:      "depend",
}

// NewAmConfigDao creates and returns a new DAO object for table data access.
func NewAmConfigDao() *AmConfigDao {
	return &AmConfigDao{
		group:   "default",
		table:   "am_config",
		columns: amConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AmConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AmConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AmConfigDao) Columns() AmConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AmConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AmConfigDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AmConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
