// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormImportHistoryDao is the data access object for table form_import_history.1111111111111111111111111111111111111111
type FormImportHistoryDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of current DAO.
	columns FormImportHistoryColumns // columns contains all the column names of Table for convenient usage.
}

// FormImportHistoryColumns defines and stores column names for table form_import_history.
type FormImportHistoryColumns struct {
	Id                   string // id
	FormImportTemplateId string // 导入的数据模板id
	FormTemplateId       string // 导入的表单模板id
	ImportFile           string // 导入的文件
	Status               string // 状态
	Result               string // 结果信息
	TotalCount           string // 总数
	CompletedCount       string // 已处理数
	SuccessCount         string // 成功数
	FailureCount         string // 失败数
	FinishdAt            string // 完成时间
	CreatedBy            string // 创建人
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
	DeletedAt            string // 删除时间
	TenantId             string // 数据租户id
	CurrentStep          string // 当前步骤
}

// formImportHistoryColumns holds the columns for table form_import_history.
var formImportHistoryColumns = FormImportHistoryColumns{
	Id:                   "id",
	FormImportTemplateId: "form_import_template_id",
	FormTemplateId:       "form_template_id",
	ImportFile:           "import_file",
	Status:               "status",
	Result:               "result",
	TotalCount:           "total_count",
	CompletedCount:       "completed_count",
	SuccessCount:         "success_count",
	FailureCount:         "failure_count",
	FinishdAt:            "finishd_at",
	CreatedBy:            "created_by",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	DeletedAt:            "deleted_at",
	TenantId:             "tenant_id",
	CurrentStep:          "current_step",
}

// NewFormImportHistoryDao creates and returns a new DAO object for table data access.
func NewFormImportHistoryDao() *FormImportHistoryDao {
	return &FormImportHistoryDao{
		group:   "default",
		table:   "form_import_history",
		columns: formImportHistoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormImportHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormImportHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormImportHistoryDao) Columns() FormImportHistoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormImportHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormImportHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormImportHistoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
