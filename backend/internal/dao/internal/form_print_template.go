// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormPrintTemplateDao is the data access object for table form_print_template.1111111111111111111111111111111111111111
type FormPrintTemplateDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of current DAO.
	columns FormPrintTemplateColumns // columns contains all the column names of Table for convenient usage.
}

// FormPrintTemplateColumns defines and stores column names for table form_print_template.
type FormPrintTemplateColumns struct {
	Id              string // id
	Name            string // 模板名
	Description     string // 模板说明
	FormTemplateId  string // 关联的模板id
	EnterWithData   string // 关联表字段设置
	TemplateContent string // 模版内容
	PrintSize       string // 纸张方向
	PrintRotation   string // 尺寸大小
	Status          string // 状态;1 正常 999 停止
	CreatedBy       string // 创建人
	CreatedAt       string // 创建时间
	UpdatedAt       string // 更新时间
	DeletedAt       string // 删除时间
	TenantId        string // 数据租户id
	Padding         string // 内边距
	Width           string // 宽度
	Height          string // 高度
	AllowEditBefore string // 是否允许打印前编辑
	SqlQueries      string // sql数据源配置
}

// formPrintTemplateColumns holds the columns for table form_print_template.
var formPrintTemplateColumns = FormPrintTemplateColumns{
	Id:              "id",
	Name:            "name",
	Description:     "description",
	FormTemplateId:  "form_template_id",
	EnterWithData:   "enter_with_data",
	TemplateContent: "template_content",
	PrintSize:       "print_size",
	PrintRotation:   "print_rotation",
	Status:          "status",
	CreatedBy:       "created_by",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
	TenantId:        "tenant_id",
	Padding:         "padding",
	Width:           "width",
	Height:          "height",
	AllowEditBefore: "allow_edit_before",
	SqlQueries:      "sql_queries",
}

// NewFormPrintTemplateDao creates and returns a new DAO object for table data access.
func NewFormPrintTemplateDao() *FormPrintTemplateDao {
	return &FormPrintTemplateDao{
		group:   "default",
		table:   "form_print_template",
		columns: formPrintTemplateColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormPrintTemplateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormPrintTemplateDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormPrintTemplateDao) Columns() FormPrintTemplateColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormPrintTemplateDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormPrintTemplateDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormPrintTemplateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
