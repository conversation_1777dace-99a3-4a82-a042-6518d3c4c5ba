// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FlowTemplateDao is the data access object for table flow_template.1111111111111111111111111111111111111111
type FlowTemplateDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns FlowTemplateColumns // columns contains all the column names of Table for convenient usage.
}

// FlowTemplateColumns defines and stores column names for table flow_template.
type FlowTemplateColumns struct {
	Id          string // id
	Name        string // 模版名
	Description string // 流程说明
	FlowSchema  string // 流程设计整体结构
	FlowStatus  string // 状态;1 正常 999 停止
	CreatedBy   string // 创建人
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
	DeletedAt   string // 删除时间
	TenantId    string // 数据租户id
}

// flowTemplateColumns holds the columns for table flow_template.
var flowTemplateColumns = FlowTemplateColumns{
	Id:          "id",
	Name:        "name",
	Description: "description",
	FlowSchema:  "flow_schema",
	FlowStatus:  "flow_status",
	CreatedBy:   "created_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	TenantId:    "tenant_id",
}

// NewFlowTemplateDao creates and returns a new DAO object for table data access.
func NewFlowTemplateDao() *FlowTemplateDao {
	return &FlowTemplateDao{
		group:   "default",
		table:   "flow_template",
		columns: flowTemplateColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FlowTemplateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FlowTemplateDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FlowTemplateDao) Columns() FlowTemplateColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FlowTemplateDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FlowTemplateDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FlowTemplateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
