// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormExportTemplateDao is the data access object for table form_export_template.1111111111111111111111111111111111111111
type FormExportTemplateDao struct {
	table   string                    // table is the underlying table name of the DAO.
	group   string                    // group is the database configuration group name of current DAO.
	columns FormExportTemplateColumns // columns contains all the column names of Table for convenient usage.
}

// FormExportTemplateColumns defines and stores column names for table form_export_template.
type FormExportTemplateColumns struct {
	Id             string // id
	Name           string // 模板名称
	Description    string // 规则说明
	FormTemplateId string // 关联的模板id
	FieldMapping   string // 字段映射
	Status         string // 状态;1 正常 999 停止
	CreatedBy      string // 创建人
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
	TenantId       string // 数据租户id
}

// formExportTemplateColumns holds the columns for table form_export_template.
var formExportTemplateColumns = FormExportTemplateColumns{
	Id:             "id",
	Name:           "name",
	Description:    "description",
	FormTemplateId: "form_template_id",
	FieldMapping:   "field_mapping",
	Status:         "status",
	CreatedBy:      "created_by",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
	TenantId:       "tenant_id",
}

// NewFormExportTemplateDao creates and returns a new DAO object for table data access.
func NewFormExportTemplateDao() *FormExportTemplateDao {
	return &FormExportTemplateDao{
		group:   "default",
		table:   "form_export_template",
		columns: formExportTemplateColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormExportTemplateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormExportTemplateDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormExportTemplateDao) Columns() FormExportTemplateColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormExportTemplateDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormExportTemplateDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormExportTemplateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
