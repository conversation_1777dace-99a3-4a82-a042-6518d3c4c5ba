// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormExportHistoryDao is the data access object for table form_export_history.1111111111111111111111111111111111111111
type FormExportHistoryDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of current DAO.
	columns FormExportHistoryColumns // columns contains all the column names of Table for convenient usage.
}

// FormExportHistoryColumns defines and stores column names for table form_export_history.
type FormExportHistoryColumns struct {
	Id                   string // id
	FormExportTemplateId string // 导出的数据模板id
	FormTemplateId       string // 导出的表单模板id
	FilterRules          string // 数据过滤规则
	ExportFile           string // 导出的文件
	Status               string // 状态
	Result               string // 结果信息
	TotalCount           string // 总数
	FinishdAt            string // 完成时间
	CreatedBy            string // 创建人
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
	DeletedAt            string // 删除时间
	TenantId             string // 数据租户id
}

// formExportHistoryColumns holds the columns for table form_export_history.
var formExportHistoryColumns = FormExportHistoryColumns{
	Id:                   "id",
	FormExportTemplateId: "form_export_template_id",
	FormTemplateId:       "form_template_id",
	FilterRules:          "filter_rules",
	ExportFile:           "export_file",
	Status:               "status",
	Result:               "result",
	TotalCount:           "total_count",
	FinishdAt:            "finishd_at",
	CreatedBy:            "created_by",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	DeletedAt:            "deleted_at",
	TenantId:             "tenant_id",
}

// NewFormExportHistoryDao creates and returns a new DAO object for table data access.
func NewFormExportHistoryDao() *FormExportHistoryDao {
	return &FormExportHistoryDao{
		group:   "default",
		table:   "form_export_history",
		columns: formExportHistoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormExportHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormExportHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormExportHistoryDao) Columns() FormExportHistoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormExportHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormExportHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormExportHistoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
