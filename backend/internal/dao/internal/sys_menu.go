// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysMenuDao is the data access object for table sys_menu.1111111111111111111111111111111111111111
type SysMenuDao struct {
	table   string         // table is the underlying table name of the DAO.
	group   string         // group is the database configuration group name of current DAO.
	columns SysMenuColumns // columns contains all the column names of Table for convenient usage.
}

// SysMenuColumns defines and stores column names for table sys_menu.
type SysMenuColumns struct {
	Id                   string // id
	Code                 string // 编号
	ParentId             string // 父菜单id
	Route                string // 路由地址
	Mark                 string // 菜单标记
	Title                string // 菜单标题
	Icon                 string // 图标
	Remark               string // 备注
	MenuType             string // 菜单类型
	IsDisplay            string // 是否显示
	ApiAssociation       string // 关联的API
	CreatedBy            string // 创建人
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
	DeletedAt            string // 删除时间
	TenantId             string // 数据租户id
	MenuFormTemplateId   string // 绑定的表单模板id
	MenuFormTemplateType string // 绑定的表单模板页面类型
	RouteType            string // 路由类型
	SortOrder            string // 排序字段
	DashboardId          string // 仪表板id
	DashboardIdMobile    string // 仪表板id-移动端
}

// sysMenuColumns holds the columns for table sys_menu.
var sysMenuColumns = SysMenuColumns{
	Id:                   "id",
	Code:                 "code",
	ParentId:             "parent_id",
	Route:                "route",
	Mark:                 "mark",
	Title:                "title",
	Icon:                 "icon",
	Remark:               "remark",
	MenuType:             "menu_type",
	IsDisplay:            "is_display",
	ApiAssociation:       "api_association",
	CreatedBy:            "created_by",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	DeletedAt:            "deleted_at",
	TenantId:             "tenant_id",
	MenuFormTemplateId:   "menu_form_template_id",
	MenuFormTemplateType: "menu_form_template_type",
	RouteType:            "route_type",
	SortOrder:            "sort_order",
	DashboardId:          "dashboard_id",
	DashboardIdMobile:    "dashboard_id_mobile",
}

// NewSysMenuDao creates and returns a new DAO object for table data access.
func NewSysMenuDao() *SysMenuDao {
	return &SysMenuDao{
		group:   "default",
		table:   "sys_menu",
		columns: sysMenuColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysMenuDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysMenuDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysMenuDao) Columns() SysMenuColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysMenuDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysMenuDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysMenuDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
