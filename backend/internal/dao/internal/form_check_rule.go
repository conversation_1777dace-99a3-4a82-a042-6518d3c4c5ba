// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormCheckRuleDao is the data access object for table form_check_rule.1111111111111111111111111111111111111111
type FormCheckRuleDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of current DAO.
	columns FormCheckRuleColumns // columns contains all the column names of Table for convenient usage.
}

// FormCheckRuleColumns defines and stores column names for table form_check_rule.
type FormCheckRuleColumns struct {
	Id             string // id
	Name           string // 验证规则名称
	Description    string // 规则说明
	FormTemplateId string // 关联的模板id
	CodeContent    string // 代码内容
	FailMsg        string // 验证失败提示内容
	Status         string // 状态;1 正常 999 停止
	CreatedBy      string // 创建人
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
	TenantId       string // 数据租户id
}

// formCheckRuleColumns holds the columns for table form_check_rule.
var formCheckRuleColumns = FormCheckRuleColumns{
	Id:             "id",
	Name:           "name",
	Description:    "description",
	FormTemplateId: "form_template_id",
	CodeContent:    "code_content",
	FailMsg:        "fail_msg",
	Status:         "status",
	CreatedBy:      "created_by",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
	TenantId:       "tenant_id",
}

// NewFormCheckRuleDao creates and returns a new DAO object for table data access.
func NewFormCheckRuleDao() *FormCheckRuleDao {
	return &FormCheckRuleDao{
		group:   "default",
		table:   "form_check_rule",
		columns: formCheckRuleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormCheckRuleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormCheckRuleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormCheckRuleDao) Columns() FormCheckRuleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormCheckRuleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormCheckRuleDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormCheckRuleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
