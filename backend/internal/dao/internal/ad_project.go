// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdProjectDao is the data access object for table ad_project.1111111111111111111111111111111111111111
type AdProjectDao struct {
	table   string           // table is the underlying table name of the DAO.
	group   string           // group is the database configuration group name of current DAO.
	columns AdProjectColumns // columns contains all the column names of Table for convenient usage.
}

// AdProjectColumns defines and stores column names for table ad_project.
type AdProjectColumns struct {
	Id          string // id
	Code        string // 编号
	Name        string // 项目名称
	Leader      string // 负责人
	Instruction string // 项目说明
	SortOrder   string // 排序字段
	CreatedBy   string // 创建人
	UpdatedBy   string // 修改人
	CreatedAt   string // 创建时间
	UpdatedAt   string // 修改时间
	DeletedAt   string // 删除时间
	TenantId    string // 数据租户id
	Source      string // 来源
	Version     string // 当前版本
}

// adProjectColumns holds the columns for table ad_project.
var adProjectColumns = AdProjectColumns{
	Id:          "id",
	Code:        "code",
	Name:        "name",
	Leader:      "leader",
	Instruction: "instruction",
	SortOrder:   "sort_order",
	CreatedBy:   "created_by",
	UpdatedBy:   "updated_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	TenantId:    "tenant_id",
	Source:      "source",
	Version:     "version",
}

// NewAdProjectDao creates and returns a new DAO object for table data access.
func NewAdProjectDao() *AdProjectDao {
	return &AdProjectDao{
		group:   "default",
		table:   "ad_project",
		columns: adProjectColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdProjectDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdProjectDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdProjectDao) Columns() AdProjectColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdProjectDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdProjectDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdProjectDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
