// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AmConfigHistoryDao is the data access object for table am_config_history.1111111111111111111111111111111111111111
type AmConfigHistoryDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns AmConfigHistoryColumns // columns contains all the column names of Table for convenient usage.
}

// AmConfigHistoryColumns defines and stores column names for table am_config_history.
type AmConfigHistoryColumns struct {
	Id            string // id
	AmConfigId    string // 关联的自动化配置id
	Status        string // 状态
	ResultCode    string // 执行结果;1 成功 999 失败
	ResultMsg     string // 执行结果MSG
	DataSnap      string // 数据源快照
	ResultSnap    string // 结果快照
	NodeLog       string // 节点详细日志;每个节点的执行信息、堆栈信息
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	DeletedAt     string // 删除时间
	TenantId      string // 数据租户id
	WorkflowState string // 执行状态数据（用于恢复执行）
	SequenceId    string // 触发的顺序id（防止消息队列重放）
}

// amConfigHistoryColumns holds the columns for table am_config_history.
var amConfigHistoryColumns = AmConfigHistoryColumns{
	Id:            "id",
	AmConfigId:    "am_config_id",
	Status:        "status",
	ResultCode:    "result_code",
	ResultMsg:     "result_msg",
	DataSnap:      "data_snap",
	ResultSnap:    "result_snap",
	NodeLog:       "node_log",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
	TenantId:      "tenant_id",
	WorkflowState: "workflow_state",
	SequenceId:    "sequence_id",
}

// NewAmConfigHistoryDao creates and returns a new DAO object for table data access.
func NewAmConfigHistoryDao() *AmConfigHistoryDao {
	return &AmConfigHistoryDao{
		group:   "default",
		table:   "am_config_history",
		columns: amConfigHistoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AmConfigHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AmConfigHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AmConfigHistoryDao) Columns() AmConfigHistoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AmConfigHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AmConfigHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AmConfigHistoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
