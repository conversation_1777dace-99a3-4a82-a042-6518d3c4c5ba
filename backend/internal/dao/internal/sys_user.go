// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysUserDao is the data access object for table sys_user.1111111111111111111111111111111111111111
type SysUserDao struct {
	table   string         // table is the underlying table name of the DAO.
	group   string         // group is the database configuration group name of current DAO.
	columns SysUserColumns // columns contains all the column names of Table for convenient usage.
}

// SysUserColumns defines and stores column names for table sys_user.
type SysUserColumns struct {
	Id                string // id
	Code              string // 员工编号
	SystemAccount     string // 系统账号
	Username          string // 用户名
	Email             string // 邮箱
	ContactPhone      string // 联系电话
	IdNumber          string // 身份证号码
	HireDate          string // 入职日期
	ResignationDate   string // 离职日期
	Status            string // 状态
	WithDepts         string // 所在部门
	WithPosts         string // 所属岗位
	WithRoles         string // 拥有的角色
	WithCompanys      string // 关联的公司
	WithProjects      string // 关联的项目
	Gender            string // 性别
	Usersale          string // 用户盐
	Password          string // 密码
	Birthday          string // 生日
	ContactAddress    string // 联系地址
	Description       string // 描述信息
	LastLoginIp       string // 最后登录ip
	LastLoginTime     string // 最后登录时间
	CreatedBy         string // 创建人
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 删除时间
	TenantId          string // 数据租户id
	Avatar            string // 用户头像
	LastUpdatePwdTime string // 最后一次修改密码的时间
	PasswordChanged   string // 密码是否已修改
}

// sysUserColumns holds the columns for table sys_user.
var sysUserColumns = SysUserColumns{
	Id:                "id",
	Code:              "code",
	SystemAccount:     "system_account",
	Username:          "username",
	Email:             "email",
	ContactPhone:      "contact_phone",
	IdNumber:          "id_number",
	HireDate:          "hire_date",
	ResignationDate:   "resignation_date",
	Status:            "status",
	WithDepts:         "with_depts",
	WithPosts:         "with_posts",
	WithRoles:         "with_roles",
	WithCompanys:      "with_companys",
	WithProjects:      "with_projects",
	Gender:            "gender",
	Usersale:          "usersale",
	Password:          "password",
	Birthday:          "birthday",
	ContactAddress:    "contact_address",
	Description:       "description",
	LastLoginIp:       "last_login_ip",
	LastLoginTime:     "last_login_time",
	CreatedBy:         "created_by",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
	TenantId:          "tenant_id",
	Avatar:            "avatar",
	LastUpdatePwdTime: "last_update_pwd_time",
	PasswordChanged:   "password_changed",
}

// NewSysUserDao creates and returns a new DAO object for table data access.
func NewSysUserDao() *SysUserDao {
	return &SysUserDao{
		group:   "default",
		table:   "sys_user",
		columns: sysUserColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysUserDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysUserDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysUserDao) Columns() SysUserColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysUserDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysUserDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysUserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
