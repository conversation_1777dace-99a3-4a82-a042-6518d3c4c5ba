// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysPostRoleDao is the data access object for table sys_post_role.1111111111111111111111111111111111111111
type SysPostRoleDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns SysPostRoleColumns // columns contains all the column names of Table for convenient usage.
}

// SysPostRoleColumns defines and stores column names for table sys_post_role.
type SysPostRoleColumns struct {
	Id     string // 关联ID
	PostId string // 岗位ID
	RoleId string // 角色ID
}

// sysPostRoleColumns holds the columns for table sys_post_role.
var sysPostRoleColumns = SysPostRoleColumns{
	Id:     "id",
	PostId: "post_id",
	RoleId: "role_id",
}

// NewSysPostRoleDao creates and returns a new DAO object for table data access.
func NewSysPostRoleDao() *SysPostRoleDao {
	return &SysPostRoleDao{
		group:   "default",
		table:   "sys_post_role",
		columns: sysPostRoleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysPostRoleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysPostRoleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysPostRoleDao) Columns() SysPostRoleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysPostRoleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysPostRoleDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysPostRoleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
