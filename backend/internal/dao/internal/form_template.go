// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormTemplateDao is the data access object for table form_template.1111111111111111111111111111111111111111
type FormTemplateDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns FormTemplateColumns // columns contains all the column names of Table for convenient usage.
}

// FormTemplateColumns defines and stores column names for table form_template.
type FormTemplateColumns struct {
	Id               string // id
	FormCode         string // 表单编号
	FormTableName    string // 表单绑定的Table名
	FormTitle        string // 表单名
	FormDesc         string // 表单介绍
	FormSchema       string // 低代码的原始结构
	FormColumns      string // 低代码获取的字段列表
	HistoryColumns   string // 历史删除的字段列表
	TableDesign      string // 列表设计
	OpenFlow         string // 开启审核流程
	OpenLog          string // 开启操作日志
	AllowCustomAudit string // 允许增加自定义审核人
	AllowCustomCc    string // 允许增加自定义抄送人
	FlowCode         string // 绑定的审核流程编号
	CreatedBy        string // 创建人
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
	DeletedAt        string // 删除时间
	TenantId         string // 数据租户id
	FlowTemplateId   string // 绑定的流程模板id
	FormType         string // 表单类型
	Permissions      string // 表单的数据行权限设置
	WithTables       string // 关联表信息
}

// formTemplateColumns holds the columns for table form_template.
var formTemplateColumns = FormTemplateColumns{
	Id:               "id",
	FormCode:         "form_code",
	FormTableName:    "form_table_name",
	FormTitle:        "form_title",
	FormDesc:         "form_desc",
	FormSchema:       "form_schema",
	FormColumns:      "form_columns",
	HistoryColumns:   "history_columns",
	TableDesign:      "table_design",
	OpenFlow:         "open_flow",
	OpenLog:          "open_log",
	AllowCustomAudit: "allow_custom_audit",
	AllowCustomCc:    "allow_custom_cc",
	FlowCode:         "flow_code",
	CreatedBy:        "created_by",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
	TenantId:         "tenant_id",
	FlowTemplateId:   "flow_template_id",
	FormType:         "form_type",
	Permissions:      "permissions",
	WithTables:       "with_tables",
}

// NewFormTemplateDao creates and returns a new DAO object for table data access.
func NewFormTemplateDao() *FormTemplateDao {
	return &FormTemplateDao{
		group:   "default",
		table:   "form_template",
		columns: formTemplateColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormTemplateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormTemplateDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormTemplateDao) Columns() FormTemplateColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormTemplateDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormTemplateDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormTemplateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
