// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CodeHistoryDao is the data access object for table code_history.1111111111111111111111111111111111111111
type CodeHistoryDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns CodeHistoryColumns // columns contains all the column names of Table for convenient usage.
}

// CodeHistoryColumns defines and stores column names for table code_history.
type CodeHistoryColumns struct {
	Id          string // id
	Title       string // 代码库标题
	Instruction string // 代码库说明
	Content     string // 代码内容
	CreatedBy   string // 创建人
	UpdatedBy   string // 修改人
	CreatedAt   string // 创建时间
	UpdatedAt   string // 修改时间
	DeletedAt   string // 删除时间
	TenantId    string // 数据租户id
}

// codeHistoryColumns holds the columns for table code_history.
var codeHistoryColumns = CodeHistoryColumns{
	Id:          "id",
	Title:       "title",
	Instruction: "instruction",
	Content:     "content",
	CreatedBy:   "created_by",
	UpdatedBy:   "updated_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	TenantId:    "tenant_id",
}

// NewCodeHistoryDao creates and returns a new DAO object for table data access.
func NewCodeHistoryDao() *CodeHistoryDao {
	return &CodeHistoryDao{
		group:   "default",
		table:   "code_history",
		columns: codeHistoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CodeHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CodeHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CodeHistoryDao) Columns() CodeHistoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CodeHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CodeHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CodeHistoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
