// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FlowInstanceHistoryAuditorDao is the data access object for table flow_instance_history_auditor.1111111111111111111111111111111111111111
type FlowInstanceHistoryAuditorDao struct {
	table   string                            // table is the underlying table name of the DAO.
	group   string                            // group is the database configuration group name of current DAO.
	columns FlowInstanceHistoryAuditorColumns // columns contains all the column names of Table for convenient usage.
}

// FlowInstanceHistoryAuditorColumns defines and stores column names for table flow_instance_history_auditor.
type FlowInstanceHistoryAuditorColumns struct {
	Id                               string // id
	FlowInstanceHistoryId            string // 审核历史id
	FlowInstanceId                   string // 流程实例id
	NodeType                         string // 节点类型（用于区分发起人、审批人、抄送人）
	UserId                           string // 审核人id
	ApprovalStatus                   string // 审批状态
	IsAutoPass                       string // 是否是自动通过
	FlowInstanceHistoryStatus        string // 节点实例状态
	FlowInstanceHistoryStatusVersion string // 节点实例状态同步时间
	FlowInstanceStatus               string // 流程实例状态
	FlowInstanceStatusVersion        string // 流程实例状态同步时间
	ReturnNodeId                     string // 退回节点id
	ArrivalTime                      string // 到达时间
	CompletionTime                   string // 完成时间
	ApprovalComment                  string // 审批意见
	Attachment                       string // 附件
	Signature                        string // 签名
	CreatedAt                        string // 创建时间
	UpdatedAt                        string // 更新时间
	DeletedAt                        string // 删除时间
	TenantId                         string // 租户id
	Pics                             string // 图片
}

// flowInstanceHistoryAuditorColumns holds the columns for table flow_instance_history_auditor.
var flowInstanceHistoryAuditorColumns = FlowInstanceHistoryAuditorColumns{
	Id:                               "id",
	FlowInstanceHistoryId:            "flow_instance_history_id",
	FlowInstanceId:                   "flow_instance_id",
	NodeType:                         "node_type",
	UserId:                           "user_id",
	ApprovalStatus:                   "approval_status",
	IsAutoPass:                       "is_auto_pass",
	FlowInstanceHistoryStatus:        "flow_instance_history_status",
	FlowInstanceHistoryStatusVersion: "flow_instance_history_status_version",
	FlowInstanceStatus:               "flow_instance_status",
	FlowInstanceStatusVersion:        "flow_instance_status_version",
	ReturnNodeId:                     "return_node_id",
	ArrivalTime:                      "arrival_time",
	CompletionTime:                   "completion_time",
	ApprovalComment:                  "approval_comment",
	Attachment:                       "attachment",
	Signature:                        "signature",
	CreatedAt:                        "created_at",
	UpdatedAt:                        "updated_at",
	DeletedAt:                        "deleted_at",
	TenantId:                         "tenant_id",
	Pics:                             "pics",
}

// NewFlowInstanceHistoryAuditorDao creates and returns a new DAO object for table data access.
func NewFlowInstanceHistoryAuditorDao() *FlowInstanceHistoryAuditorDao {
	return &FlowInstanceHistoryAuditorDao{
		group:   "default",
		table:   "flow_instance_history_auditor",
		columns: flowInstanceHistoryAuditorColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FlowInstanceHistoryAuditorDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FlowInstanceHistoryAuditorDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FlowInstanceHistoryAuditorDao) Columns() FlowInstanceHistoryAuditorColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FlowInstanceHistoryAuditorDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FlowInstanceHistoryAuditorDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FlowInstanceHistoryAuditorDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
