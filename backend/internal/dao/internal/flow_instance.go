// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FlowInstanceDao is the data access object for table flow_instance.1111111111111111111111111111111111111111
type FlowInstanceDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns FlowInstanceColumns // columns contains all the column names of Table for convenient usage.
}

// FlowInstanceColumns defines and stores column names for table flow_instance.
type FlowInstanceColumns struct {
	Id                string // id
	FlowTemplateId    string // 流程模板id
	FlowTemplateSnap  string // 流程模板快照
	FormTemplateId    string // 绑定表单模板id
	FormTemplateTitle string // 绑定表单模板标题
	FormTemplateSnap  string // 绑定表单模板快照
	FormTableName     string // 绑定表单表名称
	FormTableId       string // 绑定表单表id
	FormDataSnap      string // 表单数据快照
	FormExtraData     string // 表单工作流附加数据
	CurrentSteps      string // 当前正在进行的步骤
	FinishdSteps      string // 已经完成的步骤ID
	FlowSteps         string // 流程预生成的所有步骤
	InstanceState     string // 实例状态;1 等待审批 2 审批中 3审批通过 4审批不通过 5 作废
	CreatedBy         string // 创建人
	CreatedAt         string // 创建时间
	FinishdAt         string // 完成时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 删除时间
	TenantId          string // 数据租户id
	FlowInstanceType  string // 流程实例类型 	1创建申请 2作废申请
}

// flowInstanceColumns holds the columns for table flow_instance.
var flowInstanceColumns = FlowInstanceColumns{
	Id:                "id",
	FlowTemplateId:    "flow_template_id",
	FlowTemplateSnap:  "flow_template_snap",
	FormTemplateId:    "form_template_id",
	FormTemplateTitle: "form_template_title",
	FormTemplateSnap:  "form_template_snap",
	FormTableName:     "form_table_name",
	FormTableId:       "form_table_id",
	FormDataSnap:      "form_data_snap",
	FormExtraData:     "form_extra_data",
	CurrentSteps:      "current_steps",
	FinishdSteps:      "finishd_steps",
	FlowSteps:         "flow_steps",
	InstanceState:     "instance_state",
	CreatedBy:         "created_by",
	CreatedAt:         "created_at",
	FinishdAt:         "finishd_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
	TenantId:          "tenant_id",
	FlowInstanceType:  "flow_instance_type",
}

// NewFlowInstanceDao creates and returns a new DAO object for table data access.
func NewFlowInstanceDao() *FlowInstanceDao {
	return &FlowInstanceDao{
		group:   "default",
		table:   "flow_instance",
		columns: flowInstanceColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FlowInstanceDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FlowInstanceDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FlowInstanceDao) Columns() FlowInstanceColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FlowInstanceDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FlowInstanceDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FlowInstanceDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
