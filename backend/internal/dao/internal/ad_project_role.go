// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdProjectRoleDao is the data access object for table ad_project_role.1111111111111111111111111111111111111111
type AdProjectRoleDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of current DAO.
	columns AdProjectRoleColumns // columns contains all the column names of Table for convenient usage.
}

// AdProjectRoleColumns defines and stores column names for table ad_project_role.
type AdProjectRoleColumns struct {
	Id          string // id
	Code        string // 编号
	Name        string // 角色名
	IsDefault   string // 是否是默认角色
	Instruction string // 角色说明
	CreatedBy   string // 创建人
	UpdatedBy   string // 修改人
	CreatedAt   string // 创建时间
	UpdatedAt   string // 修改时间
	DeletedAt   string // 删除时间
	TenantId    string // 数据租户id
}

// adProjectRoleColumns holds the columns for table ad_project_role.
var adProjectRoleColumns = AdProjectRoleColumns{
	Id:          "id",
	Code:        "code",
	Name:        "name",
	IsDefault:   "is_default",
	Instruction: "instruction",
	CreatedBy:   "created_by",
	UpdatedBy:   "updated_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	TenantId:    "tenant_id",
}

// NewAdProjectRoleDao creates and returns a new DAO object for table data access.
func NewAdProjectRoleDao() *AdProjectRoleDao {
	return &AdProjectRoleDao{
		group:   "default",
		table:   "ad_project_role",
		columns: adProjectRoleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdProjectRoleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdProjectRoleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdProjectRoleDao) Columns() AdProjectRoleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdProjectRoleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdProjectRoleDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdProjectRoleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
