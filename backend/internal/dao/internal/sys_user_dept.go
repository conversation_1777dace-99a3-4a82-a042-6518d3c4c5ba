// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysUserDeptDao is the data access object for table sys_user_dept.1111111111111111111111111111111111111111
type SysUserDeptDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns SysUserDeptColumns // columns contains all the column names of Table for convenient usage.
}

// SysUserDeptColumns defines and stores column names for table sys_user_dept.
type SysUserDeptColumns struct {
	Id     string // 关联ID
	UserId string // 用户ID
	DeptId string // 部门ID
}

// sysUserDeptColumns holds the columns for table sys_user_dept.
var sysUserDeptColumns = SysUserDeptColumns{
	Id:     "id",
	UserId: "user_id",
	DeptId: "dept_id",
}

// NewSysUserDeptDao creates and returns a new DAO object for table data access.
func NewSysUserDeptDao() *SysUserDeptDao {
	return &SysUserDeptDao{
		group:   "default",
		table:   "sys_user_dept",
		columns: sysUserDeptColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysUserDeptDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysUserDeptDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysUserDeptDao) Columns() SysUserDeptColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysUserDeptDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysUserDeptDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysUserDeptDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
