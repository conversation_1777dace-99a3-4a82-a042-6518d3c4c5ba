// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FlowInstanceHistoryDao is the data access object for table flow_instance_history.1111111111111111111111111111111111111111
type FlowInstanceHistoryDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of current DAO.
	columns FlowInstanceHistoryColumns // columns contains all the column names of Table for convenient usage.
}

// FlowInstanceHistoryColumns defines and stores column names for table flow_instance_history.
type FlowInstanceHistoryColumns struct {
	Id             string // id
	FlowInstanceId string // 流程实例id
	StepId         string // 步骤id
	StepName       string // 步骤名
	NodeType       string // 节点类型
	Status         string // 当前状态
	ApprovalUsers  string // 匹配的所有审批人
	Condition      string // 存在多个审批人时的审批方式
	CreatedAt      string // 创建时间
	FinishdAt      string // 完成时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
	TenantId       string // 租户id
	PreStepId      string // 上一个节点的id（从哪个节点来的）
	PreHistoryId   string // 上一个节点审核历史id
	IsReturn       string // 是否是退回的节点
}

// flowInstanceHistoryColumns holds the columns for table flow_instance_history.
var flowInstanceHistoryColumns = FlowInstanceHistoryColumns{
	Id:             "id",
	FlowInstanceId: "flow_instance_id",
	StepId:         "step_id",
	StepName:       "step_name",
	NodeType:       "node_type",
	Status:         "status",
	ApprovalUsers:  "approval_users",
	Condition:      "condition",
	CreatedAt:      "created_at",
	FinishdAt:      "finishd_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
	TenantId:       "tenant_id",
	PreStepId:      "pre_step_id",
	PreHistoryId:   "pre_history_id",
	IsReturn:       "is_return",
}

// NewFlowInstanceHistoryDao creates and returns a new DAO object for table data access.
func NewFlowInstanceHistoryDao() *FlowInstanceHistoryDao {
	return &FlowInstanceHistoryDao{
		group:   "default",
		table:   "flow_instance_history",
		columns: flowInstanceHistoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FlowInstanceHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FlowInstanceHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FlowInstanceHistoryDao) Columns() FlowInstanceHistoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FlowInstanceHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FlowInstanceHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FlowInstanceHistoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
