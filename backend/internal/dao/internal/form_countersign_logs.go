// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FormCountersignLogsDao is the data access object for table form_countersign_logs.1111111111111111111111111111111111111111
type FormCountersignLogsDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of current DAO.
	columns FormCountersignLogsColumns // columns contains all the column names of Table for convenient usage.
}

// FormCountersignLogsColumns defines and stores column names for table form_countersign_logs.
type FormCountersignLogsColumns struct {
	Id                 string // id
	FormTemplateId     string // 表单模板id
	FormDataId         string // 表单数据id
	CountersignContent string // 回签内容
	CountersignRemark  string // 回签备注
	CreatedBy          string // 创建人
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
	DeletedAt          string // 删除时间
	TenantId           string // 数据租户id
}

// formCountersignLogsColumns holds the columns for table form_countersign_logs.
var formCountersignLogsColumns = FormCountersignLogsColumns{
	Id:                 "id",
	FormTemplateId:     "form_template_id",
	FormDataId:         "form_data_id",
	CountersignContent: "countersign_content",
	CountersignRemark:  "countersign_remark",
	CreatedBy:          "created_by",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	DeletedAt:          "deleted_at",
	TenantId:           "tenant_id",
}

// NewFormCountersignLogsDao creates and returns a new DAO object for table data access.
func NewFormCountersignLogsDao() *FormCountersignLogsDao {
	return &FormCountersignLogsDao{
		group:   "default",
		table:   "form_countersign_logs",
		columns: formCountersignLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FormCountersignLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FormCountersignLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FormCountersignLogsDao) Columns() FormCountersignLogsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FormCountersignLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FormCountersignLogsDao) Ctx(ctx context.Context) *gdb.Model {
	// 改用工具函数实现
	// tenant_id := ctx.Value("tenant_id")
	// if !g.IsNil(tenant_id) {
	// 	return g.DB().Model(dao.table).Safe().Ctx(ctx).Where("tenant_id = ?", gconv.Int(tenant_id))
	// }
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FormCountersignLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
