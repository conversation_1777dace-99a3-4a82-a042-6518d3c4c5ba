// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormExportTemplateDao is internal type for wrapping internal DAO implements.
type internalFormExportTemplateDao = *internal.FormExportTemplateDao

// formExportTemplateDao is the data access object for table form_export_template.
// You can define custom methods on it to extend its functionality as you wish.
type formExportTemplateDao struct {
	internalFormExportTemplateDao
}

var (
	// FormExportTemplate is globally public accessible object for table form_export_template operations.
	FormExportTemplate = formExportTemplateDao{
		internal.NewFormExportTemplateDao(),
	}
)

// Fill with you ideas below.
