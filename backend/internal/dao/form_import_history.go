// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormImportHistoryDao is internal type for wrapping internal DAO implements.
type internalFormImportHistoryDao = *internal.FormImportHistoryDao

// formImportHistoryDao is the data access object for table form_import_history.
// You can define custom methods on it to extend its functionality as you wish.
type formImportHistoryDao struct {
	internalFormImportHistoryDao
}

var (
	// FormImportHistory is globally public accessible object for table form_import_history operations.
	FormImportHistory = formImportHistoryDao{
		internal.NewFormImportHistoryDao(),
	}
)

// Fill with you ideas below.
