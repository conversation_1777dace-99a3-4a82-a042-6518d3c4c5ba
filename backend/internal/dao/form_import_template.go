// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"backend/internal/dao/internal"
)

// internalFormImportTemplateDao is internal type for wrapping internal DAO implements.
type internalFormImportTemplateDao = *internal.FormImportTemplateDao

// formImportTemplateDao is the data access object for table form_import_template.
// You can define custom methods on it to extend its functionality as you wish.
type formImportTemplateDao struct {
	internalFormImportTemplateDao
}

var (
	// FormImportTemplate is globally public accessible object for table form_import_template operations.
	FormImportTemplate = formImportTemplateDao{
		internal.NewFormImportTemplateDao(),
	}
)

// Fill with you ideas below.
