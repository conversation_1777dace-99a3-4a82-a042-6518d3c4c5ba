package middleware

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gogf/gf/v2/net/ghttp"
)

// 暂时不用
// GzipStaticHandler 自定义的静态文件处理器，支持预压缩的 .gz 文件
// mappath: 路由前缀，例如 "/resource/frontend"
// actualpath: 实际文件路径，例如 "resource/frontend"
func GzipStaticHandler(mappath, actualpath string) ghttp.HandlerFunc {
	return func(r *ghttp.Request) {
		// 确保请求路径以 mappath 开头
		if !strings.HasPrefix(r.URL.Path, mappath) {
			r.Middleware.Next()
			return
		}

		// 获取相对路径
		relPath := strings.TrimPrefix(r.URL.Path, mappath)
		if relPath == "" || relPath == "/" {
			relPath = "/index.html"
		}

		// 构建实际文件路径
		filePath := filepath.Join(actualpath, filepath.Clean(relPath))

		// 检查是否存在预压缩的 .gz 文件
		gzFilePath := filePath + ".gz"
		acceptEncoding := r.Header.Get("Accept-Encoding")

		// 判断客户端是否支持 gzip
		supportGzip := strings.Contains(acceptEncoding, "gzip")

		if supportGzip {
			if _, err := os.Stat(gzFilePath); err == nil {
				// 设置响应头
				r.Response.Header().Set("Content-Encoding", "gzip")
				// 根据文件扩展名设置 Content-Type
				ext := strings.ToLower(filepath.Ext(filePath))
				mimeType := mimeTypeByExtension(ext)
				r.Response.Header().Set("Content-Type", mimeType)

				// 发送 .gz 文件内容
				r.Response.ServeFile(gzFilePath)
				return
			}
		}

		// 如果不满足条件，继续发送原始文件
		if _, err := os.Stat(filePath); err == nil {
			r.Response.ServeFile(filePath)
		} else {
			// 文件不存在，返回 404
			r.Response.WriteStatus(http.StatusNotFound)
		}
	}
}

// mimeTypeByExtension 根据文件扩展名返回 MIME 类型
func mimeTypeByExtension(ext string) string {
	switch ext {
	case ".html", ".htm":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".json":
		return "application/json"
	case ".png":
		return "image/png"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".gif":
		return "image/gif"
	case ".svg":
		return "image/svg+xml"
	case ".woff", ".woff2":
		return "font/woff2"
	case ".ttf":
		return "font/ttf"
	default:
		return "application/octet-stream"
	}
}
