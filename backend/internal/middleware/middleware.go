package middleware

import (
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/library"
	"context"
	"fmt"
	"strings"

	"github.com/dgrijalva/jwt-go"
	"github.com/gogf/gf/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 无需验证的路由列表
var noAuthRoutes = []string{
	"/login/account",
	"/login/refreshToken",
	"/demo/",
	"/am_node/api",
	"/superset",
}

func isNoAuthRoute(path string) bool {
	for _, route := range noAuthRoutes {
		if strings.Contains(path, route) {
			return true
		}
	}
	return false
}

func Auth(r *ghttp.Request) {
	path := r.URL.Path

	// // 随机休眠2-5秒
	// sleepTime := time.Duration(rand.Intn(3000)+2000) * time.Millisecond
	// time.Sleep(sleepTime)

	if isNoAuthRoute(path) {
		r.Middleware.Next()
	} else {

		ctx := r.Context()
		err := g.Try(ctx, func(ctx context.Context) {
			// 从请求头中获取 token
			tokenString := r.Header.Get("Authorization")
			library.ValueIsNil(tokenString, "authorization token is nil")
			// 去掉可能的 "Bearer " 前缀
			if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
				tokenString = tokenString[7:]
			}

			// 解析 token
			token, err := jwt.ParseWithClaims(tokenString, &dto.UserClaims{}, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return []byte(library.GetJWTKey(ctx)), nil
			})
			if err != nil {
				library.ErrIsNil(ctx, gerror.NewCode(consts.CodeTokenParseError))
			}

			// 验证 token 和声明
			claims, ok := token.Claims.(*dto.UserClaims)

			if !ok || !token.Valid {
				library.ErrIsNil(ctx, gerror.NewCode(consts.CodeInvalidToken))
			}
			// 将用户信息存储在请求上下文中
			r.SetCtxVar(consts.CTX_USER, claims)
			r.SetCtxVar(consts.CTX_USER_ID, claims.Id)
			r.SetCtxVar(consts.CTX_TENANT_ID, claims.TenantId)
			g.Log().Info(ctx, fmt.Sprintf("user authorization success, username: %s ; user_id: %d ; tenant_id: %d", claims.Username, claims.Id, claims.TenantId))

			// 验证路由权限
			// claims.DeniedPermissions 存储了用户被禁止访问的路由
		})
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("authorization Err : %v", err))
			r.Response.WriteJsonExit(ghttp.DefaultHandlerResponse{
				Code:    401,
				Message: err.Error(),
				Data:    nil,
			})
		} else {
			r.Middleware.Next()
		}
	}

	//返回固定的友好信息
	if err := r.GetError(); err != nil {
		r.Response.ClearBuffer()
		r.Response.WriteHeader(200)
		r.Response.WriteJsonExit(ghttp.DefaultHandlerResponse{
			Code:    500,
			Message: strings.ReplaceAll(err.Error(), "exception recovered: ", ""),
			Data:    nil,
		})
	}

}
