package formimporttemplate

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormImportTemplate(new(sFormImportTemplate))
}

type sFormImportTemplate struct{}

// 获取导入模板列表
func (s *sFormImportTemplate) GetFormImportTemplateList(ctx context.Context, req *form.FormImportTemplateListReq) (res *form.FormImportTemplateListRes, err error) {
	res = new(form.FormImportTemplateListRes)
	m := dao.TenantCtx(dao.FormImportTemplate, ctx)
	m = m.Where(dao.FormImportTemplate.Columns().FormTemplateId, req.FormTemplateId)
	if req.Name != "" {
		m = m.Where(dao.FormImportTemplate.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取导入模板总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取导入模板数据失败")
	})
	return
}

// 获取启用导入模板列表
func (s *sFormImportTemplate) GetEnabledRules(ctx context.Context, formTemplateId int64) (res []*entity.FormImportTemplate, err error) {
	m := dao.TenantCtx(dao.FormImportTemplate, ctx)
	m = m.Where(dao.FormImportTemplate.Columns().FormTemplateId, formTemplateId)
	m = m.Where(dao.FormImportTemplate.Columns().Status, 1)
	order := "id asc"
	err = m.Order(order).Scan(&res)
	return
}

// 保存导入模板数据
func (s *sFormImportTemplate) Save(ctx context.Context, req *form.FormImportTemplateSaveReq) (res *form.FormImportTemplateSaveRes, err error) {

	res = new(form.FormImportTemplateSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {

		formImportTemplate := new(do.FormImportTemplate)

		if len(req.FieldMapping) <= 0 {
			library.Fail(ctx, "字段映射不能为空")
			return
		}

		if req.DuplicateDataValidation == 1 && len(req.DuplicateCheckField) <= 0 {
			library.Fail(ctx, "重复验证标识字段至少填写一个")
			return
		}
		if req.Id > 0 {
			var editFormImportTemplate *entity.FormImportTemplate
			editFormImportTemplate, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editFormImportTemplate, "导入模板信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			formImportTemplate.CreatedBy = library.GetUserId(ctx)
			formImportTemplate.Status = 1
		}
		formImportTemplate.Name = req.Name
		formImportTemplate.FormTemplateId = req.FormTemplateId
		formImportTemplate.StartRow = req.StartRow
		formImportTemplate.FieldMapping = req.FieldMapping
		formImportTemplate.ImportTemplate = req.ImportTemplate
		formImportTemplate.DuplicateDataValidation = req.DuplicateDataValidation
		formImportTemplate.DuplicateCheckField = req.DuplicateCheckField
		formImportTemplate.DuplicateHandlingMethod = req.DuplicateHandlingMethod

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.FormImportTemplate, ctx).WherePri(req.Id).Update(formImportTemplate)
			library.ErrIsNil(ctx, err, "修改导入模板失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.FormImportTemplate, ctx).Data(formImportTemplate).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入导入模板失败")
			res.Id = newid
		}

	})
	return

}

// 删除导入模板
func (s *sFormImportTemplate) Delete(ctx context.Context, req *form.FormImportTemplateDeleteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(req.Id), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "导入模板不存在")
			return
		}
		_, err = dao.TenantCtx(dao.FormImportTemplate, ctx).WherePri(req.Id).Delete()
		library.ErrIsNil(ctx, err, "删除导入模板失败")

	})
	return
}

// 获得详细信息
func (s *sFormImportTemplate) GetInfoByID(ctx context.Context, automationId uint64, with bool) (formImportTemplate *entity.FormImportTemplate, err error) {
	m := dao.TenantCtx(dao.FormImportTemplate, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&formImportTemplate)
	return
}

// 修改状态
func (s *sFormImportTemplate) UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(automationId), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "导入模板不存在")
			return
		}

		_, err = dao.TenantCtx(dao.FormImportTemplate, ctx).WherePri(automationId).Update(&do.FormImportTemplate{Status: lo.Ternary(enabled, 1, 999)})
		if err != nil {
			g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
			library.Fail(ctx, err.Error())
		}

	})
	return
}
