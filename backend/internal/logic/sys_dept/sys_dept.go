package sysdept

import (
	"backend/api/v1/system"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"fmt"

	"github.com/gogf/gf/text/gstr"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterSysDept(new(sSysDept))
}

type sSysDept struct {
}

// 根据条件获取部门列表
func (s *sSysDept) GetList(ctx context.Context, req *system.DeptSearchReq) (list []*with.SysDept, err error) {

	list, err = s.GetListCache(ctx)
	if err != nil {
		return
	}
	filters := make([]library.FilterFunc[*with.SysDept], 0)
	filters = append(filters, func(item *with.SysDept) bool {
		if req.DeptName != "" && !gstr.ContainsI(item.DeptName, req.DeptName) {
			return false
		}
		return true
	})

	filters = append(filters, func(item *with.SysDept) bool {
		if req.Status != "" && item.Status != gconv.Uint(req.Status) {
			return false
		}
		return true
	})

	list = library.FilterList(list, filters)

	return
}

// 获取所有部门列表，部门数据一般不多，可以从缓存中获取
func (s *sSysDept) GetListCache(ctx context.Context) (list []*with.SysDept, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cList, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyDeptList), func(ctx context.Context) (value interface{}, err error) {
			err = dao.TenantCtx(dao.SysDept, ctx).With(&with.SysUserOutline{}).Scan(&list)
			// g.Log().Info(ctx, library.SDump("err:", err))
			// g.Log().Info(ctx, library.SDump("list:", list))
			if err != nil {
				return
			}
			value = list
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)

		g.Log().Info(ctx, library.SDump("cList", cList))
		if cList != nil {
			err := gconv.Struct(cList, &list)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

// Save 部门保存
func (s *sSysDept) Save(ctx context.Context, req *system.DeptSaveReq) (err error) {
	input := do.SysDept{
		Id:       req.DeptID,
		ParentId: req.ParentID,
		DeptName: req.DeptName,
		OrderNum: req.OrderNum,
		Phone:    req.Phone,
		Email:    req.Email,
		Status:   req.Status,
		Remark:   req.Remark,
	}
	input.Leader = req.Leader
	if req.DeptID <= 0 {
		err = s.Add(ctx, &input)
	} else {
		var oldDept *with.SysDept
		oldDept, err = s.GetInfoByID(ctx, int64(req.DeptID))
		if err != nil {
			return
		}
		if oldDept == nil {
			err = fmt.Errorf("部门不存在")
			return
		}
		err = s.Edit(ctx, &input)
	}
	return
}

// Add 添加部门
func (s *sSysDept) Add(ctx context.Context, input *do.SysDept) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		input.CreatedBy = library.GetUserId(ctx)
		_, err = dao.TenantCtx(dao.SysDept, ctx).Insert(input)
		library.ErrIsNil(ctx, err, "添加部门失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyDeptList))
	})
	return
}

// Edit 部门修改
func (s *sSysDept) Edit(ctx context.Context, input *do.SysDept) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		input.UpdatedBy = library.GetUserId(ctx)
		_, err = dao.TenantCtx(dao.SysDept, ctx).WherePri(input.Id).Update(input)
		library.ErrIsNil(ctx, err, "修改部门失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyDeptList))
	})
	return
}

// Delete 删除部门
func (s *sSysDept) Delete(ctx context.Context, deptId int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []*with.SysDept
		list, err = s.GetListCache(ctx)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(list, "无部门数据")
		// 获得子部门
		g.Log().Info(ctx, "deptId:"+gconv.String(deptId))
		subList := s.GetSubDept(list, deptId)
		ids := make([]int64, 0)
		for _, dept := range subList {
			ids = append(ids, dept.Id)
		}
		ids = append(ids, deptId)

		_, err = dao.TenantCtx(dao.SysDept, ctx).Where(dao.SysDept.Columns().Id+" IN (?)", ids).Delete()
		library.ErrIsNil(ctx, err, "删除部门失败")

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyDeptList))
	})
	return
}

// 递归获取所有部门和子部门的id
func (s *sSysDept) GetDeptIds(ctx context.Context, deptId int64) (ids []int64, err error) {
	list, err := s.GetListCache(ctx)
	if err != nil {
		return
	}
	subList := s.GetSubDept(list, deptId)
	for _, dept := range subList {
		ids = append(ids, dept.Id)
	}
	ids = append(ids, deptId)
	return
}

// 递归获得所有子部门
func (s *sSysDept) GetSubDept(list []*with.SysDept, deptId int64) (subList []*with.SysDept) {
	subList = make([]*with.SysDept, 0)
	for _, dept := range list {
		if dept.ParentId == deptId {
			subList = append(subList, dept)
			_subList := s.GetSubDept(list, dept.Id)
			subList = append(subList, _subList...)
		}
	}
	return
}

// 根据ID获取部门信息
func (s *sSysDept) GetInfoByID(ctx context.Context, deptId int64) (dept *with.SysDept, err error) {
	list, err := s.GetListCache(ctx)

	if err != nil {
		return
	}

	dept = library.FindOne(list, []library.FilterFunc[*with.SysDept]{
		func(item *with.SysDept) bool {
			return item.Id == deptId
		},
	})
	return
}

// 根据ids获取部门列表
func (s *sSysDept) GetDeptListByIDs(ctx context.Context, ids []int64) (list []*with.SysDept, err error) {
	allList, err := s.GetListCache(ctx)

	if err != nil {
		return
	}

	list = lo.Filter(allList, func(item *with.SysDept, _ int) bool {
		return lo.Contains(ids, item.Id)
	})

	return
}
