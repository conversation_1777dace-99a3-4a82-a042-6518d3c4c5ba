package formimporthistory

import (
	"backend/api/v1/form"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormImportHistory(new(sFormImportHistory))
}

type sFormImportHistory struct {
}

// 获取导入数据文件列表
func (s *sFormImportHistory) GetFormImportHistoryList(ctx context.Context, req *form.FormImportHistoryListReq) (res *form.FormImportHistoryListRes, err error) {
	res = new(form.FormImportHistoryListRes)
	m := dao.TenantCtx(dao.FormImportHistory, ctx)
	m = m.Where(dao.FormImportHistory.Columns().FormImportTemplateId, req.FormImportTemplateId)

	order := "id desc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取导入数据文件历史总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取导入数据文件历史数据失败")
	})
	return
}

// 新的数据导入
func (s *sFormImportHistory) New(ctx context.Context, req *form.FormImportHistoryNewReq) (res *form.FormImportHistoryNewRes, err error) {

	res = new(form.FormImportHistoryNewRes)
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, err := service.FormImportTemplate().GetInfoByID(ctx, gconv.Uint64(req.FormImportTemplateId), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(templateInfo, "表单模板Id不正确")
		// 验证权限
		library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, templateInfo.FormTemplateId, enum.SysPermission_Import), enum.SysPermission_Import.Error())

		FormImportHistory := new(do.FormImportHistory)
		FormImportHistory.CreatedBy = library.GetUserId(ctx)
		FormImportHistory.Status = enum.ImportStatus_Pending
		FormImportHistory.FormImportTemplateId = req.FormImportTemplateId
		FormImportHistory.FormTemplateId = templateInfo.FormTemplateId
		FormImportHistory.ImportFile = req.ImportFile
		FormImportHistory.Status = gconv.Int(enum.ImportStatus_Pending)

		newid, err := dao.TenantCtx(dao.FormImportHistory, ctx).Data(FormImportHistory).InsertAndGetId()
		library.ErrIsNil(ctx, err, "插入导入数据文件历史失败")
		FormImportHistory.Id = newid
		res.Id = newid

		// 发送消息，通知有新的导入数据文件
		queue.QueueManager().Publish(consts.StreamSubjectExcelImportCreated, FormImportHistory)
	})
	return

}

// 触发导入数据文件
func (s *sFormImportHistory) TriggerImportFile(ctx context.Context, id int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		historyInfo, err := s.GetInfoByID(ctx, uint64(id), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(historyInfo, "导入数据文件不存在")

		// 更新状态为进行中
		err = s.UpdateStatus(ctx, id, enum.ImportStatus_InProgress)
		library.ErrIsNil(ctx, err, "更新导入数据文件状态失败")

		historyInfo.Status = gconv.Int(enum.ImportStatus_InProgress)
		go func() {
			s.ProcessImportFile(ctx, historyInfo)
		}()
	})
	return
}

// 处理导入数据文件
func (s *sFormImportHistory) ProcessImportFile(ctx context.Context, historyInfo *entity.FormImportHistory) (err error) {
	rowMsg := make([]string, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得导入模板信息
		importTemplteInfo, err := service.FormImportTemplate().GetInfoByID(ctx, uint64(historyInfo.FormImportTemplateId), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(importTemplteInfo, "找不到对应的导入模板信息")

		// 获得表单模板信息
		formTemplateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, importTemplteInfo.FormTemplateId)
		library.ErrIsNil(ctx, err)

		uploadPath := g.Cfg().MustGet(ctx, "upload.path").String()

		// 将文件路径转换为上传路径
		importFilePath := strings.Replace(historyInfo.ImportFile, "/files", uploadPath, 1)

		// 读取文件数据
		datas, err := library.ExcelToMapSlice(ctx, importFilePath, importTemplteInfo.StartRow)
		library.ErrIsNil(ctx, err)

		// 获得字段映射设置
		var fieldMapping []*dto.ColumnMapRule
		err = json.Unmarshal([]byte(importTemplteInfo.FieldMapping), &fieldMapping)
		library.ErrIsNil(ctx, err)

		// 将获得的Excel数据datas根据fieldMapping进行映射
		mappedDatas := make([]map[string]interface{}, len(datas))
		historyInfo.TotalCount = gconv.Int64(len(datas))
		historyInfo.Result = "[]"
		vm, err := service.Javascript().GetVM(map[string]interface{}{})
		library.ErrIsNil(ctx, err)
		for i, data := range datas {
			vm.Set("__currentRecord", data)
			mappedData := make(map[string]interface{})
			mappedData["__index"] = i // 添加索引字段
			for _, mapping := range fieldMapping {
				if mapping.Type == 0 {
					if value, exists := data[mapping.Target]; exists {
						formatValue, err := service.Javascript().FormatMappedValue(ctx, vm, formTemplateInfo.FormTableName, 0, formColumns, value, mapping.FuncCode)
						library.ErrIsNil(ctx, err)
						mappedData[gconv.String(mapping.Source)] = formatValue
					}
				} else if mapping.Type == 1 {
					if len(mapping.SubRules) > 0 {
						subMappedData := make(map[string]interface{})
						for _, subRule := range mapping.SubRules {
							if subValue, subExists := data[subRule.Target]; subExists {
								subFormatValue, err := service.Javascript().FormatMappedValue(ctx, vm, formTemplateInfo.FormTableName, 0, formColumns, subValue, subRule.FuncCode)
								library.ErrIsNil(ctx, err)
								subMappedData[gconv.String(subRule.Source)] = subFormatValue
							}
						}
						mappedData[gconv.String(mapping.Source)] = []map[string]interface{}{subMappedData}
					} else {
						if value, exists := data[mapping.Target]; exists {
							formatValue, err := service.Javascript().FormatMappedValue(ctx, vm, formTemplateInfo.FormTableName, 0, formColumns, value, mapping.FuncCode)
							library.ErrIsNil(ctx, err)
							mappedData[gconv.String(mapping.Source)] = formatValue
						}
					}
				}
			}
			mappedDatas[i] = mappedData
			// 每隔100条数据更新进度
			if i%100 == 0 {
				historyInfo.CompletedCount = int64(i + 1)
				historyInfo.CurrentStep = 1
				queue.QueueManager().Publish(consts.StreamSubjectExcelImportProgress, historyInfo)
			}
		}

		var duplicateCheckField []string
		if importTemplteInfo.DuplicateDataValidation == 1 {
			err = json.Unmarshal([]byte(importTemplteInfo.DuplicateCheckField), &duplicateCheckField)
			library.ErrIsNil(ctx, err)
		}

		// 如果有设置标识字段，则进行去重和合并
		finalMappedDatas := make([]map[string]interface{}, 0)
		if len(duplicateCheckField) > 0 {
			mergedData := make(map[string]map[string]interface{})
			for _, data := range mappedDatas {
				// 生成唯一键用于合并相同的行
				uniqueKey := ""
				for _, field := range duplicateCheckField {
					if value, exists := data[field]; exists {
						uniqueKey += fmt.Sprintf("%v-", value)
					}
				}

				if existingData, exists := mergedData[uniqueKey]; exists {
					// 合并子表数据，根据fieldMapping来判断子表
					for _, mapping := range fieldMapping {
						if mapping.Type == 1 {
							sourceKey := gconv.String(mapping.Source)
							if subTable, ok := data[sourceKey].([]map[string]interface{}); ok {
								existingSubTable, _ := existingData[sourceKey].([]map[string]interface{})
								if existingSubTable != nil {
									existingSubTable = append(existingSubTable, subTable...)
									mergedData[uniqueKey][sourceKey] = existingSubTable
								} else {
									mergedData[uniqueKey][sourceKey] = subTable
								}
							}
						}
					}
				} else {
					mergedData[uniqueKey] = data
				}
			}

			// 将合并后的数据转换回切片
			for _, data := range mergedData {
				finalMappedDatas = append(finalMappedDatas, data)
			}
			// 根据索引字段排序
			sort.SliceStable(finalMappedDatas, func(i, j int) bool {
				return finalMappedDatas[i]["__index"].(int) < finalMappedDatas[j]["__index"].(int)
			})
		} else {
			finalMappedDatas = mappedDatas
		}
		// 纪录数据总条数、成功条数、失败条数
		historyInfo.TotalCount = int64(len(finalMappedDatas))
		if len(duplicateCheckField) > 0 {
			// 处理重复数据和重复处理方式
			for i, finalMappedData := range finalMappedDatas {
				// 每隔100条数据更新进度
				if i%100 == 0 {
					historyInfo.CompletedCount = int64(i + 1)
					historyInfo.CurrentStep = 2
					queue.QueueManager().Publish(consts.StreamSubjectExcelImportProgress, historyInfo)
				}
				// 根据标识字段获得oldid
				ruleFilter := make(map[string]interface{}, 0)
				if len(duplicateCheckField) > 0 {
					for _, field := range duplicateCheckField {
						if value, exists := finalMappedData[field]; exists {
							ruleFilter[field] = value
						}
					}
				}

				oldInfo, err := service.FormData().GetDetailByRuleFilter(ctx, formTemplateInfo.Id, ruleFilter)
				library.ErrIsNil(ctx, err)
				if oldInfo != nil && !g.IsEmpty(oldInfo) {
					// 删除原数据
					if importTemplteInfo.DuplicateHandlingMethod == int(enum.DuplicateHandlingMethod_CancelSingle) {
						rowMsg = append(rowMsg, fmt.Sprintf("第%d行数据导入失败，原因：数据重复，已取消此条数据导入", i+1))
						finalMappedDatas[i]["__cancel_import"] = true
					} else if importTemplteInfo.DuplicateHandlingMethod == int(enum.DuplicateHandlingMethod_CancelAll) {
						library.Fail(ctx, fmt.Sprintf("第%d行数据导入失败，原因：数据重复，已取消所有数据导入", i+1))
					} else if importTemplteInfo.DuplicateHandlingMethod == int(enum.DuplicateHandlingMethod_DeleteOriginal) {
						// 标记需要删除旧数据
						finalMappedDatas[i]["__delete_old_data"] = true
					} else if importTemplteInfo.DuplicateHandlingMethod == int(enum.DuplicateHandlingMethod_MergeExcelPriority) {
						finalMappedDatas[i] = library.MergeMaps(oldInfo, finalMappedData, 2)

					} else if importTemplteInfo.DuplicateHandlingMethod == int(enum.DuplicateHandlingMethod_MergeDbPriority) {
						finalMappedDatas[i] = library.MergeMaps(finalMappedData, oldInfo, 1)
					}
					finalMappedDatas[i]["id"] = oldInfo["id"]
				}
			}
		}
		// 事物提交会导致其他队列任务出现问题，暂时取消
		// err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		for i, finalMappedData := range finalMappedDatas {
			// 每隔100条数据更新进度
			if i%100 == 0 {
				historyInfo.CompletedCount = int64(i + 1)
				historyInfo.CurrentStep = 3
				queue.QueueManager().Publish(consts.StreamSubjectExcelImportProgress, historyInfo)
			}
			if _, exists := finalMappedData["__cancel_import"]; exists {
				continue
			}
			delete(finalMappedData, "__index")
			// 删除旧数据
			if _, exists := finalMappedData["__delete_old_data"]; exists {
				service.FormData().Delete(ctx, formTemplateInfo.Id, gconv.Int64(finalMappedData["id"]))
				delete(finalMappedData, "__delete_old_data")
				delete(finalMappedData, "id")
			}
			// 保存表单数据
			req := &form.FormDataSaveReq{
				FormId: formTemplateInfo.Id,
				Data:   finalMappedData,
			}
			if value, exists := finalMappedData["id"]; exists {
				oldid := gconv.Int64(value)
				if oldid > 0 {
					req.Id = oldid
				}
			}
			_, saveErr := service.FormData().Save(ctx, req, vm, historyInfo.CreatedBy)
			if saveErr != nil {
				rowMsg = append(rowMsg, fmt.Sprintf("第%d行数据导入失败，原因：%s", i+1, saveErr.Error()))
			}
		}
		// return nil
		// })
		library.ErrIsNil(ctx, err)
		historyInfo.SuccessCount = int64(len(finalMappedDatas)) - int64(len(rowMsg))
		historyInfo.FailureCount = int64(len(rowMsg))
		historyInfo.CompletedCount = int64(len(finalMappedDatas))

		historyInfo.FinishdAt = gtime.Now()
		if len(rowMsg) > 200 {
			rowMsg = append(rowMsg[:100], append([]string{"..."}, rowMsg[len(rowMsg)-100:]...)...)
		}
		rowMsg = append(rowMsg, fmt.Sprintf("导入数据成功%d条，失败%d条", historyInfo.SuccessCount, historyInfo.FailureCount))
		resultJson, _ := json.Marshal(rowMsg)
		historyInfo.Result = string(resultJson)
		historyInfo.Status = gconv.Int(lo.Ternary(historyInfo.FailureCount > 0, enum.ImportStatus_PartialFailed, enum.ImportStatus_Completed))
		queue.QueueManager().Publish(consts.StreamSubjectExcelImportProgress, historyInfo)

	})

	// 处理失败，更新状态为失败
	if err != nil {
		historyInfo.Status = gconv.Int(enum.ImportStatus_Failed)
		resultJson, _ := json.Marshal([]string{err.Error()})
		historyInfo.Result = string(resultJson)
		queue.QueueManager().Publish(consts.StreamSubjectExcelImportProgress, historyInfo)
	}
	return
}

// 更新导入数据文件的方法
func (s *sFormImportHistory) Update(ctx context.Context, historyInfo *entity.FormImportHistory) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取导入数据文件的历史记录
		oldInfo, err := s.GetInfoByID(ctx, uint64(historyInfo.Id), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(oldInfo, "导入数据文件不存在")

		// 更新历史记录中的数据
		_, err = dao.TenantCtx(dao.FormImportHistory, ctx).WherePri(historyInfo.Id).Update(historyInfo)
		library.ErrIsNil(ctx, err, "更新导入数据文件历史失败")
	})
	return
}

// 修改导入数据文件状态
func (s *sFormImportHistory) UpdateStatus(ctx context.Context, id int64, status enum.ImportStatus) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.FormImportHistory, ctx).WherePri(id).Update(&do.FormImportHistory{Status: status})
		library.ErrIsNil(ctx, err, "更新导入数据文件状态失败")
	})
	return
}

// 删除导入数据文件
func (s *sFormImportHistory) Delete(ctx context.Context, id uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		historyInfo, err := s.GetInfoByID(ctx, id, false)
		library.ErrIsNil(ctx, err)
		if historyInfo == nil {
			library.Fail(ctx, "历史纪录不存在")
			return
		}
		_, err = dao.TenantCtx(dao.FormImportHistory, ctx).WherePri(id).Delete()
		library.ErrIsNil(ctx, err, "删除导入数据文件历史失败")

	})
	return
}

// 获得详细信息
func (s *sFormImportHistory) GetInfoByID(ctx context.Context, automationId uint64, with bool) (FormImportHistory *entity.FormImportHistory, err error) {
	m := dao.TenantCtx(dao.FormImportHistory, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&FormImportHistory)
	return
}
