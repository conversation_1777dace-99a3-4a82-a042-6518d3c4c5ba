package formcheckrule

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormCheckRule(new(sFormCheckRule))
}

type sFormCheckRule struct{}

// 获取验证规则列表
func (s *sFormCheckRule) GetFormCheckRuleList(ctx context.Context, req *form.FormCheckRuleListReq) (res *form.FormCheckRuleListRes, err error) {
	res = new(form.FormCheckRuleListRes)
	m := dao.TenantCtx(dao.FormCheckRule, ctx)
	m = m.Where(dao.FormCheckRule.Columns().FormTemplateId, req.FormTemplateId)
	if req.Name != "" {
		m = m.Where(dao.FormCheckRule.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取验证规则总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取验证规则数据失败")
	})
	return
}

// 获取启用验证规则列表
func (s *sFormCheckRule) GetEnabledRules(ctx context.Context, formTemplateId int64) (res []*entity.FormCheckRule, err error) {
	m := dao.TenantCtx(dao.FormCheckRule, ctx)
	m = m.Where(dao.FormCheckRule.Columns().FormTemplateId, formTemplateId)
	m = m.Where(dao.FormCheckRule.Columns().Status, 1)
	order := "id asc"
	err = m.Order(order).Scan(&res)
	return
}

// 保存验证规则数据
func (s *sFormCheckRule) Save(ctx context.Context, req *form.FormCheckRuleSaveReq) (res *form.FormCheckRuleSaveRes, err error) {

	res = new(form.FormCheckRuleSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {

		PrintTemplate := new(do.FormCheckRule)
		if req.Id > 0 {
			var editPrintTemplate *entity.FormCheckRule
			editPrintTemplate, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editPrintTemplate, "验证规则信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			PrintTemplate.CreatedBy = library.GetUserId(ctx)
			PrintTemplate.Status = 1
		}
		PrintTemplate.Name = req.Name
		PrintTemplate.FormTemplateId = req.FormTemplateId
		PrintTemplate.CodeContent = req.CodeContent
		PrintTemplate.FailMsg = req.FailMsg

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.FormCheckRule, ctx).WherePri(req.Id).Update(PrintTemplate)
			library.ErrIsNil(ctx, err, "修改验证规则失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.FormCheckRule, ctx).Data(PrintTemplate).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入验证规则失败")
			res.Id = newid
		}

	})
	return

}

// 删除验证规则
func (s *sFormCheckRule) Delete(ctx context.Context, req *form.FormCheckRuleDeleteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(req.Id), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "配置信息不存在")
			return
		}
		_, err = dao.TenantCtx(dao.FormCheckRule, ctx).WherePri(req.Id).Delete()
		library.ErrIsNil(ctx, err, "删除验证规则失败")

	})
	return
}

// 获得详细信息
func (s *sFormCheckRule) GetInfoByID(ctx context.Context, automationId uint64, with bool) (PrintTemplate *entity.FormCheckRule, err error) {
	m := dao.TenantCtx(dao.FormCheckRule, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&PrintTemplate)
	return
}

// 修改状态
func (s *sFormCheckRule) UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(automationId), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "验证规则不存在")
			return
		}

		_, err = dao.TenantCtx(dao.FormCheckRule, ctx).WherePri(automationId).Update(&do.FormCheckRule{Status: lo.Ternary(enabled, 1, 999)})
		if err != nil {
			g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
			library.Fail(ctx, err.Error())
		}

	})
	return
}
