package generate

import (
	"backend/internal/service"
	"backend/library"
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterGenerate(new(sGenerate))
}

type sGenerate struct{}

// 生成指定格式的ID ，例如
// format = "R{20060102150406}{RanNum(5)}{RanStr(5)}{AUTO_INCREMENT({xxxx},5)}" , 或者 "DD{20060102}{AUTO_INCREMENT({xxxx},5)}"
func (s *sGenerate) GenerateID(ctx context.Context, format string) (newid string) {
	randNumFormat := regexp.MustCompile(`\{RanNum\((\d+)\)\}`).FindStringSubmatch(format)
	if randNumFormat != nil {
		n, _ := strconv.Atoi(randNumFormat[1])
		format = strings.Replace(format, randNumFormat[0], library.RandNum(n), 1)
	}

	randStrFormat := regexp.MustCompile(`\{RanStr\((\d+)\)\}`).FindStringSubmatch(format)
	if randStrFormat != nil {
		n, _ := strconv.Atoi(randStrFormat[1])
		format = strings.Replace(format, randStrFormat[0], library.RandSeq(n), 1)
	}
	// 支持 {AUTO_INCREMENT({20060102},6)} 规则
	autoIncrementFormatWithDate := regexp.MustCompile(`\{AUTO_INCREMENT\(\{([^\}-]+)-(2006|200601|20060102|2006010215|201601021504|20160102150405)?\},(\d+)\)\}`).FindStringSubmatch(format)
	if autoIncrementFormatWithDate != nil {
		nextPre := autoIncrementFormatWithDate[1]
		dateFormat := autoIncrementFormatWithDate[2]
		n, _ := strconv.Atoi(autoIncrementFormatWithDate[3])
		now := time.Now()
		nextKey := fmt.Sprintf("GenerateID_%s_%s", nextPre, now.Format(dateFormat))
		nextid, err := service.Cache().GetNextId(ctx, nextKey)
		if err == nil {
			incrementStr := library.AddZero(gconv.Uint64(nextid), n)
			format = strings.Replace(format, autoIncrementFormatWithDate[0], incrementStr, 1)
		}
	}

	now := time.Now()
	datePattern := regexp.MustCompile(`\{[\d]+}`) // Match patterns like "{20060102}" or "{20060102150405}"
	dateFormatMatches := datePattern.FindAllStringSubmatch(format, -1)
	for _, match := range dateFormatMatches {
		dateFormat := strings.Trim(match[0], "{}")
		format = strings.Replace(format, match[0], now.Format(dateFormat), 1)
	}

	return format
}
