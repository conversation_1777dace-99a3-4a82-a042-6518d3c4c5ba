package sysrole

import (
	"context"

	"backend/api/v1/system"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterSysRole(new(sSysRole))
}

type sSysRole struct{}

// 获取角色列表
func (s *sSysRole) GetRoleList(ctx context.Context, req *system.SysRoleListReq) (res *system.SysRoleListRes, err error) {
	res = new(system.SysRoleListRes)
	m := dao.TenantCtx(dao.SysRole, ctx)
	if req.Name != "" {
		m = m.Where(dao.SysRole.Columns().Name+" like ?", "%"+req.Name+"%")
	}
	if req.Code != "" {
		m = m.Where(dao.SysRole.Columns().Code+" like ?", "%"+req.Code+"%")
	}
	order := "list_order desc,id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取角色总数失败")
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List) //.Fields(dao.SysRole.Columns())
		library.ErrIsNil(ctx, err, "获取日志数据失败")
	})
	return
}

// 保存角色数据
func (s *sSysRole) Save(ctx context.Context, req *system.SysRoleSaveReq) (res *system.SysRoleSaveRes, err error) {

	res = new(system.SysRoleSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {
		role := new(do.SysRole)
		if req.Id > 0 {
			var editRole *entity.SysRole
			editRole, err = s.GetInfoByID(ctx, uint64(req.Id))
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editRole, "角色信息不存在")
		} else {
			maxId, err := s.GetMaxID(ctx)
			library.ErrIsNil(ctx, err)
			role.Code = library.GenerateID("ROLE{20060102}{AUTO_INCREMENT(5)}", int64(maxId))
		}
		role.Name = req.Name
		role.Status = req.Status
		role.Remark = req.Remark
		role.ListOrder = req.ListOrder
		role.Permissions = req.Permissions

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.SysRole, ctx).WherePri(req.Id).Update(role)
			library.ErrIsNil(ctx, err, "修改角色失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.SysRole, ctx).Data(role).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入角色失败")
			res.Id = uint(newid)
		}
		// 清除角色列表缓存
		service.Cache().Remove(ctx, consts.KeyRoleList+gconv.String(library.GetTenantID(ctx)))
	})
	return

}

// 删除角色
func (s *sSysRole) Delete(ctx context.Context, req *system.SysRoleDelReq) (err error) {
	_, err = dao.TenantCtx(dao.SysRole, ctx).WherePri(req.Id).Delete()
	library.ErrIsNil(ctx, err, "删除角色失败")
	// 清除角色列表缓存
	service.Cache().Remove(ctx, consts.KeyRoleList+gconv.String(library.GetTenantID(ctx)))
	return
}

// 获得详细信息
func (s *sSysRole) GetInfoByID(ctx context.Context, roleId uint64) (role *entity.SysRole, err error) {
	err = dao.TenantCtx(dao.SysRole, ctx).WherePri(roleId).Scan(&role)
	return
}

// 获得最大ID
func (s *sSysRole) GetMaxID(ctx context.Context) (maxId uint64, err error) {
	_maxid, err := dao.TenantCtx(dao.SysRole, ctx).Max(dao.SysRole.Columns().Id)
	if err != nil {
		return
	}
	maxId = gconv.Uint64(_maxid)
	return
}

// 获取角色列表缓存
func (s *sSysRole) GetListCache(ctx context.Context) (list []*entity.SysRole, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cList, err := service.Cache().GetOrSetFunc(ctx, consts.KeyRoleList+gconv.String(library.GetTenantID(ctx)), func(ctx context.Context) (value interface{}, err error) {
			err = dao.TenantCtx(dao.SysRole, ctx).Scan(&list)
			library.ErrIsNil(ctx, err, "获取角色列表失败")
			value = list
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		if cList != nil {
			err := gconv.Struct(cList, &list)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

// 设置角色权限
func (s *sSysRole) SetRolePermissions(ctx context.Context, req *system.SysRoleSetPermsReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.SysRole, ctx).WherePri(req.Id).Update(&do.SysRole{Permissions: req.Permissions})
		library.ErrIsNil(ctx, err, "设置角色权限失败")
		// 清除角色列表缓存
		service.Cache().Remove(ctx, consts.KeyRoleList+gconv.String(library.GetTenantID(ctx)))
	})

	return
}
