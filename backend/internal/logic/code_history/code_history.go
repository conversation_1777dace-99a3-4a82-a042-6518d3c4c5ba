package codehistory

import (
	"context"

	"backend/api/v1/code"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	service.RegisterCodeHistory(new(sCodeHistory))
}

type sCodeHistory struct{}

// 获取代码库列表
func (s *sCodeHistory) GetCodeHistoryList(ctx context.Context, req *code.CodeHistoryListReq) (res *code.CodeHistoryListRes, err error) {
	res = new(code.CodeHistoryListRes)
	m := dao.TenantCtx(dao.CodeHistory, ctx)
	if req.KeyWork != "" {
		m = m.Where(dao.CodeHistory.Columns().Title+" like ?", "%"+req.KeyWork+"%")
		m = m.WhereOr(dao.CodeHistory.Columns().Instruction+" like ?", "%"+req.<PERSON>Work+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取代码库总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取代码库数据失败")
	})
	return
}

// 保存代码库数据
func (s *sCodeHistory) Save(ctx context.Context, req *code.CodeHistorySaveReq) (res *code.CodeHistorySaveRes, err error) {

	res = new(code.CodeHistorySaveRes)
	err = g.Try(ctx, func(ctx context.Context) {

		PrintTemplate := new(do.CodeHistory)
		if req.Id > 0 {
			var editPrintTemplate *entity.CodeHistory
			editPrintTemplate, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editPrintTemplate, "代码库信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			PrintTemplate.CreatedBy = library.GetUserId(ctx)

		}
		PrintTemplate.Title = req.Title
		PrintTemplate.Instruction = req.Instruction
		PrintTemplate.Content = req.Content

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.CodeHistory, ctx).WherePri(req.Id).Update(PrintTemplate)
			library.ErrIsNil(ctx, err, "修改代码库失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.CodeHistory, ctx).Data(PrintTemplate).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入代码库失败")
			res.Id = newid
		}

	})
	return

}

// 删除代码库
func (s *sCodeHistory) Delete(ctx context.Context, req *code.CodeHistoryDeleteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(req.Id), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "配置信息不存在")
			return
		}
		_, err = dao.TenantCtx(dao.CodeHistory, ctx).WherePri(req.Id).Delete()
		library.ErrIsNil(ctx, err, "删除代码库失败")

	})
	return
}

// 获得详细信息
func (s *sCodeHistory) GetInfoByID(ctx context.Context, automationId uint64, with bool) (PrintTemplate *entity.CodeHistory, err error) {
	m := dao.TenantCtx(dao.CodeHistory, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&PrintTemplate)
	return
}
