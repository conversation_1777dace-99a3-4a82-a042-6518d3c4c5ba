package uploafile

import (
	"backend/internal/service"
	"backend/library"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
)

func init() {
	service.RegisterUploadFile(new(sUploadFile))
}

type sUploadFile struct{}

// 上传图片
func (s *sUploadFile) UploadImage(ctx context.Context, file []*ghttp.UploadFile) (res []string, err error) {
	if g.<PERSON>(file) || len(file) <= 0 {
		err = fmt.Errorf("上传文件不能为空")
		return
	}
	for _, f := range file {
		if f == nil {
			continue
		}

		allowTypes := []string{
			"image/jpeg",
			"image/png",
			"image/gif",
			"image/bmp",
			"image/tiff",
			"image/webp",
			"image/svg+xml",
		}

		if !g.<PERSON>(allowTypes) && len(allowTypes) > 0 {
			mimeType := f.FileHeader.Header.Get("Content-Type")
			if !library.Contains(allowTypes, mimeType) {
				err = fmt.Errorf("非图片:%s", f.Filename)
				return
			}
		}
	}
	return s.UploadFile(ctx, file)
}

// 上传文件
func (s *sUploadFile) UploadFile(ctx context.Context, file []*ghttp.UploadFile) (res []string, err error) {
	if g.IsEmpty(file) || len(file) <= 0 {
		err = fmt.Errorf("上传文件不能为空")
		return
	}
	// 获得上传文件的配置
	uploadPath := g.Cfg().MustGet(ctx, "upload.path").String()

	if g.IsEmpty(uploadPath) {
		err = fmt.Errorf("upload.path 未配置")
		return
	}
	dateDirName := gtime.Now().Format("Ymd")
	library.IsNotExistMkdirPath(ctx, gfile.Join(uploadPath, dateDirName))
	for _, f := range file {
		if f == nil {
			err = fmt.Errorf("上传文件不能为空")
			return
		}
		// 获得文件大小
		fileSize := f.Size / 1024 / 1024
		uploadMaxFileSize := g.Cfg().MustGet(ctx, "upload.maxSize").Int64()
		if uploadMaxFileSize > 0 && fileSize > uploadMaxFileSize {
			err = fmt.Errorf("上传文件大小不能超过%dMB", uploadMaxFileSize)
			return
		}

		allowTypes := g.Cfg().MustGet(ctx, "upload.allowTypes").Strings()

		if !g.IsEmpty(allowTypes) && len(allowTypes) > 0 {
			mimeType := f.FileHeader.Header.Get("Content-Type")
			if !library.Contains(allowTypes, mimeType) {
				err = fmt.Errorf("上传文件类型不允许:%s", f.Filename)
				return
			}
		}
	}

	for _, f := range file {
		// 获得文件后缀名
		fileExt := gfile.Ext(f.Filename)

		var randFilename string
		randDirame := fmt.Sprintf("%s_%s", library.RandSeq(16), fileExt[1:])
		// randDirame := fmt.Sprintf("%s_%s", library.RandSeq(16), "")

		// 保存文件
		filePath := gfile.Join(uploadPath, dateDirName, randDirame)
		randFilename, err = f.Save(filePath)
		if err != nil {
			return
		}
		res = append(res, fmt.Sprintf("/files/%s/%s/%s", dateDirName, randDirame, randFilename))
	}
	return
}
