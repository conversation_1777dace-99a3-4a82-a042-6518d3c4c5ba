package formcountersignlogs

import (
	"backend/api/v1/form"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

type sFormCountersignLogs struct{}

func init() {
	service.RegisterFormCountersignLogs(New())
}

func New() *sFormCountersignLogs {
	return &sFormCountersignLogs{}
}

// Create 创建回签记录
func (s *sFormCountersignLogs) Create(ctx context.Context, in *do.FormCountersignLogs) (insertid int64, err error) {
	// 构造锁的key，包含足够的信息以确保唯一性
	lockKey := fmt.Sprintf("countersign_lock:%d:%d",
		in.FormTemplateId, in.FormDataId)

	// 尝试获取分布式锁
	lockId, err := service.Cache().TryLock(ctx, lockKey, 10) // 10秒TTL
	if err != nil {
		g.Log().Warning(ctx, fmt.Sprintf("操作太快啦: %v", err))
		return 0, err
	}

	// 确保释放锁
	defer service.Cache().UnLock(ctx, lockKey, lockId)

	// 在锁保护下执行检查和创建操作
	m := dao.TenantCtx(dao.FormCountersignLogs, ctx)
	count, err := m.Where(dao.FormCountersignLogs.Columns().FormTemplateId, in.FormTemplateId).
		Where(dao.FormCountersignLogs.Columns().FormDataId, in.FormDataId).
		Where(dao.FormCountersignLogs.Columns().CreatedBy, in.CreatedBy).
		Count()
	if err != nil {
		return 0, err
	}

	// 如果已经回签过，则不再创建
	if count > 0 {
		g.Log().Info(ctx, fmt.Sprintf("用户 %d 已对表单(模板ID: %d, 数据ID: %d)回签过，跳过创建",
			in.CreatedBy, in.FormTemplateId, in.FormDataId))
		return 0, nil
	}

	// 创建回签记录
	insertid, err = dao.TenantCtx(dao.FormCountersignLogs, ctx).Data(in).InsertAndGetId()
	return
}

// CreateByIds 通过表单模板ID和数据ID创建回签记录
func (s *sFormCountersignLogs) CreateByIds(ctx context.Context, formTemplateId, formDataId int64, content, remark string) (insertid int64, err error) {
	// 创建回签记录
	err = g.Try(ctx, func(ctx context.Context) {
		// 创建回签数据
		in := &do.FormCountersignLogs{
			FormTemplateId:    formTemplateId,
			FormDataId:        formDataId,
			CountersignRemark: remark,
			CreatedBy:         int64(library.GetUserId(ctx)),
		}
		if content != "" {
			in.CountersignContent = content
		}

		insertid, err = s.Create(ctx, in)
		library.ErrIsNil(ctx, err, "创建回签记录失败")
	})
	return
}

// CreateByGlobalId 通过全局ID创建回签记录
func (s *sFormCountersignLogs) CreateByGlobalId(ctx context.Context, globalId string, content string) (insertid int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 解析全局ID
		_, formTemId, formDataId, err := service.FormData().ParseGlobalId(ctx, globalId)
		library.ErrIsNil(ctx, err, "解析全局ID失败")

		g.Log().Info(ctx, fmt.Sprintf("通过全局ID %s 解析得到模板ID: %d, 数据ID: %d",
			globalId, formTemId, formDataId))

		// 调用按ID创建的方法
		insertid, err = s.CreateByIds(ctx, int64(formTemId), formDataId, content, "扫码回签")
		library.ErrIsNil(ctx, err, "创建回签记录失败")
	})
	return
}

// GetById 根据ID获取回签记录
func (s *sFormCountersignLogs) GetById(ctx context.Context, id int64) (out *entity.FormCountersignLogs, err error) {
	err = dao.TenantCtx(dao.FormCountersignLogs, ctx).Where(dao.FormCountersignLogs.Columns().Id, id).Scan(&out)
	return
}

// GetByFormDataId 根据表单数据ID获取回签记录
func (s *sFormCountersignLogs) GetByFormId(ctx context.Context, formTemplateId, formDataId int64) (out *entity.FormCountersignLogs, err error) {
	err = dao.TenantCtx(dao.FormCountersignLogs, ctx).Where(dao.FormCountersignLogs.Columns().FormDataId, formDataId).Where(dao.FormCountersignLogs.Columns().FormTemplateId, formTemplateId).Scan(&out)
	return
}

// GetByFormTemplateId 根据表单模板ID获取回签记录
func (s *sFormCountersignLogs) GetByFormTemplateId(ctx context.Context, formTemplateId int64) (out []*entity.FormCountersignLogs, err error) {
	err = dao.TenantCtx(dao.FormCountersignLogs, ctx).Where(dao.FormCountersignLogs.Columns().FormTemplateId, formTemplateId).Scan(&out)
	return
}

// Update 更新回签记录
func (s *sFormCountersignLogs) Update(ctx context.Context, in *do.FormCountersignLogs) (err error) {
	_, err = dao.TenantCtx(dao.FormCountersignLogs, ctx).Data(in).Where(dao.FormCountersignLogs.Columns().Id, in.Id).Update()
	return
}

// Delete 删除回签记录
func (s *sFormCountersignLogs) Delete(ctx context.Context, id int64) (err error) {
	_, err = dao.TenantCtx(dao.FormCountersignLogs, ctx).Where(dao.FormCountersignLogs.Columns().Id, id).Delete()
	return
}

// 获取列表
func (s *sFormCountersignLogs) GetProjectList(ctx context.Context, req *form.FormCountersignLogListReq) (res *form.FormCountersignLogListRes, err error) {
	res = new(form.FormCountersignLogListRes)
	m := dao.TenantCtx(dao.FormCountersignLogs, ctx)

	order := "id desc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List) //.Fields(dao.SysProject.Columns())
		library.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}
