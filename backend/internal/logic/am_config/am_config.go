package amconfig

import (
	"context"
	"encoding/json"
	"fmt"

	"backend/api/v1/automation"
	"backend/automation/manager"
	"backend/automation/types"
	"backend/automation/workflow"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterAmConfig(new(sAmConfig))
}

type sAmConfig struct{}

// 获取自动化配置列表
func (s *sAmConfig) GetAutoMationList(ctx context.Context, req *automation.ListReq) (res *automation.ListRes, err error) {
	res = new(automation.ListRes)
	m := dao.TenantCtx(dao.AmConfig, ctx)
	if req.Name != "" {
		m = m.Where(dao.AmConfig.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取自动化配置总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List) //.Fields(dao.SysAmConfig.Columns())
		library.ErrIsNil(ctx, err, "获取自动化配置数据失败")
	})
	return
}

// 获取自动化配置的依赖
func (s *sAmConfig) GetDepend(ctx context.Context, req *automation.DependReq) (res *automation.DependRes, err error) {
	res = new(automation.DependRes)
	m := dao.TenantCtx(dao.AmConfig, ctx)
	if req.Id > 0 {
		m = m.WhereNot(dao.AmConfig.Columns().Id, req.Id)
	}
	err = g.Try(ctx, func(ctx context.Context) {
		err = m.Fields(dao.AmConfig.Columns().Id, dao.AmConfig.Columns().Name).Scan(&res.List)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 获得所有启动中的自动化配置
func (s *sAmConfig) GetAllRunning(ctx context.Context) (res []*entity.AmConfig, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.TenantCtx(dao.AmConfig, ctx).Where(dao.AmConfig.Columns().Status, 1).Scan(&res)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 保存自动化配置数据
func (s *sAmConfig) Save(ctx context.Context, req *automation.AmConfigSaveReq) (res *automation.AmConfigSaveRes, err error) {

	res = new(automation.AmConfigSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {

		triggerNodes, nodeTrigger, err := CreateTrigger(ctx, gconv.String(req.FlowSchema))
		library.ErrIsNil(ctx, err)

		AutoMation := new(do.AmConfig)
		if req.Id > 0 {
			var editAutoMation *entity.AmConfig
			editAutoMation, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editAutoMation, "自动化配置信息不存在")
			AutoMation.Version = editAutoMation.Version + 1
		} else {
			library.ErrIsNil(ctx, err)
			AutoMation.CreatedBy = library.GetUserId(ctx)
			AutoMation.Status = 1
			AutoMation.Version = 1
		}
		AutoMation.Name = req.Name
		AutoMation.FlowSchema = req.FlowSchema
		AutoMation.TriggerData = triggerNodes[0].Data
		AutoMation.Depend = req.Depend

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.AmConfig, ctx).WherePri(req.Id).Update(AutoMation)
			library.ErrIsNil(ctx, err, "修改自动化配置失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.AmConfig, ctx).Data(AutoMation).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入自动化配置失败")
			res.Id = newid
		}
		// 创建此触发器需要执行的内容
		nodeTrigger.SetInputs(map[string]interface{}{"am_config_id": res.Id, "version": AutoMation.Version, "depend": req.Depend})
		nodeTrigger.TriggerCreate(triggerNodes[0].Data)

		// 清除自动化配置列表缓存
	})
	return
}

// 删除自动化配置
func (s *sAmConfig) Delete(ctx context.Context, req *automation.AmConfigDeleteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(req.Id), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "配置信息不存在")
			return
		}
		_, err = dao.TenantCtx(dao.AmConfig, ctx).WherePri(req.Id).Delete()
		library.ErrIsNil(ctx, err, "删除自动化配置失败")
		// 清除自动化配置列表缓存
		triggerNodes, nodeTrigger, err := CreateTrigger(ctx, amconfig.FlowSchema)
		library.ErrIsNil(ctx, err)
		depend := make([]int64, 0)
		if amconfig.Depend != "" {
			err = json.Unmarshal([]byte(amconfig.Depend), &depend)
			library.ErrIsNil(ctx, err)
		}
		nodeTrigger.SetInputs(map[string]interface{}{"am_config_id": amconfig.Id, "version": amconfig.Version, "depend": depend})
		nodeTrigger.TriggerRemove(triggerNodes[0].Data)
	})
	return
}

// 系统系统时初始化触发器
func (s *sAmConfig) InitTrigger(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var amconfigs []*entity.AmConfig
		err = dao.TenantCtx(dao.AmConfig, ctx).Where(dao.AmConfig.Columns().Status, 1).Scan(&amconfigs)
		library.ErrIsNil(ctx, err)
		for _, amconfig := range amconfigs {
			triggerNodes, nodeTrigger, err := CreateTrigger(ctx, amconfig.FlowSchema)
			library.ErrIsNil(ctx, err)
			depend := make([]int64, 0)
			if amconfig.Depend != "" {
				err = json.Unmarshal([]byte(amconfig.Depend), &depend)
				library.ErrIsNil(ctx, err)
			}
			nodeTrigger.SetInputs(map[string]interface{}{"am_config_id": amconfig.Id, "version": amconfig.Version, "depend": depend})
			nodeTrigger.TriggerCreate(triggerNodes[0].Data)
		}
	})
	return
}

// 获得详细信息
func (s *sAmConfig) GetInfoByID(ctx context.Context, automationId uint64, with bool) (AutoMation *entity.AmConfig, err error) {
	m := dao.TenantCtx(dao.AmConfig, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&AutoMation)
	return
}

// 修改状态
func (s *sAmConfig) UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(automationId), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "配置信息不存在")
			return
		}

		_, err = dao.TenantCtx(dao.AmConfig, ctx).WherePri(automationId).Update(&do.AmConfig{Status: lo.Ternary(enabled, 1, 999)})
		if err != nil {
			g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
			library.Fail(ctx, err.Error())
		}

		// 自动化配置触发器处理
		triggerNodes, nodeTrigger, err := CreateTrigger(ctx, amconfig.FlowSchema)
		library.ErrIsNil(ctx, err)
		depend := make([]int64, 0)
		if amconfig.Depend != "" {
			err = json.Unmarshal([]byte(amconfig.Depend), &depend)
			library.ErrIsNil(ctx, err)
		}
		nodeTrigger.SetInputs(map[string]interface{}{"am_config_id": amconfig.Id, "version": amconfig.Version, "depend": depend})
		if enabled {
			nodeTrigger.TriggerCreate(triggerNodes[0].Data)
		} else {
			nodeTrigger.TriggerRemove(triggerNodes[0].Data)
		}
	})
	return
}

// 根据smemberKey触发自动化配置
func (s *sAmConfig) TriggerBySmemberKey(ctx context.Context, smemberKey string, triggerData map[string]interface{}, sequence int64) (affectedRows int64, err error) {
	// 检验自动化配置中是否存在关联触发器
	amConfigs, err := service.Cache().SMembers(ctx, smemberKey)
	if err != nil {
		return
	}

	// 防循环检查：检查数据是否由自动化流程触发
	triggerType, hasTriggerType := triggerData["trigger_type"]
	triggerSourceId, hasTriggerSourceId := triggerData["trigger_source_id"]

	type TriggerInfo struct {
		TriggerItem *dto.TriggerInput
		RawData     string
	}

	triggerMap := make(map[int64]*TriggerInfo) // 用于存储最高版本的触发器及其原始数据

	if len(amConfigs) > 0 {
		for _, amConfigInfo := range amConfigs.Strings() {
			if amConfigInfo == "" {
				continue
			}
			var triggerItem *dto.TriggerInput
			err = json.Unmarshal([]byte(amConfigInfo), &triggerItem)

			// 触发器数据格式不正确或已过时，删除之
			if err != nil || triggerItem == nil || triggerItem.Version <= 0 || triggerItem.AmConfigId <= 0 {
				service.Cache().SRem(ctx, smemberKey, amConfigInfo)
				g.Log().Error(ctx, fmt.Sprintf("触发器数据格式不正确或已过时，删除之：%s", amConfigInfo))
				continue
			}

			// 防循环检查：如果数据来源是自动化流程，且来源ID与当前触发器匹配，则跳过
			if hasTriggerType && hasTriggerSourceId &&
				gconv.String(triggerType) == "automation" &&
				gconv.String(triggerSourceId) == gconv.String(triggerItem.AmConfigId) {
				g.Log().Info(ctx, fmt.Sprintf("检测到数据由当前自动化流程(%s)触发，跳过执行防止循环", gconv.String(triggerSourceId)))
				continue
			}

			// 只保留最高版本的触发器，并删除较低版本
			existingTriggerInfo, exists := triggerMap[triggerItem.AmConfigId]
			if !exists || triggerItem.Version > existingTriggerInfo.TriggerItem.Version {
				if exists {
					// 删除旧版本
					service.Cache().SRem(ctx, smemberKey, existingTriggerInfo.RawData)
				}
				triggerMap[triggerItem.AmConfigId] = &TriggerInfo{TriggerItem: triggerItem, RawData: amConfigInfo}
			} else {
				// 删除当前较低版本
				service.Cache().SRem(ctx, smemberKey, amConfigInfo)
			}
		}
	}

	// 将 map 转换为 slice
	triggerList := lo.Map(lo.Values(triggerMap), func(info *TriggerInfo, _ int) *dto.TriggerInput {
		return info.TriggerItem
	})

	// 根据依赖关系进行拓扑排序
	sortedList, err := topologicalSort(triggerList)
	library.ErrIsNil(ctx, err)

	// 按顺序执行自动化流程
	for _, data := range sortedList {
		code, triggerErr := s.Trigger(ctx, data.AmConfigId, data.Version, triggerData, sequence)
		if code == -1001 {
			g.Log().Error(ctx, fmt.Sprintf("触发器版本已过时，删除之：%d", data.AmConfigId))
			service.Cache().SRem(ctx, smemberKey, data.AmConfigId)
		}
		if triggerErr != nil {
			if err != nil {
				err = fmt.Errorf("触发自动化流程失败: %w; \r\n %v", err, triggerErr)
			} else {
				err = fmt.Errorf("触发自动化流程失败: %v", triggerErr)
			}
			continue
		}
		affectedRows++
	}
	return
}

// 拓扑排序函数
func topologicalSort(triggerDataList []*dto.TriggerInput) ([]*dto.TriggerInput, error) {
	// 构建图
	graph := make(map[int64][]int64)
	inDegree := make(map[int64]int)

	for _, data := range triggerDataList {
		for _, dep := range data.Depend {
			_, exists := lo.Find(triggerDataList, func(item *dto.TriggerInput) bool {
				return item.AmConfigId == dep
			})
			if exists {
				graph[dep] = append(graph[dep], data.AmConfigId)
				inDegree[data.AmConfigId]++
			}
		}
	}

	// 初始化队列
	queue := make([]int64, 0)
	for _, data := range triggerDataList {
		if inDegree[data.AmConfigId] == 0 {
			queue = append(queue, data.AmConfigId)
		}
	}

	// 执行拓扑排序
	sortedList := make([]*dto.TriggerInput, 0)
	visited := make(map[int64]bool) // 记录访问过的节点
	for len(queue) > 0 {
		id := queue[0]
		queue = queue[1:]

		if visited[id] {
			continue
		}
		visited[id] = true

		data, exists := lo.Find(triggerDataList, func(item *dto.TriggerInput) bool {
			return item.AmConfigId == id
		})
		if exists {
			sortedList = append(sortedList, data)
		}

		for _, neighbor := range graph[id] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}

	// 不再检查环，直接返回已排序的列表
	return sortedList, nil
}

// 初始化工作流
func (s *sAmConfig) initializeWorkflow(ctx context.Context, flowSchema string, triggerData map[string]interface{}) (*workflow.Workflow, []*dto.AMFlowSchemaNode, []*dto.AMFlowSchemaEdge, error) {
	nodes, _, edges, err := CheckAMFlowSchema(ctx, flowSchema)
	if err != nil {
		return nil, nil, nil, err
	}

	workflow := workflow.NewWorkflow()
	workflow.SetTriggerData(triggerData)

	// 添加节点
	for _, node := range nodes {
		if node.Data.Group == "trigger" {
			workflow.AddNode(node.Data.Id, node.Data.Type, triggerData, node.Data)
		} else {
			// 为普通节点准备基础输入参数（包含自动化流程ID）
			baseNodeInputs := map[string]interface{}{
				"automation_id": triggerData["automation_id"],
			}
			workflow.AddNode(node.Data.Id, node.Data.Type, baseNodeInputs, node.Data)
		}
	}

	// 添加连接线
	for _, edge := range edges {
		condition := ""
		if edge.Source.Port == "success" {
			condition = "success"
		} else if edge.Source.Port == "fail" {
			condition = "fail"
		}
		workflow.AddDataFlow(edge.Source.Cell, edge.Source.Cell, edge.Target.Cell, edge.Source.Cell, condition)
	}

	return workflow, nodes, edges, nil
}

// 前向重试
func (s *sAmConfig) Retry(ctx context.Context, amConfigHistoryId int64) (err error) {
	lockkey := fmt.Sprintf("am_config_retry_%d", amConfigHistoryId)
	lockId, err := service.Cache().TryLock(ctx, lockkey, 10)
	if err != nil || lockId == "" {
		return fmt.Errorf("获取锁失败，请稍后再试")
	}
	defer service.Cache().UnLock(ctx, lockkey, lockId)
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得执行历史信息
		historyInfo, err := service.AmConfigHistory().GetInfoByID(ctx, uint64(amConfigHistoryId), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(historyInfo, "执行历史信息不存在")

		// 验证状态
		if historyInfo.Status != int(enum.AmStatus_Failed) {
			library.Fail(ctx, "执行失败的任务才能重试")
		}

		// 获取失败的状态信息，用于恢复
		var workflowState *workflow.WorkflowState
		err = json.Unmarshal([]byte(lo.Ternary(historyInfo.WorkflowState != "", historyInfo.WorkflowState, "{}")), &workflowState)
		library.ErrIsNil(ctx, err)
		if workflowState == nil || len(workflowState.PendingQueue) <= 0 {
			library.Fail(ctx, "未保存流程运行时状态，无法重试")
		}

		// 获得自动化配置信息
		amconfig, err := s.GetInfoByID(ctx, uint64(historyInfo.AmConfigId), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(amconfig, "自动化配置信息不存在")

		// 初始化工作流
		workflow, _, _, err := s.initializeWorkflow(ctx, amconfig.FlowSchema, workflowState.TriggerData)
		library.ErrIsNil(ctx, err)

		var logs []types.Log
		err = json.Unmarshal([]byte(historyInfo.NodeLog), &logs)
		if err != nil {
			logs = make([]types.Log, 0)
		}
		var status int
		var resultMsg string

		err = g.Try(ctx, func(ctx context.Context) {
			workflow.RecoverExecution(workflowState)
			status = int(enum.AmStatus_Success)
			resultMsg = "执行成功"
		})

		// 合并日志
		logs = append(logs, workflow.GetLogs()...)
		logsBytes, _ := json.Marshal(logs)

		// 根据执行结果设置状态
		if err != nil {
			status = int(enum.AmStatus_Failed)
			resultMsg = err.Error()
			workflowState = workflow.SaveState()
		}

		// 更新历史记录
		service.AmConfigHistory().Update(ctx, &do.AmConfigHistory{
			Id:            historyInfo.Id,
			NodeLog:       string(logsBytes),
			Status:        status,
			ResultMsg:     resultMsg,
			WorkflowState: workflowState,
		})
		library.ErrIsNil(ctx, err)

	})
	return
}

// 触发自动化流程
func (s *sAmConfig) Trigger(ctx context.Context, automationId int64, version int64, triggerData map[string]interface{}, sequence int64) (code int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(automationId), false)
		library.ErrIsNil(ctx, err)
		if g.IsNil(amconfig) || amconfig.Version != version {
			code = -1001
			library.Fail(ctx, "自动化配置版本不一致")
		}

		historyInfo, err := service.AmConfigHistory().New(ctx, automationId, triggerData, sequence)
		library.ErrIsNil(ctx, err)

		if amconfig.Status != 1 {
			library.Fail(ctx, "自动化配置未启用")
		}

		// 在触发数据中加入触发来源标记
		if triggerData == nil {
			triggerData = make(map[string]interface{})
		}
		// 标记数据来源为自动化流程
		triggerData["automation_id"] = automationId

		workflow, _, _, err := s.initializeWorkflow(ctx, amconfig.FlowSchema, triggerData)
		library.ErrIsNil(ctx, err)

		err = g.Try(ctx, func(ctx context.Context) {
			// 执行工作流
			workflow.Execute()
			historyInfo.NodeLog = workflow.GetLogs()
			historyInfo.Status = enum.AmStatus_Success
			historyInfo.ResultMsg = "执行成功"
			service.AmConfigHistory().Update(ctx, historyInfo)
		})

		if err != nil {
			g.Log().Error(ctx, err)
			historyInfo.Status = enum.AmStatus_Failed
			historyInfo.NodeLog = workflow.GetLogs()
			historyInfo.ResultMsg = err.Error()
			historyInfo.WorkflowState = workflow.SaveState()
			service.AmConfigHistory().Update(ctx, historyInfo)
		}
		library.ErrIsNil(ctx, err)
	})
	return
}

// 验证flowSchema格式
func CheckAMFlowSchema(ctx context.Context, flowSchema string) (nodes []*dto.AMFlowSchemaNode, triggerNodes []*dto.AMFlowSchemaNode, edges []*dto.AMFlowSchemaEdge, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var flow_schema dto.AMFlowSchema
		err = json.Unmarshal([]byte(flowSchema), &flow_schema)
		if err != nil {
			g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
			library.Fail(ctx, "自动化流程配置格式错误")
		}
		triggerNodes = lo.Filter(flow_schema.Nodes, func(node *dto.AMFlowSchemaNode, _ int) bool {
			return node.Data.Group == "trigger"
		})
		if len(triggerNodes) == 0 {
			library.Fail(ctx, "自动化流程配置中缺少触发器节点")
		}
		if len(triggerNodes) > 1 {
			library.Fail(ctx, "自动化流程配置中只能有一个触发器节点")
		}
		nodes = flow_schema.Nodes
		edges = flow_schema.Edges

	})
	return
}

// 创建自动化触发器
func CreateTrigger(ctx context.Context, flowSchema string) (triggerNodes []*dto.AMFlowSchemaNode, trigger types.INodeTrigger, err error) {
	// 自动化配置触发器处理
	_, triggerNodes, _, err = CheckAMFlowSchema(ctx, flowSchema)
	if err != nil {
		g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
		return
	}
	trigger, err = manager.CreateNodeTrigger(triggerNodes[0].Data.Type)
	return
}
