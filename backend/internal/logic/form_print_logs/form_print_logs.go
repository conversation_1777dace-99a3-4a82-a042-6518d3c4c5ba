package formprintlogs

import (
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type sFormPrintLogs struct{}

func init() {
	service.RegisterFormPrintLogs(New())
}

func New() *sFormPrintLogs {
	return &sFormPrintLogs{}
}

// Create 创建打印记录
func (s *sFormPrintLogs) Create(ctx context.Context, in *do.FormPrintLogs) (insertid int64, err error) {
	// 构造锁的key，包含足够的信息以确保唯一性
	lockKey := fmt.Sprintf("print_lock:%d:%d",
		in.FormTemplateId, in.FormDataId)

	// 尝试获取分布式锁
	lockId, err := service.Cache().TryLock(ctx, lockKey, 10) // 10秒TTL
	if err != nil {
		g.Log().Warning(ctx, fmt.Sprintf("操作太快啦: %v", err))
		return 0, err
	}

	// 确保释放锁
	defer service.Cache().UnLock(ctx, lockKey, lockId)

	// 在锁保护下执行检查和创建操作
	m := dao.TenantCtx(dao.FormPrintLogs, ctx)
	var existingLog *entity.FormPrintLogs
	err = m.Where(dao.FormPrintLogs.Columns().FormTemplateId, in.FormTemplateId).
		Where(dao.FormPrintLogs.Columns().FormDataId, in.FormDataId).
		Scan(&existingLog)
	if err != nil {
		return 0, err
	}

	printDetailsItem := &dto.PrintDetails{
		PrintUser:       int64(library.GetUserId(ctx)),
		PrintTime:       gtime.Now(),
		PrintTemplateId: gconv.Int64(in.FormPrintTemplateId),
	}

	// 如果已经存在打印记录，则更新打印次数和最后打印时间
	if existingLog != nil {
		printDetails := []dto.PrintDetails{}
		err = json.Unmarshal([]byte(lo.Ternary(existingLog.PrintDetails == "", "[]", existingLog.PrintDetails)), &printDetails)
		if err != nil {
			return 0, err
		}
		printDetails = append(printDetails, *printDetailsItem)
		printDetailsJson, err := json.Marshal(printDetails)
		if err != nil {
			return 0, err
		}

		_, err = m.Where(dao.FormPrintLogs.Columns().Id, existingLog.Id).Data(g.Map{
			dao.FormPrintLogs.Columns().PrintCount:    existingLog.PrintCount + 1,
			dao.FormPrintLogs.Columns().LastPrintedAt: gtime.Now(),
			dao.FormPrintLogs.Columns().PrintDetails:  printDetailsJson,
		}).Update()
		if err != nil {
			return 0, err
		}
		return existingLog.Id, nil
	}

	// 创建新的打印记录
	in.PrintCount = 1
	in.LastPrintedAt = gtime.Now()
	in.CreatedBy = int64(library.GetUserId(ctx))

	insertid, err = dao.TenantCtx(dao.FormPrintLogs, ctx).Data(in).InsertAndGetId()
	return
}

// CreateByIds 通过表单模板ID和数据ID创建打印记录
func (s *sFormPrintLogs) CreateByIds(ctx context.Context, formTemplateId, formDataId int64, formPrintTemplateId int64) (insertid int64, err error) {
	// 创建打印记录
	err = g.Try(ctx, func(ctx context.Context) {
		// 创建打印数据
		in := &do.FormPrintLogs{
			FormTemplateId:      formTemplateId,
			FormDataId:          formDataId,
			FormPrintTemplateId: formPrintTemplateId,
			CreatedBy:           int64(library.GetUserId(ctx)),
		}

		insertid, err = s.Create(ctx, in)
		library.ErrIsNil(ctx, err, "创建打印记录失败")
	})
	return
}

// GetById 根据ID获取打印记录
func (s *sFormPrintLogs) GetById(ctx context.Context, id int64) (out *entity.FormPrintLogs, err error) {
	err = dao.TenantCtx(dao.FormPrintLogs, ctx).Where(dao.FormPrintLogs.Columns().Id, id).Scan(&out)
	return
}

// GetByFormDataId 根据表单数据ID获取打印记录
func (s *sFormPrintLogs) GetByFormId(ctx context.Context, formTemplateId, formDataId int64) (out *entity.FormPrintLogs, err error) {
	err = dao.TenantCtx(dao.FormPrintLogs, ctx).Where(dao.FormPrintLogs.Columns().FormDataId, formDataId).Where(dao.FormPrintLogs.Columns().FormTemplateId, formTemplateId).Scan(&out)
	return
}

// GetByFormTemplateId 根据表单模板ID获取打印记录
func (s *sFormPrintLogs) GetByFormTemplateId(ctx context.Context, formTemplateId int64) (out []*entity.FormPrintLogs, err error) {
	err = dao.TenantCtx(dao.FormPrintLogs, ctx).Where(dao.FormPrintLogs.Columns().FormTemplateId, formTemplateId).Scan(&out)
	return
}

// Update 更新打印记录
func (s *sFormPrintLogs) Update(ctx context.Context, in *do.FormPrintLogs) (err error) {
	_, err = dao.TenantCtx(dao.FormPrintLogs, ctx).Data(in).Where(dao.FormPrintLogs.Columns().Id, in.Id).Update()
	return
}

// Delete 删除打印记录
func (s *sFormPrintLogs) Delete(ctx context.Context, id int64) (err error) {
	_, err = dao.TenantCtx(dao.FormPrintLogs, ctx).Where(dao.FormPrintLogs.Columns().Id, id).Delete()
	return
}
