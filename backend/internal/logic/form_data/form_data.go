package formtemplate

import (
	"backend/api/v1/form"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/dop251/goja"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormData(new(sFormData))
}

type sFormData struct{}

// 保存表单数据
func (s *sFormData) Save(ctx context.Context, req *form.FormDataSaveReq, vm *goja.Runtime, createUser ...int64) (res *form.FormDataSaveRes, err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, req.FormId)
		library.ErrIsNil(ctx, err)

		formData := make(map[string]interface{}, 0)

		// g.Log().Info(ctx, library.SDump("formColumns", formColumns))
		// 验证数据字段
		for _, columnInfo := range formColumns {
			var columnValue interface{}
			value, exists := req.Data[columnInfo.Id]

			if exists {
				columnValue = value
			} else if req.AllowUpdatePartly && req.Id > 0 {
				continue
			}
			processedValue, err := columnInfo.ValidateVal(ctx, columnValue)
			library.ErrIsNil(ctx, err)

			// 使用处理后的值
			if exists {
				formData[columnInfo.Id] = processedValue
			}

			if columnInfo.ColumnType == "json" && g.IsEmpty(processedValue) {
				formData[columnInfo.Id] = nil
			}
		}

		// for columnName, columnValue := range req.Data {
		// 	g.Log().Info(ctx, library.SDump("columnName", columnName))
		// 	g.Log().Info(ctx, library.SDump("columnValue", columnValue))
		// 	columnInfo := formColumns.FindOne([]library.FilterFunc[*dto.FormColumn]{
		// 		func(s *dto.FormColumn) bool { return s.Id == columnName },
		// 	})
		// 	g.Log().Info(ctx, library.SDump("columnInfo", columnInfo))
		// 	// 过滤掉不存在的字段
		// 	if columnInfo == nil {
		// 		continue
		// 	}
		// 	// 验证字段数据格式
		// 	err = columnInfo.ValidateVal(ctx, columnValue)
		// 	library.ErrIsNil(ctx, err)

		// 	formData[columnName] = columnValue
		// }

		// g.Log().Info(ctx, library.SDump("formData", formData))
		if req.Id <= 0 {
			if templateInfo.OpenFlow == 1 {
				err = service.FlowTemplate().ValidateExtraData(ctx, templateInfo.FlowTemplate.FlowSchema, req.ExtraData)
				library.ErrIsNil(ctx, err)
			}
			// 处理自动填充的字段
			for _, formColumnValue := range formColumns {
				g.Log().Info(ctx, fmt.Sprintf("formColumnName:%s,formColumnValue:%v", "", formColumnValue))
				if formColumnValue.ComponentName == "Field.CodeMachine" {
					rule_string := ""
					code_rule, exists := formColumnValue.Props["rule"]
					g.Log().Info(ctx, library.SDump(fmt.Sprintf("code_rule:%s,exists:%v", code_rule, exists)))
					if !exists || g.IsEmpty(code_rule) {
						code_rule = []string{"{20060102150406}", "{AUTO_INCREMENT({##template##},8)}"}
					}
					rule_slice, err := library.ToSliceString(code_rule)
					if err == nil {
						rule_string = strings.Join(rule_slice, "")
					} else {
						rule_string = gconv.String(code_rule)
					}
					// 替换code_rule中的##template##标记
					rule_string = strings.ReplaceAll(rule_string, "##template##", templateInfo.FormTableName+"-")
					formData[formColumnValue.Id] = service.Generate().GenerateID(ctx, rule_string)
				}
			}
			if len(createUser) > 0 {
				formData["created_by"] = createUser[0]
			} else {
				formData["created_by"] = library.GetUserId(ctx)
			}
			formData["template_id"] = req.FormId
			formData["flow_status"] = lo.Ternary(templateInfo.OpenFlow == 1, enum.FlowStatus_Pending, enum.FlowStatus_Finish)
			formData["extra_data"] = req.ExtraData
			formData["expand_data_id"] = req.ExpandDataId
		}

		if len(formData) <= 0 {
			err = fmt.Errorf("表单数据不能为空")
			library.ErrIsNil(ctx, err)
		}

		rules, err := service.FormCheckRule().GetEnabledRules(ctx, templateInfo.Id)
		library.ErrIsNil(ctx, err)
		if len(rules) > 0 {
			if vm == nil {
				vm, err = service.Javascript().GetVM(nil)
				library.ErrIsNil(ctx, err)
				vm.Set("__loginUserId", library.GetUserId(ctx))
			}
			// 验证规则（js代码）
			for _, rule := range rules {
				var ruleContent dto.ScriptCode
				err = json.Unmarshal([]byte(rule.CodeContent), &ruleContent)
				library.ErrIsNil(ctx, err)
				// 构建并注入ref对象
				temFormData := lo.Assign(formData)
				temFormData["id"] = req.Id
				ref, err := service.FormTemplate().BuildScriptRef(ctx, templateInfo.FormTableName, 0, formColumns, temFormData, ruleContent.ColumnMap)
				library.ErrIsNil(ctx, err)
				service.Javascript().SetProps(ctx, vm, map[string]interface{}{"__current": ref, "__allowUpdatePartly": req.Id > 0 && req.AllowUpdatePartly})
				var checkResult bool
				checkResult, err = service.Javascript().RunBool(ctx, templateInfo.FormTableName, 0, formColumns, ruleContent, 2000*time.Second, vm)
				if err != nil {
					err = fmt.Errorf("[%s]错误:%s", rule.Name, err.Error())
					library.ErrIsNil(ctx, err)
				}
				if !checkResult {
					err = fmt.Errorf("%s", rule.FailMsg)
					library.ErrIsNil(ctx, err)
				}
			}
		}
		m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)

		g.Log().Info(ctx, library.SDump("formData end", formData))
		var id int64 = req.Id
		if req.Id > 0 {
			_, err = m.WherePri(req.Id).Update(formData)

		} else {
			id, err = m.Data(formData).InsertAndGetId()
		}
		library.ErrIsNil(ctx, err, "保存数据失败")
		dbFormData, _, err := s.GetDetail(ctx, req.FormId, id)
		library.ErrIsNil(ctx, err, "查询数据异常")
		res = &form.FormDataSaveRes{
			Info:     dbFormData,
			Template: templateInfo,
		}
		// 通知消息队列
		if req.Id <= 0 {
			queue.QueueManager().Publish(consts.StreamFormDataCreated, &dto.FormDataShort{
				FormTemplateId: templateInfo.Id,
				FormId:         id,
				ExtraData:      req.ExtraData,
				CreatedBy:      int64(library.GetUserId(ctx)),
				TableName:      templateInfo.FormTableName,
			})
			if formData["flow_status"] == enum.FlowStatus_Finish {
				queue.QueueManager().Publish(consts.StreamFormFlowFinish, &dto.FormFlowFinish{
					TableName:  templateInfo.FormTableName,
					FormDataId: id,
				})
			}
		}
		queue.QueueManager().Publish(consts.StreamFormDataChanged, &dto.FormDataShort{
			FormTemplateId: templateInfo.Id,
			FormId:         id,
			ExtraData:      req.ExtraData,
			CreatedBy:      int64(library.GetUserId(ctx)),
			TableName:      templateInfo.FormTableName,
		})
	})
	return
}

// 同步流程的审核状态到表单
func (s *sFormData) SyncFlowStatus(ctx context.Context, table_name string, form_data_id int64, flow_status enum.FlowStatus, flow_status_version *gtime.Time, flow_instance_type enum.FlowInstanceType) (affected int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if flow_instance_type == enum.FlowInstanceType_Invalid {
			if flow_status == enum.FlowStatus_Finish {
				affected, err = s.UpdateFlowStatus(ctx, table_name, form_data_id, enum.FlowStatus_Invalid, flow_status_version, flow_instance_type)
				library.ErrIsNil(ctx, err)
			}
			if flow_status == enum.FlowStatus_Reject || flow_status == enum.FlowStatus_Cancel {
				affected, err = s.UpdateFlowStatus(ctx, table_name, form_data_id, enum.FlowStatus_Finish, flow_status_version, flow_instance_type)
				library.ErrIsNil(ctx, err)
			}
		} else {
			affected, err = s.UpdateFlowStatus(ctx, table_name, form_data_id, flow_status, flow_status_version, flow_instance_type)
			library.ErrIsNil(ctx, err)
		}
	})
	return

}

// 更新表单的流程状态
func (s *sFormData) UpdateFlowStatus(ctx context.Context, table_name string, form_data_id int64, flow_status enum.FlowStatus, flow_status_version *gtime.Time, flow_instance_type enum.FlowInstanceType) (affected int64, err error) {

	result, err := g.DB().Model(table_name).Safe().Ctx(ctx).
		WherePri(form_data_id).
		Where("(flow_status_version < ? OR flow_status_version IS NULL)", flow_status_version).
		WhereNot("flow_status", flow_status).
		Update(map[string]interface{}{"flow_status": flow_status, "flow_status_version": flow_status_version})
	if err != nil {
		return
	}
	affected, err = result.RowsAffected()
	if err != nil || affected <= 0 {
		return
	}

	// 通知表单状态为通过(不包括申请作废的审批流)
	if flow_instance_type == enum.FlowInstanceType_Create && flow_status == enum.FlowStatus_Finish {
		queue.QueueManager().Publish(consts.StreamFormFlowFinish, &dto.FormFlowFinish{
			TableName:  table_name,
			FormDataId: form_data_id,
		})
	}

	// 通知表单状态已经驳回或者撤销
	if flow_instance_type == enum.FlowInstanceType_Create && (flow_status == enum.FlowStatus_Reject || flow_status == enum.FlowStatus_Cancel) {
		queue.QueueManager().Publish(consts.StreamFormFlowRejectOrCancel, &dto.FormFlowFinish{
			TableName:  table_name,
			FormDataId: form_data_id,
		})
	}

	// 通知表单状态为作废
	if flow_instance_type == enum.FlowInstanceType_Invalid && flow_status == enum.FlowStatus_Invalid {
		queue.QueueManager().Publish(consts.StreamFormInvalid, &dto.FormStreamInfo{
			TableName:  table_name,
			FormDataId: form_data_id,
		})
	}

	return
}

// 更新表单数据
func (s *sFormData) UpdateFormData(ctx context.Context, table_name string, form_data_id int64, form_data map[string]interface{}) (err error) {
	_, err = g.DB().Model(table_name).Safe().Ctx(ctx).WherePri(form_data_id).Update(form_data)
	return
}

// 获取表单数据详情
func (s *sFormData) GetDetail(ctx context.Context, FormTemId int64, id int64) (formData map[string]interface{}, template *with.FormTemplate, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, _, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, FormTemId)
		library.ErrIsNil(ctx, err)
		formData, err = s.GetFormData(ctx, templateInfo.FormTableName, id)
		library.ErrIsNil(ctx, err)
		template = templateInfo
		// g.Log().Info(ctx, library.SDump("formData", formData))

	})
	return
}

func (s *sFormData) GetDetailByFilter(ctx context.Context, FormTemId int64, filter map[string]interface{}) (formData map[string]interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, FormTemId)
		library.ErrIsNil(ctx, err)
		formData, err = s.GetFormDataByFilter(ctx, templateInfo.FormTableName, filter, formColumns)
		library.ErrIsNil(ctx, err)
		// g.Log().Info(ctx, library.SDump("formData", formData))

	})
	return
}

func (s *sFormData) GetDetailByRuleFilter(ctx context.Context, FormTemId int64, ruleFilter map[string]interface{}) (formData map[string]interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, FormTemId)
		library.ErrIsNil(ctx, err)

		m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)

		m, err = constructRuleFilterConditions(ctx, m, ruleFilter, formColumns)
		library.ErrIsNil(ctx, err)
		record, err := m.One()
		library.ErrIsNil(ctx, err)
		if g.IsEmpty(record) {
			return
		}
		formData = record.Map()
		s.PopulateCreatedUser(ctx, &formData)
	})
	return
}

func (s *sFormData) GetFormDataByFilter(ctx context.Context, formTableName string, filter map[string]interface{}, formColumns library.MySlice[*dto.FormColumn]) (formData map[string]interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := g.DB().Model(formTableName).Safe().Ctx(ctx)
		formData = make(map[string]interface{})

		m, err = constructRuleFilterConditions(ctx, m, filter, formColumns)
		library.ErrIsNil(ctx, err)

		record, err := m.Order("id", "desc").One()
		library.ErrIsNil(ctx, err)
		for k, v := range record {
			formData[k] = v
		}
		if _, exists := formData["created_by"]; exists {
			if !g.IsEmpty(formData["created_by"]) {
				create_user, err := service.SysUser().GetInfoByID(ctx, gconv.Int64(formData["created_by"]))
				library.ErrIsNil(ctx, err)
				formData["created_user"] = create_user
			}
		}

	})
	return
}

func (s *sFormData) GetFormData(ctx context.Context, formTableName string, id int64) (formData map[string]interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := g.DB().Model(formTableName).Safe().Ctx(ctx)
		formData = make(map[string]interface{})
		record, err := m.WherePri(id).One()
		library.ErrIsNil(ctx, err)
		for k, v := range record {
			formData[k] = v
		}
		s.PopulateCreatedUser(ctx, &formData)
	})
	return
}

func (s *sFormData) PopulateCreatedUser(ctx context.Context, formData *map[string]interface{}) {
	if _, exists := (*formData)["created_by"]; exists {
		if !g.IsEmpty((*formData)["created_by"]) {
			create_user, err := service.SysUser().GetInfoByID(ctx, gconv.Int64((*formData)["created_by"]))
			library.ErrIsNil(ctx, err)
			(*formData)["created_user"] = create_user
		}
	}
}

// 删除
func (s *sFormData) Delete(ctx context.Context, FormTemId int64, id int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 验证是否存在关联数据

		// 删除表单数据
		templateInfo, err := service.FormTemplate().GetFormTemplateInfoCache(ctx, FormTemId)
		library.ErrIsNil(ctx, err, "获取表单模板信息失败")
		m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)

		_, err = m.WherePri(id).Delete()
		library.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// 申请作废表单数据
func (s *sFormData) Invalidate(ctx context.Context, extraData *dto.InstanceExtraData, FormTemId int64, id int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {

		templateInfo, _, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, FormTemId)
		library.ErrIsNil(ctx, err, "获取表单模板信息失败")

		// m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)
		oldInfo, err := s.GetFormData(ctx, templateInfo.FormTableName, id)
		library.ErrIsNil(ctx, err, "获取表单数据失败")
		flowStatus := enum.FlowStatus(gconv.Int(oldInfo["flow_status"]))
		if flowStatus == enum.FlowStatus_Pending || flowStatus == enum.FlowStatus_Running {
			library.Fail(ctx, "审核中的单据不能作废，您可以尝试撤销审核")
		}
		if flowStatus != enum.FlowStatus_Finish {
			library.Fail(ctx, "当前状态下单据不能作废")
		}

		relatedDatas, err := s.CheckRelatedData(ctx, templateInfo, oldInfo, false)
		library.ErrIsNil(ctx, err)
		if len(relatedDatas) > 0 {
			library.Fail(ctx, fmt.Sprintf("当前单据存在关联数据，不能作废，关联单据名:%v", relatedDatas[0].FormTitle))
		}
		flow_status_version := gtime.Now()
		if v, ok := oldInfo["flow_status_version"].(*gvar.Var); ok {
			flow_status_version = gtime.New(v.Val()).Add(time.Duration(1) * time.Second)
		}

		if templateInfo.OpenFlow == 1 {
			affected, err := s.UpdateFlowStatus(ctx, templateInfo.FormTableName, id, enum.FlowStatus_ApplyInvalid, flow_status_version, enum.FlowInstanceType_Create)
			library.ErrIsNil(ctx, err)
			// 创建申请作废审批流
			if affected > 0 {
				queue.QueueManager().Publish(consts.StreamFormDataApplyInvalid, &dto.FormDataShort{
					FormTemplateId: FormTemId,
					FormId:         id,
					ExtraData:      extraData,
					CreatedBy:      int64(library.GetUserId(ctx)),
				})
			}
		} else {
			affected, err := s.UpdateFlowStatus(ctx, templateInfo.FormTableName, id, enum.FlowStatus_Invalid, flow_status_version, enum.FlowInstanceType_Create)
			library.ErrIsNil(ctx, err)
			if affected > 0 {
				err = s.UpdateInvalidStatus(ctx, templateInfo.FormTableName, id, extraData.Attachment)
				library.ErrIsNil(ctx, err)

				// 通知表单状态为已作废
				queue.QueueManager().Publish(consts.StreamFormInvalid, &dto.FormStreamInfo{
					TableName:  templateInfo.FormTableName,
					FormDataId: id,
				})
			}
		}
	})
	return
}

// 更新作废状态和作废理由信息
func (s *sFormData) UpdateInvalidStatus(ctx context.Context, tableName string, id int64, extraData *dto.InstanceExtraDataAttachment) (err error) {
	updateData := map[string]interface{}{
		"invalid_at":     gtime.Now(),
		"invalid_reason": lo.Ternary(g.IsEmpty(extraData), nil, extraData),
	}
	_, err = g.DB().Model(tableName).Safe().Ctx(ctx).WherePri(id).Update(updateData)
	library.ErrIsNil(ctx, err)
	return
}

// 查询是否存在关联数据
func (s *sFormData) CheckRelatedData(ctx context.Context, templateInfo *with.FormTemplate, formData map[string]interface{}, includeMainTableIgnore bool) (relatedData []*dto.FormRelatedData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 验证是否存在关联数据
		relatedData = make([]*dto.FormRelatedData, 0)

		// 查找关联此表单的表单
		templates, err := service.FormTemplate().GetRelatedFormTemplateList(ctx, templateInfo.FormTableName)
		library.ErrIsNil(ctx, err)

		for _, template := range templates {
			var templateWithTables []*dto.FormWithInfo
			err = json.Unmarshal([]byte(lo.Ternary(template.WithTables == "", "[]", template.WithTables)), &templateWithTables)
			library.ErrIsNil(ctx, err)

			formWithInfos := lo.Filter(templateWithTables, func(s *dto.FormWithInfo, _ int) bool {
				return s.TableName == templateInfo.FormTableName
			})
			if len(formWithInfos) <= 0 {
				continue
			}
			for _, formWithInfo := range formWithInfos {
				var formColumns []*dto.FormColumn
				err = json.Unmarshal([]byte(lo.Ternary(template.FormColumns == "", "[]", template.FormColumns)), &formColumns)
				library.ErrIsNil(ctx, err)

				ruleFilter := make(map[string]interface{})
				if g.IsEmpty(formWithInfo.CurrentParentColumnName) {
					ruleFilter[formWithInfo.CurrentColumnName] = formData[formWithInfo.RelatedColumnName]
				} else {
					ruleFilter[fmt.Sprintf("%s.%s", formWithInfo.CurrentParentColumnName, formWithInfo.CurrentColumnName)] = formData[formWithInfo.RelatedColumnName]
				}
				formDatas, err := s.GetListByRules(ctx, 0, template.FormTableName, template.Id, formColumns, ruleFilter)
				library.ErrIsNil(ctx, err)
				formDatas = lo.Filter(formDatas, func(s map[string]interface{}, _ int) bool {
					flowStatus := enum.FlowStatus(gconv.Int(s["flow_status"]))
					return flowStatus != enum.FlowStatus_Cancel && flowStatus != enum.FlowStatus_Invalid
				})
				if len(formDatas) > 0 {
					relatedData = append(relatedData, &dto.FormRelatedData{
						TableName:            formWithInfo.TableName,
						FormTitle:            template.FormTitle,
						FormData:             formDatas[0],
						AllowMainTableIgnore: formWithInfo.AllowMainTableIgnore,
					})
				}
			}
		}

		// // 反序列化templateInfo.WithTables
		// var formWithInfos []*dto.FormWithInfo
		// err = json.Unmarshal([]byte(lo.Ternary(templateInfo.WithTables == "", "[]", templateInfo.WithTables)), &formWithInfos)
		// library.ErrIsNil(ctx, err)

		// for _, formWithInfo := range formWithInfos {
		// 	ruleFilter := make(map[string]interface{})
		// 	ruleFilter[formWithInfo.RelatedColumnName] = formData[formWithInfo.CurrentColumnName]
		// 	formDatas, err := s.GetListByRules(ctx, formWithInfo.Type, formWithInfo.TableName, formWithInfo.FormTemplateID, formColumns, ruleFilter)
		// 	library.ErrIsNil(ctx, err)
		// 	if len(formDatas) > 0 {
		// 		supportTables, err := service.FormTemplate().GetSupportTablesCache(ctx)
		// 		library.ErrIsNil(ctx, err)
		// 		supportTable, hasTable := lo.Find(supportTables, func(s *dto.SupportTable) bool {
		// 			return s.TableName == formWithInfo.TableName
		// 		})
		// 		if !hasTable {
		// 			err = fmt.Errorf("表[%s]不存在", formWithInfo.TableName)
		// 			library.ErrIsNil(ctx, err)
		// 		}
		// 		relatedData = append(relatedData, &dto.FormRelatedData{
		// 			TableName: formWithInfo.TableName,
		// 			FormTitle: supportTable.Label,
		// 			FormData:  formDatas[0],
		// 		})
		// 		break
		// 	}
		// }

	})
	if len(relatedData) > 0 && !includeMainTableIgnore {
		relatedData = lo.Filter(relatedData, func(s *dto.FormRelatedData, _ int) bool {
			return !s.AllowMainTableIgnore
		})

		g.Log().Info(ctx, library.SDump("验证有关联数据", relatedData))
	}
	return
}

func (s *sFormData) GetListByRules(ctx context.Context, formType int, formTableName string, FormTemplateId int64, formColumns []*dto.FormColumn, ruleFilter map[string]interface{}) (formDatas []map[string]interface{}, err error) {
	m := g.DB().Model(formTableName).Safe().Ctx(ctx)

	if formType == 0 && FormTemplateId > 0 {
		m, err = constructRuleFilterConditions(ctx, m, ruleFilter, formColumns)
		library.ErrIsNil(ctx, err)
	} else {
		for filterKey, filterValue := range ruleFilter {
			m = m.Where(filterKey, filterValue)
		}
	}
	records, err := m.All()
	library.ErrIsNil(ctx, err)
	formDatas = make([]map[string]interface{}, 0)
	for _, record := range records {
		item := make(map[string]interface{})
		for k, v := range record {
			item[k] = v
		}
		formDatas = append(formDatas, item)
	}
	return
}

// 获得某个表单的某个字段关联数据列表
func (s *sFormData) GetColumnWithList(ctx context.Context, req *form.ColumnWithListReq) (res *form.ColumnWithListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

		templateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, int64(req.FormId))
		library.ErrIsNil(ctx, err)

		var allColumns []*dto.FormColumn
		allColumns = append(allColumns, formColumns...)
		allColumns = append(allColumns, dto.FixedFields...)

		var columnInfo *dto.FormColumn
		var parentColumnInfo *dto.FormColumn
		// filterKey 带 . 的是子表
		filterKeyArr := strings.Split(req.ColumnName, ".")
		if len(filterKeyArr) > 1 {
			parentColumnInfo, _ = lo.Find(allColumns, func(s *dto.FormColumn) bool {
				return s.Id == filterKeyArr[0]
			})
			if parentColumnInfo != nil {
				columnInfo, _ = lo.Find(parentColumnInfo.Children, func(s *dto.FormColumn) bool {
					return s.Id == filterKeyArr[1]
				})
			}
		} else {
			columnInfo, _ = lo.Find(allColumns, func(s *dto.FormColumn) bool { return s.Id == req.ColumnName })
			if g.IsNil(columnInfo) {
				for _, formColumn := range allColumns {
					columnInfo = formColumn.Children.FindOne([]library.FilterFunc[*dto.FormColumn]{
						func(s *dto.FormColumn) bool { return s.Id == req.ColumnName },
					})
					if !g.IsNil(columnInfo) {
						parentColumnInfo = formColumn
						break
					}

				}
			}
		}

		library.ValueIsNil(columnInfo, fmt.Sprintf("字段%s不存在", req.ColumnName))

		if columnInfo.ComponentName == "Field.WithSingleSelect" || columnInfo.ComponentName == "Field.WithMultipleSelect" {

			option_label, exists := columnInfo.Props["option_label"]
			if !exists || g.IsEmpty(option_label) {
				err = fmt.Errorf("字段%s的未设置关联表单显示字段", columnInfo.Label)
				library.ErrIsNil(ctx, err)
			}

			option_value, exists := columnInfo.Props["option_value"]
			if !exists || g.IsEmpty(option_value) {
				err = fmt.Errorf("字段[%s]未设置关联表单值字段", columnInfo.Label)
				library.ErrIsNil(ctx, err)
			}
			table_name, exists := columnInfo.Props["table_name"]

			if !exists || g.IsEmpty(table_name) {
				err = fmt.Errorf("字段[%s]未设置关联表单名称", columnInfo.Label)
				library.ErrIsNil(ctx, err)
			}
			// 验证表是否受支持

			supportTables, err := service.FormTemplate().GetSupportTablesCache(ctx)
			library.ErrIsNil(ctx, err)
			supportTable, hasTable := lo.Find(supportTables, func(s *dto.SupportTable) bool {
				return s.TableName == gconv.String(table_name)
			})
			if !hasTable {
				err = fmt.Errorf("字段[%s]关联表[%s]不受支持", columnInfo.Label, gconv.String(table_name))
				library.ErrIsNil(ctx, err)
			}

			// // 验证表是否存在
			// table_exists, err := service.FormTemplate().TableNameExists(ctx, gconv.String(table_name))
			// library.ErrIsNil(ctx, err)
			// if !table_exists {
			// 	err = fmt.Errorf("字段[%s]关联表[%s]不存在", columnInfo.Label, gconv.String(table_name))
			// 	library.ErrIsNil(ctx, err)
			// }
			m := g.DB().Model(gconv.String(supportTable.TableName)).Safe().Ctx(ctx)
			// 数据表是每个租户独立的，所以这里需要使用租户上下文,但非form_data表是公用的,因此saas化还需要测试
			// m := dao.TenantTableCtx(gconv.String(supportTable.TableName), ctx)
			var supportFormColumns library.MySlice[*dto.FormColumn]

			// 是否禁用已经被关联的数据
			if req.EnabledLinkedData {
				m, err = constructLinkedDataFilterConditions(ctx, m, supportTable.TableName, templateInfo.FormTableName, columnInfo)
				library.ErrIsNil(ctx, err)
			}

			if supportTable.Type == 0 && supportTable.FormTemplateId > 0 {
				library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, supportTable.FormTemplateId, enum.SysPermission_View), enum.SysPermission_View.Error(supportTable.Label))
				unlimitedSearch := service.SysUser().CheckFormPermission(ctx, supportTable.FormTemplateId, enum.SysPermission_UnlimitedSearch)
				var permissions *dto.FormPermissions
				var tableDesign *dto.FormTableDesign
				_, supportFormColumns, tableDesign, permissions, err = service.FormTemplate().GetDatabaseDesign(ctx, supportTable.FormTemplateId)
				library.ErrIsNil(ctx, err)

				// 构造数据权限过滤条件
				m, err = constructPermissionFilterConditions(ctx, m, permissions, supportFormColumns, nil)
				library.ErrIsNil(ctx, err)
				ruleFilerCount := 0
				for _, item := range tableDesign.Filter {
					if item.Switch && len(req.RuleFilter) > 0 && req.RuleFilter[item.Id] != nil {
						ruleFilerCount++
					}
				}
				m, err = constructFilterConditions(ctx, m, req.Filter, supportFormColumns, tableDesign, unlimitedSearch, true, ruleFilerCount > 0)
				library.ErrIsNil(ctx, err)
			} else {
				for filterKey, filterValue := range req.Filter {
					if g.IsEmpty(filterValue) {
						continue
					}
					m = m.Where(filterKey+" like ?", "%"+gconv.String(filterValue)+"%")
				}
			}

			// 构造规则过滤条件
			if len(req.RuleFilter) > 0 {
				m, err = constructRuleFilterConditions(ctx, m, req.RuleFilter, supportFormColumns)
				library.ErrIsNil(ctx, err)
			}

			if supportTable.Type == 0 {
				m = m.Where("flow_status", enum.FlowStatus_Finish)
			}
			res = new(form.ColumnWithListRes)
			res.Total, err = m.Count()
			library.ErrIsNil(ctx, err, "获取总数失败")
			var fields []string = []string{gconv.String(option_label), gconv.String(option_value)}
			if gconv.String(option_value) != "id" {
				fields = append(fields, "id")
			}
			fillRulesObj, fillRulesExists := columnInfo.Props["fillRules"]
			if fillRulesExists {
				fillRulesStr := gconv.String(fillRulesObj)
				var fileRules []*dto.FillRule
				if err = json.Unmarshal([]byte(fillRulesStr), &fileRules); err == nil {
					fields = append(fields, lo.Map(fileRules, func(rule *dto.FillRule, _ int) string { return rule.Source })...)
				}
			}
			fields = lo.Uniq(fields)
			m = m.Fields(fields)
			if req.SortByUsedCount {
				m, err = sortByUsedCount(ctx, m, supportTable.TableName, templateInfo.FormTableName, columnInfo, parentColumnInfo)
				library.ErrIsNil(ctx, err)
			} else {
				m = m.OrderDesc("id")
			}
			list := make([]map[string]interface{}, 0)
			result, err := m.Page(req.PageNum, req.PageSize).All()
			library.ErrIsNil(ctx, err, "获取数据失败")
			for _, record := range result {
				item := make(map[string]interface{})
				for k, v := range record {
					item[k] = v
				}
				list = append(list, item)
			}

			res.List = list
		}

	})
	return
}

func (s *sFormData) GetCustomTableDatas(ctx context.Context, tableName string, filter map[string]interface{}, ruleFilter map[string]interface{}, pageNum int, pageSize int) (result []map[string]interface{}, total int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := g.DB().Model(tableName).Safe().Ctx(ctx)
		for filterKey, filterValue := range filter {
			if g.IsEmpty(filterValue) {
				continue
			}
			m = m.Where(filterKey+" like ?", "%"+gconv.String(filterValue)+"%")
		}
		// 构造规则过滤条件
		if len(ruleFilter) > 0 {
			m, err = constructRuleFilterConditions(ctx, m, ruleFilter, []*dto.FormColumn{})
			library.ErrIsNil(ctx, err)
		}
		total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取总数失败")
		list := make([]map[string]interface{}, 0)
		res, err := m.Page(pageNum, pageSize).All()
		library.ErrIsNil(ctx, err, "获取数据失败")
		for _, record := range res {
			item := make(map[string]interface{})
			for k, v := range record {
				item[k] = v
			}
			list = append(list, item)
		}
		result = list
	})
	return
}

// 获得某个表单的某个字段关联的表单信息
func (s *sFormData) GetColumnWithInfo(ctx context.Context, req *form.ColumnWithInfoReq) (res *form.ColumnWithInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var table_name string
		column_name := req.ColumnName
		if g.IsEmpty(req.TableName) {
			_, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, int64(req.FormId))
			library.ErrIsNil(ctx, err)

			columnInfo := formColumns.FindOne([]library.FilterFunc[*dto.FormColumn]{
				func(s *dto.FormColumn) bool { return s.Id == req.ColumnName },
			})

			if g.IsNil(columnInfo) {
				for _, formColumn := range formColumns {
					columnInfo = formColumn.Children.FindOne([]library.FilterFunc[*dto.FormColumn]{
						func(s *dto.FormColumn) bool { return s.Id == req.ColumnName },
					})
					if !g.IsNil(columnInfo) {
						break
					}

				}
			}

			library.ValueIsNil(columnInfo, fmt.Sprintf("字段%s不存在", req.ColumnName))

			column_name = columnInfo.Label
			if columnInfo.ComponentName != "Field.WithSingleSelect" && columnInfo.ComponentName != "Field.WithMultipleSelect" {
				library.Fail(ctx, fmt.Sprintf("字段%s不是关联表单字段", columnInfo.Label))
			}
			var exists bool
			table_name, exists = columnInfo.Props["table_name"].(string)

			if !exists || g.IsEmpty(table_name) {
				err = fmt.Errorf("字段[%s]未设置关联表单名称", columnInfo.Label)
				library.ErrIsNil(ctx, err)
			}
		} else {
			table_name = req.TableName
		}
		// 验证表是否受支持
		supportTables, err := service.FormTemplate().GetSupportTablesCache(ctx)
		library.ErrIsNil(ctx, err)
		supportTable, hasTable := lo.Find(supportTables, func(s *dto.SupportTable) bool {
			return s.TableName == gconv.String(table_name)
		})
		if !hasTable {
			err = fmt.Errorf("字段[%s]关联表[%s]不受支持", column_name, gconv.String(table_name))
			library.ErrIsNil(ctx, err)
		}
		res = &form.ColumnWithInfoRes{
			SupportTable: supportTable,
		}
	})
	return
}

// 验证tableDesign.Filter或tableDesign.Columns中是否存在signback_type
func (s *sFormData) checkFieldExistsInTableDesign(tableDesign *dto.FormTableDesign, id string) (res bool) {
	hasSignbackType := lo.SomeBy(tableDesign.Filter, func(item *dto.FormTableDesignItem) bool {
		return item.Id == id
	})

	if !hasSignbackType {
		hasSignbackType = lo.SomeBy(tableDesign.Columns, func(item *dto.FormTableDesignItem) bool {
			return item.Id == id
		})
	}
	return hasSignbackType
}

// 构建查询条件
func (s *sFormData) buildQueryConditions(ctx context.Context, req *form.FormDataListReq, unlimitedSearch bool, loginUser *dto.UserClaims, isTable bool, noCheckPermission ...bool) (m *gdb.Model, templateInfo *with.FormTemplate, linkedColumnInfo *dto.FormColumn, linkedTableName string, parentColumnInfo *dto.FormColumn, tableDesign *dto.FormTableDesign, err error) {
	templateInfo, formColumns, tableDesign, permissions, err := service.FormTemplate().GetDatabaseDesign(ctx, int64(req.FormId))
	if err != nil {
		return
	}
	m = g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)
	if s.checkFieldExistsInTableDesign(tableDesign, "signback_type") && isTable {
		m = m.LeftJoin(dao.FormCountersignLogs.Table(), "fc", fmt.Sprintf("%s.id = fc.form_data_id and fc.form_template_id = %d and fc.deleted_at is null", templateInfo.FormTableName, templateInfo.Id))
	}
	if s.checkFieldExistsInTableDesign(tableDesign, "print_status") && isTable {
		m = m.LeftJoin(dao.FormPrintLogs.Table(), "fp", fmt.Sprintf("%s.id = fp.form_data_id and fp.form_template_id = %d and fp.deleted_at is null", templateInfo.FormTableName, templateInfo.Id))
	}

	if len(noCheckPermission) <= 0 || !noCheckPermission[0] {
		m, err = constructPermissionFilterConditions(ctx, m, permissions, formColumns, loginUser)
		if err != nil {
			return
		}
	}

	if req.ShowInvalid > 0 {
		invaildBuild := m.Builder()
		if req.ShowInvalid == 1 {
			invaildBuild = invaildBuild.WhereNot("flow_status", enum.FlowStatus_Invalid)
			invaildBuild = invaildBuild.WhereNot("flow_status", enum.FlowStatus_Cancel)
			m = m.Where(invaildBuild)
		} else if req.ShowInvalid == 2 {
			invaildBuild = invaildBuild.Where("flow_status", enum.FlowStatus_Invalid)
			invaildBuild = invaildBuild.WhereOr("flow_status", enum.FlowStatus_Cancel)
			m = m.Where(invaildBuild)
		}
	}

	ruleFilerCount := 0
	for _, item := range tableDesign.Filter {
		// if item.Switch && item.SearchSettle != nil && req.RuleFilter != nil && req.RuleFilter[item.Id] != nil {
		// 	ruleFilerCount++
		// }
		if item.Switch && len(req.RuleFilter) > 0 && req.RuleFilter[item.Id] != nil {
			ruleFilerCount++
		}
	}

	m, err = constructFilterConditions(ctx, m, req.Filter, formColumns, tableDesign, unlimitedSearch, false, ruleFilerCount > 0)
	if err != nil {
		return
	}

	if len(req.RuleFilter) > 0 {
		m, err = constructRuleFilterConditions(ctx, m, req.RuleFilter, formColumns)
		if err != nil {
			return
		}
	}

	if (req.EnabledLinkedData || req.SortByUsedCount) && !g.IsNil(req.LinkedData) && req.LinkedData.TemplateId > 0 && req.LinkedData.FieldName != "" {
		linkedTemplateInfo, linkedFormColumns, _, _, errLocal := service.FormTemplate().GetDatabaseDesign(ctx, req.LinkedData.TemplateId)
		if errLocal != nil {
			err = errLocal
			return
		}
		filterKeyArr := strings.Split(req.LinkedData.FieldName, ".")
		if len(filterKeyArr) > 1 {
			parentColumnInfo, _ = lo.Find(linkedFormColumns, func(s *dto.FormColumn) bool {
				return s.Id == filterKeyArr[0]
			})
			if parentColumnInfo != nil {
				linkedColumnInfo, _ = lo.Find(parentColumnInfo.Children, func(s *dto.FormColumn) bool {
					return s.Id == filterKeyArr[1]
				})
			}
		} else {
			linkedColumnInfo = linkedFormColumns.FindOne([]library.FilterFunc[*dto.FormColumn]{
				func(s *dto.FormColumn) bool { return s.Id == req.LinkedData.FieldName },
			})
		}
		if linkedColumnInfo == nil {
			err = fmt.Errorf("字段[%s]不存在", req.LinkedData.FieldName)
			return
		}
		linkedTableName = linkedTemplateInfo.FormTableName
	}

	if req.EnabledLinkedData {
		m, err = constructLinkedDataFilterConditions(ctx, m, templateInfo.FormTableName, linkedTableName, linkedColumnInfo)
		if err != nil {
			return
		}
	}

	return
}

// 新增方法：获取某个字段的总和
func (s *sFormData) GetFieldSum(ctx context.Context, req *form.FormDataListSumReq, unlimitedSearch bool, loginUser *dto.UserClaims, noCheckPermission ...bool) (res *form.FormDataListSumRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(form.FormDataListSumRes)
		m, _, _, _, _, _, err := s.buildQueryConditions(ctx, &form.FormDataListReq{
			CommonFormDataReq: form.CommonFormDataReq{
				FormId:            req.FormId,
				Filter:            req.Filter,
				RuleFilter:        req.RuleFilter,
				ShowInvalid:       req.ShowInvalid,
				EnabledLinkedData: req.EnabledLinkedData,
				SortByUsedCount:   req.SortByUsedCount,
				LinkedData:        req.LinkedData,
			},
		}, unlimitedSearch, loginUser, false, noCheckPermission...)
		if err != nil {
			return
		}

		// req.FieldName 带 . 的是子表
		filterKeyArr := strings.Split(req.FieldName, ".")
		if len(filterKeyArr) > 1 {
			// 构建 JSON_TABLE 查询
			jsonTableQuery := fmt.Sprintf(
				"JSON_TABLE(%s, '$[*]' COLUMNS (amount DECIMAL(20,6) PATH '$.%s')) AS j",
				filterKeyArr[0],
				filterKeyArr[1],
			)
			m = m.InnerJoin(jsonTableQuery)
			m = m.Fields("SUM(CAST(j.amount as DECIMAL(20,6))) as total")
		} else {
			m = m.Fields(fmt.Sprintf("SUM(CAST(%s as DECIMAL(20,6))) as total", req.FieldName))
		}
		record, err := m.One()
		library.ErrIsNil(ctx, err, "获取总数失败")
		res.Total = gconv.Float64(record["total"])

	})
	return
}

// 获取表单数据列表
func (s *sFormData) GetList(ctx context.Context, req *form.FormDataListReq, unlimitedSearch bool, loginUser *dto.UserClaims, noCheckPermission ...bool) (res *form.FormDataListRes, err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		// templateInfo, formColumns, tableDesign, permissions, err := service.FormTemplate().GetDatabaseDesign(ctx, int64(req.FormId))
		// library.ErrIsNil(ctx, err)

		// m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)

		// if len(noCheckPermission) <= 0 || !noCheckPermission[0] {
		// 	// 构造数据权限过滤条件
		// 	m, err = constructPermissionFilterConditions(ctx, m, permissions, formColumns, loginUser)
		// 	library.ErrIsNil(ctx, err)
		// }

		// if req.ShowInvalid > 0 {

		// 	invaildBuild := m.Builder()
		// 	if req.ShowInvalid == 1 {
		// 		invaildBuild = invaildBuild.WhereNot("flow_status", enum.FlowStatus_Invalid)
		// 		invaildBuild = invaildBuild.WhereNot("flow_status", enum.FlowStatus_Cancel)
		// 		m = m.Where(invaildBuild)
		// 	} else if req.ShowInvalid == 2 {
		// 		invaildBuild = invaildBuild.Where("flow_status", enum.FlowStatus_Invalid)
		// 		invaildBuild = invaildBuild.WhereOr("flow_status", enum.FlowStatus_Cancel)
		// 		m = m.Where(invaildBuild)
		// 	}
		// }
		// m, err = constructFilterConditions(ctx, m, req.Filter, formColumns, tableDesign, unlimitedSearch, false)
		// library.ErrIsNil(ctx, err)

		// // 构造规则过滤条件
		// if len(req.RuleFilter) > 0 {
		// 	m, err = constructRuleFilterConditions(ctx, m, req.RuleFilter, formColumns)
		// 	library.ErrIsNil(ctx, err)
		// }

		// var linkedColumnInfo *dto.FormColumn
		// var linkedTableName string
		// if (req.EnabledLinkedData || req.SortByUsedCount) && !g.IsNil(req.LinkedData) && req.LinkedData.TemplateId > 0 && req.LinkedData.FieldName != "" {
		// 	linkedTemplateInfo, linkedFormColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, req.LinkedData.TemplateId)
		// 	library.ErrIsNil(ctx, err)
		// 	// filterKey 带 . 的是子表
		// 	filterKeyArr := strings.Split(req.LinkedData.FieldName, ".")
		// 	if len(filterKeyArr) > 1 {
		// 		parentColumnInfo, _ := lo.Find(linkedFormColumns, func(s *dto.FormColumn) bool {
		// 			return s.Id == filterKeyArr[0]
		// 		})
		// 		if parentColumnInfo != nil {
		// 			linkedColumnInfo, _ = lo.Find(parentColumnInfo.Children, func(s *dto.FormColumn) bool {
		// 				return s.Id == filterKeyArr[1]
		// 			})
		// 		}

		// 	} else {
		// 		linkedColumnInfo = linkedFormColumns.FindOne([]library.FilterFunc[*dto.FormColumn]{
		// 			func(s *dto.FormColumn) bool { return s.Id == req.LinkedData.FieldName },
		// 		})
		// 	}
		// 	library.ValueIsNil(linkedColumnInfo, fmt.Sprintf("字段[%s]不存在", req.LinkedData.FieldName))
		// 	linkedTableName = linkedTemplateInfo.FormTableName
		// }

		// // 启用关联数据过滤（不显示已经被关联的数据）
		// if req.EnabledLinkedData {
		// 	m, err = constructLinkedDataFilterConditions(ctx, m, templateInfo.FormTableName, linkedTableName, linkedColumnInfo)
		// 	library.ErrIsNil(ctx, err)
		// }

		m, templateInfo, linkedColumnInfo, linkedTableName, parentColumnInfo, tableDesign, err := s.buildQueryConditions(ctx, req, unlimitedSearch, loginUser, true, noCheckPermission...)
		library.ErrIsNil(ctx, err)

		res = new(form.FormDataListRes)
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err)
		// 弹窗选择列表项时，会字段不全导致问题，先不按需取字段了
		// m = m.Fields(strings.Join(append(tableDesign.Columns, "id"), ","))
		list := make([]map[string]interface{}, 0)
		if req.SortByUsedCount {
			m, err = sortByUsedCount(ctx, m, templateInfo.FormTableName, linkedTableName, linkedColumnInfo, parentColumnInfo)
			library.ErrIsNil(ctx, err)
		} else {
			m = m.OrderDesc(fmt.Sprintf("%s.id", templateInfo.FormTableName))
		}

		if req.PageSize != -1 {
			m = m.Page(req.PageNum, req.PageSize)
		}
		showFields := fmt.Sprintf("%s.*", templateInfo.FormTableName)
		if lo.SomeBy(tableDesign.Columns, func(item *dto.FormTableDesignItem) bool {
			return item.Id == "print_status"
		}) {
			showFields = fmt.Sprintf("%s,(case when fp.id is null then 1 else 2 end) print_status", showFields)
		}
		if lo.SomeBy(tableDesign.Columns, func(item *dto.FormTableDesignItem) bool {
			return item.Id == "signback_type"
		}) {
			showFields = fmt.Sprintf("%s,(case when fc.id is null then 1 else 2 end) signback_type", showFields)
		}
		result, err := m.Fields(showFields).All()
		library.ErrIsNil(ctx, err, "获取数据失败")
		for _, record := range result {
			item := make(map[string]interface{})
			for k, v := range record {
				item[k] = v
			}
			list = append(list, item)
		}
		if len(list) > 0 {
			userids := lo.Map(list, func(v map[string]interface{}, _ int) int64 {
				return gconv.Int64(v["created_by"])
			})

			users, err := service.SysUser().GetListByUserIds(ctx, lo.Uniq(userids))
			library.ErrIsNil(ctx, err, "获取创建人信息失败")

			list = lo.Map(list, func(v map[string]interface{}, _ int) map[string]interface{} {
				user, hasUser := lo.Find(users, func(user *with.SysUser) bool {
					return user.Id == gconv.Int64(v["created_by"])
				})
				if hasUser {
					v["created_user"] = user
				}
				return v
			})
		}

		res.List = list

	})
	return
}

// 获得自定义表单数据列表
func (s *sFormData) GetListCustom(ctx context.Context, req *form.FormDataListCustomReq) (res *form.FormDataListCustomRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		list, err := s.GetListCustomEx(ctx, req.Filter, req.TableName)
		library.ErrIsNil(ctx, err)
		res = new(form.FormDataListCustomRes)
		res.List = list
	})

	return
}

// 获得自定义表单数据列表
func (s *sFormData) GetListCustomEx(ctx context.Context, filter map[string]interface{}, tableName string) (res []map[string]interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 验证是否表是否在支持列表
		supportTable, err := service.FormTemplate().GetSupportTablesCache(ctx)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(supportTable, "暂不支持该表单类型")
		supportIndex := library.FindIndex(supportTable, []library.FilterFunc[*dto.SupportTable]{
			func(s *dto.SupportTable) bool {
				return s.TableName == tableName
			},
		})
		if supportIndex < 0 {
			library.ErrIsNil(ctx, fmt.Errorf("暂不支持该表单类型"))
		}

		m := g.DB().Model(tableName).Safe().Ctx(ctx)
		// 构造查询条件
		if supportTable[supportIndex].FormTemplateId > 0 {
			_, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, supportTable[supportIndex].FormTemplateId)
			library.ErrIsNil(ctx, err)
			m, err = constructRuleFilterConditions(ctx, m, filter, formColumns)
			library.ErrIsNil(ctx, err)
		} else {
			if len(filter) > 0 {
				for filterKey, filterValue := range filter {
					m = m.Where(filterKey, filterValue)
				}
			}
		}

		result, err := m.All()
		library.ErrIsNil(ctx, err, "获取数据失败")
		res = gconv.Maps(result)
	})

	return
}

// 获得拓展key对应的模板id和数据id等信息
func (s *sFormData) GetExpandInfo(ctx context.Context, expand_key string, expand_data_id int64) (expandInfo *dto.ExpandInfo, err error) {
	template_id, err := service.FormTemplate().GetTemplateIdByExpandKey(ctx, expand_key)
	if err != nil {
		return
	}
	if template_id <= 0 {
		return
	}
	templateInfo, _, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, template_id)
	if err != nil {
		return
	}
	expandInfo = &dto.ExpandInfo{
		FormTemplateId: template_id,
	}
	if expand_data_id > 0 {
		m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)
		var record gdb.Record
		record, err = m.Fields("id").Where("expand_data_id", expand_data_id).One()
		if err != nil || record == nil {
			return
		}

		formdata_id, has := record["id"]
		if has {
			expandInfo.FormDataId = gconv.Int64(formdata_id)
		}
	}
	return
}

func (s *sFormData) TableDataList(ctx context.Context, req *form.TableDataListReq, noCheckPermission ...bool) (res *form.TableDataListRes, err error) {
	supportTables, err := service.FormTemplate().GetSupportTablesCache(ctx)
	if err != nil {
		return
	}
	supportTable, hasTable := lo.Find(supportTables, func(s *dto.SupportTable) bool {
		return s.TableName == gconv.String(req.TableName) && s.Type == req.TableType
	})
	if !hasTable {
		err = fmt.Errorf("表[%s]查询不受支持", req.TableName)
		return
	}
	noCheckPer := len(noCheckPermission) > 0 && noCheckPermission[0]
	if supportTable.Type == 0 {
		// 验证权限
		if !noCheckPer {
			library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, supportTable.FormTemplateId, enum.SysPermission_View), enum.SysPermission_View.Error())
		}
		var formDataListRes *form.FormDataListRes
		formDataListRes, err = s.GetList(ctx, &form.FormDataListReq{
			CommonFormDataReq: form.CommonFormDataReq{
				FormId:     supportTable.FormTemplateId,
				Filter:     req.Filter,
				RuleFilter: req.RuleFilter,
			},
			PageReq: req.PageReq,
		}, true, nil, noCheckPer)
		if err != nil {
			return
		}
		res = &form.TableDataListRes{
			List:    formDataListRes.List,
			ListRes: formDataListRes.ListRes,
		}
		return
	} else if supportTable.PermissionMark != "" {
		if !noCheckPer {
			library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, supportTable.PermissionMark, enum.SysPermission_View), enum.SysPermission_View.Error())
		}
		var list []map[string]interface{}
		var total int
		list, total, err = service.FormData().GetCustomTableDatas(ctx, supportTable.TableName, req.Filter, req.RuleFilter, req.PageNum, req.PageSize)
		if err != nil {
			return
		}
		res = &form.TableDataListRes{}
		res.List = list
		res.Total = total
		return
	}
	return
}

// 生成一个全局唯一的id
func (s *sFormData) GenerateGlobalId(ctx context.Context, formType int32, formTemId int64, formDataId int64) (id string, err error) {
	return fmt.Sprintf("%s-%010d", library.CodePrefixEncode(gconv.Int32(formTemId), formType), formDataId), nil
}

// 根据全局唯一Id拆解出模板id和数据id
func (s *sFormData) ParseGlobalId(ctx context.Context, globalId string) (formType int32, formTemId int32, formDataId int64, err error) {
	arr := strings.Split(globalId, "-")
	if len(arr) != 2 {
		err = fmt.Errorf("全局唯一Id格式错误")
		return
	}
	// 验证 arr[1] 是否是数字
	if !gstr.IsNumeric(arr[1]) {
		err = fmt.Errorf("全局唯一Id格式错误")
		return
	}
	formTemId, formType, err = library.CodePrefixDecode(arr[0])
	if err != nil {
		return
	}
	formDataId = gconv.Int64(arr[1])
	return
}

// 格式化列的label
func (s *sFormData) FormatColumnLabel(ctx context.Context, columnInfo *dto.FormColumn, value interface{}) (result interface{}, err error) {
	// 岗位和部门字段需查询数据库获得label，其他字段可由columnInfo.FormatLabel 自行格式化
	var ids []int64
	if columnInfo.ComponentName == "Field.DeptSelect" || columnInfo.ComponentName == "Field.PostSelect" {
		valStr, ok := value.(*gvar.Var)
		if ok {
			json.Unmarshal([]byte(valStr.String()), &ids)
		}
	}
	switch columnInfo.ComponentName {
	case "Field.DeptSelect":
		var list []*with.SysDept
		list, err = service.SysDept().GetDeptListByIDs(ctx, ids)
		if err != nil {
			return
		}
		deptNames := lo.Map(list, func(item *with.SysDept, _ int) string {
			return item.DeptName
		})
		result = strings.Join(deptNames, ",")
		return
	case "Field.PostSelect":
		var list []*with.SysPost
		list, err = service.SysPost().GetPostListByIDs(ctx, ids)
		if err != nil {
			return
		}
		postNames := lo.Map(list, func(item *with.SysPost, _ int) string {
			return item.PostName
		})
		result = strings.Join(postNames, ",")
		return
	default:
		return columnInfo.FormatLabel(ctx, value)
	}
}

func constructLinkedDataFilterConditions(ctx context.Context, model *gdb.Model, tableName string, linkedTableName string, columnInfo *dto.FormColumn) (m *gdb.Model, err error) {
	m = model
	columnKey := columnInfo.Id
	columnValueKey, isExists := columnInfo.Props["option_value"]
	if !isExists {
		return nil, fmt.Errorf("关联数据过滤条件中未找到option_value")
	}
	if columnInfo.ComponentName == "Field.WithSingleSelect" {
		m = m.Where("NOT EXISTS(?)", g.DB().Model(linkedTableName).
			Fields(1).
			Where(fmt.Sprintf("json_unquote(json_extract(%s, '$.value')) = %s.%s", columnKey, tableName, columnValueKey)).
			Where("flow_status != ?", enum.FlowStatus_Cancel).
			Where("flow_status != ?", enum.FlowStatus_Invalid).
			Where("flow_status != ?", enum.FlowStatus_Reject))

	} else if columnInfo.ComponentName == "Field.WithMultipleSelect" {
		return nil, fmt.Errorf("不支持多选组件关联数据过滤")
	}
	return
}

func sortByUsedCount(ctx context.Context, model *gdb.Model, tableName string, linkedTableName string, columnInfo *dto.FormColumn, parentColumnInfo *dto.FormColumn) (m *gdb.Model, err error) {
	m = model

	columnValueKey, isExists := columnInfo.Props["option_value"]
	if !isExists {
		return nil, fmt.Errorf("关联数据过滤条件中未找到option_value")
	}

	var subquery string
	if parentColumnInfo != nil {
		// 子表场景：使用JSON_TABLE处理数组
		subquery = fmt.Sprintf(`(
			SELECT 
				jt.value,
				COUNT(*) as ref_count
			FROM %s,
			JSON_TABLE(
				%s,
				'$[*]' COLUMNS (
					value VARCHAR(255) PATH '$.%s.value'
				)
			) AS jt
			WHERE jt.value IS NOT NULL and created_by = %d
			GROUP BY jt.value
		)`, linkedTableName, parentColumnInfo.Id, columnInfo.Id, library.GetUserId(ctx))
	} else {
		// 普通字段场景
		subquery = fmt.Sprintf(`(
		SELECT 
			JSON_UNQUOTE(JSON_EXTRACT(%s, '$.value')) as value,
			COUNT(*) as ref_count
		FROM %s
		WHERE created_by = %d
		GROUP BY JSON_UNQUOTE(JSON_EXTRACT(%s, '$.value'))
		)`, columnInfo.Id, linkedTableName, library.GetUserId(ctx), columnInfo.Id)
	}

	m = m.LeftJoin(subquery, "link", fmt.Sprintf("link.value = %s.%s", tableName, columnValueKey)).
		OrderDesc("link.ref_count").
		OrderDesc("id")
	return
}

// 根据传入的filter构造所有查询条件
func constructRuleFilterConditions(ctx context.Context, model *gdb.Model, filter map[string]interface{}, formColumns library.MySlice[*dto.FormColumn]) (m *gdb.Model, err error) {
	m = model

	var allColumns []*dto.FormColumn
	allColumns = append(allColumns, formColumns...)
	allColumns = append(allColumns, dto.FixedFields...)
	for filterKey, filterValue := range filter {

		var filterColumnInfo *dto.FormColumn
		var parentColumnInfo *dto.FormColumn
		// filterKey 带 . 的是子表
		filterKeyArr := strings.Split(filterKey, ".")
		if len(filterKeyArr) > 1 {
			parentColumnInfo, _ = lo.Find(allColumns, func(s *dto.FormColumn) bool {
				return s.Id == filterKeyArr[0]
			})
			if parentColumnInfo != nil {
				filterColumnInfo, _ = lo.Find(parentColumnInfo.Children, func(s *dto.FormColumn) bool {
					return s.Id == filterKeyArr[1]
				})
			}

		} else {
			filterColumnInfo, _ = lo.Find(allColumns, func(s *dto.FormColumn) bool {
				return s.Id == filterKey
			})
		}

		if filterColumnInfo == nil {
			m = m.Where(filterKey, filterValue)
		} else {
			m, err = buildRuleWhereClause(ctx, m, filterValue, filterColumnInfo, parentColumnInfo)
			library.ErrIsNil(ctx, err)
		}

	}
	return
}

// 构造数据权限过滤条件
func constructPermissionFilterConditions(ctx context.Context, model *gdb.Model, permissions *dto.FormPermissions, formColumns []*dto.FormColumn, loginUser *dto.UserClaims) (m *gdb.Model, err error) {
	permissionCount := 0
	build := model.Builder()
	build = build.Where("1!=1")
	if permissions != nil {
		if loginUser == nil {
			loginUser = service.SysUser().GetLoginUser(ctx)
		}
		if loginUser == nil {
			err = fmt.Errorf("未登录")
			return
		}
		// 本人
		if permissions.Self.Enabled {
			permissionCount++
			build = build.WhereOr("created_by", loginUser.Id)
		}
		// 本部门
		if permissions.Dept.Enabled {
			permissionCount++
			if loginUser != nil {
				var deptUserIds []int64
				deptUserIds, err = service.SysUser().GetDeptUserIds(ctx, loginUser.WithDepts)
				if err != nil {
					return
				}
				build = build.WhereOrIn("created_by", deptUserIds)
			}
		}
		// 用户字段关联的用户
		if permissions.FieldUsers.Enabled {
			permissionCount++
			for _, column := range permissions.FieldUsers.Data {
				build = build.WhereOr(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"id": ', ?, '}'), '$')`, column.Value), loginUser.Id)
			}
		}
		// 本部门负责人
		if permissions.DeptLeader.Enabled {
			permissionCount++
			var leaderDeptIds []int64
			leaderDeptIds, err = service.SysUser().GetLeaderDeptIds(ctx, loginUser.Id)
			if err != nil {
				return
			}
			var deptUserIds []int64
			deptUserIds, err = service.SysUser().GetDeptUserIds(ctx, leaderDeptIds)
			if err != nil {
				return
			}
			build = build.WhereOrIn("created_by", deptUserIds)
		}
		// 指定岗位
		if permissions.SpecifiedPosition.Enabled && len(permissions.SpecifiedPosition.Data) > 0 {
			permissionCount++
			_, isExists := lo.Find(permissions.SpecifiedPosition.Data, func(item dto.FormPositionData) bool {
				if loginUser != nil && len(loginUser.WithPosts) > 0 {
					_, isExistsPost := lo.Find(loginUser.WithPosts, func(postid int64) bool {
						return item.ID == postid
					})
					return isExistsPost
				}
				return false
			})
			if isExists {
				m = model
				return
			}
		}
		// 指定人员
		if permissions.SpecifiedUsers.Enabled && len(permissions.SpecifiedUsers.Data) > 0 {
			permissionCount++
			_, isExists := lo.Find(permissions.SpecifiedUsers.Data, func(item dto.FormUserDetail) bool {
				return loginUser.Id == item.ID
			})
			if isExists {
				m = model
				return
			}
		}
		// 项目负责人
		if permissions.ProjectLeader.Enabled {
			permissionCount++
			columnInfo, isExists := lo.Find(formColumns, func(column *dto.FormColumn) bool {
				return column.Id == permissions.ProjectLeader.Data.Value
			})
			if isExists {
				var projectIds []int64
				projectIds, err = service.SysUser().GetLeaderProjectIds(ctx, loginUser.Id)
				if err != nil {
					return
				}
				if columnInfo.ComponentName == "Field.WithSingleSelect" {
					build = build.WhereOrIn(fmt.Sprintf("JSON_UNQUOTE(JSON_EXTRACT(%s, '$.value'))", permissions.ProjectLeader.Data.Value), projectIds)
				} else if columnInfo.ComponentName == "Field.WithMultipleSelect" {
					for projectId := range projectIds {
						build = build.WhereOr(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"value": ', ?, '}'), '$')`, permissions.ProjectLeader.Data.Value), projectId)
					}
				}

			}
		}
		// 项目角色
		if permissions.ProjectRole.Enabled {
			permissionCount++
			columnInfo, isExists := lo.Find(formColumns, func(column *dto.FormColumn) bool {
				return column.Id == permissions.ProjectRole.Data.ColumnName.Value
			})
			if isExists {
				// 获得用户作为xx角色存在的项目id列表
				projectRoleIds := lo.Map(permissions.ProjectRole.Data.List, func(item dto.FormRoleDataItem, _ int) int64 {
					return item.Id
				})
				projectRoleIds = lo.Uniq(projectRoleIds)
				var projectIds []int64
				projectIds, err = service.SysUser().GetRoleProjectIds(ctx, loginUser.Id, projectRoleIds)
				if err != nil {
					return
				}
				if columnInfo.ComponentName == "Field.WithSingleSelect" {
					build = build.WhereOrIn(fmt.Sprintf("JSON_UNQUOTE(JSON_EXTRACT(%s, '$.value'))", permissions.ProjectRole.Data.ColumnName.Value), projectIds)
				} else if columnInfo.ComponentName == "Field.WithMultipleSelect" {
					for projectId := range projectIds {
						build = build.WhereOr(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"value": ', ?, '}'), '$')`, permissions.ProjectRole.Data.ColumnName.Value), projectId)
					}
				}
			}
		}
		// 自定义条件
		if permissions.CustomCondition.Enabled {
			permissionCount++
			customBuilder, err := buildCustomCondition(ctx, model, &permissions.CustomCondition.Data, formColumns, loginUser)
			if err != nil {
				return nil, err
			}
			build = build.WhereOr(customBuilder)
		}
	}
	if permissionCount > 0 {
		m = model.Where(build)
	} else {
		m = model
	}
	return
}

// 根据传入的filter构造所有查询条件
func constructFilterConditions(ctx context.Context, model *gdb.Model, filter map[string]interface{}, formColumns library.MySlice[*dto.FormColumn], tableDesign *dto.FormTableDesign, unlimitedSearch bool, noFilterLimit bool, hasRuleFilter bool) (m *gdb.Model, err error) {
	m = model

	var allColumns []*dto.FormColumn
	allColumns = append(allColumns, formColumns...)
	allColumns = append(allColumns, dto.FixedFields...)
	hasFilter := false
	for filterKey, filterValue := range filter {
		// 过滤器设置中是否存在该字段
		filterItem, filterExist := lo.Find(tableDesign.Filter, func(s *dto.FormTableDesignItem) bool {
			return s.Id == filterKey
		})

		if !noFilterLimit && !filterExist {
			continue
		}
		if filterItem != nil {
			filterValue = filterItem.HandleSearchWords(filterValue)
		}
		var filterColumnInfo *dto.FormColumn
		var parentColumnInfo *dto.FormColumn
		// filterKey 带 . 的是子表
		filterKeyArr := strings.Split(filterKey, ".")
		if len(filterKeyArr) > 1 {
			parentColumnInfo, _ = lo.Find(allColumns, func(s *dto.FormColumn) bool {
				return s.Id == filterKeyArr[0]
			})
			if parentColumnInfo != nil {
				filterColumnInfo, _ = lo.Find(parentColumnInfo.Children, func(s *dto.FormColumn) bool {
					return s.Id == filterKeyArr[1]
				})
			}

		} else {
			filterColumnInfo, _ = lo.Find(allColumns, func(s *dto.FormColumn) bool {
				return s.Id == filterKey
			})
		}
		if g.IsEmpty(filterValue) && library.IsTrulyEmptyValue(filterValue) {
			continue
		}
		if filterColumnInfo == nil {
			if filterKey == "print_status" {
				m = m.Where(lo.Ternary(gconv.Int(filterValue) == int(enum.PrintType_Printed), "fp.id is not null", "fp.id is null"))
				hasFilter = true
			} else if filterKey == "signback_type" {
				m = m.Where(lo.Ternary(gconv.Int(filterValue) == int(enum.SignBackType_Signed), "fc.id is not null", "fc.id is null"))
				hasFilter = true
			}
			continue
		}

		// 根据字段类型(filterColumnInfo.ColumnType)   、字段名(filterColumnInfo.Id)、字段值 (filterValue) 构造查询条件
		m, err = buildWhereClause(ctx, m, filterValue, filterColumnInfo, unlimitedSearch, parentColumnInfo)
		library.ErrIsNil(ctx, err)
		hasFilter = true
	}
	if !hasFilter && !unlimitedSearch && !hasRuleFilter {
		m = m.Where("1!=1")
	}
	return
}

// 根据字段类型、字段名、字段值构造单个过滤规则条件
func buildRuleWhereClause(ctx context.Context, model *gdb.Model, filterValue interface{}, columnInfo *dto.FormColumn, parentColumnInfo *dto.FormColumn) (m *gdb.Model, err error) {
	m = model
	columnName := columnInfo.Id
	// 是否能被转换成 dto.RuleFilterValue 结构
	operator := enum.Operator_Equal
	ruleFilterValue := dto.RuleFilterValue{}
	convertErr := library.ToStruct(filterValue, &ruleFilterValue)
	if convertErr == nil {
		operator = ruleFilterValue.Operator
		filterValue = ruleFilterValue.Value
	}
	if g.IsEmpty(operator) {
		operator = enum.Operator_Equal
	}
	if columnInfo.ColumnType == "json" {
		switch value := filterValue.(type) {
		case string:
			if columnInfo.ComponentName == "Field.UserSelect" {
				var arrValue []map[string]interface{}
				unmarshalErr := json.Unmarshal([]byte(value), &arrValue)
				if unmarshalErr == nil {
					arrItem := arrValue[0]
					userid, ok := arrItem["id"]
					if ok {
						filterValue = userid
					}
				}

			} else {
				var jsonValue map[string]interface{}
				unmarshalErr := json.Unmarshal([]byte(value), &jsonValue)
				if unmarshalErr == nil {
					filterValue = jsonValue["value"]
				}
			}
		case map[string]interface{}:
			filterValue = value["value"]
		}
	}
	if parentColumnInfo != nil {
		switch columnInfo.ComponentName {
		case "Field.WithSingleSelect":
			m = m.Where(fmt.Sprintf("JSON_CONTAINS(JSON_EXTRACT(%s, '$[*].%s'), JSON_OBJECT('value', ?))", parentColumnInfo.Id, columnName), gconv.Int64(filterValue))
		case "Field.WithMultipleSelect":
			m = m.Where(fmt.Sprintf("JSON_CONTAINS(JSON_EXTRACT(%s, '$[*].%s'),JSON_OBJECT('value', ?))", parentColumnInfo.Id, columnName), gconv.Int64(filterValue))
		case "Field.UserSelect", "Field.DeptSelect", "Field.PostSelect":
			m = m.Where(fmt.Sprintf("JSON_CONTAINS(JSON_EXTRACT(%s, '$[*].%s'),JSON_OBJECT('id', ?))", parentColumnInfo.Id, columnName), gconv.Int64(filterValue))
		// 子表关于比较符的筛选暂未实现，有需要再说
		default:
			m = m.Where(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"%s": "', ?, '"}'), '$')`, parentColumnInfo.Id, columnName), filterValue)
		}
	} else {
		switch columnInfo.ComponentName {
		case "Field.WithSingleSelect":
			m = m.Where(fmt.Sprintf("JSON_EXTRACT(%s, '$.value') = ?", columnName), gconv.Int64(filterValue))
		case "Field.WithMultipleSelect":
			m = m.Where(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"value": ', ?, '}'), '$')`, columnName), gconv.Int64(filterValue))
		case "Field.UserSelect", "Field.DeptSelect", "Field.PostSelect":
			if columnName == "created_by" {
				m = m.Where(columnName, gconv.Int64(filterValue))
			} else {
				m = m.Where(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"id": ', ?, '}'), '$')`, columnName), gconv.Int64(filterValue))
			}
		default:
			switch operator {
			case enum.Operator_Equal:
				m = m.Where(columnName, filterValue)
			default:
				m = m.Where(columnName+" "+string(operator)+" ?", filterValue)
			}
		}
	}
	return
}

// 根据字段类型、字段名、字段值构造单个查询条件
func buildWhereClause(ctx context.Context, model *gdb.Model, filterValue interface{}, columnInfo *dto.FormColumn, unlimitedSearch bool, parentColumnInfo *dto.FormColumn) (m *gdb.Model, err error) {
	m = model
	columnType := strings.ToLower(columnInfo.ColumnType)
	columnName := columnInfo.Id

	if parentColumnInfo != nil {
		switch columnInfo.ComponentName {
		case "Field.Number":
			// 数字类型子表字段的筛选
			numRange, ok := filterValue.([]interface{})
			if !ok || len(numRange) != 2 {
				return nil, fmt.Errorf("请选择正确的数字范围")
			}
			startStr, endStr := gconv.String(numRange[0]), gconv.String(numRange[1])
			if !g.IsEmpty(startStr) {
				m = m.Where(fmt.Sprintf("(SELECT count(1) FROM JSON_TABLE(%s, '$[*]' COLUMNS (value DOUBLE PATH '$.%s')) AS jt WHERE jt.value is not null and jt.value >= ?) >0", parentColumnInfo.Id, columnName), gconv.Float64(startStr))
			}
			if !g.IsEmpty(endStr) {
				m = m.Where(fmt.Sprintf("(SELECT count(1) FROM JSON_TABLE(%s, '$[*]' COLUMNS (value DOUBLE PATH '$.%s')) AS jt WHERE jt.value is not null and jt.value <= ?) >0", parentColumnInfo.Id, columnName), gconv.Float64(endStr))
			}
		case "Field.WithSingleSelect":
			m = m.Where(fmt.Sprintf("JSON_CONTAINS(JSON_EXTRACT(%s, '$[*].%s'), JSON_OBJECT('value', ?))", parentColumnInfo.Id, columnName), gconv.Int64(filterValue))
		case "Field.WithMultipleSelect":
			m = m.Where(fmt.Sprintf(`JSON_CONTAINS(JSON_EXTRACT(%s, '$[*].%s'), JSON_OBJECT('value', ?))`, parentColumnInfo.Id, columnName), gconv.Int64(filterValue))
		case "Field.UserSelect", "Field.DeptSelect", "Field.PostSelect":
			// 用户、部门、岗位关联类型子表字段的筛选
			if !columnInfo.IsFixed {
				m = m.Where(fmt.Sprintf(`JSON_CONTAINS(JSON_EXTRACT(%s, '$[*].%s'), JSON_OBJECT('id', ?))`, parentColumnInfo.Id, columnName), gconv.Int64(filterValue))
			} else {
				m = m.Where(fmt.Sprintf("JSON_UNQUOTE(JSON_EXTRACT(%s, '$[*].%s')) = ?", parentColumnInfo.Id, columnName), filterValue)
			}
		case "Field.DatePicker":
			dateRange, ok := filterValue.([]interface{})
			if !ok || len(dateRange) != 2 {
				return nil, fmt.Errorf("请选择正确的日期范围")
			}
			var startTime, endTime *gtime.Time
			startTime, endTime, err = handleDatetimeType(filterValue, columnInfo, columnName)
			if err != nil {
				return nil, err
			}
			// m = m.Where(fmt.Sprintf("JSON_UNQUOTE(JSON_EXTRACT(%s, '$[*].%s')) BETWEEN ? AND ?", parentColumnInfo.Id, columnName), startTime, endTime)
			if !g.IsEmpty(startTime) && !g.IsEmpty(endTime) {
				m = m.Where(fmt.Sprintf("(SELECT count(1) FROM JSON_TABLE(%s, '$[*]' COLUMNS (value DATETIME PATH '$.%s')) AS jt WHERE jt.value IS NOT NULL AND jt.value between ? and ? ) > 0", parentColumnInfo.Id, columnName), startTime, endTime)
			}
		default:
			// 其他类型子表字段的筛选，假设为字符串类型
			m = m.Where(fmt.Sprintf("JSON_UNQUOTE(JSON_EXTRACT(%s, '$[*].%s')) LIKE ?", parentColumnInfo.Id, columnName), "%"+gconv.String(filterValue)+"%")
		}

	} else {
		// if !unlimitedSearch {
		// 	m = m.Where(columnName, filterValue)
		// 	return
		// }
		switch columnInfo.ComponentName {
		case "Field.Number":
			m, err = handleNumberType(m, filterValue, columnName)
			if err != nil {
				return nil, err
			}
		case "Field.WithSingleSelect":
			m = m.Where(fmt.Sprintf("JSON_EXTRACT(%s, '$.value') = ?", columnName), gconv.Int64(filterValue))
		case "Field.WithMultipleSelect":
			m = m.Where(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"value": ', ?, '}'), '$')`, columnName), gconv.Int64(filterValue))
		case "Field.UserSelect", "Field.DeptSelect", "Field.PostSelect":
			if !columnInfo.IsFixed {
				m = m.Where(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"id": ', ?, '}'), '$')`, columnName), gconv.Int64(filterValue))
			} else {
				m = m.Where(columnName, filterValue)
			}
		case "Field.RadioGroup":
			m = m.Where(columnName, filterValue)
		case "Field.DatePicker":
			dateRange, ok := filterValue.([]interface{})
			if !ok || len(dateRange) != 2 {
				return nil, fmt.Errorf("请选择正确的日期范围")
			}
			var startTime, endTime *gtime.Time
			startTime, endTime, err = handleDatetimeType(filterValue, columnInfo, columnName)
			if err != nil {
				return nil, err
			}
			m = m.Where(columnName+" between ? and ?", startTime, endTime)

		default:
			switch {
			case strings.HasPrefix(columnType, "varchar") || columnType == "json":
				m = m.Where(columnName+" like ?", "%"+gconv.String(filterValue)+"%")
			case columnType == "tinyint" || columnType == "int":
				m = m.Where(columnName, filterValue)
			default:
				g.Log().Info(ctx, library.SDump("columnType", columnType))
				err = fmt.Errorf("暂不支持该字段类型的检索")
				return
			}
		}
	}

	return
}
func handleNumberType(model *gdb.Model, filterValue interface{}, columnName string) (*gdb.Model, error) {
	numRange, ok := filterValue.([]interface{})
	if !ok || len(numRange) != 2 {
		return nil, fmt.Errorf("请选择正确数字范围")
	}
	startStr, endStr := gconv.String(numRange[0]), gconv.String(numRange[1])
	if !g.IsEmpty(startStr) {
		model = model.Where(columnName+" >= ", gconv.Float64(startStr))
	}
	if !g.IsEmpty(endStr) {
		model = model.Where(columnName+" <= ", gconv.Float64(endStr))
	}
	return model, nil
}
func handleDatetimeType(filterValue interface{}, columnInfo *dto.FormColumn, columnName string) (startTime *gtime.Time, endTime *gtime.Time, err error) {
	dateType, exists := columnInfo.Props["datetype"]
	if !exists || g.IsEmpty(dateType) {
		dateType = "date"
	}

	dateRange, ok := filterValue.([]interface{})
	if !ok || len(dateRange) != 2 {
		return nil, nil, fmt.Errorf("请选择正确的日期范围")
	}

	startTimeStr, endTimeStr := gconv.String(dateRange[0]), gconv.String(dateRange[1])

	switch dateType {
	case "date":
		startTimeStr += " 00:00:00"
		endTimeStr += " 23:59:59"

	case "month":
		startTimeStr += "-01 00:00:00"
		endTimeStr, err = handleMonthEnd(endTimeStr)
		if err != nil {
			return nil, nil, err
		}

	case "year":
		startTimeStr += "-01-01 00:00:00"
		endTimeStr, err = handleYearEnd(endTimeStr)
		if err != nil {
			return nil, nil, err
		}
	}

	startTime, err = gtime.StrToTime(startTimeStr)
	if err != nil {
		return nil, nil, err
	}

	endTime, err = gtime.StrToTime(endTimeStr)
	if err != nil {
		return nil, nil, err
	}
	return
}

func handleMonthEnd(endTimeStr string) (string, error) {
	t, err := time.Parse("2006-01", endTimeStr)
	if err != nil {
		return "", err
	}
	end := time.Date(t.Year(), t.Month()+1, 0, 23, 59, 59, 0, t.Location())
	return end.Format("2006-01-02 15:04:05"), nil
}

func handleYearEnd(endTimeStr string) (string, error) {
	t, err := time.Parse("2006", endTimeStr)
	if err != nil {
		return "", err
	}
	end := time.Date(t.Year()+1, 1, 0, 23, 59, 59, 0, t.Location())
	return end.Format("2006-01-02 15:04:05"), nil
}

// UpdateSingleField 更新表单单个字段
func (s *sFormData) UpdateSingleField(ctx context.Context, req *form.FormDataUpdateFieldReq) (err error) {
	return g.Try(ctx, func(ctx context.Context) {
		// 获取模板信息和字段定义
		templateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, req.FormId)
		library.ErrIsNil(ctx, err)

		// 查找要更新的字段信息
		var targetColumn *dto.FormColumn
		for _, column := range formColumns {
			if column.Id == req.FieldName {
				targetColumn = column
				break
			}
		}

		if targetColumn == nil {
			library.ErrIsNil(ctx, fmt.Errorf("字段 %s 不存在", req.FieldName))
			return
		}

		// 必须开启了 Props.allowListEdit 才能进行列表编辑
		if allowListEdit, exists := targetColumn.Props["allowListEdit"]; exists && allowListEdit != true {
			library.ErrIsNil(ctx, fmt.Errorf("字段 %s 不支持列表编辑", req.FieldName))
			return
		}

		// 验证字段是否支持列表编辑（仅支持选择类型）
		allowedComponents := []string{"Field.RadioGroup", "Field.Select"}
		isAllowed := false
		for _, component := range allowedComponents {

			if targetColumn.ComponentName == component {
				// 对于 Field.Select，还需要检查是否为单选模式
				if component == "Field.Select" {
					if mode, exists := targetColumn.Props["mode"]; exists && mode != "single" {
						continue
					}
				}
				isAllowed = true
				break
			}
		}

		if !isAllowed {
			library.ErrIsNil(ctx, fmt.Errorf("字段 %s 不支持列表编辑", req.FieldName))
			return
		}

		// 验证字段值
		processedValue, err := targetColumn.ValidateVal(ctx, req.FieldValue)
		library.ErrIsNil(ctx, err)

		// 构建更新数据
		updateData := make(map[string]interface{})
		updateData[req.FieldName] = processedValue

		// 执行更新
		m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)
		_, err = m.WherePri(req.Id).Update(updateData)
		library.ErrIsNil(ctx, err, "更新字段失败")

		// 发布数据变更事件
		queue.QueueManager().Publish(consts.StreamFormDataChanged, &dto.FormDataShort{
			FormTemplateId: templateInfo.Id,
			FormId:         req.Id,
			CreatedBy:      int64(library.GetUserId(ctx)),
			TableName:      templateInfo.FormTableName,
		})
	})
}

// buildCustomCondition 构建自定义权限条件
func buildCustomCondition(ctx context.Context, model *gdb.Model, customData *dto.CustomConditionData, formColumns []*dto.FormColumn, loginUser *dto.UserClaims) (*gdb.WhereBuilder, error) {

	// 构建引用数据缓存
	refDataCache, err := buildRefDataCache(ctx, customData.RefDatas, loginUser)
	if err != nil {
		return nil, err
	}

	// 构建条件组
	return buildConditionGroups(ctx, model, &customData.ConditionGroups, refDataCache, formColumns, loginUser)
}

// buildRefDataCache 构建引用数据缓存
func buildRefDataCache(ctx context.Context, refDatas []dto.RefDataConfig, loginUser *dto.UserClaims) (map[string][]map[string]interface{}, error) {
	cache := make(map[string][]map[string]interface{})

	for _, refData := range refDatas {
		if refData.TableName == "" {
			continue
		}

		dataList, err := queryRefData(ctx, &refData, loginUser)
		if err != nil {
			g.Log().Warning(ctx, "构建引用数据查询失败:", err)
			continue
		}

		cache[refData.Id] = dataList
	}

	return cache, nil
}

// buildConditionGroups 构建条件组
func buildConditionGroups(ctx context.Context, model *gdb.Model, mainGroups *dto.MainConditionGroups, refDataCache map[string][]map[string]interface{}, formColumns []*dto.FormColumn, loginUser *dto.UserClaims) (*gdb.WhereBuilder, error) {
	builder := model.Builder()
	for i, group := range mainGroups.Conditions {
		if len(group.Rules) == 0 {
			continue
		}

		subBuilder := model.Builder()
		if err := buildConditionGroup(ctx, &subBuilder, &group, refDataCache, formColumns, loginUser); err != nil {
			return nil, err
		}

		if i == 0 || mainGroups.Logic != "OR" {
			builder = builder.Where(subBuilder)
		} else {
			builder = builder.WhereOr(subBuilder)
		}
	}

	return builder, nil
}

// queryRefData 查询引用数据（重命名自buildRefDataQuery）
func queryRefData(ctx context.Context, refData *dto.RefDataConfig, loginUser *dto.UserClaims) ([]map[string]interface{}, error) {
	// 验证表是否受支持
	supportTables, err := service.FormTemplate().GetSupportTablesCache(ctx)
	if err != nil {
		return nil, err
	}

	supportTable, hasTable := lo.Find(supportTables, func(s *dto.SupportTable) bool {
		return s.TableName == refData.TableName
	})
	if !hasTable {
		return nil, fmt.Errorf("表[%s]不受支持", refData.TableName)
	}

	m := g.DB().Model(refData.TableName).Safe().Ctx(ctx)

	// 应用过滤条件
	if len(refData.FilterCondition.Conditions) > 0 {
		var refFormColumns []*dto.FormColumn
		if supportTable.Type == 0 && supportTable.FormTemplateId > 0 {
			_, refFormColumns, _, _, err = service.FormTemplate().GetDatabaseDesign(ctx, supportTable.FormTemplateId)
			if err != nil {
				return nil, err
			}
		}

		m, err = buildMainConditionGroupsToQueryBuilder(ctx, m, &refData.FilterCondition, refFormColumns, loginUser)
		if err != nil {
			return nil, err
		}
	}

	// 查询并转换数据
	result, err := m.All()
	if err != nil {
		return nil, err
	}

	return convertToMapArray(result), nil
}

// convertToMapArray 转换查询结果为map数组
func convertToMapArray(result gdb.Result) []map[string]interface{} {
	dataList := make([]map[string]interface{}, 0, len(result))
	for _, record := range result {
		item := make(map[string]interface{})
		for k, v := range record {
			item[k] = v
		}
		dataList = append(dataList, item)
	}
	return dataList
}

// buildConditionGroup 构建单个条件组
func buildConditionGroup(ctx context.Context, builder **gdb.WhereBuilder, group *dto.ConditionGroup, refDataCache map[string][]map[string]interface{}, formColumns []*dto.FormColumn, loginUser *dto.UserClaims) error {
	if builder == nil || *builder == nil || group == nil || len(group.Rules) == 0 {
		return nil
	}

	for i, rule := range group.Rules {
		logic := ""
		if i > 0 {
			logic = group.Logic
		}

		if err := addConditionRule(ctx, builder, &rule, refDataCache, formColumns, loginUser, logic); err != nil {
			return err
		}
	}

	return nil
}

// addConditionRule 添加条件规则
func addConditionRule(ctx context.Context, builder **gdb.WhereBuilder, rule *dto.ConditionRule, refDataCache map[string][]map[string]interface{}, formColumns []*dto.FormColumn, loginUser *dto.UserClaims, logic string) error {
	if builder == nil || *builder == nil || rule == nil || rule.Field == "" {
		return nil
	}

	fieldName := getFormattedFieldName(formColumns, rule.Field)
	conditionValue, err := getConditionValue(ctx, rule, refDataCache, loginUser)
	if err != nil || conditionValue == nil {
		return err
	}

	newBuilder, err := buildConditionByOperator(*builder, fieldName, rule.Operator, conditionValue, logic)
	if err != nil {
		return err
	}
	*builder = newBuilder
	return nil
}

// getConditionValue 获取条件值
func getConditionValue(ctx context.Context, rule *dto.ConditionRule, refDataCache map[string][]map[string]interface{}, loginUser *dto.UserClaims) (interface{}, error) {
	switch rule.ValueType {
	case "fixed":
		return rule.Value, nil
	case "refData":
		if rule.RefDataSourceId == "currentUser" {
			return service.SysUser().GetUserProperty(ctx, loginUser, rule.RefDataSourceFieldId), nil
		}
		return getRefDataValues(refDataCache, rule)
	default:
		return nil, fmt.Errorf("未知的值类型: %s", rule.ValueType)
	}
}

// getRefDataValues 从引用数据缓存中获取值
func getRefDataValues(refDataCache map[string][]map[string]interface{}, rule *dto.ConditionRule) (interface{}, error) {
	refDataList, exists := refDataCache[rule.RefDataSourceId]
	if !exists || len(refDataList) == 0 {
		return nil, nil // 跳过此条件
	}

	var values []interface{}
	for _, data := range refDataList {
		if value, ok := data[rule.RefDataSourceFieldId]; ok {
			values = append(values, value)
		}
	}

	if len(values) == 0 {
		return nil, nil // 跳过此条件
	}

	// 根据操作符决定使用单个值还是数组
	if rule.Operator == "in" || rule.Operator == "not in" {
		return values, nil
	}
	return values[0], nil // 对于其他操作符，使用第一个值
}

// getFormattedFieldName 获取格式化的字段名
func getFormattedFieldName(formColumns []*dto.FormColumn, fieldName string) string {
	for _, col := range formColumns {
		if col.Id == fieldName {
			switch col.ComponentName {
			case "Field.UserSelect":
				return fmt.Sprintf("%s->>'$[0].id'", fieldName)
			case "Field.WithSingleSelect":
				return fmt.Sprintf("%s->>'$.value'", fieldName)
			default:
				if col.ColumnType == "json" {
					return fmt.Sprintf("%s->>'$.value'", fieldName)
				}
			}
			break
		}
	}
	return fieldName
}

// buildConditionByOperator 根据操作符构建条件
func buildConditionByOperator(builder *gdb.WhereBuilder, fieldName string, operator string, conditionValue interface{}, logic string) (*gdb.WhereBuilder, error) {
	if builder == nil || fieldName == "" || operator == "" {
		return builder, fmt.Errorf("参数不能为空")
	}

	switch operator {
	case "=", "!=", ">", "<", ">=", "<=":
		if logic == "OR" {
			return builder.WhereOr(fieldName+" "+operator+" ?", conditionValue), nil
		}
		return builder.Where(fieldName+" "+operator+" ?", conditionValue), nil

	case "in", "not in":
		return buildInCondition(builder, fieldName, operator, conditionValue, logic)

	case "like":
		likeValue := "%" + gconv.String(conditionValue) + "%"
		if logic == "OR" {
			return builder.WhereOr(fieldName+" LIKE ?", likeValue), nil
		}
		return builder.Where(fieldName+" LIKE ?", likeValue), nil

	default:
		return builder, fmt.Errorf("不支持的操作符: %s", operator)
	}
}

// buildInCondition 构建IN条件
func buildInCondition(builder *gdb.WhereBuilder, fieldName string, operator string, conditionValue interface{}, logic string) (*gdb.WhereBuilder, error) {
	var values []interface{}

	switch v := conditionValue.(type) {
	case []interface{}:
		values = v
	case []int64:
		values = make([]interface{}, len(v))
		for i, val := range v {
			values[i] = val
		}
	default:
		values = []interface{}{conditionValue}
	}

	if len(values) == 0 {
		return builder, nil // 空数组，跳过此条件
	}

	isOrLogic := logic == "OR"
	isNotIn := operator == "not in"

	switch {
	case isNotIn && isOrLogic:
		return builder.WhereOrNotIn(fieldName, values), nil
	case isNotIn:
		return builder.WhereNotIn(fieldName, values), nil
	case isOrLogic:
		return builder.WhereOrIn(fieldName, values), nil
	default:
		return builder.WhereIn(fieldName, values), nil
	}
}

// buildMainConditionGroupsToQueryBuilder 在Model上构建条件组（用于引用数据查询）
func buildMainConditionGroupsToQueryBuilder(ctx context.Context, model *gdb.Model, mainGroups *dto.MainConditionGroups, formColumns []*dto.FormColumn, loginUser *dto.UserClaims) (*gdb.Model, error) {
	if len(mainGroups.Conditions) == 0 {
		return model, nil
	}

	for i, group := range mainGroups.Conditions {
		if len(group.Rules) == 0 {
			continue
		}

		subBuilder := model.Builder()
		if err := buildConditionGroupForQuery(ctx, &subBuilder, &group, formColumns, loginUser); err != nil {
			return model, err
		}

		if i == 0 || mainGroups.Logic != "OR" {
			model = model.Where(subBuilder)
		} else {
			model = model.WhereOr(subBuilder)
		}
	}

	return model, nil
}

// buildConditionGroupForQuery 为查询构建条件组
func buildConditionGroupForQuery(ctx context.Context, builder **gdb.WhereBuilder, group *dto.ConditionGroup, formColumns []*dto.FormColumn, loginUser *dto.UserClaims) error {
	if builder == nil || *builder == nil || group == nil || len(group.Rules) == 0 {
		return nil
	}

	for i, rule := range group.Rules {
		logic := ""
		if i > 0 {
			logic = group.Logic
		}

		if err := addConditionRuleForQuery(ctx, builder, &rule, formColumns, loginUser, logic); err != nil {
			return err
		}
	}

	return nil
}

// addConditionRuleForQuery 为查询添加条件规则
func addConditionRuleForQuery(ctx context.Context, builder **gdb.WhereBuilder, rule *dto.ConditionRule, formColumns []*dto.FormColumn, loginUser *dto.UserClaims, logic string) error {
	if builder == nil || *builder == nil || rule == nil || rule.Field == "" {
		return nil
	}

	fieldName := getFormattedFieldName(formColumns, rule.Field)

	var conditionValue interface{}
	if rule.ValueType == "fixed" {
		conditionValue = rule.Value
	} else if rule.ValueType == "refData" && rule.RefDataSourceId == "currentUser" {
		conditionValue = service.SysUser().GetUserProperty(ctx, loginUser, rule.RefDataSourceFieldId)
		if conditionValue == nil {
			return nil // 跳过此条件
		}
	} else {
		return nil // 在引用数据查询阶段，其他refData类型跳过
	}

	newBuilder, err := buildConditionByOperator(*builder, fieldName, rule.Operator, conditionValue, logic)
	if err != nil {
		return err
	}
	*builder = newBuilder
	return nil
}
