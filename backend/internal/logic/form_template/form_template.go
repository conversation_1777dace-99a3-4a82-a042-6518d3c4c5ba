package formtemplate

import (
	"backend/api/v1/form"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/gogf/gf/text/gstr"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormTemplate(new(sFormTemplate))
}

type sFormTemplate struct{}

// 保存表单的设计结构
func (s *sFormTemplate) SaveFormDesign(ctx context.Context, input *form.FormTemlateSaveFormDesignReq) (res *form.FormTemlateSaveFormDesignRes, err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		// 验证字段数据格式
		var form_columns []*dto.FormColumn
		var added_columns []*dto.FormColumn
		err = json.Unmarshal([]byte(input.FormColumns), &form_columns)
		library.ErrIsNil(ctx, err)
		jsonData, err := json.Marshal(form_columns)
		library.ErrIsNil(ctx, err, "字段格式不正确")
		input.FormColumns = string(jsonData)
		err = json.Unmarshal([]byte(input.FormColumns), &added_columns)
		library.ErrIsNil(ctx, err, "字段格式不正确")

		with_infos, err := s.computeWithInfos(ctx, form_columns)
		library.ErrIsNil(ctx, err)

		do := &do.FormTemplate{
			Id:             input.Id,
			FormTitle:      input.FormTitle,
			FormSchema:     input.FormSchema,
			FormColumns:    input.FormColumns,
			HistoryColumns: input.HistoryColumns,
			FormType:       input.FormType,
			WithTables:     with_infos,
		}
		var id int64
		// 新增
		if input.Id <= 0 {
			id, err = s.Add(ctx, do)
		} else {
			id, err = s.Edit(ctx, do)
		}
		library.ErrIsNil(ctx, err)
		newInfo, err := s.GetFormTemplateInfo(ctx, id)
		library.ErrIsNil(ctx, err, "保存数据异常")

		// 理论上这里除了修改数据，还需要记录修改记录，后续完善

		// 新增表
		err = s.CreateTable(ctx, newInfo.FormTableName)
		library.ErrIsNil(ctx, err, "创建表单表失败")
		// 新增字段
		err = s.AddFields(ctx, newInfo.FormTableName, added_columns)
		library.ErrIsNil(ctx, err, "新增表单表字段失败")

		// 新版本增加的字段（每次修改结构都会查询字段是否存在，不合理，后续优化）
		newColumns := []*dto.FormColumn{
			{Id: "invalid_at", ColumnType: "DATETIME", Label: "作废时间"},
			{Id: "invalid_reason", ColumnType: "json", Label: "作废的理由等信息"},
		}
		err = s.AddFields(ctx, newInfo.FormTableName, newColumns)
		library.ErrIsNil(ctx, err, "新增表单表字段失败 -2")

		res = new(form.FormTemlateSaveFormDesignRes)
		err = gconv.Struct(newInfo, &res)
		library.ErrIsNil(ctx, err)

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableList))
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateId+gconv.String(id)))
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableName+res.FormTableName))

		// 清除表单表字段缓存（防止字段发生变化，数据无法插入或查询）
		g.DB().GetCore().ClearTableFields(ctx, res.FormTableName)

	})
	return
}

// 保存表单的基础设置字段数据（包含列表设计、表单设置）
func (s *sFormTemplate) SaveFormDefault(ctx context.Context, input *form.FormTemlateSaveFormDefaultReq) (res *form.FormTemlateSaveFormDefaultRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

		if !g.IsEmpty(input.TableDesign) {
			var table_design dto.FormTableDesign
			err = json.Unmarshal(gconv.Bytes(input.TableDesign), &table_design)
			library.ErrIsNil(ctx, err, "列表设计字段格式不正确")
			jsonData, err := json.Marshal(table_design)
			library.ErrIsNil(ctx, err, "列表设计字段格式不正确")
			input.TableDesign = string(jsonData)
		}

		var data *do.FormTemplate
		_ = gconv.Struct(input, &data)

		// 获得历史信息
		oldInfo, err := s.GetFormTemplateInfo(ctx, input.Id)
		library.ErrIsNil(ctx, err)
		if input.OpenFlow == "1" {
			// 先保存流程信息
			flowTemplateId, err := service.FlowTemplate().Save(ctx, oldInfo.FlowTemplateId, &do.FlowTemplate{
				FlowSchema: input.FlowSchema,
				FlowStatus: input.OpenFlow,
			})
			library.ErrIsNil(ctx, err)
			if oldInfo.FlowTemplateId != flowTemplateId {
				data.FlowTemplateId = flowTemplateId
			}
		}
		// 保存表单信息
		id, err := s.Edit(ctx, data)
		library.ErrIsNil(ctx, err)

		newInfo, err := s.GetFormTemplateInfo(ctx, id)
		library.ErrIsNil(ctx, err, "保存数据异常")
		_ = gconv.Struct(newInfo, &res)
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableList))
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateId+gconv.String(id)))
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableName+res.FormTableName))

	})
	return
}

// Add
func (s *sFormTemplate) Add(ctx context.Context, input *do.FormTemplate) (id int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 生成一个唯一的表单名称
		input.FormTableName, err = s.GetUniqueFormName(ctx)
		library.ErrIsNil(ctx, err, "获取表名失败")
		// 生成表单编号
		input.FormCode = library.GenerateID("FT-{RanStr(6)}-{20060102150406}{RanNum(8)}", 0)
		library.ErrIsNil(ctx, err, "获取表单编号失败")

		input.CreatedBy = library.GetUserId(ctx)
		id, err = dao.TenantCtx(dao.FormTemplate, ctx).InsertAndGetId(input)

		library.ErrIsNil(ctx, err, "添加表单模板失败")

	})
	return
}

// Edit 修改
func (s *sFormTemplate) Edit(ctx context.Context, input *do.FormTemplate) (id int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.FormTemplate, ctx).WherePri(input.Id).Update(input)
		library.ErrIsNil(ctx, err, "修改表单模板失败")
		id = gconv.Int64(input.Id)

	})
	return
}

// 获得与xx表有关联的模板列表
func (s *sFormTemplate) GetRelatedFormTemplateList(ctx context.Context, tableName string) (res []*entity.FormTemplate, err error) {
	err = dao.TenantCtx(dao.FormTemplate, ctx).Where(fmt.Sprintf(`JSON_CONTAINS(%s,  CONCAT('{"table_name": "', ?, '"}'), '$')`, dao.FormTemplate.Columns().WithTables), tableName).Scan(&res)
	return
}

// 获得表单模板信息
func (s *sFormTemplate) GetFormTemplateInfo(ctx context.Context, id int64) (res *with.FormTemplate, err error) {
	err = dao.TenantCtx(dao.FormTemplate, ctx).WithAll().WherePri(id).Scan(&res)
	if err != nil {
		return
	}
	return
}

// 获得表单模板信息
func (s *sFormTemplate) GetFormTemplateInfoCache(ctx context.Context, id int64) (res *with.FormTemplate, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cacheRes, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateId+gconv.String(id)), func(ctx context.Context) (value interface{}, err error) {
			value, err = s.GetFormTemplateInfo(ctx, id)
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		err = gconv.Struct(cacheRes, &res)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 获得一个有序且唯一的表单名称
func (s *sFormTemplate) GetUniqueFormName(ctx context.Context) (name string, err error) {
	tenant_id := library.AddZero(library.GetTenantID(ctx), 5)

	next_id, err := service.Cache().GetNextIdString(ctx, fmt.Sprintf("form_name_%s", tenant_id), 5)
	if err != nil {
		return
	}
	name = fmt.Sprintf("form_data_%s_%s", tenant_id, next_id)

	// 是否存在这个表名
	count, err := dao.TenantCtx(dao.FormTemplate, ctx).Where(dao.FormTemplate.Columns().FormTableName, name).Count()
	if err != nil {
		return
	}
	if count > 0 {
		return s.GetUniqueFormName(ctx)
	}
	return
}

// 验证表名是否已经存在
func (s *sFormTemplate) TableNameExists(ctx context.Context, formTableName string) (isExists bool, err error) {
	tables, err := s.GetTablesCache(ctx)
	if err != nil {
		return
	}

	isExists = false
	if slices.Contains(tables, formTableName) {
		isExists = true
	}
	return
}

func (s *sFormTemplate) GetTablesCache(ctx context.Context) (tables []string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cacheRes, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyTables), func(ctx context.Context) (value interface{}, err error) {
			value, err = g.DB().Tables(ctx)
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		err = gconv.Struct(cacheRes.Val(), &tables)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 创建一个新表
func (s *sFormTemplate) CreateTable(ctx context.Context, tableName string) (err error) {
	isExists, err := s.TableNameExists(ctx, tableName)
	if err != nil {
		return
	}
	if isExists {
		g.Log().Info(ctx, fmt.Sprintf("Creating table %s\n Failed: %s", tableName, "表名已存在"))
		return
	}

	// 创建表
	_, err = g.DB().Ctx(ctx).Exec(ctx, fmt.Sprintf(`
		CREATE TABLE %s(
			id BIGINT AUTO_INCREMENT COMMENT 'id' ,
			template_id BIGINT    COMMENT '表单模板id' ,
			created_by BIGINT     COMMENT '创建人' ,
			created_at DATETIME   COMMENT '创建时间' ,
			updated_at DATETIME   COMMENT '更新时间' ,
			deleted_at DATETIME   COMMENT '删除时间' ,
			tenant_id BIGINT      COMMENT '数据租户id' ,
			flow_status INT       COMMENT '审批流状态' ,
			flow_status_version DATETIME   COMMENT '审批流状态更新时间' ,
			extra_data	json      COMMENT '附加数据' ,
			expand_data_id BIGINT COMMENT '扩展的表单数据id(如果本表是用于拓展某个自建表单的，则会记录此id)' ,
			PRIMARY KEY (id) 
		)  COMMENT = '表单数据表';
	`, tableName))
	return
}

// 更新的时候，需要添加表字段
func (s *sFormTemplate) AddFields(ctx context.Context, formTableName string, fields []*dto.FormColumn) (err error) {
	for _, field := range fields {
		if err = s.AddField(ctx, formTableName, field); err != nil {
			break
		}
	}
	return

}

// 添加耽搁字段
func (s *sFormTemplate) AddField(ctx context.Context, formTableName string, field *dto.FormColumn) (err error) {
	if field.Id == "" || field.ColumnType == "" {
		return fmt.Errorf("invalid field definition: %+v", field)
	}
	// 检查字段是否存在
	exists, err := s.FieldExists(ctx, formTableName, field.Id)
	if err != nil {
		return
	}
	if exists {
		g.Log().Warning(ctx, fmt.Sprintf("Field %s already exists in table %s\n", field.Id, formTableName))
		return
	}
	columnDefinition := fmt.Sprintf("`%s` %s", field.Id, field.ColumnType)
	alterTableSQL := fmt.Sprintf("ALTER TABLE `%s` ADD COLUMN %s", formTableName, columnDefinition)
	_, err = g.DB().Ctx(ctx).Exec(ctx, alterTableSQL)
	return
}

// 检查字段是否存在
func (s *sFormTemplate) FieldExists(ctx context.Context, formTableName string, fieldName string) (bool, error) {
	query := fmt.Sprintf("SHOW COLUMNS FROM `%s` LIKE '%s'", formTableName, fieldName)
	count, err := g.DB().Ctx(ctx).Query(ctx, query)
	if err != nil {
		return false, err
	}
	return count.Len() > 0, nil
}

// 获取表单模板列表
func (s *sFormTemplate) GetList(ctx context.Context, req *form.FormTemlateListReq) (res *form.FormTemlateListRes, err error) {
	res = new(form.FormTemlateListRes)
	m := dao.TenantCtx(dao.FormTemplate, ctx)
	if req.FormTitle != "" {
		m = m.Where(dao.FormTemplate.Columns().FormTitle+" like ?", "%"+req.FormTitle+"%")
	}
	if req.FormCode != "" {
		m = m.Where(dao.FormTemplate.Columns().FormCode+" like ?", "%"+req.FormCode+"%")
	}
	if req.OpenFlow > 0 {
		m = m.Where(dao.FormTemplate.Columns().OpenFlow, req.OpenFlow)
	}
	if req.NoInMenu {
		var noInIds []int64
		noInIds, err = service.SysMenu().GetBindFormIds(ctx)
		if err != nil {
			return
		}
		m = m.WhereNotIn(dao.FormTemplate.Columns().Id, noInIds)
	} else {
		// m = m.WhereNot(fmt.Sprintf("ifnull(%s,0)", dao.FormTemplate.Columns().FormType), enum.FormTemplateType_Expand)
	}
	order := "created_at desc,id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取总数失败")
		if req.PageSize > 0 {
			m = m.Page(req.PageNum, req.PageSize)
		}
		if !req.NoInMenu {
			m = m.With(&with.SysUserOutline{})
		}
		err = m.Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

// 获得表单用于数据库相关的设计结构（包括列信息、表格信息）
func (s *sFormTemplate) GetDatabaseDesign(ctx context.Context, id int64) (templateInfo *with.FormTemplate, formColumns library.MySlice[*dto.FormColumn], tableDesign *dto.FormTableDesign, permissions *dto.FormPermissions, err error) {

	err = g.Try(ctx, func(ctx context.Context) {

		// 获得表的名称
		templateInfo, err = s.GetFormTemplateInfoCache(ctx, id)
		library.ErrIsNil(ctx, err, "获取表单模板信息失败")

		isExist, err := s.TableNameExists(ctx, templateInfo.FormTableName)
		library.ErrIsNil(ctx, err, "获取表单模板信息失败")
		if !isExist {
			err = fmt.Errorf("表单数据结构异常，请联系管理员")
			library.ErrIsNil(ctx, err)
		}

		err = json.Unmarshal([]byte(templateInfo.FormColumns), &formColumns)
		library.ErrIsNil(ctx, err, "表单模板中的表单设计结构异常，请联系管理员")

		err = json.Unmarshal([]byte(lo.Ternary(templateInfo.TableDesign == "", "{}", templateInfo.TableDesign)), &tableDesign)
		library.ErrIsNil(ctx, err, "表单模板中的表格设计结构异常，请联系管理员")

		json.Unmarshal([]byte(lo.Ternary(templateInfo.Permissions == "", "{}", templateInfo.Permissions)), &permissions)
	})
	return
}

// 删除
func (s *sFormTemplate) Delete(ctx context.Context, id int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 验证是否已经存在数据
		templateInfo, err := s.GetFormTemplateInfoCache(ctx, id)
		library.ErrIsNil(ctx, err, "获取表单模板信息失败")
		m := g.DB().Model(templateInfo.FormTableName).Safe().Ctx(ctx)

		count, err := m.Count()
		library.ErrIsNil(ctx, err)
		if count > 0 {
			err = fmt.Errorf("该表单已经存在数据，不能删除，请先妥善处理表单数据")
			library.ErrIsNil(ctx, err)
		}

		_, err = dao.TenantCtx(dao.FormTemplate, ctx).WherePri(id).Delete()
		library.ErrIsNil(ctx, err, "删除失败")

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableList))
	})
	return
}

// 获得所有支持关联的表
func (s *sFormTemplate) GetSupportTablesCache(ctx context.Context) (res []*dto.SupportTable, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cList, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableList), func(ctx context.Context) (value interface{}, err error) {
			list := make([]*dto.SupportTable, 0)
			template_list := make([]*entity.FormTemplate, 0)
			err = dao.TenantCtx(dao.FormTemplate, ctx).Fields(strings.Join([]string{
				dao.FormTemplate.Columns().FormTitle,
				dao.FormTemplate.Columns().FormTableName,
				dao.FlowInstance.Columns().Id,
			}, ",")).Scan(&template_list)
			if err != nil {
				return
			}
			for _, item := range template_list {
				list = append(list, &dto.SupportTable{
					TableName:      item.FormTableName,
					Label:          item.FormTitle,
					Type:           0,
					FormTemplateId: item.Id,
				})
			}
			// 手写的相关表
			custom_list := []*dto.SupportTable{
				{TableName: "form_template", Label: "表单模板", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Form},
				{TableName: "process_template", Label: "流程模板", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Form},
				{TableName: "sys_config", Label: "系统配置", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Form_Permission},
				{TableName: "sys_dept", Label: "部门", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Dept_User},
				{TableName: "sys_menu", Label: "菜单", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Menu},
				{TableName: "sys_post", Label: "岗位", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Post},
				{TableName: "sys_role", Label: "系统角色", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Role},
				{TableName: "sys_user", Label: "人员", Type: 1, PermissionMark: enum.SysPermissionMark_SystemSettle_Dept_User},
				{TableName: "ad_project", Label: "项目", Type: 1, PermissionMark: enum.SysPermissionMark_SystemBase_Project},
				{TableName: "ad_project_role", Label: "项目角色", Type: 1, PermissionMark: enum.SysPermissionMark_SystemBase_Project_Role},
			}
			// 合并 2 个数组
			value = append(list, custom_list...)
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		if cList != nil {
			err := gconv.Struct(cList, &res)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

// 支持的关联表
func (s *sFormTemplate) GetRelatedTables(ctx context.Context, req *form.SupportTableListReq) (res *form.SupportTableListRes, err error) {
	maxLen := 200
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得所有支持的表
		list, err := s.GetSupportTablesCache(ctx)
		library.ErrIsNil(ctx, err, "获得支持表失败")
		// 构造检索条件
		filters := make([]library.FilterFunc[*dto.SupportTable], 0)
		filters = append(filters, func(item *dto.SupportTable) bool {
			if req.Label != "" && !gstr.ContainsI(item.Label, req.Label) {
				return false
			}
			return true
		})
		filters = append(filters, func(item *dto.SupportTable) bool {
			if req.Type > 0 && item.Type+1 != req.Type {
				return false
			}
			return true
		})
		list = library.FilterList(list, filters)

		// 返回list前20条数据
		if len(list) > maxLen {
			list = list[:maxLen]
		}
		res = &form.SupportTableListRes{
			List: list,
		}
	})
	return
}

// 获得表单模板信息
func (s *sFormTemplate) GetFormTemplateByTableName(ctx context.Context, form_table_name string) (res *entity.FormTemplate, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cacheRes, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyFormTemplateTableName+form_table_name), func(ctx context.Context) (value interface{}, err error) {
			value = &entity.FormTemplate{}
			err = dao.TenantCtx(dao.FormTemplate, ctx).Where(dao.FormTemplate.Columns().FormTableName, form_table_name).Scan(value)
			return
		}, 10*time.Second)
		library.ErrIsNil(ctx, err)
		err = gconv.Struct(cacheRes, &res)
		library.ErrIsNil(ctx, err)
	})
	return
}

func (s *sFormTemplate) GetFormTemplateRelatedColumns(ctx context.Context, form_table_name string, show_fixed_fields ...bool) (res []*dto.FormColumn, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, err := s.GetFormTemplateByTableName(ctx, form_table_name)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(templateInfo, "表单模板不存在")

		err = json.Unmarshal([]byte(templateInfo.FormColumns), &res)
		library.ErrIsNil(ctx, err, "表单模板中的表单设计结构异常，请联系管理员")
		if len(show_fixed_fields) > 0 && show_fixed_fields[0] {
			res = append(res, dto.FixedFields...)
		}
	})
	return
}

func (s *sFormTemplate) GetCustomTableRelatedColumns(ctx context.Context, form_table_name string) (res []*dto.FormColumn, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		fields, err := s.GetCustomTableRelatedFields(ctx, form_table_name)
		library.ErrIsNil(ctx, err)
		res = make([]*dto.FormColumn, 0)
		res = lo.Map(fields, func(item *dto.SupportColumn, _ int) *dto.FormColumn {
			return &dto.FormColumn{
				Id:            item.ColumnName,
				Label:         item.Label,
				ComponentName: "Field.Input",
				ColumnType:    item.ColumnType,
			}

		})
	})
	return
}

// 表支持的关联列
func (s *sFormTemplate) GetRelatedColumns(ctx context.Context, req *form.SupportColumnListReq) (res *form.SupportColumnListRes, err error) {
	res = &form.SupportColumnListRes{}
	res.List, err = s.GetRelatedColumnsByTableName(ctx, req.Table, req.Type, req.ShowFixedFields)
	if len(res.List) > 0 {
		for index, item := range res.List {
			if item.ComponentName == "Field.Table" {
				res.List[index].Children = append(dto.FixedTableFields, res.List[index].Children...)
			}
		}
	}
	return
}

func (s *sFormTemplate) GetRelatedColumnsByTableName(ctx context.Context, table_name string, table_type int, show_fixed_fields ...bool) (res []*dto.FormColumn, err error) {
	if table_type == 0 {
		res, err = s.GetFormTemplateRelatedColumns(ctx, table_name, show_fixed_fields...)
	} else {
		res, err = s.GetCustomTableRelatedColumns(ctx, table_name)
	}
	return
}

// 表支持的关联字段(模板表)
func (s *sFormTemplate) GetFormTemplateRelatedFields(ctx context.Context, form_table_name string) (res []*dto.SupportColumn, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		formColumns, err := s.GetFormTemplateRelatedColumns(ctx, form_table_name)
		library.ErrIsNil(ctx, err)
		res = make([]*dto.SupportColumn, 0)
		res = append(res, &dto.SupportColumn{
			ColumnName: "id",
			Label:      "主键ID",
		})
		res = append(res, lo.Map(formColumns, func(item *dto.FormColumn, _ int) *dto.SupportColumn {
			return &dto.SupportColumn{
				ColumnName:       item.Id,
				Label:            item.Label,
				ColumnRenderType: item.ComponentName,
			}
		})...)
	})
	return
}

// 表支持的关联字段（自定义表）
func (s *sFormTemplate) GetCustomTableRelatedFields(ctx context.Context, form_table_name string) (res []*dto.SupportColumn, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		db := g.DB()
		dbName := db.GetConfig().Name
		query := fmt.Sprintf(`SELECT 
			COLUMN_NAME, 
			COLUMN_TYPE, 
			COLUMN_COMMENT 
		FROM 
			information_schema.COLUMNS 
		WHERE 
			TABLE_SCHEMA = '%s' AND 
			TABLE_NAME = ?`, dbName)

		err = db.GetScan(ctx, &res, query, form_table_name)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 表支持的关联字段
func (s *sFormTemplate) GetRelatedFields(ctx context.Context, req *form.SupportFieldsListReq) (res *form.SupportFieldsListRes, err error) {
	res = &form.SupportFieldsListRes{}
	if req.Type == 0 {
		res.List, err = s.GetFormTemplateRelatedFields(ctx, req.Table)
	} else {
		res.List, err = s.GetCustomTableRelatedFields(ctx, req.Table)
	}
	return
}

// 获得拓展key对应的模板id
func (s *sFormTemplate) GetTemplateIdByExpandKey(ctx context.Context, expand_key string) (template_id int64, err error) {
	value, err := service.SysConfig().GetValue(ctx, "form.template.expand."+expand_key)
	if err != nil {
		return
	}
	template_id = gconv.Int64(value)
	return
}

// 设置拓展key对应的模板id
func (s *sFormTemplate) SetTemplateIdByExpandKey(ctx context.Context, expand_key string, template_id int64) (err error) {
	err = service.SysConfig().SetValue(ctx, "form.template.expand."+expand_key, gconv.String(template_id), time.Now().Unix())
	return
}

// 更新表单模版的权限设置
func (s *sFormTemplate) PermissionSettle(ctx context.Context, req *form.FormTemlatePermissionSettingReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Info(ctx, library.SDump("req.Permissions", req.Permissions))
		// 开启事务
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			for _, item := range req.Permissions {
				if item.ID > 0 {
					updateData := &do.FormTemplate{
						Permissions: item.Permissions,
					}
					g.Log().Info(ctx, library.SDump("updateData", updateData))
					_, err := dao.TenantCtx(dao.FormTemplate, ctx).Where(dao.FormTemplate.Columns().Id, item.ID).Update(updateData)
					if err != nil {
						return err
					}
				}
			}
			return nil
		})
		library.ErrIsNil(ctx, err)

	})
	return
}

// 构建脚本引用
func (s *sFormTemplate) BuildScriptRef(ctx context.Context, formTableName string, formTableType int, formColumns []*dto.FormColumn, formData map[string]interface{}, columnMapRules []dto.ColumnMapRule) (scriptRef map[string]interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

		scriptRef = lo.Assign(formData)
		originData := make(map[string]interface{})
		originColumns := make(map[string]interface{})
		for _, columnMapRule := range columnMapRules {
			// 规则类型 0: 普通字段 1: 子表字段 2：其他表（非关联）
			if columnMapRule.Type == 0 {
				sourceName := gconv.String(columnMapRule.Source)
				columnValue, isExists := formData[sourceName]
				originColumns[columnMapRule.Target] = sourceName
				if isExists {
					scriptRef[columnMapRule.Target] = columnValue
				}
			} else if columnMapRule.Type == 1 && len(columnMapRule.SubRules) > 0 {
				sourceName := gconv.String(columnMapRule.Source)
				columnValue, isExists := formData[sourceName]
				originSubTableColumns := make(map[string]interface{})
				for _, subRule := range columnMapRule.SubRules {
					sourceSubName := gconv.String(subRule.Source)
					originSubTableColumns[subRule.Target] = sourceSubName
				}
				if isExists {
					columnValueSubTable := make([]map[string]interface{}, 0)
					if columnValueSlice, ok := columnValue.([]interface{}); ok {
						for _, subItem := range columnValueSlice {
							subItemMap := gconv.Map(subItem)
							subRef, err := s.BuildScriptRef(ctx, sourceName, 2, formColumns, subItemMap, columnMapRule.SubRules)
							library.ErrIsNil(ctx, err)
							columnValueSubTable = append(columnValueSubTable, subRef)
						}
					}
					originSubTableColumns["__name"] = sourceName
					scriptRef[columnMapRule.Target] = columnValueSubTable
				}
				originColumns[columnMapRule.Target] = originSubTableColumns
			} else if columnMapRule.Type == 2 {
				source := gconv.Map(columnMapRule.Source)
				tableName := gconv.String(source["table_name"])
				tableType := gconv.Int(source["type"])
				var formColumns []*dto.FormColumn
				if tableType == 0 {
					formColumns, err = s.GetFormTemplateRelatedColumns(ctx, tableName)
				} else {
					formColumns, err = s.GetCustomTableRelatedColumns(ctx, tableName)
				}
				library.ErrIsNil(ctx, err)
				tableRef, err := s.BuildScriptRef(ctx, tableName, tableType, formColumns, map[string]interface{}{}, columnMapRule.SubRules)
				library.ErrIsNil(ctx, err)
				originColumns[columnMapRule.Target] = tableRef["__origin"]
			}

		}
		originData["formTableName"] = formTableName
		originData["formTableType"] = formTableType
		originData["columns"] = originColumns
		scriptRef["__origin"] = originData
	})
	return
}

func (s *sFormTemplate) computeWithInfos(ctx context.Context, form_columns []*dto.FormColumn) (with_infos []*dto.FormWithInfo, err error) {
	// 获得所有支持的表
	support_tables, err := s.GetSupportTablesCache(ctx)
	library.ErrIsNil(ctx, err, "获得支持表失败")

	for _, form_column := range form_columns {

		if form_column.ComponentName == "Field.Table" && len(form_column.Children) > 0 {
			childWithInfos, err := s.computeWithInfos(ctx, form_column.Children)
			library.ErrIsNil(ctx, err)
			for _, childWithInfo := range childWithInfos {
				childWithInfo.CurrentParentColumnName = form_column.Id
				with_infos = append(with_infos, childWithInfo)
			}
		} else if gstr.HasPrefix(form_column.ComponentName, "Field.With") {
			// 获得表名称
			table_name, ok := form_column.Props["table_name"].(string)
			if !ok {
				continue
			}
			// 获得关联表的字段名称
			option_value, ok := form_column.Props["option_value"].(string)
			if !ok {
				continue
			}
			support_table, ok := lo.Find(support_tables, func(item *dto.SupportTable) bool {
				return item.TableName == table_name
			})
			if !ok {
				continue
			}

			allow_main_table_ignore, ok := form_column.Props["allowMainIgnore"].(bool)
			if !ok {
				allow_main_table_ignore = false
			}

			with_infos = append(with_infos, &dto.FormWithInfo{
				Type:                 support_table.Type,
				TableName:            support_table.TableName,
				FormTemplateID:       support_table.FormTemplateId,
				RelatedColumnName:    option_value,
				CurrentColumnName:    form_column.Id,
				CurrentColumnType:    lo.Ternary(form_column.ComponentName == "Field.WithSingleSelect", 1, 2),
				AllowMainTableIgnore: allow_main_table_ignore,
			})
		}
	}

	return
}
