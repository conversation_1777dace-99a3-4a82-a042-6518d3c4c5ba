package flowinstance

import (
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterFlowInstance(new(sFlowInstance))
}

type sFlowInstance struct{}

// 新建流程实例
func (s *sFlowInstance) Create(ctx context.Context, form_template_id int64, form_id int64, extraData *dto.InstanceExtraData, noOpenError bool, instanceType enum.FlowInstanceType, createdBy int64) (instance *dto.FlowInstance, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得表单数据
		formData, formTemplate, err := service.FormData().GetDetail(ctx, form_template_id, form_id)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(formTemplate, "表单模板不存在")
		if formTemplate.OpenFlow != 1 {
			if noOpenError {
				return
			}
			err = fmt.Errorf("表单模板未开启工作流审批功能")
			library.ErrIsNil(ctx, err)
		}
		library.ValueIsNil(formTemplate.FlowTemplate, "流程模板不存在")
		var flowSchema *dto.FlowSchema
		err = json.Unmarshal([]byte(formTemplate.FlowTemplate.FlowSchema), &flowSchema)
		library.ErrIsNil(ctx, err, "流程模板结构异常")
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(formData, "表单数据不存在")
		var formColumns []*dto.FormColumn
		err = json.Unmarshal([]byte(formTemplate.FormColumns), &formColumns)
		library.ErrIsNil(ctx, err, "表单模板结构异常")

		if createdBy <= 0 {
			createdBy = gconv.Int64(formData["created_by"])
		}
		createUser, err := service.SysUser().GetInfoByID(ctx, createdBy)
		library.ErrIsNil(ctx, err, "获得创建人信息失败")
		// 预生成整个实例生命周期的所有步骤
		steps, err := s.GenerateSteps(ctx, flowSchema, formData, createUser, extraData)
		library.ErrIsNil(ctx, err)

		// 保存流程实例数据
		info := &do.FlowInstance{
			FlowTemplateId:    formTemplate.FlowTemplate.Id,
			FlowTemplateSnap:  formTemplate.FlowTemplate.FlowSchema,
			FormTemplateId:    form_template_id,
			FormTemplateTitle: formTemplate.FormTitle,
			FormTemplateSnap:  formTemplate.FormSchema,
			FormTableName:     formTemplate.FormTableName,
			FormTableId:       form_id,
			InstanceState:     enum.FlowStatus_Pending,
			FormDataSnap: &dto.InstanceFormDatSnap{
				FormData:    formData,
				FormSchema:  formTemplate.FormSchema,
				FormColumns: formColumns,
			},
			CreatedBy:        createUser.Id,
			FlowSteps:        steps,
			FormExtraData:    extraData,
			FlowInstanceType: instanceType,
		}
		lastId, err := dao.TenantCtx(dao.FlowInstance, ctx).InsertAndGetId(info)
		library.ErrIsNil(ctx, err, "添加实例数据失败")

		// instance, err := s.GetInstance(ctx, lastId)
		// library.ErrIsNil(ctx, err)
		// queue.QueueManager().Publish(consts.StreamSubjectFlowCreated, instance)

		queue.QueueManager().Publish(consts.StreamSubjectFlowCreated, map[string]interface{}{
			"id": lastId,
		})
	})
	return
}

// 审批流转
func (s *sFlowInstance) StartNextApproval(ctx context.Context, historyAuditor *entity.FlowInstanceHistoryAuditor, instance *dto.FlowInstance) (err error) {
	g.Log().Info(ctx, "************StartNextApproval***************")
	err = g.Try(ctx, func(ctx context.Context) {
		// 如果传入的历史纪录id为空,则先创建一个开始节点的审核历史纪录
		if historyAuditor == nil {
			if instance == nil {
				library.Fail(ctx, "实例数据异常")
			}
			if instance.InstanceState != int(enum.FlowStatus_Pending) {
				g.Log().Info(ctx, "流程实例已经启动，重复操作")
				return
			}
			err = s.UpdateInstanceStatus(ctx, instance.Id, enum.FlowStatus_Running, instance)
			if err != nil {
				return
			}

			_, _, err := s.StartInstanceFirst(ctx, instance)
			library.ErrIsNil(ctx, err)

			return
		}

		if instance == nil {
			instance, err = s.GetInstance(ctx, historyAuditor.FlowInstanceId)
			library.ErrIsNil(ctx, err)
		}
		history, err := service.FlowInstanceHistory().GetInfoByID(ctx, historyAuditor.FlowInstanceHistoryId)
		library.ErrIsNil(ctx, err)
		// 获得当前审批历史的信息
		switch enum.NodeActionType(historyAuditor.ApprovalStatus) {
		case enum.NodeActionType_Pending: // 如果是待处理，则需要通知 undo

		case enum.NodeActionType_Agree:
			err = s.handleApprovalAgree(ctx, instance, history, historyAuditor)
		case enum.NodeActionType_Reject:
			err = s.handleApprovalReject(ctx, instance, history, historyAuditor)
		case enum.NodeActionType_Return:
			err = s.handleApprovalReturn(ctx, instance, history, historyAuditor)
		case enum.NodeActionType_Transfer: // 转交（暂不处理）
		}
		library.ErrIsNil(ctx, err)

		// 如果是开始节点

	})
	return
}

// 节点审核完成
func (s *sFlowInstance) HistoryFinish(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory) (err error) {
	// 将本节点的id加入实例的已完成节点id列表
	if history.Status != int(enum.ApprovalStatus_Return) {
		instance.FinishdStepsUn = append(instance.FinishdStepsUn, history.StepId)
	}
	instance.CurrentStepsUn = lo.Filter(instance.CurrentStepsUn, func(item *dto.InstanceFlowNode, _ int) bool {
		return lo.IndexOf(instance.FinishdStepsUn, item.Id) < 0
	})
	// 更新实例数据
	_, err = s.UpdateInstance(ctx, instance.Id, &do.FlowInstance{
		FinishdSteps: instance.FinishdStepsUn,
		CurrentSteps: instance.CurrentStepsUn,
	})
	return
}

// 处理审批拒绝
func (s *sFlowInstance) handleApprovalReject(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory, historyAuditor *entity.FlowInstanceHistoryAuditor) (err error) {
	g.Log().Info(ctx, "************handleApprovalReject***************")
	err = g.Try(ctx, func(ctx context.Context) {

		// 更新节点实例纪录状态
		history.Status = int(enum.ApprovalStatus_Reject)
		err = service.FlowInstanceHistory().UpdateStatus(ctx, history.Id, history.Status)
		library.ErrIsNil(ctx, err, "更新实例节点状态失败")

		err = s.HistoryFinish(ctx, instance, history)
		library.ErrIsNil(ctx, err, "更新节点状态到实例中失败")

		err = s.EndInstance(ctx, instance, enum.FlowStatus_Reject)
		library.ErrIsNil(ctx, err, "结束流程失败")

		// //撤销同节点其他审批人未审批的纪录，并结束流程
		// err = service.FlowInstanceHistoryAuditor().CancelPendingAuditors(ctx, historyAuditor.FlowInstanceHistoryId)
		// library.ErrIsNil(ctx, err, "撤销同节点审批人未审批的纪录失败")

		err = service.FlowInstanceHistoryAuditor().CancelAllPendingAuditors(ctx, historyAuditor.FlowInstanceId)
		library.ErrIsNil(ctx, err, "撤销审批人未审批的纪录失败")
	})
	return
}

// 处理审批回退
func (s *sFlowInstance) handleApprovalReturn(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory, historyAuditor *entity.FlowInstanceHistoryAuditor) (err error) {
	g.Log().Info(ctx, "************handleApprovalReturn***************")
	err = g.Try(ctx, func(ctx context.Context) {
		returnNode, hasReturnNode := lo.Find(instance.FlowStepsUn.Steps, func(item *dto.InstanceFlowNode) bool {
			return item.Id == historyAuditor.ReturnNodeId
		})
		if !hasReturnNode {
			err = fmt.Errorf("未找到退回节点")
			library.ErrIsNil(ctx, err)
		}
		// // 撤销同节点未审批的纪录
		// err = service.FlowInstanceHistoryAuditor().CancelPendingAuditors(ctx, historyAuditor.FlowInstanceHistoryId)
		// library.ErrIsNil(ctx, err, "撤销同节点审批人未审批的纪录失败")

		waitCancelStepIds, err := s.getWaitCancelStepsId(ctx, instance, returnNode.Id, history.StepId)
		library.ErrIsNil(ctx, err)

		err = service.FlowInstanceHistoryAuditor().CancelAllPendingAuditorsByStepIds(ctx, historyAuditor.FlowInstanceId, waitCancelStepIds)
		library.ErrIsNil(ctx, err, "撤销审批人未审批的纪录失败")

		// 更新节点实例纪录状态
		history.Status = int(enum.ApprovalStatus_Return)
		err = service.FlowInstanceHistory().UpdateStatus(ctx, history.Id, history.Status)
		library.ErrIsNil(ctx, err, "更新实例节点状态失败")

		err = s.HistoryFinish(ctx, instance, history)
		library.ErrIsNil(ctx, err, "更新节点状态到实例中失败")

		returnNode.PreId = history.StepId
		returnNode.PreHistoryId = history.Id
		returnNode.IsReturn = 1

		// 清除审批人中已经存在的审批意见、附件、图片、签名
		returnNode.Approver = lo.Map(returnNode.Approver, func(item *dto.InstanceFlowAuditor, _ int) *dto.InstanceFlowAuditor {
			item.ApprovalComment = ""
			item.Attachment = nil
			item.Pics = nil
			item.Signature = nil
			return item
		})

		// 启动退回节点
		_, _, err = s.StartNodeFirst(ctx, instance, returnNode)
		library.ErrIsNil(ctx, err)

		if returnNode.Shape == enum.NodeType_StartNode {
			queue.QueueManager().Publish(consts.StreamFormFlowEndOrReturn, &dto.FormStreamInfo{
				TableName:    instance.FormTableName,
				FormDataId:   instance.FormTableId,
				ApprovalType: lo.Ternary(instance.FlowInstanceType == 1, "create", "invalidate"),
			})
		}
	})
	return
}

// 递归查询从退回的节点id开始 到 当前节点id 的 所有未审批记录，并撤销
func (s *sFlowInstance) getWaitCancelStepsId(ctx context.Context, instance *dto.FlowInstance, returnNodeStepId string, currentStepId string) (waitCancelStepIds []string, err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		// 初始化待撤销的审批人列表
		waitCancelStepIds = make([]string, 0)

		// 递归函数，用于遍历节点
		var traverse func(nodeId string)
		traverse = func(nodeId string) {
			// 查找所有以当前节点为源的边
			edges := lo.Filter(instance.FlowStepsUn.Edges, func(edge *dto.InstanceFlowEdge, _ int) bool {
				return edge.Source == nodeId
			})

			for _, edge := range edges {
				// 查找目标节点
				targetNode, hasTargetNode := lo.Find(instance.FlowStepsUn.Steps, func(node *dto.InstanceFlowNode) bool {
					return node.Id == edge.Target
				})

				// 递归遍历目标节点
				if hasTargetNode {
					waitCancelStepIds = append(waitCancelStepIds, targetNode.Id)
					if targetNode.Id != currentStepId {
						traverse(targetNode.Id)
					}
				}
			}
		}

		// 从退回节点开始遍历
		traverse(returnNodeStepId)
	})
	return
}

// 处理审批通过
func (s *sFlowInstance) handleApprovalAgree(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory, historyAuditor *entity.FlowInstanceHistoryAuditor) (err error) {
	g.Log().Info(ctx, "************handleApprovalAgree***************")
	err = g.Try(ctx, func(ctx context.Context) {
		var approvers []*dto.InstanceFlowAuditor
		err = json.Unmarshal([]byte(history.ApprovalUsers), &approvers)
		library.ErrIsNil(ctx, err, "节点审批人信息异常")
		switch enum.NodeCondition(history.Condition) {
		case enum.NodeCondition_Parallel: // 1. 并行会签，可同时处理（需所有人同意）
			noAgreens, err := service.FlowInstanceHistoryAuditor().GetAuditorsByStatus(ctx, history.Id, enum.NodeActionType_Pending)
			library.ErrIsNil(ctx, err)
			if len(noAgreens) > 0 {
				return
			}
			// 所有人都同意，则进入下一步
		case enum.NodeCondition_Sequence: // 2. 顺序会签，按匹配顺序依次审批（需所有人同意）
			_, index, _ := lo.FindIndexOf(approvers, func(item *dto.InstanceFlowAuditor) bool {
				return item.UserId == historyAuditor.UserId
			})
			if len(approvers) > (index + 1) {
				_, err = s.createHistoryAuditor(ctx, instance, history, approvers[index+1])
				return
			}
			// 所有人都同意，则进入下一步
		case enum.NodeCondition_Or: // 3. 或签（只要有一个人同意即可）
			// 直接进入下一步
		}
		// 更新节点实例纪录状态
		history.Status = int(enum.ApprovalStatus_Pass)
		err = service.FlowInstanceHistory().UpdateStatus(ctx, history.Id, history.Status)
		library.ErrIsNil(ctx, err, "更新实例节点状态失败")

		// 更新当前节点其他审批人的审批状态为已经跳过
		err = service.FlowInstanceHistoryAuditor().SkipPendingAuditors(ctx, history.Id)
		library.ErrIsNil(ctx, err)

		err = s.HistoryFinish(ctx, instance, history)
		library.ErrIsNil(ctx, err, "更新节点状态到实例中失败")

		err = s.processNextStep(ctx, instance, history)
		library.ErrIsNil(ctx, err, "处理下一步失败")

	})
	return
}

// 处理下一步
func (s *sFlowInstance) processNextStep(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory) (err error) {

	g.Log().Info(ctx, "************processNextStep***************")
	if enum.NodeType(history.NodeType) == enum.NodeType_EndNode {
		// 结束流程
		err = s.EndInstance(ctx, instance, enum.FlowStatus_Finish)
	} else {
		// 下一步
		targetEdges := lo.Filter(instance.FlowStepsUn.Edges, func(edge *dto.InstanceFlowEdge, _ int) bool {
			return edge.Source == history.StepId
		})
		if len(targetEdges) <= 0 {
			err = fmt.Errorf("未找到下一步节点")
			library.ErrIsNil(ctx, err)
		}

		targetEdges = lo.UniqBy(targetEdges, func(item *dto.InstanceFlowEdge) string {
			return item.Target
		})
		// 逐步添加审批纪录
		for _, edge := range targetEdges {
			targetNode, hasTargetNode := lo.Find(instance.FlowStepsUn.Steps, func(item *dto.InstanceFlowNode) bool {
				return item.Id == edge.Target
			})
			if hasTargetNode {
				if targetNode.Shape == enum.NodeType_MeetNode {
					// 汇合点需等待所有连接此节点的上级节点都已完成
					waitSourceEdges := lo.Filter(instance.FlowStepsUn.Edges, func(edge *dto.InstanceFlowEdge, _ int) bool {
						if edge.Target == targetNode.Id && edge.Source != history.StepId {
							_, finishd := lo.Find(instance.FinishdStepsUn, func(item string) bool {
								return item == edge.Source
							})
							return !finishd
						}
						return false
					})
					if len(waitSourceEdges) > 0 {
						return
					}
				}
				targetNode.PreId = history.StepId
				targetNode.PreHistoryId = history.Id
				// 启动步骤
				_, _, err = s.StartNodeFirst(ctx, instance, targetNode)
				library.ErrIsNil(ctx, err)
			}
		}
	}
	return
}

// 更新流程实例状态
func (s *sFlowInstance) UpdateInstanceStatus(ctx context.Context, instance_id int64, flow_stated enum.FlowStatus, oldInstance *dto.FlowInstance) (err error) {
	statusInstance := &do.FlowInstance{
		Id:            instance_id,
		InstanceState: int(flow_stated),
	}
	var isFinish = false
	if flow_stated == enum.FlowStatus_Finish ||
		flow_stated == enum.FlowStatus_Reject ||
		flow_stated == enum.FlowStatus_Cancel {
		statusInstance.FinishdAt = gtime.Now()
		isFinish = true
	}
	affected, err := s.UpdateInstance(ctx, instance_id, statusInstance)
	if err != nil {
		return
	}
	statusInstance.UpdatedAt = gtime.Now()
	// queue.QueueManager().Publish(consts.StreamSubjectFlowUpdateStatus, statusInstance)
	queue.QueueManager().Publish(consts.StreamSubjectFlowUpdateStatus, map[string]interface{}{
		"Id":        instance_id,
		"UpdatedAt": gtime.Now(),
	})

	if affected > 0 && isFinish && oldInstance != nil {
		queue.QueueManager().Publish(consts.StreamFormFlowEndOrReturn, &dto.FormStreamInfo{
			TableName:    oldInstance.FormTableName,
			FormDataId:   oldInstance.FormTableId,
			ApprovalType: lo.Ternary(oldInstance.FlowInstanceType == 1, "create", "invalidate"),
		})
	}
	return
}

// 更新流程实例信息
func (s *sFlowInstance) UpdateInstance(ctx context.Context, instance_id int64, info *do.FlowInstance) (affected int64, err error) {
	result, err := dao.TenantCtx(dao.FlowInstance, ctx).WherePri(instance_id).Update(info)
	if err != nil {
		return
	}
	affected, err = result.RowsAffected()
	if err != nil || affected <= 0 {
		return
	}
	// 删除缓存
	service.Cache().Remove(ctx, fmt.Sprintf("%s%d", consts.KeyFormInstanceId, instance_id))
	return
}

// 结束流程实例
func (s *sFlowInstance) EndInstance(ctx context.Context, instance *dto.FlowInstance, flow_status enum.FlowStatus) (err error) {
	instance.InstanceState = int(flow_status)
	err = s.UpdateInstanceStatus(ctx, instance.Id, enum.FlowStatus(instance.InstanceState), instance)
	if err != nil {
		return
	}
	// queue.QueueManager().Publish(consts.StreamSubjectFlowFinish, instance)

	queue.QueueManager().Publish(consts.StreamSubjectFlowFinish, map[string]interface{}{
		"id": instance.Id,
	})

	return
}

// 实例首次启动
func (s *sFlowInstance) StartInstanceFirst(ctx context.Context, instance *dto.FlowInstance) (history *entity.FlowInstanceHistory, historyAuditors []*entity.FlowInstanceHistoryAuditor, err error) {
	g.Log().Info(ctx, "************StartInstanceFirst***************")
	err = g.Try(ctx, func(ctx context.Context) {
		if g.IsEmpty(instance.FlowStepsUn.Steps) {
			err = fmt.Errorf("未找到实例的开始节点")
			library.ErrIsNil(ctx, err)
		}
		startNode, hasStartNode := lo.Find(instance.FlowStepsUn.Steps, func(item *dto.InstanceFlowNode) bool {
			return item.Shape == enum.NodeType_StartNode
		})
		if !hasStartNode {
			err = fmt.Errorf("未找到实例的开始节点")
			library.ErrIsNil(ctx, err)
		}

		// 启动节点
		historyTem, historyAuditorsTem, err := s.StartNodeFirst(ctx, instance, startNode)
		library.ErrIsNil(ctx, err)

		history = historyTem
		historyAuditors = historyAuditorsTem
	})

	return
}

// 节点首次启动
func (s *sFlowInstance) StartNodeFirst(ctx context.Context, instance *dto.FlowInstance, node *dto.InstanceFlowNode) (history *entity.FlowInstanceHistory, historyAuditors []*entity.FlowInstanceHistoryAuditor, err error) {
	g.Log().Info(ctx, "************StartNodeFirst***************")
	err = g.Try(ctx, func(ctx context.Context) {
		// 根据节点创建审核历史
		history, err := service.FlowInstanceHistory().Create(ctx, instance.Id, node)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(ctx, "节点历史纪录创建失败")
		historyAuditors = make([]*entity.FlowInstanceHistoryAuditor, 0)
		var approvers []*dto.InstanceFlowAuditor
		err = json.Unmarshal([]byte(history.ApprovalUsers), &approvers)
		library.ErrIsNil(ctx, err, "节点审批人信息异常")
		if len(approvers) > 0 {
			// 顺序会签，取第一个匹配审批人
			if enum.NodeCondition(history.Condition) == enum.NodeCondition_Sequence {
				approvers = approvers[:1]
			}
			for _, approver := range approvers {
				historyAuditor, err := s.createHistoryAuditor(ctx, instance, history, approver)
				library.ErrIsNil(ctx, err)
				historyAuditors = append(historyAuditors, historyAuditor)
			}

		}
		// 更新实例当前节点
		instance.CurrentStepsUn = append(instance.CurrentStepsUn, node)
		_, err = s.UpdateInstance(ctx, instance.Id, &do.FlowInstance{
			CurrentSteps: instance.CurrentStepsUn,
		})
		library.ErrIsNil(ctx, err, "更新实例状态失败")
		// 如果有审批人，根据审批人进行审批流转
		if len(historyAuditors) > 0 {
			for _, item := range historyAuditors {
				err = s.StartNextApproval(ctx, item, instance)
				library.ErrIsNil(ctx, err)
			}
		} else {

			// 根据选项设置决定启动下一步，还是直接拒绝 undo
			if node.AuditorIsNull == enum.NodeAuditorIsNull_AutoReject {
				// 自动拒绝
				history.Status = int(enum.ApprovalStatus_Reject)
				err = service.FlowInstanceHistory().UpdateStatus(ctx, history.Id, history.Status)
				library.ErrIsNil(ctx, err, "更新实例节点状态失败")
				return
			} else {
				// 自动通过
				history.Status = int(enum.ApprovalStatus_Pass)
				err = service.FlowInstanceHistory().UpdateStatus(ctx, history.Id, history.Status)
				library.ErrIsNil(ctx, err, "更新实例节点状态失败")
			}

			if node.Shape != enum.NodeType_EndNode {
				// 记录节点审批完成（结束节点让它停留在实例上，所以不更新实例的相关状态）
				err = s.HistoryFinish(ctx, instance, history)
				library.ErrIsNil(ctx, err)
			}
			// 进入下一步
			err = s.processNextStep(ctx, instance, history)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

func (s *sFlowInstance) createHistoryAuditor(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory, approver *dto.InstanceFlowAuditor) (*entity.FlowInstanceHistoryAuditor, error) {
	approver.ArrivalTime = time.Now()
	if enum.NodeType(history.NodeType) == enum.NodeType_AudioNode {
		// 审批类型节点
		approver.ApprovalStatus = enum.NodeActionType_Pending
		// 验证审批人之前是否已经同意过了
		if history.IsReturn != 1 && instance.FlowTemplateSnapUn.RepeatApprove {
			auditor, _ := service.FlowInstanceHistoryAuditor().IsExistAgreeLog(ctx, instance.Id, approver.UserId)
			if auditor {
				approver.ApprovalStatus = enum.NodeActionType_Agree
			}
		}
	} else if enum.NodeType(history.NodeType) == enum.NodeType_StartNode {
		// 开始节点
		approver.ApprovalStatus = enum.NodeActionType_Pending
		if instance.InstanceState == int(enum.FlowStatus_Pending) {
			approver.CompletionTime = time.Now()
			approver.ApprovalStatus = enum.NodeActionType_Agree
			approver.IsAutoPass = 1
		}
	} else {
		// 抄送、汇合节点
		approver.CompletionTime = time.Now()
		approver.ApprovalStatus = enum.NodeActionType_Agree
		approver.IsAutoPass = 1
	}

	return service.FlowInstanceHistoryAuditor().Create(ctx, history, approver)

}

// 执行模拟流程
func (s *sFlowInstance) GenerateSteps(ctx context.Context, flowSchema *dto.FlowSchema, form_data map[string]interface{}, createUser *with.SysUser, extraData *dto.InstanceExtraData) (flowSteps *dto.FlowSchemaInstance, err error) {
	steps := make([]*dto.InstanceFlowNode, 0)
	edges := make([]*dto.InstanceFlowEdge, 0)
	mapSteps := make(map[string]int)
	err = g.Try(ctx, func(ctx context.Context) {

		var currentNodes []*dto.InstanceFlowNode
		startNode, err := s.GetNextNode(ctx, flowSchema, form_data, nil, createUser, extraData)

		library.ErrIsNil(ctx, err)
		if len(startNode) != 1 {
			err = fmt.Errorf("流程中必须存在且仅有一个'开始节点'")
			library.ErrIsNil(ctx, err)
		}
		currentNodes = append(currentNodes, startNode[0])
		nodeCount := 0
		for len(currentNodes) > 0 && nodeCount < 1000 {

			currentNode := currentNodes[0]
			currentNodes = currentNodes[1:]
			if _, has := mapSteps[currentNode.Id]; !has {
				steps = append(steps, currentNode)
				mapSteps[currentNode.Id] = 1
				if currentNode.Shape == enum.NodeType_EndNode {
					break
				}
				nextNodes, err := s.GetNextNode(ctx, flowSchema, form_data, currentNode, createUser, extraData)

				library.ErrIsNil(ctx, err)
				if len(nextNodes) <= 0 {
					err = fmt.Errorf("节点[%s]没有可选的下一步，如果流程结束，请添加一个'结束节点'", currentNode.Name)
					library.ErrIsNil(ctx, err)
				}
				edges = append(edges, lo.Map(nextNodes, func(item *dto.InstanceFlowNode, _ int) *dto.InstanceFlowEdge {
					return &dto.InstanceFlowEdge{
						Source: currentNode.Id,
						Target: item.Id,
					}
				})...)
				currentNodes = append(currentNodes, nextNodes...)
				nodeCount++
			}
		}
	})
	flowSteps = &dto.FlowSchemaInstance{
		Steps: steps,
		Edges: edges,
	}
	// g.Log().Info(ctx, library.SDump("当前流转节点", steps))
	return
}

// 根据当前节点获取下一个节点
func (s *sFlowInstance) GetNextNode(ctx context.Context, flowSchema *dto.FlowSchema, form_data map[string]interface{}, currentNode *dto.InstanceFlowNode, createUser *with.SysUser, extraData *dto.InstanceExtraData) (nextNodes []*dto.InstanceFlowNode, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		nextNodes = make([]*dto.InstanceFlowNode, 0)
		if currentNode == nil {
			// 查找开始节点
			startNode, has := lo.Find(flowSchema.Nodes, func(item *dto.FlowSchemaNode) bool {
				return item.Shape == enum.NodeType_StartNode
			})
			if !has {
				return
			}
			firstFlowNode := &dto.InstanceFlowNode{
				Id:        startNode.Id,
				Shape:     startNode.Shape,
				Name:      startNode.Data.Label,
				Condition: startNode.Data.Condition,
			}
			startUser := &dto.InstanceFlowAuditor{
				UserId:   createUser.Id,
				Username: createUser.Username,
			}
			if extraData != nil && extraData.Attachment != nil {
				startUser.ApprovalComment = extraData.Attachment.Reason
				startUser.Attachment = extraData.Attachment.Attachment
				startUser.Pics = extraData.Attachment.Pics
			}
			firstFlowNode.Approver = append(firstFlowNode.Approver, startUser)
			nextNodes = append(nextNodes, firstFlowNode)
		} else {
			currentNodeEdges := lo.Filter(flowSchema.Edges, func(item *dto.FlowSchemaEdge, _ int) bool {
				return item.Source.Cell == currentNode.Id
			})
			if len(currentNodeEdges) <= 0 {
				return
			}
			var customEdges []*dto.FlowSchemaEdge
			var elseEdges []*dto.FlowSchemaEdge

			for _, item := range currentNodeEdges {
				if !g.IsEmpty(item.Data) && !g.IsEmpty(item.Data.FlowCondition) && item.Data.FlowCondition.Type == "1" {
					customEdges = append(customEdges, item)
				} else {
					elseEdges = append(elseEdges, item)
				}
			}
			if len(customEdges) > 0 && len(elseEdges) <= 0 {
				err = fmt.Errorf("自定义流转条件的分支必须与默认流转条件（其他情况）的分支同时存在")
				library.ErrIsNil(ctx, err)
			}

			// 自定义流转条件
			if len(customEdges) > 0 {
				for _, customEdge := range customEdges {
					evaluateCondition := func(ctx context.Context, condition interface{}) (result bool, err error) {
						var condition_ dto.SettleCondition
						if err := library.ToStruct(condition, &condition_); err != nil {
							return false, err
						}
						return s.EvaluateCondition(ctx, &condition_, form_data, createUser)
					}
					eva, err := customEdge.Data.FlowCondition.Condition.EvaluateFlowConditionDetail(ctx, evaluateCondition)
					// eva, err := s.EvaluateFlowConditionDetail(ctx, customEdge.Data.FlowCondition.Condition, form_data, createUser)
					g.Log().Info(ctx, library.SDump("自定义流转条件总体结果", eva))
					library.ErrIsNil(ctx, err)
					if !eva {
						continue
					}
					nextNode, err := s.GetNextNodeByEdge(ctx, flowSchema.Nodes, customEdge, createUser, extraData, form_data)
					library.ErrIsNil(ctx, err)
					if nextNode != nil {
						nextNodes = append(nextNodes, nextNode)
					}
				}
			}
			// 默认流转条件 （其他情况）
			if len(nextNodes) <= 0 && len(elseEdges) > 0 {
				for _, elseEdge := range elseEdges {
					nextNode, err := s.GetNextNodeByEdge(ctx, flowSchema.Nodes, elseEdge, createUser, extraData, form_data)
					library.ErrIsNil(ctx, err)
					if nextNode != nil {
						nextNodes = append(nextNodes, nextNode)
					}
				}
			}
		}
	})
	return
}

// 根据连接线获取下一个节点
func (s *sFlowInstance) GetNextNodeByEdge(ctx context.Context, nodes []*dto.FlowSchemaNode, edge *dto.FlowSchemaEdge, createUser *with.SysUser, extraData *dto.InstanceExtraData, form_data map[string]interface{}) (node *dto.InstanceFlowNode, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		nextNodeMeta, has := lo.Find(nodes, func(item *dto.FlowSchemaNode) bool {
			return item.Id == edge.Target.Cell
		})
		if !has {
			return
		}
		approvers := make([]*dto.InstanceFlowAuditor, 0)

		if !g.IsEmpty(nextNodeMeta.Data) {
			// 获得当前节点的审批人
			approvers, err = s.GetNodeApprovers(ctx, nextNodeMeta.Data, createUser, extraData, form_data)
			library.ErrIsNil(ctx, err)
		}
		node = &dto.InstanceFlowNode{
			Id:            nextNodeMeta.Id,
			Shape:         nextNodeMeta.Shape,
			Name:          nextNodeMeta.Data.Label,
			Condition:     nextNodeMeta.Data.Condition,
			Approver:      approvers,
			AuditorIsNull: nextNodeMeta.Data.AuditorIsNull,
			// ...
		}
	})
	return
}

// 获得节点审批人
func (s *sFlowInstance) GetNodeApprovers(ctx context.Context, node *dto.FlowSchemaNodeData, createUser *with.SysUser, extraData *dto.InstanceExtraData, form_data map[string]interface{}) ([]*dto.InstanceFlowAuditor, error) {
	var approvers []*dto.InstanceFlowAuditor

	err := g.Try(ctx, func(ctx context.Context) {
		approvers = []*dto.InstanceFlowAuditor{}
		if g.IsEmpty(node.Auditor) {
			return
		}
		settle := node.Auditor

		switch settle.Value {
		case enum.AuditorType_SpecificPerson:
			approvers = getSpecificPersonsApprovers(settle)
		case enum.AuditorType_FieldUsers:
			approvers = getFieldUsersApprovers(settle, form_data)
		case enum.AuditorType_DeptHead:
			approvers = getDeptHeadApprovers(ctx, createUser, extraData, settle)
		case enum.AuditorType_HierarchicalDeptHeads:
			approvers = getHierarchicalDeptHeadsApprovers(ctx, createUser, extraData, settle)
		case enum.AuditorType_SpecifiedDeptHead:
			approvers = getSpecifiedDeptHeadApprovers(ctx, settle)
		case enum.AuditorType_SpecifiedPosition:
			approvers = getSpecifiedPositionApprovers(ctx, settle)
		case enum.AuditorType_ProjectRole:
			approvers = getProjectRoleApprovers(ctx, settle.ProjectRole, form_data)
		case enum.AuditorType_ProjectLeader:
			approvers = getProjectLeaderApprovers(ctx, settle.ProjectLeader, form_data)
		case enum.AuditorType_ByApplicantChoice:
			approvers = getApplicantChoiceApprovers(extraData, node)
		}
	})

	if err != nil {
		return nil, err
	}

	// 去重
	approvers = lo.UniqBy(approvers, func(item *dto.InstanceFlowAuditor) int64 {
		return item.UserId
	})

	// // 给审批人添加审批状态和到达时间
	// arrivalTime := time.Now()

	// approvers = lo.Map(approvers, func(item *dto.InstanceFlowAuditor, i int) *dto.InstanceFlowAuditor {
	// 	item.ArrivalTime = arrivalTime
	// 	item.ApprovalStatus = enum.ApprovalStatus_Running
	// 	if node.Condition == enum.NodeCondition_Sequence && i != 0 {
	// 		item.ApprovalStatus = enum.ApprovalStatus_Pending
	// 	}
	// 	return item
	// })

	return approvers, nil
}

// 字段关联用户处理
func getFieldUsersApprovers(settle *dto.SettleAuditor, form_data map[string]interface{}) []*dto.InstanceFlowAuditor {
	var approvers []*dto.InstanceFlowAuditor

	for _, column := range settle.FieldUsers {
		// 从form_data中获取字段值
		fieldValue, has := form_data[column]
		if !has {
			continue
		}
		fieldValueVar, ok := fieldValue.(*gvar.Var)
		if !ok {
			continue
		}
		fieldValueStr := fieldValueVar.String()
		var users []*dto.SysUserBrief
		err := json.Unmarshal([]byte(fieldValueStr), &users)
		if err != nil || len(users) <= 0 {
			continue
		}
		approvers = append(approvers, lo.Map(users, func(item *dto.SysUserBrief, _ int) *dto.InstanceFlowAuditor {
			return &dto.InstanceFlowAuditor{
				UserId:   item.ID,
				Username: item.Username,
			}
		})...)

	}
	return approvers
}

// 指定审批人处理
func getSpecificPersonsApprovers(settle *dto.SettleAuditor) []*dto.InstanceFlowAuditor {
	return lo.Map(settle.SpecifiedUsers, func(item *dto.SettleSpecifiedUsers, _ int) *dto.InstanceFlowAuditor {
		return &dto.InstanceFlowAuditor{
			UserId:   item.Id,
			Username: item.Username,
		}
	})
}

// 部门负责人处理
func getDeptHeadApprovers(ctx context.Context, createUser *with.SysUser, extraData *dto.InstanceExtraData, settle *dto.SettleAuditor) []*dto.InstanceFlowAuditor {
	rule := enum.DeptLeaderRule_Up
	if !g.IsEmpty(settle.DeptLeader) {
		rule = settle.DeptLeader.Rule
	}
	return getApprovers(ctx, createUser, extraData, settle.DeptLeader.Level, 0, rule)
}

// 逐级部门负责人处理
func getHierarchicalDeptHeadsApprovers(ctx context.Context, createUser *with.SysUser, extraData *dto.InstanceExtraData, settle *dto.SettleAuditor) []*dto.InstanceFlowAuditor {
	maxDeptLevel := 999
	rule := enum.DeptLeaderRule_Null
	if !g.IsEmpty(settle.SequentialDeptLeader) {
		if settle.SequentialDeptLeader.Type == 2 {
			maxDeptLevel = settle.SequentialDeptLeader.Level
		}
		rule = settle.SequentialDeptLeader.Rule
	}
	return getApprovers(ctx, createUser, extraData, 0, maxDeptLevel, rule)
}

// 指定部门负责人处理
func getSpecifiedDeptHeadApprovers(ctx context.Context, settle *dto.SettleAuditor) []*dto.InstanceFlowAuditor {
	var approvers []*dto.InstanceFlowAuditor

	for _, specifiedDeptId := range settle.SpecifiedDeptLeader {
		deptLeader, err := service.SysDept().GetInfoByID(ctx, specifiedDeptId)
		library.ErrIsNil(ctx, err)
		if !g.IsEmpty(deptLeader) && !g.IsEmpty(deptLeader.LeaderUser) {
			approvers = append(approvers, &dto.InstanceFlowAuditor{
				UserId:   deptLeader.LeaderUser.Id,
				Username: deptLeader.LeaderUser.Username,
			})
		}
	}

	return approvers
}

// 指定岗位处理
func getSpecifiedPositionApprovers(ctx context.Context, settle *dto.SettleAuditor) []*dto.InstanceFlowAuditor {
	var approvers []*dto.InstanceFlowAuditor

	for _, specifiedPositionId := range settle.SpecifiedPosition {
		postUsers, err := service.SysUser().GetListByPostId(ctx, specifiedPositionId)
		library.ErrIsNil(ctx, err)
		approvers = append(approvers, lo.Map(postUsers, func(item *with.SysUser, _ int) *dto.InstanceFlowAuditor {
			return &dto.InstanceFlowAuditor{
				UserId:   item.Id,
				Username: item.Username,
			}
		})...)
	}

	return approvers
}

// 申请人自选处理
func getApplicantChoiceApprovers(extraData *dto.InstanceExtraData, node *dto.FlowSchemaNodeData) []*dto.InstanceFlowAuditor {
	var approvers []*dto.InstanceFlowAuditor

	if !g.IsEmpty(extraData) && !g.IsEmpty(extraData.SelectedApprovers) && len(extraData.SelectedApprovers) > 0 {
		nodeApprovers, has := lo.Find(extraData.SelectedApprovers, func(item *dto.InstanceFlowNodeApprovers) bool {
			return item.Id == node.Id
		})
		if has {
			approvers = lo.Map(nodeApprovers.Approver, func(item *dto.SysUserBrief, _ int) *dto.InstanceFlowAuditor {
				return &dto.InstanceFlowAuditor{
					UserId:   item.ID,
					Username: item.Username,
				}
			})
		}
	}

	return approvers
}

// 获得项目id
func getProjectId(ctx context.Context, projectColumn string, formData map[string]interface{}) (projectId uint64, err error) {
	if g.IsEmpty(projectColumn) || g.IsEmpty(formData) {
		err = fmt.Errorf("项目列名或表单数据为空")
		return
	}
	projectInfo, has := formData[projectColumn]
	if !has {
		err = fmt.Errorf("表单数据中没有找到项目列%s", projectColumn)
		return
	}
	if projectInfo == nil {
		err = fmt.Errorf("项目列的值未填写")
		return
	}
	projectInfoVar, ok := projectInfo.(*gvar.Var)
	if !ok {
		err = fmt.Errorf("项目列%s的值不是变量类型", projectColumn)
		return
	}
	jsonStr, ok := projectInfoVar.Val().(string)
	if !ok {
		err = fmt.Errorf("项目列%s的值不是字符串类型", projectColumn)
		return
	}
	var option dto.SelectdOption
	if err = json.Unmarshal([]byte(jsonStr), &option); err != nil {
		err = fmt.Errorf("项目列%s的值不是JSON格式", projectColumn)
		return
	}
	projectId = gconv.Uint64(option.Value)
	return
}

// 项目负责人
func getProjectLeaderApprovers(ctx context.Context, projectColumn string, formData map[string]interface{}) []*dto.InstanceFlowAuditor {

	projectId, err := getProjectId(ctx, projectColumn, formData)
	if err != nil {
		g.Log().Info(ctx, library.SDump("getProjectLeaderApprovers err ", err))
		return nil
	}
	project, err := service.Project().GetInfoByID(ctx, projectId, true)
	if err != nil || project == nil || project.LeaderUser == nil {
		return nil
	}
	approvers := make([]*dto.InstanceFlowAuditor, 0)
	approvers = append(approvers, &dto.InstanceFlowAuditor{
		UserId:   project.LeaderUser.Id,
		Username: project.LeaderUser.Username,
	})
	return approvers
}

// 项目角色
func getProjectRoleApprovers(ctx context.Context, settle *dto.SettleProjectRole, formData map[string]interface{}) []*dto.InstanceFlowAuditor {
	projectId, err := getProjectId(ctx, settle.ColumnName, formData)
	if err != nil {
		return nil
	}
	projectRoleIds := lo.Map(settle.Roles, func(item *dto.SettleProjectRoleItem, _ int) int64 {
		return item.Id
	})

	users, err := service.SysUser().GetListByProjectRoles(ctx, gconv.Int64(projectId), projectRoleIds)
	if err != nil || len(users) <= 0 {
		return nil
	}
	return lo.Map(users, func(item *with.SysUserOutline, _ int) *dto.InstanceFlowAuditor {
		return &dto.InstanceFlowAuditor{
			UserId:   item.Id,
			Username: item.Username,
		}
	})
}

func getUserDepts(createUser *with.SysUser, extraData *dto.InstanceExtraData) []*with.SysUserDept {
	userDepts := createUser.Depts
	if len(userDepts) <= 0 {
		return nil
	}
	if !g.IsEmpty(extraData) && !g.IsEmpty(extraData.SelectedDeptId) {
		userDepts = lo.Filter(userDepts, func(item *with.SysUserDept, _ int) bool {
			return item.Id == extraData.SelectedDeptId
		})
	}
	return userDepts
}
func getApprovers(ctx context.Context, createUser *with.SysUser, extraData *dto.InstanceExtraData, skiDeptLevel int, maxDeptLevel int, rule enum.DeptLeaderRule) []*dto.InstanceFlowAuditor {
	var approvers []*dto.InstanceFlowAuditor
	userDepts := getUserDepts(createUser, extraData)
	library.ValueIsNil(userDepts, fmt.Sprintf("用户“%s”的未绑定部门", createUser.Username))
	deptDeepMap := map[int64]int{}
	depthDepts := lo.Map(userDepts, func(item *with.SysUserDept, _ int) *dto.DeptDepth {
		library.ValueIsNil(item.Dept, fmt.Sprintf("用户“%s”的部门“%d”不存在", createUser.Username, item.DeptId))
		deptWith, err := service.SysDept().GetInfoByID(ctx, item.Dept.Id)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(deptWith, fmt.Sprintf("用户“%s”的部门“%d”不存在", createUser.Username, item.DeptId))
		return &dto.DeptDepth{
			Dept:  deptWith,
			Depth: 0,
		}
	})
	deptCount := 0

	for len(depthDepts) > 0 && deptCount < 999 {
		dept := depthDepts[0]
		depthDepts = depthDepts[1:]

		if !g.IsEmpty(dept.Dept.LeaderUser) && dept.Depth >= skiDeptLevel { // && !(dept.Depth == 0 && dept.Dept.LeaderUser.Id == createUser.Id)
			approvers = append(approvers, &dto.InstanceFlowAuditor{
				UserId:   dept.Dept.LeaderUser.Id,
				Username: dept.Dept.LeaderUser.Username,
			})
		}

		if (dept.Depth == 0 && !g.IsEmpty(dept.Dept.LeaderUser) && dept.Dept.LeaderUser.Id == createUser.Id) || (dept.Depth-skiDeptLevel) < maxDeptLevel && dept.Dept.ParentId > 0 {
			parentDept, err := service.SysDept().GetInfoByID(ctx, dept.Dept.ParentId)
			library.ErrIsNil(ctx, err)
			if parentDept != nil {
				if _, has := deptDeepMap[parentDept.Id]; !has {
					depthDepts = append(depthDepts, &dto.DeptDepth{
						Dept:  parentDept,
						Depth: lo.Ternary(g.IsEmpty(dept.Dept.LeaderUser) && rule == enum.DeptLeaderRule_Up, dept.Depth, dept.Depth+1),
					})
				}
			}
		}

		deptDeepMap[dept.Dept.Id] = dept.Depth
		deptCount++
	}

	return approvers
}

// 条件评估
func (s *sFlowInstance) EvaluateCondition(ctx context.Context, condition *dto.SettleCondition, formData map[string]interface{}, createUser *with.SysUser) (result bool, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var rightValue interface{}
		var leftValue interface{}
		leftValue, err := s.GetMappedParamValue(ctx, condition.LeftParam, formData, createUser)
		library.ErrIsNil(ctx, err)
		if condition.ValueType != "variable" {
			rightValue = condition.RightParam
		} else {
			if condition.LeftParam.Column != nil {
				switch condition.LeftParam.Column.ValueType {
				case enum.ParamValueType_Dept:
					rightValue = condition.RightParam
					deptIds, _ := library.ToSliceInt64(rightValue)
					deptIds = lo.Reduce(deptIds, func(agg []int64, item int64, index int) []int64 {
						if len(agg) == 0 {
							agg = []int64{}
						}
						subDeptIds, err := service.SysDept().GetDeptIds(ctx, item)
						library.ErrIsNil(ctx, err)
						agg = append(agg, subDeptIds...)
						return agg
					}, []int64{})
					deptIds = lo.Uniq(deptIds)
					rightValue = deptIds
				case enum.ParamValueType_Post:
					rightValue = condition.RightParam
				case enum.ParamValueType_User:
					var users []*dto.SysUserBrief
					err = json.Unmarshal([]byte(gconv.String(condition.RightParam)), &users)
					library.ErrIsNil(ctx, err)
					rightValue = lo.Map(users, func(item *dto.SysUserBrief, _ int) int64 {
						return int64(item.ID)
					})
				default:
					var rightParam *dto.FLowEdgeParam
					err = json.Unmarshal([]byte(gconv.String(condition.RightParam)), &rightParam)
					library.ErrIsNil(ctx, err)
					rightValue, err = s.GetMappedParamValue(ctx, rightParam, formData, createUser)
					library.ErrIsNil(ctx, err)
				}

			}
		}
		result = library.CompareValues(condition.Operator, leftValue, rightValue)
	})
	return
}

// func (s *sFlowInstance) EvaluateConditionGroup(ctx context.Context, group *dto.SettleConditionGroup, formData map[string]interface{}, createUser *with.SysUser) (result bool, err error) {
// 	err = g.Try(ctx, func(ctx context.Context) {
// 		result = group.GroupRelation == "AND"
// 		for _, condition := range group.Conditions {
// 			conditionResult, err := s.EvaluateCondition(ctx, condition, formData, createUser)
// 			library.ErrIsNil(ctx, err)
// 			if group.GroupRelation == "AND" {
// 				result = result && conditionResult
// 				if !result {
// 					return
// 				}
// 			} else if group.GroupRelation == "OR" {
// 				result = result || conditionResult
// 				if result {
// 					return
// 				}
// 			}
// 		}
// 	})
// 	return
// }

// func (s *sFlowInstance) EvaluateFlowConditionDetail(ctx context.Context, flowCondition *dto.SettleFlowConditionDetail, formData map[string]interface{}, createUser *with.SysUser) (result bool, err error) {
// 	err = g.Try(ctx, func(ctx context.Context) {
// 		result = flowCondition.ConditionGroupRelation == "AND"
// 		for _, group := range flowCondition.ConditionGroups {
// 			groupResult, err := s.EvaluateConditionGroup(ctx, group, formData, createUser)
// 			library.ErrIsNil(ctx, err)
// 			if flowCondition.ConditionGroupRelation == "AND" {
// 				result = result && groupResult
// 				if !result {
// 					return
// 				}
// 			} else if flowCondition.ConditionGroupRelation == "OR" {
// 				result = result || groupResult
// 				if result {
// 					return
// 				}
// 			}
// 		}
// 	})
// 	return
// }

func (s *sFlowInstance) GetInstance(ctx context.Context, flow_instance_id int64) (instance *dto.FlowInstance, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		info, err := s.GetInfoCache(ctx, flow_instance_id)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(info, "流程实例不存在")
		instance = &dto.FlowInstance{}
		err = gconv.Struct(info, instance)
		library.ErrIsNil(ctx, err)
		// 序列化流程模板快照
		if !g.IsEmpty(info.FlowTemplateSnap) {
			var flowSchema *dto.FlowSchema
			err = json.Unmarshal([]byte(info.FlowTemplateSnap), &flowSchema)
			library.ErrIsNil(ctx, err, "流程模板快照结构异常")
			instance.FlowTemplateSnapUn = flowSchema
		}
		// 序列化表单快照
		if !g.IsEmpty(info.FormDataSnap) {
			var formSchema *dto.InstanceFormDatSnap
			err = json.Unmarshal([]byte(info.FormDataSnap), &formSchema)
			library.ErrIsNil(ctx, err)
			instance.FormDataSnapUn = formSchema
		}
		// 序列化附加数据
		if !g.IsEmpty(info.FormExtraData) {
			var extraData *dto.InstanceExtraData
			err = json.Unmarshal([]byte(info.FormExtraData), &extraData)
			library.ErrIsNil(ctx, err, "附加数据结构异常")
			instance.FormExtraDataUn = extraData
		}
		// 序列化步骤结构
		if !g.IsEmpty(info.FlowSteps) {
			var flowSteps *dto.FlowSchemaInstance
			err = json.Unmarshal([]byte(info.FlowSteps), &flowSteps)
			library.ErrIsNil(ctx, err, "表单实例步骤结构异常")
			instance.FlowStepsUn = flowSteps
		}

		// 序列化已完成步骤id
		if !g.IsEmpty(info.FinishdSteps) {
			var finishdSteps []string
			err = json.Unmarshal([]byte(info.FinishdSteps), &finishdSteps)
			library.ErrIsNil(ctx, err, "已完成步骤结构异常")
			instance.FinishdStepsUn = finishdSteps
		}

		// 序列化已完成步骤id
		if !g.IsEmpty(info.CurrentSteps) {
			var currentSteps []*dto.InstanceFlowNode
			err = json.Unmarshal([]byte(info.CurrentSteps), &currentSteps)
			library.ErrIsNil(ctx, err, "正在进行的步骤结构异常")
			instance.CurrentStepsUn = currentSteps
		}
	})
	return
}

// 根据表单模板id和表单数据id获得流程实例信息
func (s *sFlowInstance) GetInstanceByForm(ctx context.Context, form_template_id int64, form_data_id int64) (instance *with.FlowInstance, err error) {
	err = dao.TenantCtx(dao.FlowInstance, ctx).With(&with.SysUser{}, &with.FlowInstanceHistory{}, &with.FlowInstanceHistoryAuditor{}, &with.SysUserShort{}).Where(dao.FlowInstance.Columns().FormTemplateId, form_template_id).Where(dao.FlowInstance.Columns().FormTableId, form_data_id).Scan(&instance)
	return
}
func (s *sFlowInstance) GetAllAuditInstances(ctx context.Context, formTemplateId int64, formId int64) (list []*dto.InstanceTypeSummary, err error) {
	err = dao.TenantCtx(dao.FlowInstance, ctx).
		Where(dao.FlowInstance.Columns().FormTemplateId, formTemplateId).
		Where(dao.FlowInstance.Columns().FormTableId, formId).
		Fields(dao.FlowInstance.Columns().Id, dao.FlowInstance.Columns().FlowInstanceType, dao.FlowInstance.Columns().InstanceState).
		Scan(&list)
	library.ErrIsNil(ctx, err)
	return
}

func (s *sFlowInstance) GetInstanceInfo(ctx context.Context, instanceId int64) (instance *with.FlowInstance, err error) {
	err = dao.TenantCtx(dao.FlowInstance, ctx).With(&with.SysUser{}, &with.FlowInstanceHistory{}, &with.FlowInstanceHistoryAuditor{}, &with.SysUserShort{}).WherePri(instanceId).Scan(&instance)
	return
}

// 获得流程实例详细信息
func (s *sFlowInstance) GetInfoByID(ctx context.Context, flow_instance_id int64) (instance *with.FlowInstance, err error) {
	err = dao.TenantCtx(dao.FlowInstance, ctx).With(&with.SysUser{}).WherePri(flow_instance_id).Scan(&instance)
	return
}

// 获得流程实例详细信息-带缓存
func (s *sFlowInstance) GetInfoCache(ctx context.Context, flow_instance_id int64) (instance *with.FlowInstance, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cInstance, err := service.Cache().GetOrSetFunc(ctx, fmt.Sprintf("%s%d", consts.KeyFormInstanceId, flow_instance_id), func(ctx context.Context) (value interface{}, err error) {
			value, err = s.GetInfoByID(ctx, flow_instance_id)
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		err = json.Unmarshal([]byte(gconv.String(cInstance)), &instance)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 根据流转参数设置，获得映射的参数值
func (s *sFlowInstance) GetMappedParamValue(ctx context.Context, param *dto.FLowEdgeParam, formData map[string]interface{}, createUser *with.SysUser) (value interface{}, err error) {

	if g.IsEmpty(param) || g.IsEmpty(param.Column) {
		err = fmt.Errorf("参数设置不能为空")
		return
	}
	if param.Name == "fieldsValue" {
		return s.GetMappedParamFormValue(ctx, formData, param.Column)
	} else if param.Name == "currentUser" {
		return s.GetMappedParamCurrentUser(ctx, createUser, param.Column)
	} else {
		err = fmt.Errorf("暂不支持“%s”类型参数", param.Label)
	}
	return
}

// 根据流转参数设置，获得映射的参数值(表单值相关)
func (s *sFlowInstance) GetMappedParamFormValue(ctx context.Context, formData map[string]interface{}, param *dto.FLowEdgeParamColumns) (value interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if !strings.Contains(param.ColumnName, ".") {
			// 从 formSchema.FormData 中获取参数值 ，需判断是否存在此key
			mapValue, ok := formData[param.ColumnName]
			if !ok {
				// err = fmt.Errorf("表单中不存在“%s”字段", param.ColumnName)
				// library.ErrIsNil(ctx, err)
				g.Log().Info(ctx, fmt.Sprintf("表单中不存在“%s”字段", param.ColumnName))
			}
			value = mapValue
		} else {
			// 子表数据
			parts := strings.Split(param.ColumnName, ".")
			subTableName := parts[0]
			columnName := parts[1]
			subTableValue, ok := formData[subTableName]
			if !ok {
				// err = fmt.Errorf("表单中不存在“%s”子表", subTableName)
				// library.ErrIsNil(ctx, err)
				g.Log().Info(ctx, fmt.Sprintf("表单中不存在“%s”子表", subTableName))
			}
			subTableMap := subTableValue.([]map[string]interface{})
			valueArr := make([]interface{}, 0)
			if len(subTableMap) > 0 {
				for _, subTable := range subTableMap {
					mapValue, ok := subTable[columnName]
					if !ok {
						// err = fmt.Errorf("子表“%s”中不存在“%s”字段", subTableName, columnName)
						// library.ErrIsNil(ctx, err)
						g.Log().Info(ctx, fmt.Sprintf("子表“%s”中不存在“%s”字段", subTableName, columnName))
					}
					valueArr = append(valueArr, mapValue)
				}
			}
			value = valueArr
		}

	})
	return
}

// 根据流转参数设置，获得映射的参数值(表单值相关)
func (s *sFlowInstance) GetMappedParamCurrentUser(ctx context.Context, createUser *with.SysUser, param *dto.FLowEdgeParamColumns) (value interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		library.ValueIsNil(createUser, "获取发起人失败")
		switch param.ColumnName {
		case "userid":
			value = createUser.Id
		case "depts":
			value = lo.Map(createUser.Depts, func(item *with.SysUserDept, _ int) int64 {
				return item.DeptId
			})
		case "posts":
			value = lo.Map(createUser.Posts, func(item *with.SysUserPost, _ int) int64 {
				return item.PostId
			})
		default:
			err = fmt.Errorf("暂不支持“%s”类型参数", param.Label)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}
