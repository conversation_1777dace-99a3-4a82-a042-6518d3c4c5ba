package flowinstancehistory

import (
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/queue"
	"context"

	"github.com/samber/lo"
)

func init() {
	service.RegisterFlowInstanceHistory(new(sFlowInstanceHistory))
}

type sFlowInstanceHistory struct {
}

// 获得流程实例审核历史详细信息
func (s *sFlowInstanceHistory) GetInfoByID(ctx context.Context, flow_instance_history_id int64) (instance *entity.FlowInstanceHistory, err error) {
	err = dao.TenantCtx(dao.FlowInstanceHistory, ctx).WithAll().WherePri(flow_instance_history_id).Scan(&instance)
	return
}
func (s *sFlowInstanceHistory) GetInfoByInstanceAndStepId(ctx context.Context, instance_id int64, step_id string, pre_history_id int64) (instance *entity.FlowInstanceHistory, err error) {
	err = dao.TenantCtx(dao.FlowInstanceHistory, ctx).
		WithAll().
		Where(dao.FlowInstanceHistory.Columns().StepId, step_id).
		Where(dao.FlowInstanceHistory.Columns().PreHistoryId, pre_history_id).
		Where(dao.FlowInstanceHistory.Columns().FlowInstanceId, instance_id).
		Scan(&instance)
	return
}

// 创建流程审核历史
func (s *sFlowInstanceHistory) Create(ctx context.Context, instance_id int64, flow_node *dto.InstanceFlowNode) (history *entity.FlowInstanceHistory, err error) {
	history, err = s.GetInfoByInstanceAndStepId(ctx, instance_id, flow_node.Id, flow_node.PreHistoryId)
	if err != nil {
		return
	}
	if history != nil {
		return
	}

	doHistory := &do.FlowInstanceHistory{
		FlowInstanceId: instance_id,
		StepId:         flow_node.Id,
		StepName:       flow_node.Name,
		NodeType:       flow_node.Shape,
		Status:         lo.Ternary(flow_node.Shape == enum.NodeType_EndNode, enum.ApprovalStatus_Pass, enum.ApprovalStatus_Running), // enum.ApprovalStatus_Running,
		ApprovalUsers:  flow_node.Approver,
		Condition:      flow_node.Condition,
		IsReturn:       flow_node.IsReturn,
		PreStepId:      flow_node.PreId,
		PreHistoryId:   flow_node.PreHistoryId,
	}
	lastid, err := dao.TenantCtx(dao.FlowInstanceHistory, ctx).InsertAndGetId(doHistory)
	if err != nil {
		return
	}
	history, err = s.GetInfoByID(ctx, lastid)
	if err != nil {
		return
	}
	return
}

func (s *sFlowInstanceHistory) UpdateStatus(ctx context.Context, flow_instance_history_id int64, status int) (err error) {
	historyOld, err := s.GetInfoByID(ctx, flow_instance_history_id)
	if err != nil {
		return
	}
	if historyOld == nil {
		return
	}
	// 记录更新影响的行数
	result, err := dao.TenantCtx(dao.FlowInstanceHistory, ctx).
		WherePri(flow_instance_history_id).
		WhereNot(dao.FlowInstanceHistory.Columns().Status, status).
		Update(&do.FlowInstanceHistory{
			Status: status,
		})
	if err != nil {
		return
	}
	var affected int64 = 0
	affected, err = result.RowsAffected()
	if err != nil || affected <= 0 {
		return
	}

	if historyOld.NodeType == string(enum.NodeType_StartNode) {
		var instance *with.FlowInstance
		instance, err = service.FlowInstance().GetInfoByID(ctx, historyOld.FlowInstanceId)
		if err != nil {
			return
		}
		if instance != nil {
			queue.QueueManager().Publish(consts.StreamFormFlowStarted, &dto.FormStreamInfo{
				TableName:    instance.FormTableName,
				FormDataId:   instance.FormTableId,
				ApprovalType: lo.Ternary(instance.FlowInstanceType == 1, "create", "invalidate"),
			})
		}
	}

	return
}

// 撤销某流程实例的所有未审核纪录
func (s *sFlowInstanceHistory) CancelAllPending(ctx context.Context, instance_id int64) (err error) {
	_, err = dao.TenantCtx(dao.FlowInstanceHistory, ctx).
		Where(dao.FlowInstanceHistory.Columns().FlowInstanceId, instance_id).
		Where(dao.FlowInstanceHistory.Columns().Status, enum.ApprovalStatus_Running).
		Update(&do.FlowInstanceHistory{
			Status: enum.ApprovalStatus_Cancel,
		})
	return
}
