package sysuserrole

import (
	"backend/internal/dao"
	"backend/internal/service"
	"context"
)

func init() {
	service.RegisterSysUserRole(new(sSysUserRole))
}

type sSysUserRole struct {
}

// 获得角色绑定的用户数量
func (s *sSysUserRole) GetRoleUserLen(ctx context.Context, roleId int64) (count int, err error) {
	count, err = dao.TenantCtx(dao.SysUserRole, ctx).Where(dao.SysUserRole.Columns().RoleId+" = ?", roleId).Count()
	return
}
