package formimporthistory

import (
	"backend/api/v1/form"
	"backend/internal/consts"
	"backend/internal/dao"
	model "backend/internal/model/common"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/dop251/goja"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormExportHistory(new(sFormExportHistory))
}

type sFormExportHistory struct {
}

// 获取导出数据文件列表
func (s *sFormExportHistory) GetFormExportHistoryList(ctx context.Context, req *form.FormExportHistoryListReq) (res *form.FormExportHistoryListRes, err error) {
	res = new(form.FormExportHistoryListRes)
	m := dao.TenantCtx(dao.FormExportHistory, ctx)
	m = m.Where(dao.FormExportHistory.Columns().FormExportTemplateId, req.FormExportTemplateId)
	m = m.Where(dao.FormExportHistory.Columns().CreatedBy, library.GetUserId(ctx))

	order := "id desc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取导出数据文件历史总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取导出数据文件历史数据失败")
	})
	return
}

// 新的数据导出
func (s *sFormExportHistory) New(ctx context.Context, req *form.FormExportHistoryNewReq) (res *form.FormExportHistoryNewRes, err error) {

	res = new(form.FormExportHistoryNewRes)
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, err := service.FormExportTemplate().GetInfoByID(ctx, gconv.Uint64(req.FormExportTemplateId), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(templateInfo, "表单模板Id不正确")

		// 验证权限
		library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, templateInfo.FormTemplateId, enum.SysPermission_Export), enum.SysPermission_Export.Error())
		unlimitedSearch := service.SysUser().CheckFormPermission(ctx, templateInfo.FormTemplateId, enum.SysPermission_UnlimitedSearch)

		FormExportHistory := new(do.FormExportHistory)

		FormExportHistory.CreatedBy = library.GetUserId(ctx)
		FormExportHistory.FormExportTemplateId = req.FormExportTemplateId
		FormExportHistory.FormTemplateId = templateInfo.FormTemplateId

		filterRules := &dto.FilterRules{
			Filter:          req.Filter,
			RuleFilter:      req.RuleFilter,
			UnlimitedSearch: unlimitedSearch,
		}

		filterRulesJson, err := json.Marshal(filterRules)
		library.ErrIsNil(ctx, err)
		FormExportHistory.FilterRules = string(filterRulesJson)
		FormExportHistory.Status = gconv.Int(enum.ExportStatus_Pending)
		newid, err := dao.TenantCtx(dao.FormExportHistory, ctx).Data(FormExportHistory).InsertAndGetId()
		library.ErrIsNil(ctx, err, "插入导出数据文件历史失败")
		FormExportHistory.Id = newid
		res.Id = newid

		// 发送消息，通知有新的导出数据任务
		queue.QueueManager().Publish(consts.StreamSubjectExcelExportCreated, FormExportHistory)
	})
	return

}

// 触发导出数据文件
func (s *sFormExportHistory) TriggerExportFile(ctx context.Context, id int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		historyInfo, err := s.GetInfoByID(ctx, uint64(id), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(historyInfo, "导出数据文件不存在")

		// 更新状态为进行中
		err = s.UpdateStatus(ctx, id, enum.ExportStatus_InProgress)
		library.ErrIsNil(ctx, err, "更新导出数据文件状态失败")

		go func() {
			s.ProcessExportFile(ctx, historyInfo)
		}()
	})
	return
}

// 处理导出数据任务
func (s *sFormExportHistory) ProcessExportFile(ctx context.Context, historyInfo *entity.FormExportHistory) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得导出模板信息
		exportTemplteInfo, err := service.FormExportTemplate().GetInfoByID(ctx, uint64(historyInfo.FormExportTemplateId), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(exportTemplteInfo, "找不到对应的导出模板信息")

		// 获得表单模板信息
		formTemplateInfo, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, exportTemplteInfo.FormTemplateId)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(formTemplateInfo, "找不到对应的表单模板信息")

		// 获取导出路径
		exportPath := g.Cfg().MustGet(ctx, "export.path").String()
		exportMappath := g.Cfg().MustGet(ctx, "export.mappath").String()

		// 根据过滤条件获取数据
		var filterRules dto.FilterRules
		err = json.Unmarshal([]byte(lo.Ternary(historyInfo.FilterRules == "", "{}", historyInfo.FilterRules)), &filterRules)
		library.ErrIsNil(ctx, err, "解析过滤规则失败")

		loginUser, err := service.SysUser().MockGetUserClaims(ctx, historyInfo.CreatedBy)
		library.ErrIsNil(ctx, err)

		showInvalidInt := 0
		showInvalid, showInvalidOk := filterRules.Filter["showInvalid"]
		if showInvalidOk {
			showInvalidInt = gconv.Int(showInvalid)
			delete(filterRules.Filter, "showInvalid")
		}

		req := &form.FormDataListReq{
			CommonFormDataReq: form.CommonFormDataReq{
				FormId:      exportTemplteInfo.FormTemplateId,
				Filter:      filterRules.Filter,
				RuleFilter:  filterRules.RuleFilter,
				ShowInvalid: showInvalidInt,
			},
			PageReq: model.PageReq{PageNum: 1, PageSize: -1},
		}

		// 获得字段映射设置
		var fieldMapping []*dto.ColumnMapRule
		err = json.Unmarshal([]byte(exportTemplteInfo.FieldMapping), &fieldMapping)
		library.ErrIsNil(ctx, err)

		// 判断字段是否有权限导出
		permissions, err := service.SysUser().ComputeRolesPermissions(ctx, loginUser.WithRoles)
		library.ErrIsNil(ctx, err)
		if len(permissions) > 0 {
			formPermissions, has := lo.Find(permissions, func(v *dto.SysPermissions) bool {
				return v.FormId == exportTemplteInfo.FormTemplateId
			})
			if !has {
				return
			}

			if len(formPermissions.FieldPermission) > 0 {
				fieldMapping = s.handleFieldPermissions(ctx, fieldMapping, formPermissions)
			}
		}

		// 获取数据列表
		formDatasRes, err := service.FormData().GetList(ctx, req, filterRules.UnlimitedSearch, loginUser)
		library.ErrIsNil(ctx, err, "获取数据列表失败")

		// 处理字段分组
		dataList := s.handleExportGroupField(ctx, formDatasRes.List, formColumns, fieldMapping)
		// 根据字段映射设置，修改数据
		vm, err := service.Javascript().GetVM(map[string]interface{}{})
		library.ErrIsNil(ctx, err)
		dataList, err = s.FormatDataListByFieldMapping(ctx, vm, formTemplateInfo.FormTableName, 0, formColumns, dataList, fieldMapping)
		library.ErrIsNil(ctx, err)

		// 根据字段映射准备导出列的配置信息
		exportColumns, err := s.BuildMapColumns(ctx, fieldMapping)
		library.ErrIsNil(ctx, err)

		// 调用导出工具生成Excel文件
		excelTitle := fmt.Sprintf("%s_%s", exportTemplteInfo.Name, historyInfo.CreatedAt.Time.Format("2006-01-02"))
		filePath, err := library.MapSliceToExcelFile(ctx, dataList, exportColumns, true, excelTitle, exportPath)
		library.ErrIsNil(ctx, err, "导出Excel文件失败")

		// 将filePath中开头的exportPath替换为exportMappath
		filePath = filePath[len(exportPath):]
		filePath = exportMappath + filePath

		// 更新导出结果
		historyInfo.Status = gconv.Int(enum.ExportStatus_Completed)
		resultJson, _ := json.Marshal([]string{fmt.Sprintf("导出成功，共%d条数据", gconv.Int64(formDatasRes.Total))})
		historyInfo.Result = string(resultJson)
		historyInfo.ExportFile = filePath
		historyInfo.FinishdAt = gtime.Now()
		historyInfo.TotalCount = gconv.Int64(formDatasRes.Total)
		queue.QueueManager().Publish(consts.StreamSubjectExcelExportProgress, historyInfo)
	})

	// 处理失败，更新状态为失败
	if err != nil {
		historyInfo.Status = gconv.Int(enum.ExportStatus_Failed)
		resultJson, _ := json.Marshal([]string{err.Error()})
		historyInfo.Result = string(resultJson)
		queue.QueueManager().Publish(consts.StreamSubjectExcelExportProgress, historyInfo)
	}
	return
}

// 处理字段分组
func (s *sFormExportHistory) handleExportGroupField(ctx context.Context, dataList []map[string]interface{}, formColumns []*dto.FormColumn, fieldMapping []*dto.ColumnMapRule) []map[string]interface{} {
	if len(fieldMapping) == 0 || len(dataList) == 0 {
		return dataList
	}

	// 提取分组字段
	groupFields := lo.Map(lo.Filter(fieldMapping, func(v *dto.ColumnMapRule, _ int) bool {
		return v.IsGroup
	}), func(v *dto.ColumnMapRule, _ int) string {
		return gconv.String(v.Source)
	})

	// 提取字段分组
	fieldGroupMap := lo.Filter(fieldMapping, func(v *dto.ColumnMapRule, _ int) bool {
		if v.Type == 3 {
			v.Source = "group_" + library.RandSeq(16)
			return true
		}
		return false
	})

	if len(groupFields) == 0 {
		// 没有分组字段，返回原数据
		return dataList
	}

	// 使用 map 进行分组
	groupMap := make(map[string][]map[string]interface{})
	for _, record := range dataList {
		var keyParts []string
		for _, field := range groupFields {
			if val, exists := record[field]; exists {
				column, has := lo.Find(formColumns, func(v *dto.FormColumn) bool {
					return v.Id == field
				})
				if has {
					formatValue, err := column.FormatValue(ctx, val)
					if err == nil {
						val = formatValue
					}
				}
				keyParts = append(keyParts, fmt.Sprintf("%v", val))
			} else {
				keyParts = append(keyParts, "")
			}
		}
		groupKey := strings.Join(keyParts, "|")

		groupMap[groupKey] = append(groupMap[groupKey], record)
	}
	// 构建分组后的结果
	var groupedData []map[string]interface{}
	for _, records := range groupMap {
		groupedRecord := make(map[string]interface{})

		// 合并非分组字段
		for recordIndex, record := range records {
			for field, value := range record {
				// 跳过分组字段
				if lo.Contains(groupFields, field) {
					if recordIndex == 0 {
						groupedRecord[field] = value
					}
					continue
				}

				// 初始化切片
				if _, exists := groupedRecord[field]; !exists {
					groupedRecord[field] = []interface{}{}
				}
				// 追加值到切片
				groupedRecord[field] = append(groupedRecord[field].([]interface{}), value)
			}
		}

		for _, fieldGroup := range fieldGroupMap {
			groupedRecord[gconv.String(fieldGroup.Source)] = records
		}
		groupedData = append(groupedData, groupedRecord)
	}

	return groupedData
}
func (s *sFormExportHistory) handleFieldPermissions(ctx context.Context, fieldMapping []*dto.ColumnMapRule, formPermissions *dto.SysPermissions) []*dto.ColumnMapRule {
	var handleFieldMapping []*dto.ColumnMapRule
	for _, fieldMapping := range fieldMapping {
		// 字段分组需要对子字段进行处理
		if fieldMapping.Type == 3 {
			subRulesPtr := lo.Map(fieldMapping.SubRules, func(v dto.ColumnMapRule, _ int) *dto.ColumnMapRule {
				return &v
			})
			groupFieldMapping := s.handleFieldPermissions(ctx, subRulesPtr, formPermissions)
			if len(groupFieldMapping) <= 0 {
				continue
			}
			fieldMapping.SubRules = lo.Map(groupFieldMapping, func(v *dto.ColumnMapRule, _ int) dto.ColumnMapRule {
				return *v
			})
		} else {
			fieldPermission, has := lo.Find(formPermissions.FieldPermission, func(v dto.FieldPermission) bool {
				return v.Field == fieldMapping.Source
			})
			if !has || !fieldPermission.HasPermission {
				continue
			}
			if fieldMapping.SubRules != nil && len(fieldMapping.SubRules) > 0 {
				var handleSubFieldMapping []dto.ColumnMapRule
				for _, subFieldMapping := range fieldMapping.SubRules {
					subFieldPermission, has := lo.Find(fieldPermission.Children, func(v dto.FieldPermission) bool {
						return v.Field == subFieldMapping.Source
					})
					if has && subFieldPermission.HasPermission {
						handleSubFieldMapping = append(handleSubFieldMapping, subFieldMapping)
					}
				}
				fieldMapping.SubRules = handleSubFieldMapping
			}
		}
		handleFieldMapping = append(handleFieldMapping, fieldMapping)
	}
	return handleFieldMapping
}

func (s *sFormExportHistory) BuildMapColumns(ctx context.Context, fieldMapping []*dto.ColumnMapRule) (exportColumns []library.ColumnConfig, err error) {
	exportColumns = make([]library.ColumnConfig, 0)
	for _, rule := range fieldMapping {
		column := library.ColumnConfig{}
		column.ColumnName = rule.Target
		column.MapKey = gconv.String(rule.Source)
		if !g.IsEmpty(rule.FuncCode) && rule.FuncCode.CodeName != "" {
			column.MapKey = rule.FuncCode.CodeName
		}
		column.Width = float64(rule.Width)
		if rule.Type == 1 || rule.Type == 3 {
			subColumns, err := s.BuildMapColumns(ctx, lo.Map(rule.SubRules, func(v dto.ColumnMapRule, k int) *dto.ColumnMapRule {
				return &v
			}))
			if err != nil {
				return nil, err
			}
			column.SubColumns = subColumns
		}
		exportColumns = append(exportColumns, column)
	}
	return
}

func (s *sFormExportHistory) FormatDataListByFieldMapping(ctx context.Context, vm *goja.Runtime, formTableName string, formTableType int, formColumns []*dto.FormColumn, dataList []map[string]interface{}, fieldMapping []*dto.ColumnMapRule) (formatDataList []map[string]interface{}, err error) {
	formatDataList = make([]map[string]interface{}, 0)
	for _, data := range dataList {
		formatData := make(map[string]interface{})
		vm.Set("__currentRecord", data)
		for _, rule := range fieldMapping {
			if rule.Type == 0 {
				sourceValue := interface{}("")
				source := gconv.String(rule.Source)
				if _, ok := data[source]; ok {
					sourceValue = data[source]
				}
				if !g.IsEmpty(rule.FuncCode) && rule.FuncCode.CodeContent != "" {
					if rule.FuncCode.CodeName == "" {
						rule.FuncCode.CodeName = library.RandSeq(16)
					}
					formatValue, err := service.Javascript().FormatMappedValue(ctx, vm, formTableName, 0, formColumns, sourceValue, rule.FuncCode)
					if err != nil {
						return nil, err
					}
					formatData[rule.FuncCode.CodeName] = formatValue
				}
				// else {
				formatData[source] = sourceValue
				// }
			} else if rule.Type == 1 || rule.Type == 3 {
				source := gconv.String(rule.Source)
				subData := make([]map[string]interface{}, 0)
				if rule.Type == 1 {
					if subDataStr, ok := data[source].(*g.Var); ok {
						json.Unmarshal([]byte(lo.Ternary(subDataStr.String() == "", "[]", subDataStr.String())), &subData)
					}
				} else if rule.Type == 3 {
					if subDataSlice, ok := data[source].([]map[string]interface{}); ok {
						subData = subDataSlice
					}
				}
				_subFieldMapping := lo.Map(rule.SubRules, func(v dto.ColumnMapRule, k int) *dto.ColumnMapRule {
					return &v
				})

				formatSubData, err := s.FormatDataListByFieldMapping(ctx, vm, formTableName, 0, formColumns, subData, _subFieldMapping)
				if err != nil {
					return nil, err
				}
				newSubRules := lo.Map(_subFieldMapping, func(v *dto.ColumnMapRule, k int) dto.ColumnMapRule {
					return *v
				})
				rule.SubRules = append([]dto.ColumnMapRule{}, newSubRules...)

				formatData[source] = formatSubData
			}
		}
		formatDataList = append(formatDataList, formatData)
	}

	return
}

// 更新导出数据文件的方法
func (s *sFormExportHistory) Update(ctx context.Context, historyInfo *entity.FormExportHistory) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取导出数据文件的历史记录
		oldInfo, err := s.GetInfoByID(ctx, uint64(historyInfo.Id), false)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(oldInfo, "导出数据文件不存在")

		// 更新历史记录中的数据
		_, err = dao.TenantCtx(dao.FormExportHistory, ctx).WherePri(historyInfo.Id).Update(historyInfo)
		library.ErrIsNil(ctx, err, "更新导出数据文件历史失败")
	})
	return
}

// 修改导出数据文件状态
func (s *sFormExportHistory) UpdateStatus(ctx context.Context, id int64, status enum.ExportStatus) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.FormExportHistory, ctx).WherePri(id).Update(&do.FormExportHistory{Status: status})
		library.ErrIsNil(ctx, err, "更新导出数据文件状态失败")
	})
	return
}

// 删除导出数据文件
func (s *sFormExportHistory) Delete(ctx context.Context, id uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		historyInfo, err := s.GetInfoByID(ctx, id, false)
		library.ErrIsNil(ctx, err)
		if historyInfo == nil {
			library.Fail(ctx, "历史纪录不存在")
			return
		}
		_, err = dao.TenantCtx(dao.FormExportHistory, ctx).WherePri(id).Delete()
		library.ErrIsNil(ctx, err, "删除导出数据文件历史失败")

	})
	return
}

// 获得详细信息
func (s *sFormExportHistory) GetInfoByID(ctx context.Context, automationId uint64, with bool) (FormExportHistory *entity.FormExportHistory, err error) {
	m := dao.TenantCtx(dao.FormExportHistory, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&FormExportHistory)
	return
}
