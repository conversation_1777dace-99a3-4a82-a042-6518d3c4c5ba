package sysconfig

import (
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"context"

	"github.com/gogf/gf/util/gconv"
	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	service.RegisterSysConfig(new(sSysConfig))
}

type sSysConfig struct {
}

// 获得所有的配置信息
func (s *sSysConfig) GetAllConfig(ctx context.Context) (list []*entity.SysConfig, err error) {
	err = dao.TenantCtx(dao.SysConfig, ctx).Scan(&list)
	return
}

func (s *sSysConfig) GetConfig(ctx context.Context, configKey string) (config *entity.SysConfig, err error) {
	err = dao.TenantCtx(dao.SysConfig, ctx).Where(dao.SysConfig.Columns().ConfigKey+" = ?", configKey).Scan(&config)
	return
}

// 获得一个配置
func (s *sSysConfig) GetValue(ctx context.Context, configKey string) (configValue string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		value, err := service.Cache().GetOrSetFunc(
			ctx,
			library.GetTenantCacheKey(ctx, configKey),
			func(ctx context.Context) (value interface{}, err error) {
				config, err := s.GetConfig(ctx, configKey)
				library.ErrIsNil(ctx, err, "获取配置失败")
				if config != nil {
					value = config.ConfigValue
				}
				return
			}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		if value == nil || g.IsEmpty(value) {
			configValue = ""
		} else {
			configValue = gconv.String(value)
		}
	})
	return
}

// 设置一个配置
func (s *sSysConfig) SetValue(ctx context.Context, configKey string, configValue string, version int64, removeCaches ...bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		config, err := s.GetConfig(ctx, configKey)
		library.ErrIsNil(ctx, err)

		if config == nil {
			newConfig := &entity.SysConfig{
				ConfigKey:   configKey,
				ConfigValue: configValue,
				Version:     version,
			}
			_, err = dao.TenantCtx(dao.SysConfig, ctx).Insert(newConfig)
		} else {
			updateConfig := &do.SysConfig{
				ConfigValue: configValue,
				Version:     version,
			}
			_, err = dao.TenantCtx(dao.SysConfig, ctx).WherePri(config.Id).Where(dao.SysConfig.Columns().Version+" <= ?", version).Update(updateConfig)
		}
		library.ErrIsNil(ctx, err)
		// 删除缓存
		removeCache := false
		if len(removeCaches) > 0 {
			removeCache = removeCaches[0]
		}
		if removeCache {
			service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, configKey))
		}
	})
	return

}

// 删除一个配置
func (s *sSysConfig) Delete(ctx context.Context, configKey string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.SysConfig, ctx).Where(dao.SysConfig.Columns().ConfigKey+" = ?", configKey).Delete()
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, configKey))
	})
	return
}
