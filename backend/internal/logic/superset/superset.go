package superset

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
)

const (
	CacheKeyPrefix = "superset:v21:"
	// SupersetAccessTokenCacheKey 缓存Superset访问令牌的键名
	SupersetAccessTokenCacheKey = CacheKeyPrefix + "access_token"
	// SupersetAccessTokenCacheTTL 缓存Superset访问令牌的过期时间（5分钟）
	SupersetAccessTokenCacheTTL = 3 * 60 * time.Second // 5分钟，单位：秒
	// SupersetCSRFTokenCacheKey 缓存Superset CSRF令牌的键名
	SupersetCSRFTokenCacheKey = CacheKeyPrefix + "csrf_token"
	// SupersetCSRFTokenCacheTTL 缓存Superset CSRF令牌的过期时间（5分钟）
	SupersetCSRFTokenCacheTTL = 3 * 60 * time.Second // 5分钟，单位：秒
	// SupersetSessionCookieKey 缓存Superset会话信息的键名
	SupersetSessionCookieKey = CacheKeyPrefix + "session_cookie"
	// SupersetSessionCookieTTL 缓存Superset会话信息的过期时间（5分钟）
	SupersetSessionCookieTTL = 4 * 60 * time.Second // 5分钟，单位：秒
	// SupersetGuestTokenCacheKeyPrefix 缓存Superset Guest Token的键名前缀
	SupersetGuestTokenCacheKeyPrefix = CacheKeyPrefix + "guest_token:"
	// SupersetGuestTokenCacheTTL 缓存Superset Guest Token的过期时间（50分钟）
	SupersetGuestTokenCacheTTL = 4 * 60 * time.Second // 4 分钟，单位：秒
)

type sSuperset struct {
	// 配置信息
	apiURL         string   // Superset API URL
	uiURL          string   // Superset UI URL
	username       string   // Superset 用户名
	password       string   // Superset 密码
	allowedDomains []string // 允许的域名列表
}

func (s *sSuperset) GetAPIURL(ctx context.Context) string {
	if g.IsEmpty(s.apiURL) {
		return "http://superset:8088/api/v1"
	}
	return s.apiURL
}

// loginToSuperset 登录到Superset并获取访问令牌
func (s *sSuperset) loginToSuperset(ctx context.Context) (accessToken string, err error) {

	domain := library.GetDomain(ctx)
	g.Log().Info(ctx, "domain:", domain)

	// 创建HTTP客户端
	client := g.Client()
	// 设置内容类型为JSON
	client.SetContentType("application/json")
	// 启用Cookie跟踪
	// 注意：goframe的Client默认已经启用Cookie

	// 构建登录请求数据
	loginData := g.Map{
		"username": s.username,
		"password": s.password,
		"provider": "db", // 默认使用数据库认证方式，可根据实际情况调整
		"refresh":  false,
	}

	// 打印登录请求信息用于调试
	g.Log().Info(ctx, "登录请求URL:", fmt.Sprintf("%s/security/login", s.GetAPIURL(ctx)))
	g.Log().Info(ctx, "登录请求体:", loginData)

	// 发送登录请求
	loginResponse, err := client.Post(ctx, fmt.Sprintf("%s/security/login", s.GetAPIURL(ctx)), loginData)
	if err != nil {
		return "", fmt.Errorf("登录Superset失败: %v", err)
	}
	defer loginResponse.Close()

	// 检查登录响应状态码
	if loginResponse.StatusCode != 200 {
		return "", fmt.Errorf("Superset登录失败，状态码: %d, 响应内容: %s",
			loginResponse.StatusCode, loginResponse.ReadAllString())
	}

	// 保存会话 Cookie
	cookieMap := loginResponse.GetCookieMap()
	g.Log().Info(ctx, "登录响应的cookieMap:", cookieMap)
	if len(cookieMap) > 0 {
		// 将 Cookie 存入缓存
		cookieJson, _ := json.Marshal(cookieMap)
		err = service.Cache().Set(ctx, SupersetSessionCookieKey, string(cookieJson), SupersetSessionCookieTTL)
		if err != nil {
			// 缓存失败不影响业务逻辑，只记录日志
			g.Log().Warning(ctx, "Failed to cache Superset session cookie:", err)
		}
	}

	// 解析登录响应，获取认证token
	var loginResult struct {
		AccessToken string `json:"access_token"`
	}
	loginRespContent := loginResponse.ReadAllString()
	err = json.Unmarshal([]byte(loginRespContent), &loginResult)
	if err != nil {
		return "", fmt.Errorf("解析Superset登录响应失败: %v, 响应内容: %s", err, loginRespContent)
	}

	return loginResult.AccessToken, nil
}

// requestGuestToken 使用访问令牌请求Superset的guest token
func (s *sSuperset) requestGuestToken(ctx context.Context, _ *gclient.Client, accessToken string, resourceID, resourceType string) (token string, err error) {
	// 获取CSRF令牌
	csrfToken, err := s.getCSRFToken(ctx, accessToken)
	if err != nil {
		return "", fmt.Errorf("获取CSRF令牌失败: %v", err)
	}

	// 创建新的HTTP客户端，确保使用最新的Cookie和令牌
	client := g.Client()
	// 设置内容类型为JSON
	client.SetContentType("application/json")
	// 设置认证头
	client.SetHeader("X-CSRFToken", csrfToken) // 注意：这里使用X-CSRFToken而不是X-Csrftoken
	client.SetHeader("Authorization", "Bearer "+accessToken)

	g.Log().Info(ctx, "CSRFToken:", csrfToken)
	g.Log().Info(ctx, "accessToken:", accessToken)

	// 获取并设置会话 Cookie
	cookieMap, _ := s.getSessionCookies(ctx)
	g.Log().Info(ctx, "使用的cookieMap:", cookieMap)
	if len(cookieMap) > 0 {
		client.SetCookieMap(cookieMap)
	}

	// 构建请求体
	reqData := g.Map{
		"resources": []g.Map{},
		"rls":       []g.Map{},
		"user": g.Map{
			"username":   "guest",
			"first_name": "Guest",
			"last_name":  "User",
		},
		"type": "guest",
	}

	// 如果提供了资源ID和仪表盘类型，添加到resources中
	if resourceID != "" && resourceType != "" {
		reqData["resources"] = []g.Map{
			{
				"id":   resourceID,
				"type": resourceType,
			},
		}
	}

	// 打印完整的请求信息用于调试
	g.Log().Info(ctx, "Guest Token请求URL:", fmt.Sprintf("%s/security/guest_token/", s.GetAPIURL(ctx)))
	g.Log().Info(ctx, "Guest Token请求体:", reqData)

	// 发送POST请求到Superset API获取guest token
	response, err := client.Post(ctx, fmt.Sprintf("%s/security/guest_token/", s.GetAPIURL(ctx)), reqData)
	if err != nil {
		return "", fmt.Errorf("调用Superset guest_token API失败: %v", err)
	}
	defer response.Close()

	// 检查响应状态码
	if response.StatusCode != 200 {
		return "", fmt.Errorf("Superset guest_token API返回错误状态码: %d, 响应内容: %s",
			response.StatusCode, response.ReadAllString())
	}

	// 解析响应
	var result struct {
		Token string `json:"token"`
	}
	respContent := response.ReadAllString()
	err = json.Unmarshal([]byte(respContent), &result)
	if err != nil {
		return "", fmt.Errorf("解析Superset guest_token API响应失败: %v, 响应内容: %s", err, respContent)
	}

	return result.Token, nil
}

// getSessionCookies 从缓存中获取会话 Cookie
func (s *sSuperset) getSessionCookies(ctx context.Context) (map[string]string, error) {
	// 尝试从缓存中获取会话 Cookie
	cachedCookie, err := service.Cache().Get(ctx, SupersetSessionCookieKey)
	if err != nil || cachedCookie.IsEmpty() {
		// 如果缓存中不存在有效的 Cookie，返回空映射
		return map[string]string{}, nil
	}

	// 解析 Cookie JSON
	var cookieMap map[string]string
	err = json.Unmarshal([]byte(cachedCookie.String()), &cookieMap)
	if err != nil {
		// 解析失败，返回空映射
		return map[string]string{}, nil
	}

	return cookieMap, nil
}

// getCSRFToken 获取Superset的CSRF令牌
func (s *sSuperset) getCSRFToken(ctx context.Context, accessToken string) (csrfToken string, err error) {
	// 尝试从缓存中获取CSRF令牌
	cachedToken, err := service.Cache().Get(ctx, SupersetCSRFTokenCacheKey)
	if err == nil && !cachedToken.IsEmpty() {
		// 如果缓存中存在有效的令牌，直接返回
		return cachedToken.String(), nil
	}

	// 创建HTTP客户端
	client := g.Client()
	client.SetContentType("application/json")
	client.SetHeader("Authorization", "Bearer "+accessToken)

	// 获取并设置会话 Cookie
	cookieMap, _ := s.getSessionCookies(ctx)
	if len(cookieMap) > 0 {
		client.SetCookieMap(cookieMap)
	}

	// 发送GET请求到Superset API获取CSRF令牌
	response, err := client.Get(ctx, fmt.Sprintf("%s/security/csrf_token/", s.GetAPIURL(ctx)))
	if err != nil {
		return "", fmt.Errorf("获取Superset CSRF令牌失败: %v", err)
	}
	defer response.Close()

	// 检查响应状态码
	if response.StatusCode != 200 {
		return "", fmt.Errorf("获取Superset CSRF令牌失败，状态码: %d, 响应内容: %s",
			response.StatusCode, response.ReadAllString())
	}

	// 保存会话 Cookie
	cookieMap = response.GetCookieMap()
	g.Log().Info(ctx, "CSRF请求的cookieMap:", cookieMap)
	if len(cookieMap) > 0 {
		// 将 Cookie 存入缓存
		cookieJson, _ := json.Marshal(cookieMap)
		err = service.Cache().Set(ctx, SupersetSessionCookieKey, string(cookieJson), SupersetSessionCookieTTL)
		if err != nil {
			// 缓存失败不影响业务逻辑，只记录日志
			g.Log().Warning(ctx, "Failed to cache Superset session cookie from CSRF request:", err)
		}
	}

	// 解析响应
	respContent := response.ReadAllString()
	var result struct {
		Result string `json:"result"`
	}

	err = json.Unmarshal([]byte(respContent), &result)
	if err != nil {
		return "", fmt.Errorf("解析Superset CSRF令牌响应失败: %v, 响应内容: %s", err, respContent)
	}

	// 将新获取的令牌存入缓存，设置5分钟过期时间
	err = service.Cache().Set(ctx, SupersetCSRFTokenCacheKey, result.Result, SupersetCSRFTokenCacheTTL)
	if err != nil {
		// 缓存失败不影响业务逻辑，只记录日志
		g.Log().Warning(ctx, "Failed to cache Superset CSRF token:", err)
	}

	return result.Result, nil
}

// GetAccessToken 获取缓存的访问令牌或重新登录获取新令牌
func (s *sSuperset) GetAccessToken(ctx context.Context) (accessToken string, err error) {
	// 尝试从缓存中获取访问令牌
	cachedToken, err := service.Cache().Get(ctx, SupersetAccessTokenCacheKey)
	if err == nil && !cachedToken.IsEmpty() {
		// 如果缓存中存在有效的令牌，直接返回
		return cachedToken.String(), nil
	}

	// 缓存中不存在有效的令牌，重新登录获取
	accessToken, err = s.loginToSuperset(ctx)
	if err != nil {
		return "", err
	}

	// 将新获取的令牌存入缓存，设置5分钟过期时间
	err = service.Cache().Set(ctx, SupersetAccessTokenCacheKey, accessToken, SupersetAccessTokenCacheTTL)
	if err != nil {
		// 缓存失败不影响业务逻辑，只记录日志
		g.Log().Warning(ctx, "Failed to cache Superset access token:", err)
	}

	return accessToken, nil
}

// GetGuestToken 调用superset-api，获得guest_token
func (s *sSuperset) GetGuestToken(ctx context.Context, resourceID string, resourceType string) (token string, url string, expiresAt int64, err error) {
	// 创建HTTP客户端
	client := g.Client()

	// 第一步：使用缓存获取认证token
	accessToken, err := s.GetAccessToken(ctx)
	if err != nil {
		return "", "", 0, err
	}

	// 第二步：使用认证token获取guest token
	token, err = s.requestGuestToken(ctx, client, accessToken, resourceID, resourceType)
	if err != nil {
		return "", "", 0, err
	}

	// 设置过期时间为1小时后
	expiresAt = time.Now().Add(1 * time.Hour).Unix()

	return token, s.uiURL, expiresAt, nil
}

// GetDashboardList 获取仪表盘列表
func (s *sSuperset) GetDashboardList(ctx context.Context, req service.DashboardListRequest) (*service.DashboardListResponse, error) {
	// 获取访问令牌
	accessToken, err := s.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 获取CSRF令牌
	csrfToken, err := s.getCSRFToken(ctx, accessToken)
	if err != nil {
		return nil, fmt.Errorf("获取CSRF令牌失败: %v", err)
	}

	// 创建HTTP客户端
	client := g.Client()
	client.SetContentType("application/json")
	client.SetHeader("Authorization", "Bearer "+accessToken)
	client.SetHeader("X-CSRFToken", csrfToken) // 注意大小写

	// 获取并设置会话 Cookie
	cookieMap, _ := s.getSessionCookies(ctx)
	if len(cookieMap) > 0 {
		client.SetCookieMap(cookieMap)
	}

	// 构建请求URL
	url := fmt.Sprintf("%s/dashboard/", s.GetAPIURL(ctx))

	// 构建请求体，使用q参数包装查询条件
	qParams := g.Map{
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	// 添加排序列和排序方向（如果提供）
	if req.OrderColumn != "" {
		qParams["order_column"] = req.OrderColumn
	}
	if req.OrderDirection != "" {
		qParams["order_direction"] = req.OrderDirection
	}

	// 添加选择列（如果提供）
	if len(req.SelectColumns) > 0 {
		qParams["select_columns"] = req.SelectColumns
	} else {
		// 默认只选择需要的字段
		qParams["select_columns"] = []string{"id", "dashboard_title", "slug", "uuid"}
	}

	// 包装到q参数中
	reqData := g.Map{
		"q": qParams,
	}

	// 打印请求信息用于调试
	g.Log().Info(ctx, "Dashboard列表请求URL:", url)
	g.Log().Info(ctx, "Dashboard列表请求体:", reqData)

	// 发送GET请求到Superset API获取仪表盘列表
	response, err := client.Get(ctx, url, reqData)
	if err != nil {
		return nil, fmt.Errorf("调用Superset dashboard API失败: %v", err)
	}
	defer response.Close()

	// 检查响应状态码
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("Superset dashboard API返回错误状态码: %d, 响应内容: %s",
			response.StatusCode, response.ReadAllString())
	}

	// 解析响应
	respContent := response.ReadAllString()
	g.Log().Info(ctx, "Dashboard列表响应:", respContent)
	var supersetResp struct {
		Count  int                     `json:"count"`
		Result []service.DashboardInfo `json:"result"`
	}

	err = json.Unmarshal([]byte(respContent), &supersetResp)
	if err != nil {
		return nil, fmt.Errorf("解析Superset dashboard API响应失败: %v, 响应内容: %s", err, respContent)
	}

	// 构建响应
	responseData := &service.DashboardListResponse{
		Count:  supersetResp.Count,
		Result: supersetResp.Result,
	}

	return responseData, nil
}

// GetEmbeddedConfig 获取仪表盘的嵌入式配置
func (s *sSuperset) GetEmbeddedConfig(ctx context.Context, dashboardID string) (*service.EmbeddedConfig, error) {
	// 获取访问令牌
	accessToken, err := s.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 获取CSRF令牌
	csrfToken, err := s.getCSRFToken(ctx, accessToken)
	if err != nil {
		return nil, fmt.Errorf("获取CSRF令牌失败: %v", err)
	}

	// 创建HTTP客户端
	client := g.Client()
	client.SetContentType("application/json")
	client.SetHeader("Authorization", "Bearer "+accessToken)
	client.SetHeader("X-CSRFToken", csrfToken) // 注意大小写

	// 获取并设置会话 Cookie
	cookieMap, _ := s.getSessionCookies(ctx)
	if len(cookieMap) > 0 {
		client.SetCookieMap(cookieMap)
	}

	// 发送GET请求到Superset API获取嵌入式配置
	url := fmt.Sprintf("%s/dashboard/%s/embedded", s.GetAPIURL(ctx), dashboardID)
	response, err := client.Get(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("调用Superset embedded API失败: %v", err)
	}
	defer response.Close()

	// 检查响应状态码
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("Superset embedded API返回错误状态码: %d, 响应内容: %s",
			response.StatusCode, response.ReadAllString())
	}

	// 解析响应
	respContent := response.ReadAllString()
	g.Log().Info(ctx, "Dashboard嵌入式配置响应:", respContent)
	var result struct {
		Result service.EmbeddedConfig `json:"result"`
	}

	err = json.Unmarshal([]byte(respContent), &result)
	if err != nil {
		return nil, fmt.Errorf("解析Superset embedded API响应失败: %v, 响应内容: %s", err, respContent)
	}

	return &result.Result, nil
}

// SetEmbeddedConfig 设置仪表盘的嵌入式配置
func (s *sSuperset) SetEmbeddedConfig(ctx context.Context, dashboardID string) (*service.EmbeddedConfig, error) {
	// 获取访问令牌
	accessToken, err := s.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 获取CSRF令牌
	csrfToken, err := s.getCSRFToken(ctx, accessToken)
	if err != nil {
		return nil, fmt.Errorf("获取CSRF令牌失败: %v", err)
	}

	// 创建HTTP客户端
	client := g.Client()
	client.SetContentType("application/json")
	client.SetHeader("Authorization", "Bearer "+accessToken)
	client.SetHeader("X-CSRFToken", csrfToken) // 注意大小写

	// 获取并设置会话 Cookie
	cookieMap, _ := s.getSessionCookies(ctx)
	if len(cookieMap) > 0 {
		client.SetCookieMap(cookieMap)
	}

	apiHost := library.GetDomain(ctx)

	// 构建请求体
	reqData := g.Map{
		"allowed_domains": append([]string{apiHost}, s.allowedDomains...),
	}

	// 发送POST请求到Superset API设置嵌入式配置
	url := fmt.Sprintf("%s/dashboard/%s/embedded", s.GetAPIURL(ctx), dashboardID)
	response, err := client.Post(ctx, url, reqData)
	if err != nil {
		return nil, fmt.Errorf("调用Superset embedded API失败: %v", err)
	}
	defer response.Close()

	// 检查响应状态码
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("Superset embedded API返回错误状态码: %d, 响应内容: %s",
			response.StatusCode, response.ReadAllString())
	}

	// 解析响应
	respContent := response.ReadAllString()
	g.Log().Info(ctx, "Dashboard嵌入式配置响应:", respContent)
	var result struct {
		Result service.EmbeddedConfig `json:"result"`
	}

	err = json.Unmarshal([]byte(respContent), &result)
	if err != nil {
		return nil, fmt.Errorf("解析Superset embedded API响应失败: %v, 响应内容: %s", err, respContent)
	}

	return &result.Result, nil
}

// GetEmbeddedGuestToken 通过仪表盘ID获取嵌入的guest token
func (s *sSuperset) GetEmbeddedGuestToken(ctx context.Context, dashboardID string) (token string, uuid string, url string, expiresAt int64, err error) {
	// 生成缓存键，基于仪表盘ID和允许的域名
	// domainsStr := "*"
	// if len(allowedDomains) > 0 {
	// 	domainsStr = strings.Join(allowedDomains, ",")
	// }
	cacheKey := fmt.Sprintf("%s:%s", SupersetGuestTokenCacheKeyPrefix, dashboardID)

	// 尝试从缓存中获取guest token
	var cachedData struct {
		Token     string `json:"token"`
		URL       string `json:"url"`
		ExpiresAt int64  `json:"expires_at"`
		UUID      string `json:"uuid"`
	}

	cachedValue, err := service.Cache().Get(ctx, cacheKey)
	if err == nil && !cachedValue.IsEmpty() {
		// 如果缓存中存在有效的数据，解析并返回
		err = json.Unmarshal([]byte(cachedValue.String()), &cachedData)
		if err == nil {
			// 检查是否过期
			if cachedData.ExpiresAt > time.Now().Unix() {
				g.Log().Info(ctx, "从缓存中获取guest token成功，仪表盘ID:", dashboardID)
				return cachedData.Token, cachedData.UUID, cachedData.URL, cachedData.ExpiresAt, nil
			}
			// 如果过期，继续获取新的token
			g.Log().Info(ctx, "guest token已过期，重新获取，仪表盘ID:", dashboardID)
		}
	}

	// 缓存中不存在有效的数据，重新获取
	// 第一步：获取或创建嵌入式配置，获取UUID
	embeddedConfig, err := s.GetEmbeddedConfig(ctx, dashboardID)
	if err != nil {
		// 创建嵌入式配置
		embeddedConfig, err = s.SetEmbeddedConfig(ctx, dashboardID)
		if err != nil {
			return "", "", "", 0, fmt.Errorf("创建嵌入式配置失败: %v", err)
		}
	}

	// 第二步：使用UUID获取guest token
	token, url, expiresAt, err = s.GetGuestToken(ctx, embeddedConfig.UUID, "dashboard")
	if err != nil {
		return "", "", "", 0, fmt.Errorf("获取guest token失败: %v", err)
	}

	// 将获取到的数据存入缓存
	cachedData = struct {
		Token     string `json:"token"`
		URL       string `json:"url"`
		ExpiresAt int64  `json:"expires_at"`
		UUID      string `json:"uuid"`
	}{
		Token:     token,
		URL:       url,
		ExpiresAt: expiresAt,
		UUID:      embeddedConfig.UUID,
	}

	cachedJson, _ := json.Marshal(cachedData)
	// 注意：Cache().Set的过期时间单位是毫秒，需要将秒转换为毫秒
	err = service.Cache().Set(ctx, cacheKey, string(cachedJson), SupersetGuestTokenCacheTTL) // 50分钟
	if err != nil {
		// 缓存失败不影响业务逻辑，只记录日志
		g.Log().Warning(ctx, "Failed to cache Superset guest token:", err)
	}

	return token, embeddedConfig.UUID, url, expiresAt, nil
}

func init() {
	service.RegisterSuperset(New())
}

func New() *sSuperset {
	// 创建一个上下文用于获取配置
	ctx := context.Background()

	// 从配置中获取Superset相关配置
	return &sSuperset{
		apiURL:         g.Cfg().MustGet(ctx, "superset.api_url").String(),
		uiURL:          g.Cfg().MustGet(ctx, "superset.ui_url").String(),
		username:       g.Cfg().MustGet(ctx, "superset.username").String(),
		password:       g.Cfg().MustGet(ctx, "superset.password").String(),
		allowedDomains: g.Cfg().MustGet(ctx, "superset.allowed_domains").Strings(),
	}
}
