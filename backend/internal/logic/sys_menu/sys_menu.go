package SysMenu

import (
	"backend/api/v1/system"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterSysMenu(new(sSysMenu))
}

type sSysMenu struct {
}

func (s *sSysMenu) TGetList(ctx context.Context) (list []*with.SysMenu, err error) {
	// list = make([]*with.SysMenu, 0)
	err = dao.TenantCtx(dao.SysMenu, ctx).WithAll().Scan(&list)
	return
}

func (s *sSysMenu) GetBindFormIds(ctx context.Context) (ids []int64, err error) {
	list, err := s.GetListCache(ctx)
	if err != nil {
		return
	}
	if len(list) > 0 {
		list = lo.Filter(list, func(item *with.SysMenu, _ int) bool {
			if item.RouteType == 2 && item.MenuFormTemplateId > 0 {
				return true
			}
			return false
		})
	}
	ids = lo.Map(list, func(item *with.SysMenu, _ int) int64 {
		return item.MenuFormTemplateId
	})

	ids = lo.Uniq(ids)
	return
}

// 根据条件获取菜单列表
func (s *sSysMenu) GetList(ctx context.Context, req *system.MenuSearchReq) (list []*with.SysMenu, err error) {
	g.Log().Info(ctx, fmt.Sprintf("req:%+v", req))

	list, err = s.GetListCache(ctx)
	if err != nil {
		return
	}
	filters := make([]library.FilterFunc[*with.SysMenu], 0)

	filters = append(filters, func(item *with.SysMenu) bool {
		if req.ParentId > 0 {
			ids, err := s.GetMenuIds(ctx, req.ParentId)
			if err != nil {
				return false
			}
			if !library.Contains(ids, item.Id) {
				return false
			}
		}
		return true
	})

	filters = append(filters, func(item *with.SysMenu) bool {
		if req.RouteType > 0 && item.RouteType != req.RouteType {
			return false
		}
		return true
	})
	filters = append(filters, func(item *with.SysMenu) bool {
		if req.MenuType > 0 && item.MenuType != req.MenuType {
			return false
		}
		return true
	})
	filters = append(filters, func(item *with.SysMenu) bool {
		if req.Title != "" && !gstr.ContainsI(item.Title, req.Title) {
			return false
		}
		return true
	})
	filters = append(filters, func(item *with.SysMenu) bool {
		createAt := req.CreatedAt
		switch t := createAt.(type) {
		case []interface{}:
			// 时间区间判断
			if len(t) != 2 {
				return false
			}

			startTime, err := gtime.StrToTime(gconv.String(t[0]))
			if err != nil {
				return false
			}
			endTime, err := gtime.StrToTime(gconv.String(t[1]))
			if err != nil {
				return false
			}

			// 结束时间修改为每天的结束
			if endTime.Time.Format("15:04:05") == "00:00:00" {
				endTime = endTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			}
			// g.Log().Info(ctx, fmt.Sprintf("startTime:%+v, endTime:%+v", startTime, endTime))
			itemTime := item.CreatedAt.Time
			if !itemTime.After(startTime.Time) || !itemTime.Before(endTime.Time) {
				return false
			}
			return true
		}
		return true
	})

	list = library.FilterList(list, filters)

	return
}

// 获取所有菜单列表，菜单数据一般不多，可以从缓存中获取
func (s *sSysMenu) GetListCache(ctx context.Context) (list []*with.SysMenu, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cList, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyMenuList), func(ctx context.Context) (value interface{}, err error) {
			err = dao.TenantCtx(dao.SysMenu, ctx).WithAll().Scan(&list)
			if err != nil {
				return
			}
			// 根据 parentId 排序
			sort.Slice(list, func(i, j int) bool {
				// if list[i].ParentId != list[j].ParentId {
				// 	return list[i].ParentId > list[j].ParentId
				// }
				// return list[i].SortOrder < list[j].SortOrder
				return list[i].SortOrder < list[j].SortOrder
			})
			value = list
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		if cList != nil {
			err := gconv.Struct(cList, &list)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

// Save 菜单保存
func (s *sSysMenu) Save(ctx context.Context, req *system.MenuSaveReq) (err error) {
	if req.RouteType == consts.MenuRouteTypeForm && req.MenuFormTemplateId <= 0 {
		err = gerror.New("请选择表单模板")
		return
	}

	// 权限标识是否重复
	menus, err := s.GetListCache(ctx)
	if err != nil {
		return
	}
	if lo.CountBy(menus, func(item *with.SysMenu) bool {
		if item.Id != req.Id && item.Mark == req.Mark {
			return true
		}
		return false
	}) > 0 {
		err = gerror.New("权限标识重复")
		return
	}
	if req.RouteType == consts.MenuRouteTypeForm {
		// req.Route = fmt.Sprintf("/system/form-list?id=%d", req.MenuFormTemplateId)
		req.Route = "/system/form-list"
	}

	if req.Id <= 0 {
		err = s.Add(ctx, req)
	} else {
		var oldMenu *with.SysMenu
		oldMenu, err = s.GetInfoByID(ctx, int64(req.Id))
		if err != nil {
			return
		}
		if oldMenu == nil {
			err = fmt.Errorf("菜单不存在")
			return
		}
		err = s.Edit(ctx, req)
	}
	return
}

// Add 添加菜单
func (s *sSysMenu) Add(ctx context.Context, req *system.MenuSaveReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		code := library.GenerateID("M{20060102150406}{RanNum(8)}", 0)
		newMenu := do.SysMenu{
			Code:                 code,
			ParentId:             req.ParentId,
			MenuType:             req.MenuType,
			Title:                req.Title,
			Route:                req.Route,
			Mark:                 req.Mark,
			RouteType:            req.RouteType,
			Icon:                 req.Icon,
			ApiAssociation:       req.ApiAssociation,
			MenuFormTemplateId:   req.MenuFormTemplateId,
			MenuFormTemplateType: req.MenuFormTemplateType,
			Remark:               req.Remark,
			CreatedBy:            library.GetUserId(ctx),
			DashboardId:          req.DashboardId,
			DashboardIdMobile:    req.DashboardIdMobile,
		}
		maxSort, err := dao.TenantCtx(dao.SysMenu, ctx).Max(dao.SysMenu.Columns().SortOrder)
		library.ErrIsNil(ctx, err)
		newMenu.SortOrder = maxSort + 1
		_, err = dao.TenantCtx(dao.SysMenu, ctx).Insert(newMenu)
		library.ErrIsNil(ctx, err, "添加菜单失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyMenuList))
	})
	return
}

// 移动一个菜单的顺序
func (s *sSysMenu) MoveSort(ctx context.Context, id int64, direction string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取当前菜单信息
		menu, err := s.GetInfoByID(ctx, id)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(menu, "无效选中菜单")

		var targetMenu *with.SysMenu

		if direction == "up" {
			// 查找同一 parentId 下，sortOrder 比当前菜单小的第一个菜单
			err = dao.TenantCtx(dao.SysMenu, ctx).
				Where("parent_id", menu.ParentId).
				Where("sort_order < ?", menu.SortOrder).
				Order("sort_order DESC").
				Limit(1).
				Scan(&targetMenu)
		} else if direction == "down" {
			// 查找同一 parentId 下，sortOrder 比当前菜单大的第一个菜单
			err = dao.TenantCtx(dao.SysMenu, ctx).
				Where("parent_id", menu.ParentId).
				Where("sort_order > ?", menu.SortOrder).
				Order("sort_order ASC").
				Limit(1).
				Scan(&targetMenu)
		} else {
			library.Fail(ctx, "无效的方向")
		}

		library.ErrIsNil(ctx, err)
		library.ValueIsNil(targetMenu, lo.Ternary(direction == "up", "已经是第一个菜单", "已经是最后一个菜单"))

		// 交换顺序
		err = s.SwapSort(ctx, menu.Id, targetMenu.Id)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 交换两个菜单的顺序
func (s *sSysMenu) SwapSort(ctx context.Context, activeId int64, overId int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		activeRole, err := s.GetInfoByID(ctx, activeId)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(activeRole, "无效选中菜单")
		overRole, err := s.GetInfoByID(ctx, overId)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(overRole, "无效目标菜单")
		activeRole.SortOrder, overRole.SortOrder = overRole.SortOrder, activeRole.SortOrder

		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			_, err = dao.TenantCtx(dao.SysMenu, ctx).WherePri(activeRole.Id).Update(activeRole)
			if err != nil {
				return err
			}
			_, err = dao.TenantCtx(dao.SysMenu, ctx).WherePri(overRole.Id).Update(overRole)
			if err != nil {
				return err
			}
			return nil
		})
		library.ErrIsNil(ctx, err)
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyMenuList))
	})
	return
}

// Edit 菜单修改
func (s *sSysMenu) Edit(ctx context.Context, req *system.MenuSaveReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.SysMenu, ctx).WherePri(req.Id).Update(do.SysMenu{
			ParentId:             req.ParentId,
			MenuType:             req.MenuType,
			Title:                req.Title,
			Route:                req.Route,
			Mark:                 req.Mark,
			RouteType:            req.RouteType,
			Icon:                 req.Icon,
			ApiAssociation:       req.ApiAssociation,
			MenuFormTemplateId:   req.MenuFormTemplateId,
			MenuFormTemplateType: req.MenuFormTemplateType,
			Remark:               req.Remark,
			DashboardId:          req.DashboardId,
			DashboardIdMobile:    req.DashboardIdMobile,
		})
		library.ErrIsNil(ctx, err, "修改菜单失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyMenuList))
	})
	return
}

// Delete 删除菜单
func (s *sSysMenu) Delete(ctx context.Context, menuId int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []*with.SysMenu
		list, err = s.GetListCache(ctx)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(list, "无菜单数据")
		// 获得子菜单
		g.Log().Info(ctx, "menuId:"+gconv.String(menuId))
		subList := s.GetSubMenu(list, menuId)
		ids := make([]int64, 0)
		for _, menu := range subList {
			ids = append(ids, menu.Id)
		}
		ids = append(ids, menuId)

		_, err = dao.TenantCtx(dao.SysMenu, ctx).Where(dao.SysMenu.Columns().Id+" IN (?)", ids).Delete()
		library.ErrIsNil(ctx, err, "删除菜单失败")

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyMenuList))
	})
	return
}

// 递归获取所有菜单和子菜单的id
func (s *sSysMenu) GetMenuIds(ctx context.Context, menuId int64) (ids []int64, err error) {
	list, err := s.GetListCache(ctx)
	if err != nil {
		return
	}
	subList := s.GetSubMenu(list, menuId)
	for _, menu := range subList {
		ids = append(ids, menu.Id)
	}
	ids = append(ids, menuId)
	return
}

// 递归获得所有子菜单
func (s *sSysMenu) GetSubMenu(list []*with.SysMenu, menuId int64) (subList []*with.SysMenu) {
	subList = make([]*with.SysMenu, 0)
	for _, menu := range list {
		if menu.ParentId == menuId {
			subList = append(subList, menu)
			_subList := s.GetSubMenu(list, menu.Id)
			subList = append(subList, _subList...)
		}
	}
	return
}

// 根据ID获取菜单信息
func (s *sSysMenu) GetInfoByID(ctx context.Context, menuId int64) (menu *with.SysMenu, err error) {
	list, err := s.GetListCache(ctx)

	if err != nil {
		return
	}

	menu = library.FindOne(list, []library.FilterFunc[*with.SysMenu]{
		func(item *with.SysMenu) bool {
			return item.Id == menuId
		},
	})
	g.Log().Info(ctx, fmt.Sprintf("menu:%+v", menu))
	if menu != nil && g.IsEmpty(menu.ApiAssociation) {
		menu.ApiAssociation = "[]"
	}
	return
}

// 获得在ID列表中的所有菜单
func (s *sSysMenu) GetListInIds(ctx context.Context, ids []int64) (list []*with.SysMenu, err error) {
	return s.getListFilteredByIds(ctx, ids, true)
}

// 获得不在ID列表中的所有菜单
func (s *sSysMenu) GetListNotInIds(ctx context.Context, ids []int64) (list []*with.SysMenu, err error) {
	return s.getListFilteredByIds(ctx, ids, false)
}

// 通用的过滤函数
func (s *sSysMenu) getListFilteredByIds(ctx context.Context, ids []int64, include bool) (list []*with.SysMenu, err error) {
	list, err = s.GetListCache(ctx)
	if err != nil {
		return
	}
	list = library.FilterList(list, []library.FilterFunc[*with.SysMenu]{
		func(item *with.SysMenu) bool {
			contains := library.Contains(ids, item.Id)
			if include {
				return contains
			}
			return !contains
		},
	})
	return
}

// 获得不在ID列表中的所有菜单关联的API
func (s *sSysMenu) GetApiAssociationNotInIds(ctx context.Context, ids []int64) (apiList []string, err error) {
	list, err := s.GetListNotInIds(ctx, ids)
	if err != nil {
		return
	}
	apiSet := make(map[string]struct{})
	apiList = make([]string, 0)
	for _, menu := range list {
		if menu.ApiAssociation != "" {
			var associations []string
			if g.IsEmpty(menu.ApiAssociation) {
				continue
			}
			jsonErr := json.Unmarshal([]byte(menu.ApiAssociation), &associations)
			if jsonErr != nil {
				continue
			}
			for _, association := range associations {
				if g.IsEmpty(association) {
					continue
				}
				mapkey := gmd5.MustEncrypt(association)
				if _, exists := apiSet[mapkey]; !exists {
					apiSet[mapkey] = struct{}{}
					apiList = append(apiList, association)
				}
			}
		}
	}
	return
}
