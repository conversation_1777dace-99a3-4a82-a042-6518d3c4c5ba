package flowinstancehistoryauditor

import (
	"backend/api/v1/flow"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFlowInstanceHistoryAuditor(new(sFlowInstanceHistoryAuditor))
}

type sFlowInstanceHistoryAuditor struct {
}

// 获得流程实例审核人历史详细信息
func (s *sFlowInstanceHistoryAuditor) GetInfoByID(ctx context.Context, flow_instance_history_auditor_id int64) (historyAuditorEntity *entity.FlowInstanceHistoryAuditor, err error) {
	err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).WithAll().WherePri(flow_instance_history_auditor_id).Scan(&historyAuditorEntity)
	return
}

// 创建一个流程实例审核人历史
func (s *sFlowInstanceHistoryAuditor) Create(ctx context.Context, history *entity.FlowInstanceHistory, approver *dto.InstanceFlowAuditor) (historyAuditorEntity *entity.FlowInstanceHistoryAuditor, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err := dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
			Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceHistoryId, history.Id).
			Where(dao.FlowInstanceHistoryAuditor.Columns().UserId, approver.UserId).
			Scan(&historyAuditorEntity)
		library.ErrIsNil(ctx, err, "获取审核人审核历史失败")
		if historyAuditorEntity != nil {
			return
		}
		historyAuditor := &do.FlowInstanceHistoryAuditor{
			FlowInstanceHistoryId: history.Id,
			FlowInstanceId:        history.FlowInstanceId,
			NodeType:              history.NodeType,
			UserId:                approver.UserId,
			ApprovalStatus:        approver.ApprovalStatus,
			IsAutoPass:            approver.IsAutoPass,
			ArrivalTime:           gtime.NewFromTime(approver.ArrivalTime),
			CompletionTime:        gtime.NewFromTime(approver.CompletionTime),
			ApprovalComment:       approver.ApprovalComment,
			Attachment:            lo.Ternary(g.IsEmpty(approver.Attachment), "[]", approver.Attachment),
			Pics:                  lo.Ternary(g.IsEmpty(approver.Pics), "[]", approver.Pics),
			Signature:             approver.Signature,
		}

		lastid, err := dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).InsertAndGetId(historyAuditor)
		library.ErrIsNil(ctx, err, "添加审核人审核历史失败")

		entity, err := s.GetInfoByID(ctx, lastid)
		library.ErrIsNil(ctx, err, "获取审核人审核历史失败")
		historyAuditorEntity = entity
	})

	return
}

// 验证审核人是否已经存在审核通过的节点记录
func (s *sFlowInstanceHistoryAuditor) IsExistAgreeLog(ctx context.Context, flow_instance_id int64, user_id int64) (isExist bool, err error) {
	var agreeLog *entity.FlowInstanceHistoryAuditor
	err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceId, flow_instance_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().UserId, user_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, enum.NodeActionType_Agree).
		Scan(&agreeLog)
	if err != nil {
		return false, err
	}
	if agreeLog != nil && agreeLog.ApprovalStatus == string(enum.NodeActionType_Agree) {
		return true, nil
	}
	return false, nil
}

func (s *sFlowInstanceHistoryAuditor) GetLastLogByUser(ctx context.Context, flow_instance_id int64, user_id int64) (lastLog *entity.FlowInstanceHistoryAuditor, err error) {
	err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceId, flow_instance_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().UserId, user_id).
		OrderDesc(dao.FlowInstanceHistoryAuditor.Columns().Id).
		Scan(&lastLog)
	return
}

// 获得某个节点实例的审核人历史列表（根据状态）
func (s *sFlowInstanceHistoryAuditor) GetAuditorsByStatus(ctx context.Context, flow_instance_history_id int64, approval_status enum.NodeActionType) (historyAuditors []*entity.FlowInstanceHistoryAuditor, err error) {
	err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceHistoryId, flow_instance_history_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, approval_status).
		Scan(&historyAuditors)
	return
}

// 更新当前节点未审核的审批人状态为已跳过
func (s *sFlowInstanceHistoryAuditor) SkipPendingAuditors(ctx context.Context, flow_instance_history_id int64) error {
	_, err := dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceHistoryId, flow_instance_history_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, enum.NodeActionType_Pending).
		Update(do.FlowInstanceHistoryAuditor{
			ApprovalStatus: enum.NodeActionType_Skipped,
			CompletionTime: gtime.Now(),
		})
	return err
}

// 撤销某节点实例的所有未审核纪录
func (s *sFlowInstanceHistoryAuditor) CancelPendingAuditors(ctx context.Context, flow_instance_history_id int64) (err error) {
	_, err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceHistoryId, flow_instance_history_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, enum.NodeActionType_Pending).
		Update(&do.FlowInstanceHistoryAuditor{
			ApprovalStatus: enum.NodeActionType_Cancel,
			CompletionTime: gtime.Now(),
		})
	return
}

// 撤销某流程实例的所有未审核纪录
func (s *sFlowInstanceHistoryAuditor) CancelAllPendingAuditors(ctx context.Context, instance_id int64) (err error) {
	_, err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceId, instance_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, enum.NodeActionType_Pending).
		Update(&do.FlowInstanceHistoryAuditor{
			ApprovalStatus: enum.NodeActionType_Cancel,
			CompletionTime: gtime.Now(),
		})
	return
}

// 撤销某流程实例的中StepId在cancelStepIds的未审核纪录
func (s *sFlowInstanceHistoryAuditor) CancelAllPendingAuditorsByStepIds(ctx context.Context, instance_id int64, cancelStepIds []string) (err error) {
	// 先查询stepids对应的historyids
	historys := make([]*entity.FlowInstanceHistory, 0)
	err = dao.TenantCtx(dao.FlowInstanceHistory, ctx).
		Where(dao.FlowInstanceHistory.Columns().FlowInstanceId, instance_id).
		Where(dao.FlowInstanceHistory.Columns().StepId, cancelStepIds).
		Scan(&historys)

	if err != nil {
		return
	}

	historyIds := lo.Map(historys, func(history *entity.FlowInstanceHistory, _ int) int64 {
		return history.Id
	})

	_, err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceId, instance_id).
		Where(dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, enum.NodeActionType_Pending).
		Where(dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceHistoryId, historyIds).
		Update(&do.FlowInstanceHistoryAuditor{
			ApprovalStatus: enum.NodeActionType_Cancel,
			CompletionTime: gtime.Now(),
		})
	return
}

// 撤销审核
func (s *sFlowInstanceHistoryAuditor) UserCancel(ctx context.Context, req *flow.ApproveReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		loginUser := service.SysUser().GetLoginUser(ctx)
		library.ValueIsNil(loginUser, "请先登录")

		instance, err := service.FlowInstance().GetInstance(ctx, req.InstanceId)
		library.ErrIsNil(ctx, err, "获取流程实例失败")

		if !instance.FlowTemplateSnapUn.AllowRevoke {
			library.Fail(ctx, "该流程不允许撤销")
		}
		// 验证权限
		if instance.CreatedBy != int64(library.GetUserId(ctx)) {
			library.Fail(ctx, "无权操作")
		}

		// 流程是否在审核中
		if instance.InstanceState != int(enum.FlowStatus_Running) {
			library.Fail(ctx, "当前状态不允许取消")
		}

		// 取消所有未审核的审核人记录
		err = s.CancelAllPendingAuditors(ctx, req.InstanceId)
		library.ErrIsNil(ctx, err, "取消未审核的审核人记录失败")
		// 取消未审核的节点实例
		err = service.FlowInstanceHistory().CancelAllPending(ctx, req.InstanceId)
		library.ErrIsNil(ctx, err, "取消未审核的节点记录失败")
		approver := &dto.InstanceFlowAuditor{
			UserId:          loginUser.Id,
			Username:        loginUser.Username,
			ApprovalStatus:  enum.NodeActionType_Cancel,
			ArrivalTime:     time.Now(),
			CompletionTime:  time.Now(),
			Attachment:      lo.Ternary(g.IsEmpty(req.Attachment), "[]", req.Attachment),
			Pics:            lo.Ternary(g.IsEmpty(req.Pics), "[]", req.Pics),
			Signature:       lo.Ternary(g.IsEmpty(req.Signature), "[]", req.Signature),
			ApprovalComment: req.ApprovalComment,
		}
		// 新增取消流程的节点记录
		history, err := service.FlowInstanceHistory().Create(ctx, req.InstanceId, &dto.InstanceFlowNode{
			Id:       "",
			Name:     "撤销审批",
			Shape:    enum.NodeType_EndNode,
			Status:   enum.ApprovalStatus_Pass,
			Approver: []*dto.InstanceFlowAuditor{approver},
		})
		library.ErrIsNil(ctx, err, "新增审批记录失败")
		// 新增取消流程的审批人记录
		_, err = s.Create(ctx, history, approver)
		library.ErrIsNil(ctx, err, "新增审批人记录失败")

		// 如果是在开始节点取消，还要记录一下通过开始节点.
		_, isStartNode := lo.Find(instance.CurrentStepsUn, func(node *dto.InstanceFlowNode) bool {
			return node.Shape == enum.NodeType_StartNode
		})
		if isStartNode {
			queue.QueueManager().Publish(consts.StreamFormFlowStarted, &dto.FormStreamInfo{
				TableName:    instance.FormTableName,
				FormDataId:   instance.FormTableId,
				ApprovalType: lo.Ternary(instance.FlowInstanceType == 1, "create", "invalidate"),
			})
		}
		// 修改流程状态为取消
		err = service.FlowInstance().UpdateInstanceStatus(ctx, req.InstanceId, enum.FlowStatus_Cancel, instance)
		library.ErrIsNil(ctx, err, "取消流程失败")
	})
	return
}

// 步骤处理
func (s *sFlowInstanceHistoryAuditor) UserApprove(ctx context.Context, req *flow.ApproveReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.ApprovalStatus == enum.NodeActionType_Cancel {
			err = s.UserCancel(ctx, req)
			library.ErrIsNil(ctx, err)
			return
		}
		historyAuditor, err := s.GetInfoByID(ctx, req.HistoryAuditorId)
		library.ErrIsNil(ctx, err, "获取审核人纪录失败")
		library.ValueIsNil(historyAuditor, "纪录已删除或不存在")

		// 验证权限
		if historyAuditor.UserId != int64(library.GetUserId(ctx)) {
			library.Fail(ctx, "无权操作")
		}

		if enum.NodeActionType(historyAuditor.ApprovalStatus) != enum.NodeActionType_Pending {
			library.Fail(ctx, "该记录已处理，无需操作")
		}

		if req.ApprovalStatus == enum.NodeActionType_Return && g.IsEmpty(req.ReturnNodeId) {
			library.Fail(ctx, "请选择退回节点")
		}

		instance, err := service.FlowInstance().GetInfoCache(ctx, req.InstanceId)
		library.ErrIsNil(ctx, err, "获取流程实例失败")

		if instance.InstanceState != int(enum.FlowStatus_Running) {
			library.Fail(ctx, "该流程已结束，无法操作")
		}

		if instance.FlowInstanceType == int(enum.FlowInstanceType_Invalid) && req.ApprovalStatus == enum.NodeActionType_Return {
			library.Fail(ctx, "作废流程不允许退回")
		}

		historyAuditor.ApprovalStatus = string(req.ApprovalStatus)
		historyAuditor.ApprovalComment = req.ApprovalComment
		historyAuditor.ReturnNodeId = req.ReturnNodeId
		historyAuditor.Attachment = lo.Ternary(g.IsEmpty(req.Attachment), "[]", req.Attachment)
		historyAuditor.Pics = lo.Ternary(g.IsEmpty(req.Pics), "[]", req.Pics)
		historyAuditor.Signature = lo.Ternary(g.IsEmpty(req.Signature), "[]", req.Signature)
		historyAuditor.CompletionTime = gtime.Now()

		_, err = dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).WherePri(req.HistoryAuditorId).Update(historyAuditor)
		library.ErrIsNil(ctx, err, "更新审核人纪录失败")

		// 触发下一步
		queue.QueueManager().Publish(consts.StreamSubjectFlowAudit, &dto.NextApproval{
			HistoryAuditor: historyAuditor,
			Instance:       nil,
		})

	})
	return
}

// 修改流程实例审核人的历史信息

// 查询历史
func (s *sFlowInstanceHistoryAuditor) GetList(ctx context.Context, req *dto.HistoryAuditorListReq) (res *dto.HistoryAuditorListRes, err error) {
	res = &dto.HistoryAuditorListRes{}
	m := dao.TenantCtx(dao.FlowInstanceHistoryAuditor, ctx).As("iha").LeftJoin(dao.FlowInstance.Table(), "i", fmt.Sprintf("i.%s = iha.%s", dao.FlowInstance.Columns().Id, dao.FlowInstanceHistoryAuditor.Columns().FlowInstanceId))

	if req.FlowInstanceType > 0 {
		m = m.Where("i."+dao.FlowInstance.Columns().FlowInstanceType, req.FlowInstanceType)
	}
	if req.CreatedBy > 0 {
		m = m.Where("i."+dao.FlowInstance.Columns().CreatedBy, req.CreatedBy)
	}
	if req.FormTemplateId > 0 {
		m = m.Where("i."+dao.FlowInstance.Columns().FormTemplateId, req.FormTemplateId)
	}
	if req.FlowInstanceState > 0 {
		m = m.Where("i."+dao.FlowInstance.Columns().InstanceState, req.FlowInstanceState)
	}
	if req.CreatedBy > 0 {
		m = m.Where("i."+dao.FlowInstance.Columns().CreatedBy, req.CreatedBy)
	}
	if !g.IsEmpty(req.ApprovalStatus) {
		if req.ApprovalStatusType == 1 {
			m = m.WhereNot("iha."+dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, req.ApprovalStatus)
		} else {
			m = m.Where("iha."+dao.FlowInstanceHistoryAuditor.Columns().ApprovalStatus, req.ApprovalStatus)
		}
	}
	if req.ApprovalUser > 0 {
		m = m.Where("iha."+dao.FlowInstanceHistoryAuditor.Columns().UserId, req.ApprovalUser)
	}

	if req.IsAutoPass >= 0 {
		m = m.Where("iha."+dao.FlowInstanceHistoryAuditor.Columns().IsAutoPass, req.IsAutoPass)
	}

	if !g.IsEmpty(req.NodeType) {
		m = m.Where("iha."+dao.FlowInstanceHistoryAuditor.Columns().NodeType, req.NodeType)
	}

	g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err)
		m.Page(req.PageNum, req.PageSize).Fields("iha.*").WithAll().OrderDesc(dao.FlowInstanceHistoryAuditor.Columns().Id).Scan(&res.List)

	})
	return
}
