package sqlquery

import (
	"backend/api/v1/form"
	"backend/internal/service"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterSqlQuery(new(sSqlQuery))
}

type sSqlQuery struct{}

// 测试SQL查询
func (s *sSqlQuery) TestQuery(ctx context.Context, req *form.SqlQueryTestReq) (*form.SqlQueryTestRes, error) {
	// 替换参数
	sql, err := s.ReplaceParameters(req.Sql, req.Parameters, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("参数替换失败: %v", err)
	}

	// 执行查询
	return s.executeQuery(ctx, sql, req.Type)
}

// 执行SQL查询
func (s *sSqlQuery) ExecuteQuery(ctx context.Context, req *form.SqlQueryExecuteReq) (*form.SqlQueryExecuteRes, error) {
	// 替换参数
	sql, err := s.ReplaceParameters(req.Sql, req.Parameters, req.FormData, req.WithTableData)
	if err != nil {
		return nil, fmt.Errorf("参数替换失败: %v", err)
	}

	// 执行查询
	result, err := s.executeQuery(ctx, sql, req.Type)
	if err != nil {
		return nil, fmt.Errorf("查询执行失败: %v", err)
	}

	return &form.SqlQueryExecuteRes{
		Data:          result.Data,
		Message:       result.Message,
		ExecutionTime: result.ExecutionTime,
		RowCount:      result.RowCount,
	}, nil
}

// 验证SQL语法
func (s *sSqlQuery) ValidateSQL(ctx context.Context, req *form.SqlQueryValidateReq) (*form.SqlQueryValidateRes, error) {
	// 使用EXPLAIN语句验证SQL语法
	explainSQL := fmt.Sprintf("EXPLAIN %s", req.Sql)

	db := g.DB()
	_, err := db.GetAll(ctx, explainSQL)

	if err != nil {
		return &form.SqlQueryValidateRes{
			Valid:   false,
			Message: fmt.Sprintf("SQL语法错误: %v", err),
		}, nil
	}

	return &form.SqlQueryValidateRes{
		Valid:   true,
		Message: "SQL语法验证通过",
	}, nil
}

// 获取数据库结构
func (s *sSqlQuery) GetDatabaseSchema(ctx context.Context, req *form.SqlQuerySchemaReq) (*form.SqlQuerySchemaRes, error) {
	tables, err := s.getDatabaseTables(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取数据库结构失败: %v", err)
	}

	return &form.SqlQuerySchemaRes{
		Tables: tables,
	}, nil
}

// 替换SQL中的参数占位符
func (s *sSqlQuery) ReplaceParameters(sql string, parameters []form.SqlParameter, formData g.Map, withTableData map[string]map[string]interface{}) (string, error) {
	result := sql

	// 创建JavaScript运行时
	vm, err := service.Javascript().GetVM(map[string]interface{}{})
	if err != nil {
		return "", fmt.Errorf("创建JavaScript运行时失败: %v", err)
	}

	for _, param := range parameters {
		paramPlaceholder := fmt.Sprintf("#{%s}", param.Parameter)
		var value interface{}

		// 如果有实际值（执行时使用），优先使用实际值
		if param.Value != "" {
			value = param.Value
		} else if param.FieldPath != "" {
			// 从表单数据或当前用户信息中获取值
			value = s.GetValueFromFormData(formData, param.FieldPath, withTableData)
		}

		// 如果没有获取到值，使用默认值
		if value == nil || gconv.String(value) == "" {
			value = param.DefaultValue
		} else if param.FuncCode != nil && param.FuncCode.CodeContent != "" { // 如果有自定义函数代码，使用JavaScript处理参数值
			// 设置JavaScript运行时的上下文变量
			vm.Set("__current", value)

			// 执行自定义代码处理参数值
			processedValue, err := service.Javascript().FormatMappedValue(context.Background(), vm, "", 0, nil, value, *param.FuncCode)
			if err != nil {
				g.Log().Error(context.Background(), fmt.Sprintf("参数 %s 的JavaScript代码执行失败: %v", param.Parameter, err))
				// 代码执行失败时，使用原始值
			} else {
				value = processedValue
			}
		}

		// 替换参数占位符（简单字符串替换，防止SQL注入）
		escapedValue := strings.ReplaceAll(gconv.String(value), "'", "''")
		result = strings.ReplaceAll(result, paramPlaceholder, fmt.Sprintf("'%s'", escapedValue))
	}

	return result, nil
}

// 从表单数据中获取字段值
func (s *sSqlQuery) GetValueFromFormData(formData g.Map, fieldPath string, withTableData map[string]map[string]interface{}) string {
	ctx := context.Background()

	// 处理当前用户信息
	if strings.HasPrefix(fieldPath, "currentUser.") {
		loginUser := service.SysUser().GetLoginUser(ctx)
		if loginUser == nil {
			return ""
		}

		property := strings.TrimPrefix(fieldPath, "currentUser.")
		result := service.SysUser().GetUserProperty(ctx, loginUser, property)
		return gconv.String(result)
	} else if strings.HasPrefix(fieldPath, "fieldsValue.") {
		if formData == nil {
			return ""
		}

		fieldName := strings.TrimPrefix(fieldPath, "fieldsValue.")
		if val, ok := formData[fieldName]; ok {
			return gconv.String(val)
		}
		return ""
	} else if strings.Contains(fieldPath, ".") {
		keys := strings.Split(fieldPath, ".")
		if len(keys) == 2 {
			relationField := keys[0]
			targetField := keys[1]

			// 检查是否是关联表字段
			if tableData, ok := withTableData[relationField]; ok && tableData != nil {
				if val, exists := tableData[targetField]; exists {
					return gconv.String(val)
				}
			}
		}
	}

	return ""
}

// 执行SQL查询（内部方法）
func (s *sSqlQuery) executeQuery(ctx context.Context, sql string, queryType string) (*form.SqlQueryTestRes, error) {
	startTime := time.Now()

	// 获取数据库连接
	db := g.DB()

	var result interface{}
	var rowCount int

	if queryType == "table" {
		// 表格查询，返回数组
		records, err := db.GetAll(ctx, sql)
		if err != nil {
			return nil, fmt.Errorf("SQL执行失败: %v", err)
		}

		result = records.List()
		rowCount = len(records)
	} else {
		// 单值查询，返回单个值
		record, err := db.GetOne(ctx, sql)
		if err != nil {
			return nil, fmt.Errorf("SQL执行失败: %v", err)
		}

		if record.IsEmpty() {
			result = nil
			rowCount = 0
		} else {
			// 如果记录只有一个字段，直接返回该字段的值
			if len(record.Map()) == 1 {
				for _, v := range record.Map() {
					result = v
					break
				}
			} else {
				result = record.Map()
			}
			rowCount = 1
		}
	}

	executionTime := time.Since(startTime).Milliseconds()

	return &form.SqlQueryTestRes{
		Data:          result,
		Message:       "执行成功",
		ExecutionTime: executionTime,
		RowCount:      rowCount,
	}, nil
}

// 获取数据库表结构（内部方法）
func (s *sSqlQuery) getDatabaseTables(ctx context.Context) ([]form.DatabaseTable, error) {
	db := g.DB()

	// 获取所有表
	tablesQuery := `
		SELECT TABLE_NAME, TABLE_COMMENT 
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = DATABASE() 
		ORDER BY TABLE_NAME
	`

	tableRecords, err := db.GetAll(ctx, tablesQuery)
	if err != nil {
		return nil, err
	}

	var tables []form.DatabaseTable

	for _, tableRecord := range tableRecords {
		tableName := gconv.String(tableRecord["TABLE_NAME"])
		tableComment := gconv.String(tableRecord["TABLE_COMMENT"])

		// 获取表的列信息
		columnsQuery := `
			SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
			FROM information_schema.COLUMNS 
			WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? 
			ORDER BY ORDINAL_POSITION
		`

		columnRecords, err := db.GetAll(ctx, columnsQuery, tableName)
		if err != nil {
			continue
		}

		var columns []form.DatabaseColumn
		for _, columnRecord := range columnRecords {
			columns = append(columns, form.DatabaseColumn{
				Name:    gconv.String(columnRecord["COLUMN_NAME"]),
				Type:    gconv.String(columnRecord["DATA_TYPE"]),
				Comment: gconv.String(columnRecord["COLUMN_COMMENT"]),
			})
		}

		tables = append(tables, form.DatabaseTable{
			Name:    tableName,
			Comment: tableComment,
			Columns: columns,
		})
	}

	return tables, nil
}
