package excel

import (
	"backend/internal/service"
	"context"

	"github.com/xuri/excelize/v2"
)

func init() {
	service.RegisterExcelService(new(sExcelService))
}

type sExcelService struct {
}

// 解析Excel内容
func (s *sExcelService) ParseExcelContent(ctx context.Context, filePath string) (err error) {
	// TODO: 在这里添加解析Excel内容的具体逻辑
	return
}

// 将数据转成Excel
func (s *sExcelService) ConvertDataToExcel(ctx context.Context, data interface{}) (filePath string, err error) {
	// TODO: 在这里添加将数据转成Excel的具体逻辑
	return
}

// 将Excel内容转成map切片
func (s *sExcelService) ExcelToMapSlice(ctx context.Context, filePath string, startRow int) (result []map[string]string, err error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows(f.GetSheetName(0))
	if err != nil {
		return nil, err
	}

	if len(rows) < startRow {
		return nil, nil // 没有足够的数据
	}

	headers := rows[startRow-1]
	for _, row := range rows[startRow:] {
		rowMap := make(map[string]string)
		for i, cell := range row {
			if i < len(headers) {
				rowMap[headers[i]] = cell
			}
		}
		result = append(result, rowMap)
	}

	return result, nil
}
