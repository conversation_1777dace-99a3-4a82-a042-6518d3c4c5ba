package sysuser

import (
	"backend/api/v1/system"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gogf/gf/errors/gerror"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/samber/lo"
	"golang.org/x/time/rate"
)

func init() {
	service.RegisterSysUser(new(sSysUser))
}

type sSysUser struct {
	userLimiters sync.Map
}

func (s *sSysUser) GetListByProjectRoles(ctx context.Context, projectId int64, projectRoleIds []int64) (users []*with.SysUserOutline, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var projectUsers []*with.SysProjectUser
		err = dao.TenantCtx(dao.SysUserProject, ctx).WithAll().Where(dao.SysUserProject.Columns().ProjectId, projectId).WhereIn(dao.SysUserProject.Columns().ProjectRoleId, projectRoleIds).Scan(&projectUsers)
		library.ErrIsNil(ctx, err)

		if projectUsers == nil {
			return
		}

		users = lo.Map(projectUsers, func(v *with.SysProjectUser, _ int) *with.SysUserOutline {
			return v.User
		})

		users = lo.UniqBy(users, func(v *with.SysUserOutline) int64 {
			return v.Id
		})
	})
	return
}

func (s *sSysUser) GetListByPostId(ctx context.Context, postId int64) (users []*with.SysUser, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		usersCache, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.KeyUserPostId, postId)), func(ctx context.Context) (value interface{}, err error) {
			var usersValue []*with.SysUser
			m := dao.TenantCtx(dao.SysUser, ctx).As("u")
			m = m.Where(fmt.Sprintf("JSON_OVERLAPS(u.%s, ?)", dao.SysUser.Columns().WithPosts), gconv.String(postId))
			m.OrderAsc(dao.SysUser.Columns().Id).Scan(&usersValue)
			value = usersValue
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		if usersCache != nil {
			err := gconv.Struct(usersCache, &users)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

func (s *sSysUser) GetListByUserIds(ctx context.Context, userIds []int64) (users []*with.SysUser, err error) {
	err = dao.TenantCtx(dao.SysUser, ctx).As("u").WhereIn(dao.SysUser.Columns().Id, userIds).Scan(&users)
	return
}

// 检索用户信息
func (s *sSysUser) GetList(ctx context.Context, req *system.SysUserListReq) (res *system.SysUserListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = &system.SysUserListRes{}
		m := dao.TenantCtx(dao.SysUser, ctx).As("u")

		// if req.DeptId > 0 {
		// 	m = m.LeftJoin(dao.SysUserDept.Table(), "ud", fmt.Sprintf("u.%s = ud.%s", dao.SysUser.Columns().Id, dao.SysUserDept.Columns().UserId))
		// 	var deptIds []int64
		// 	deptIds, err = service.SysDept().GetDeptIds(ctx, req.DeptId)
		// 	if err != nil {
		// 		return
		// 	}
		// 	m = m.Where(fmt.Sprintf("ud.%s IN (?)", dao.SysUserDept.Columns().DeptId), deptIds)
		// }
		// if req.Keyword != "" {
		// 	build := m.Builder().WhereLike("u."+dao.SysUser.Columns().Username, "%"+req.Keyword+"%").WhereOrLike("u."+dao.SysUser.Columns().Code, "%"+req.Keyword+"%")
		// 	m = m.Where(build)
		// }

		if req.DeptId > 0 {
			var deptIds []int64
			deptIds, err = service.SysDept().GetDeptIds(ctx, req.DeptId)
			if err != nil {
				return
			}
			var jsonIds []byte
			jsonIds, err = json.Marshal(deptIds)
			if err != nil {
				return
			}

			m = m.Where(fmt.Sprintf("JSON_OVERLAPS(u.%s, ?)", dao.SysUser.Columns().WithDepts), string(jsonIds))
		}
		if req.PostId > 0 {
			m = m.Where(fmt.Sprintf("JSON_OVERLAPS(u.%s, ?)", dao.SysUser.Columns().WithDepts), gconv.String(req.PostId))
		}

		if req.Status > 0 {
			m = m.Where("u."+dao.SysUser.Columns().Status, req.Status)
		}
		if req.Code != "" {
			m = m.WhereLike("u."+dao.SysUser.Columns().Code, "%"+req.Code+"%")
		}
		if req.Username != "" {
			m = m.WhereLike("u."+dao.SysUser.Columns().Username, "%"+req.Username+"%")
		}
		if req.Email != "" {
			m = m.WhereLike("u."+dao.SysUser.Columns().Email, "%"+req.Email+"%")
		}
		if req.SystemAccount != "" {
			m = m.WhereLike("u."+dao.SysUser.Columns().SystemAccount, "%"+req.SystemAccount+"%")
		}
		if req.ContactPhone != "" {
			m = m.WhereLike("u."+dao.SysUser.Columns().ContactPhone, "%"+req.ContactPhone+"%")
		}
		g.Try(ctx, func(ctx context.Context) {
			res.Total, err = m.Count()
			library.ErrIsNil(ctx, err)
			m = m.Fields(
				"u.id",               // id
				"u.code",             // 员工编号
				"u.system_account",   // 系统账号
				"u.username",         // 用户名
				"u.email",            // 邮箱
				"u.contact_phone",    // 联系电话
				"u.id_number",        // 身份证号码
				"u.hire_date",        // 入职日期
				"u.resignation_date", // 离职日期
				"u.status",           // 状态
				"u.with_depts",       // 所在部门
				"u.with_posts",       // 所属岗位
				"u.with_roles",       // 拥有的角色
				"u.with_companys",    // 关联的公司
				"u.with_projects",    // 关联的项目
				"u.gender",           // 性别
				"u.birthday",         // 生日
				"u.contact_address",  // 联系地址
				"u.description",      // 描述信息
				"u.last_login_ip",    // 最后登录ip
				"u.last_login_time",  // 最后登录时间
				"u.created_by",       // 创建人
				"u.created_at",       // 创建时间
				"u.updated_at",       // 更新时间
				"u.deleted_at",       // 删除时间
				"u.tenant_id",        // 数据租户id
				"u.avatar",           // 用户头像
			)
			if !req.Summary {
				m = m.With(&with.SysUserDept{}, &with.SysUserPost{}, &entity.SysDept{}, &entity.SysPost{})
			}
			m.Page(req.PageNum, req.PageSize).Order(fmt.Sprintf("CASE `%s` WHEN 2 THEN 1 WHEN 4 THEN 2 WHEN 1 THEN 3 WHEN 999 THEN 4 ELSE 5 END, %s DESC", dao.SysUser.Columns().Status, dao.SysUser.Columns().Id)).Scan(&res.List)
		})
	})
	return
}

// Save 保存用户信息
func (s *sSysUser) Save(ctx context.Context, req *system.SysUserSaveReq) (userId int64, err error) {
	// 获得岗位树节点路径
	err = g.Try(ctx, func(ctx context.Context) {
		var userDto *do.SysUser
		err = gconv.Struct(req, &userDto)
		id := gconv.Int64(userDto.Id)
		if id <= 0 {
			id, err = s.Add(ctx, userDto)
		} else {
			var oldUser *with.SysUser
			oldUser, err = s.GetInfoByID(ctx, int64(req.Id))
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(oldUser, "用户不存在")
			err = s.Edit(ctx, userDto)
		}
		library.ErrIsNil(ctx, err)

		// 修改关联关系
		err = s.EditWithDept(ctx, id, req.WithDepts)
		library.ErrIsNil(ctx, err)

		err = s.EditWithPost(ctx, id, req.WithPosts)
		library.ErrIsNil(ctx, err)

		err = s.EditWithRole(ctx, id, req.WithRoles)
		library.ErrIsNil(ctx, err)

		err = s.EditWithProject(ctx, id, req.WithProjects)
		library.ErrIsNil(ctx, err)

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.KeyUserCurren, id)))

		userId = id

	})

	return
}

// Add 添加
func (s *sSysUser) Add(ctx context.Context, user *do.SysUser) (lastid int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		code := library.GenerateID("U{**************}{RanNum(8)}", 0)
		nextId, err := service.Cache().GetNextId(ctx, consts.KeyNextUserAccount)
		library.ErrIsNil(ctx, err)
		systemAccount := fmt.Sprintf("%d", nextId+consts.ACCOUNT_CODE_START)
		user.Code = code
		user.SystemAccount = systemAccount
		user.Usersale = grand.S(10)
		user.Password = library.EnPwd(consts.DEFAULT_PASSWORD, gconv.String(user.Usersale))
		user.CreatedBy = library.GetUserId(ctx)
		lastid, err = dao.TenantCtx(dao.SysUser, ctx).InsertAndGetId(user)
		library.ErrIsNil(ctx, err, "添加失败")

	})
	return
}

// Edit 修改
func (s *sSysUser) Edit(ctx context.Context, user *do.SysUser) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.SysUser, ctx).WherePri(user.Id).Update(user)
		library.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

// 删除
func (s *sSysUser) Delete(ctx context.Context, userid int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.SysUser, ctx).WherePri(userid).Delete()
		// 验证关联关系？

		library.ErrIsNil(ctx, err, "删除用户失败")
		// 删除关联数据？

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.KeyUserCurren, userid)))
	})
	return

}

// 重置用户密码 @password 为空时，使用默认密码
func (s *sSysUser) EditPassword(ctx context.Context, id int64, password string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		updateUser := &do.SysUser{}
		if g.IsEmpty(password) {
			password = consts.DEFAULT_PASSWORD
			updateUser.PasswordChanged = 0
		} else {
			updateUser.LastUpdatePwdTime = gtime.Now()
			updateUser.PasswordChanged = 1
		}
		updateUser.Usersale = grand.S(10)
		updateUser.Password = library.EnPwd(password, gconv.String(updateUser.Usersale))
		_, err = dao.TenantCtx(dao.SysUser, ctx).WherePri(id).Update(&updateUser)
		library.ErrIsNil(ctx, err, "修改密码失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.KeyUserCurren, id)))
	})
	return
}

// 修改部门关联关系
func (s *sSysUser) EditWithDept(ctx context.Context, userId int64, withIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dao.TenantCtx(dao.SysUserDept, ctx).Where(dao.SysUserDept.Columns().UserId+" = ?", userId).Delete()
		if len(withIds) > 0 {
			for _, id := range withIds {
				_, err = dao.TenantCtx(dao.SysUserDept, ctx).Insert(do.SysUserDept{
					UserId: userId,
					DeptId: id,
				})
				library.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// 修改岗位关联关系
func (s *sSysUser) EditWithPost(ctx context.Context, userId int64, withIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dao.TenantCtx(dao.SysUserPost, ctx).Where(dao.SysUserPost.Columns().UserId+" = ?", userId).Delete()
		if len(withIds) > 0 {
			for _, id := range withIds {
				_, err = dao.TenantCtx(dao.SysUserPost, ctx).Insert(do.SysUserPost{
					UserId: userId,
					PostId: id,
				})
				library.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// 修改项目关联关系
func (s *sSysUser) EditWithProject(ctx context.Context, userId int64, withProjects []*dto.AdProjectUser) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dao.TenantCtx(dao.SysUserProject, ctx).Where(dao.SysUserProject.Columns().UserId+" = ?", userId).Delete()
		if len(withProjects) > 0 {
			for _, project := range withProjects {
				_, err = dao.TenantCtx(dao.SysUserProject, ctx).Insert(do.SysUserProject{
					UserId:        userId,
					ProjectId:     project.ProjectId,
					ProjectRoleId: project.ProjectRoleId,
				})
				library.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// 修改角色关联关系
func (s *sSysUser) EditWithRole(ctx context.Context, userId int64, withIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dao.TenantCtx(dao.SysUserRole, ctx).Where(dao.SysUserRole.Columns().UserId+" = ?", userId).Delete()
		if len(withIds) > 0 {
			for _, id := range withIds {
				_, err = dao.TenantCtx(dao.SysUserRole, ctx).Insert(do.SysUserRole{
					UserId: userId,
					RoleId: id,
				})
				library.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// 获取用户信息
func (s *sSysUser) GetInfoByID(ctx context.Context, id int64) (user *with.SysUser, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userCache, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.KeyUserCurren, id)), func(ctx context.Context) (value interface{}, err error) {
			var userValue *with.SysUser
			err = dao.TenantCtx(dao.SysUser, ctx).WherePri(id).WithAll().Scan(&userValue)
			if err != nil {
				return
			}
			value = userValue
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)

		if !g.IsEmpty(userCache) && userCache != nil {
			err := gconv.Struct(userCache, &user)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

// 根据账号获取用户信息
func (s *sSysUser) GetUserByAccount(ctx context.Context, account string) (user *with.SysUser, err error) {
	g.Log().Info(ctx, fmt.Sprintf("account:%s", account))
	err = dao.TenantCtx(dao.SysUser, ctx).With(&with.SysUserRole{}, &entity.SysRole{}).
		Where(dao.SysUser.Columns().SystemAccount, account).
		WhereOr(dao.SysUser.Columns().Username, account).
		WhereOr(dao.SysUser.Columns().ContactPhone, account).
		WhereOr(dao.SysUser.Columns().Email, account).Scan(&user)
	return
}

// 获取用户的令牌桶
func (s *sSysUser) GetUserLimiter(ctx context.Context, userName string) *rate.Limiter {
	limiter, ok := s.userLimiters.Load(userName)
	if !ok {
		// 每分钟最多5次登录尝试
		limiter = rate.NewLimiter(rate.Every(time.Minute/5), 5)
		s.userLimiters.Store(userName, limiter)
	}
	return limiter.(*rate.Limiter)
}

// 用户登录验证
func (s *sSysUser) LoginValidate(ctx context.Context, userName string, password string) (user *with.SysUser, err error) {
	// 获取用户的令牌桶
	limiter := s.GetUserLimiter(ctx, userName)
	// 检查是否可以获取一个令牌
	if !limiter.Allow() {
		return nil, gerror.New("登录尝试次数过多，请稍后再试")
	}

	err = g.Try(ctx, func(ctx context.Context) {
		user, err = s.GetUserByAccount(ctx, userName)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(user, "账号或密码错误")
		if library.EnPwd(password, user.Usersale) != user.Password {
			library.ErrIsNil(ctx, gerror.New("账号或密码错误"))
		}
	})
	return
}

// 验证是否存在某个权限

// 根据用户所属角色，计算所拥有的菜单权限ID
func (s *sSysUser) ComputeAllowMenuIds(ctx context.Context, user *with.SysUser) (menuIds []int64, err error) {
	permissionSet := make(map[int64]struct{})
	menuIds = make([]int64, 0)
	if user.Roles != nil {
		for _, role := range user.Roles {
			if role.Role != nil {
				var permissions []dto.SysPermissions
				jsonErr := json.Unmarshal([]byte(role.Role.Permissions), &permissions)
				if jsonErr != nil {
					continue
				}
				for _, permission := range permissions {
					if permission.Type != string(enum.SysPermissionType_Menu) {
						continue
					}
					if permission.Permission == nil {
						continue
					}

					view, hasView := permission.Permission["view"]
					if !hasView || !view {
						continue
					}
					if _, exists := permissionSet[permission.MenuId]; !exists {
						permissionSet[permission.MenuId] = struct{}{}
						menuIds = append(menuIds, permission.MenuId)
					}
				}

			}
		}
	}
	return
}

// 模拟用户的UserClaims
func (s *sSysUser) MockGetUserClaims(ctx context.Context, userid int64) (userClaims *dto.UserClaims, err error) {
	user, err := s.GetInfoByID(ctx, userid)
	if err != nil {
		return
	}

	var withRoles []int64
	var withDepts []int64
	var withPosts []int64

	json.Unmarshal([]byte(lo.Ternary(g.IsEmpty(user.WithRoles), "[]", user.WithRoles)), &withRoles)
	json.Unmarshal([]byte(lo.Ternary(g.IsEmpty(user.WithDepts), "[]", user.WithDepts)), &withDepts)
	json.Unmarshal([]byte(lo.Ternary(g.IsEmpty(user.WithPosts), "[]", user.WithPosts)), &withPosts)

	// 构造jwt中存储的信息
	userClaims = &dto.UserClaims{
		Id:        user.Id,
		Code:      user.Code,
		Username:  user.Username,
		TenantId:  user.TenantId,
		WithDepts: withDepts,
		WithRoles: withRoles,
		WithPosts: withPosts,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(consts.JWT_EXPIRATION_TIME).Unix(),
		},
	}

	return
}

// 获得用户的jwttoken
func (s *sSysUser) GetUserJwtToken(ctx context.Context, user *with.SysUser) (jwtToken string, err error) {
	if user.Status == consts.LeaveUserStatus {
		err = gerror.New("账号已被冻结")
		return
	}
	expirationTime := time.Now().Add(consts.JWT_EXPIRATION_TIME)

	allowMenuIds, err := s.ComputeAllowMenuIds(ctx, user)
	if err != nil {
		return
	}
	deniedPermissions, err := service.SysMenu().GetApiAssociationNotInIds(ctx, allowMenuIds)

	if err != nil {
		return
	}

	var withRoles []int64
	var withDepts []int64
	var withPosts []int64

	json.Unmarshal([]byte(lo.Ternary(g.IsEmpty(user.WithRoles), "[]", user.WithRoles)), &withRoles)
	json.Unmarshal([]byte(lo.Ternary(g.IsEmpty(user.WithDepts), "[]", user.WithDepts)), &withDepts)
	json.Unmarshal([]byte(lo.Ternary(g.IsEmpty(user.WithPosts), "[]", user.WithPosts)), &withPosts)

	withRolesName := lo.Map(user.Roles, func(item *with.SysUserRole, index int) string {
		return item.Role.Name
	})

	// 构造jwt中存储的信息
	jwtUserClaims := &dto.UserClaims{
		Id:                user.Id,
		Code:              user.Code,
		Username:          user.Username,
		TenantId:          user.TenantId,
		WithDepts:         withDepts,
		WithRoles:         withRoles,
		WithPosts:         withPosts,
		WithRolesName:     withRolesName,
		DeniedPermissions: deniedPermissions,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expirationTime.Unix(),
		},
	}

	// 生成jwttoken
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwtUserClaims)
	jwtToken, err = token.SignedString([]byte(library.GetJWTKey(ctx)))
	if err != nil {
		return
	}

	return
}

func (s *sSysUser) GetJwtRefreshToken(ctx context.Context, user *with.SysUser, autoLogin bool) (jwtRefreshToken string, err error) {
	refExpirationTime := time.Now().Add(24 * time.Hour)

	if autoLogin {
		refExpirationTime = time.Now().Add(30 * 24 * time.Hour)
	}

	jwtRefreshTokenClaims := &dto.RefreshTokenClaims{
		Id: user.Id,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: refExpirationTime.Unix(),
		},
	}

	// 生成 refreshToken
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, jwtRefreshTokenClaims)
	jwtRefreshToken, err = refreshToken.SignedString([]byte(library.GetJWTKey(ctx)))
	if err != nil {
		return
	}

	return
}

// 根据refreshToken 刷新jwttoken
func (s *sSysUser) RefreshToken(ctx context.Context, refreshToken string) (newToken string, err error) {
	// 解析refreshToken
	token, err := jwt.ParseWithClaims(refreshToken, &dto.RefreshTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(library.GetJWTKey(ctx)), nil
	})
	if err != nil {
		return
	}
	// 验证 token 和声明
	if claims, ok := token.Claims.(*dto.RefreshTokenClaims); ok && token.Valid {
		var user *with.SysUser
		// 获得用户信息
		user, err = s.GetInfoByID(ctx, claims.Id)
		if err != nil {
			return
		}
		// 生成新的jwttoken
		newToken, err = s.GetUserJwtToken(ctx, user)
		if err != nil {
			return
		}

	} else {
		err = gerror.New("refreshToken 无效")
	}

	return
}

// 更新用户的登录ip和时间
func (s *sSysUser) UpdateUserLoginInfo(ctx context.Context, userid int64, ip string, time *gtime.Time) (err error) {
	_, err = dao.TenantCtx(dao.SysUser, ctx).WherePri(userid).Update(&do.SysUser{
		LastLoginIp:   ip,
		LastLoginTime: time,
	})
	return
}

// 从ctx中获得存储的当前登录用户
func (s *sSysUser) GetLoginUser(ctx context.Context) (user *dto.UserClaims) {
	value := ctx.Value(consts.CTX_USER)
	if value == nil {
		return
	}
	user, _ = value.(*dto.UserClaims)
	return
}

// // 从ctx中获得存储的当前登录用户id
// func (s *sSysUser) GetLoginUserId(ctx context.Context) (userId int64) {
// 	user := s.GetLoginUser(ctx)
// 	if user == nil {
// 		return
// 	}
// 	userId = user.Id
// 	return
// }

// 计算登录用户的功能权限
func (s *sSysUser) ComputeUserPermissions(ctx context.Context) (permissions []*dto.SysPermissions, err error) {
	loginUser := s.GetLoginUser(ctx)
	if loginUser == nil {
		return
	}
	permissions, err = s.ComputeRolesPermissions(ctx, loginUser.WithRoles)
	return
}

func (s *sSysUser) CheckFormPermission(ctx context.Context, formId int64, permissionName enum.SysPermission) (result bool) {
	result, _ = s.CheckPermission(ctx, nil, enum.SysPermissionType_Form, formId, permissionName)
	return
}
func (s *sSysUser) CheckMenuPermission(ctx context.Context, mark enum.SysPermissionMark, permissionName enum.SysPermission) (result bool) {
	result, _ = s.CheckPermission(ctx, nil, enum.SysPermissionType_Menu, mark.String(), permissionName)
	return
}

// 验证用户是否拥有某个功能权限
func (s *sSysUser) CheckPermission(ctx context.Context, permissions []*dto.SysPermissions, permissionType enum.SysPermissionType, permissionKey interface{}, permissionName enum.SysPermission) (result bool, err error) {
	result = false
	if permissions == nil {
		var cacheValue *gvar.Var
		cacheValue, err = service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.UserSysPermissions, library.GetUserId(ctx))), func(ctx context.Context) (value interface{}, err error) {
			value, err = s.ComputeUserPermissions(ctx)
			return
		}, time.Second*60)
		if err != nil {
			return
		}
		err = gconv.Struct(cacheValue.Val(), &permissions)
		if err != nil {
			return
		}
	}

	permission, hasPermission := lo.Find(permissions, func(item *dto.SysPermissions) bool {
		if permissionType == enum.SysPermissionType_Menu {
			switch value := permissionKey.(type) {
			case string:
				return strings.TrimSpace(item.Mark) == strings.TrimSpace(value)
			default:
				return item.MenuId == gconv.Int64(permissionKey)
			}
		}
		return item.FormId == gconv.Int64(permissionKey)
	})
	if !hasPermission || permission.Permission == nil {
		return
	}

	permissionValue, hasPermissionValue := permission.Permission[string(permissionName)]
	if !hasPermissionValue || !permissionValue {
		return
	}
	result = true
	return
}

// 计算角色的功能权限
func (s *sSysUser) ComputeRolesPermissions(ctx context.Context, roleIds []int64) (permissions []*dto.SysPermissions, err error) {
	for _, roleId := range roleIds {
		menuInfo, err := service.SysRole().GetInfoByID(ctx, gconv.Uint64(roleId))
		if err != nil || menuInfo == nil {
			return permissions, err
		}
		var rolePermissions []dto.SysPermissions
		jsonErr := json.Unmarshal([]byte(menuInfo.Permissions), &rolePermissions)
		if jsonErr != nil {
			continue
		}

		for _, rp := range rolePermissions {
			rolePermission := rp
			_, permissionIndex, hasPermission := lo.FindIndexOf(permissions, func(item *dto.SysPermissions) bool {
				if rolePermission.Type == string(enum.SysPermissionType_Menu) {
					return rolePermission.MenuId == item.MenuId
				}
				return rolePermission.FormId == item.FormId
			})
			if !hasPermission {
				permissions = append(permissions, &rolePermission)
			} else {
				if rolePermission.Permission == nil {
					continue
				}

				oldPermission := permissions[permissionIndex]
				tempPermission := *oldPermission
				tempPermission.Permission = make(map[string]bool)
				for k, v := range oldPermission.Permission {
					tempPermission.Permission[k] = v
				}

				if oldPermission.Permission == nil {
					oldPermission.Permission = make(map[string]bool, 0)
				}
				for key, value := range rolePermission.Permission {
					if value {
						oldPermission.Permission[key] = value
					}
				}

				if view, hasView := rolePermission.Permission[enum.SysPermission_View.String()]; !hasView || !view {
					continue
				}

				// 没有设置字段权限，代表拥有所有字段权限
				if view, hasView := tempPermission.Permission[enum.SysPermission_View.String()]; hasView && view && (tempPermission.FieldPermission == nil || len(tempPermission.FieldPermission) == 0) {
					continue
				}

				if rolePermission.FieldPermission == nil || len(rolePermission.FieldPermission) == 0 {
					oldPermission.FieldPermission = nil
					continue
				}

				for _, field := range rolePermission.FieldPermission {
					_, oldPermissionIndex, _ := lo.FindIndexOf(oldPermission.FieldPermission, func(item dto.FieldPermission) bool {
						return item.Field == field.Field
					})
					if oldPermissionIndex == -1 {
						oldPermission.FieldPermission = append(oldPermission.FieldPermission, field)
					} else {
						oldPermission.FieldPermission[oldPermissionIndex].HasPermission = true
					}
				}
			}
		}

	}
	return
}

// 构造前端需要的CurrentUser
func (s *sSysUser) GetCurrentUser(ctx context.Context) (currentUser *system.CurrentSysUserRes, err error) {
	loginUser := s.GetLoginUser(ctx)
	if loginUser == nil {
		return nil, gerror.NewCode(consts.CodeNoLoginUserInfo)
	}

	userDetail, err := s.GetInfoByID(ctx, loginUser.Id)
	if err != nil {
		return
	}

	currentUser = &system.CurrentSysUserRes{}
	err = gconv.Struct(userDetail, currentUser)
	if err != nil {
		return
	}
	sysPermissions, err := s.ComputeUserPermissions(ctx)
	if err != nil {
		return
	}
	userMenus, err := service.SysMenu().GetListCache(ctx)
	if err != nil {
		return
	}
	// 过滤掉没有权限的菜单
	userMenus = lo.Filter(userMenus, func(item *with.SysMenu, _ int) bool {
		if item.RouteType != 999 {
			hasPermission, err := s.CheckPermission(ctx, sysPermissions, enum.SysPermissionType_Menu, item.Id, enum.SysPermission_Menu)
			if err != nil || !hasPermission {
				return false
			}
		}
		return true
	})
	// 过滤掉是菜单分组，并且没有子菜单的菜单
	userMenus = lo.Filter(userMenus, func(item *with.SysMenu, _ int) bool {
		if item.RouteType == 999 && lo.CountBy(userMenus, func(subItem *with.SysMenu) bool {
			return subItem.ParentId == item.Id
		}) <= 0 {
			return false
		}
		return true
	})

	gconv.Struct(userMenus, &currentUser.ApiAssociation)
	currentUser.Name = userDetail.Username
	currentUser.Access = "admin"

	currentUser.SysPermissions = sysPermissions

	service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, fmt.Sprintf("%s%d", consts.UserSysPermissions, library.GetUserId(ctx))))
	return

}

// 获得岗位关联的用户id列表
func (s *sSysUser) GetPostUserIds(ctx context.Context, postIds []int64) (userIds []int64, err error) {
	var results []entity.SysUserPost
	err = dao.TenantCtx(dao.SysUserPost, ctx).WhereIn(dao.SysUserPost.Columns().PostId, postIds).Fields(dao.SysUserPost.Columns().UserId).Distinct().Scan(&results)
	if err != nil {
		return
	}
	userIds = lo.Map(results, func(item entity.SysUserPost, _ int) int64 {
		return item.UserId
	})
	return
}

// 获得部门关联的用户id列表
func (s *sSysUser) GetDeptUserIds(ctx context.Context, deptIds []int64) (userIds []int64, err error) {
	var results []entity.SysUserDept
	err = dao.TenantCtx(dao.SysUserDept, ctx).WhereIn(dao.SysUserDept.Columns().DeptId, deptIds).Fields(dao.SysUserDept.Columns().UserId).Distinct().Scan(&results)
	if err != nil {
		return
	}
	userIds = lo.Map(results, func(item entity.SysUserDept, _ int) int64 {
		return item.UserId
	})
	return
}

// 获得当前用户所管理的部门id列表
func (s *sSysUser) GetLeaderDeptIds(ctx context.Context, userId int64) (deptIds []int64, err error) {
	var results []entity.SysDept
	err = dao.TenantCtx(dao.SysDept, ctx).Where(dao.SysDept.Columns().Leader, userId).Fields(dao.SysDept.Columns().Id).Distinct().Scan(&results)
	if err != nil {
		return
	}
	deptIds = lo.Map(results, func(item entity.SysDept, _ int) int64 {
		return item.Id
	})
	return
}

// 获得当前用户所管理的项目id列表
func (s *sSysUser) GetLeaderProjectIds(ctx context.Context, userId int64) (deptIds []int64, err error) {
	var results []entity.AdProject
	err = dao.TenantCtx(dao.AdProject, ctx).Where(dao.AdProject.Columns().Leader, userId).Fields(dao.AdProject.Columns().Id).Distinct().Scan(&results)
	if err != nil {
		return
	}
	deptIds = lo.Map(results, func(item entity.AdProject, _ int) int64 {
		return int64(item.Id)
	})
	return
}

// GetRoleProjectIds 获得角色关联的项目id列表
func (s *sSysUser) GetRoleProjectIds(ctx context.Context, userId int64, roleIds []int64) (projectIds []int64, err error) {
	var results []entity.SysUserProject
	err = dao.TenantCtx(dao.SysUserProject, ctx).Where(dao.SysUserProject.Columns().UserId, userId).WhereIn(dao.SysUserProject.Columns().ProjectRoleId, roleIds).Fields(dao.SysUserProject.Columns().ProjectId).Distinct().Scan(&results)
	if err != nil {
		return
	}
	projectIds = lo.Map(results, func(item entity.SysUserProject, _ int) int64 {
		return item.ProjectId
	})
	return
}

// GetUserProperty 获取用户属性值（用于参数绑定等场景）
func (s *sSysUser) GetUserProperty(ctx context.Context, loginUser *dto.UserClaims, property string) interface{} {
	if loginUser == nil || property == "" {
		return nil
	}

	switch property {
	case "user_id":
		return loginUser.Id
	case "username":
		return loginUser.Username
	case "dept_id":
		if len(loginUser.WithDepts) > 0 {
			return loginUser.WithDepts[0]
		}
		return nil
	case "dept_ids":
		return loginUser.WithDepts
	case "post_ids":
		return loginUser.WithPosts
	case "role_codes":
		return loginUser.WithRolesName
	case "phonenumber", "email":
		userInfo, err := service.SysUser().GetInfoByID(ctx, loginUser.Id)
		if err != nil {
			return nil
		}
		if property == "phonenumber" {
			return userInfo.ContactPhone
		}
		return userInfo.Email
	default:
		return nil
	}
}
