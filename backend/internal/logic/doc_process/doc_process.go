package docprocess

import "backend/internal/service"

func init() {
	service.RegisterDocProcess(new(sDocProcess))
}

type sDocProcess struct {
}

// 创建流程实例
func (s *sDocProcess) CreateProcessInstance(processID int, creator string, params map[string]interface{}) (processInstanceID int, err error) {
	return
}

// 进入下一个流程节点
func (s *sDocProcess) GoNextNode(processInstanceID int, operator string, params map[string]interface{}) (nodeID int, err error) {
	return
}

// 返回上一个流程节点
func (s *sDocProcess) GoPrevNode(processInstanceID int, operator string, params map[string]interface{}) (nodeID int, err error) {
	return

}

// 节点操作
func (s *sDocProcess) NodeOperation(nodeID int, operation string, params map[string]interface{}) (err error) {
	// 判定节点类型： 开始节点、审批节点、抄送节点

	return
}

// 创建or修改流程模板
func (s *sDocProcess) SaveProcessTemplate(templateName string, creator string, params map[string]interface{}) (processTemplateID int, err error) {
	return

}
