package logic

import (
	"backend/internal/consts"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"context"
	"fmt"
	"regexp"
	"sync"
	"time"

	"github.com/aid<PERSON><PERSON>ov/nanoid/v2"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	ctx := gctx.New()
	service.RegisterCache(NewCache(ctx))
}

type sCache struct {
	*gcache.Cache
	redis *gredis.Redis
}

func NewCache(ctx context.Context) *sCache {
	result := &sCache{}

	cache := gcache.New()

	model := g.Cfg().MustGet(ctx, "system.cache.model").String()
	if model == "redis" {
		redis := g.Redis()
		cache.SetAdapter(gcache.NewAdapterRedis(redis))
		result.redis = redis
	}
	result.Cache = cache
	return result
}

// // 将配置表数据读入缓存
// func (c *sCache) InitConfig(ctx context.Context) (err error) {
// 	configList, err := service.SysConfig().GetAllConfig(ctx)
// 	if err != nil {
// 		return
// 	}
// 	for _, config := range configList {
// 		cache_key := fmt.Sprintf("%s_%d", config.ConfigKey, config.TenantId)
// 		// 以redis为准，如果redis没有该key，则写入redis
// 		hasKey, _ := c.Contains(ctx, cache_key)
// 		if !hasKey {
// 			c.Set(ctx, cache_key, config.ConfigValue, 0)
// 		}
// 	}
// 	return
// }

func (c *sCache) InitConfig(ctx context.Context) error {
	configList, err := service.SysConfig().GetAllConfig(ctx)
	if err != nil {
		return err
	}

	var wg sync.WaitGroup
	errChan := make(chan error, len(configList))

	for _, config := range configList {
		wg.Add(1)
		go func(config entity.SysConfig) {
			defer wg.Done()
			cacheKey := fmt.Sprintf("%s_%d", config.ConfigKey, config.TenantId)
			// 以redis为准，如果redis没有该key，则写入redis
			if hasKey, _ := c.Contains(ctx, cacheKey); !hasKey {
				if err := c.Set(ctx, cacheKey, config.ConfigValue, 0); err != nil {
					errChan <- fmt.Errorf("failed to set cache for key %s: %w", cacheKey, err)
				}
			}
		}(*config)
	}

	wg.Wait()
	close(errChan)

	if len(errChan) > 0 {
		return <-errChan
	}
	return nil
}

// 获取下一个自增id
func (c *sCache) GetNextId(ctx context.Context, key string) (id int64, err error) {
	if c.redis == nil {
		err = fmt.Errorf("redis is not set")
		return
	}
	key = fmt.Sprintf("NextId_%s", key)

	id, err = c.redis.Incr(ctx, library.GetTenantCacheKey(ctx, key))
	g.Log().Info(ctx, "key:"+key)

	// 异步更新数据库
	if err == nil {
		config := &entity.SysConfig{
			ConfigKey:   key,
			ConfigValue: fmt.Sprintf("%d", id),
			Version:     id,
			TenantId:    int64(library.GetTenantID(ctx)),
		}

		err = queue.QueueManager().Publish(consts.StreamSubjectConfigUpdate, config)
	}
	return
}

func (c *sCache) GetNextIdString(ctx context.Context, key string, n int) (id string, err error) {
	_id, err := c.GetNextId(ctx, key)
	id = fmt.Sprintf("%d", _id)
	if n > 0 {
		id = fmt.Sprintf("%0"+fmt.Sprintf("%d", n)+"d", _id)
	}
	return
}

// SAdd 添加values 到指定的 SET 中
func (c *sCache) SAdd(ctx context.Context, key string, values ...interface{}) error {
	if c.redis == nil {
		return fmt.Errorf("redis is not set")
	}
	_, err := c.redis.SAdd(ctx, key, nil, values...)
	if err != nil {
		return fmt.Errorf("SADD 错误: %v", err)
	}
	return nil
}

// SIsMember 检查指定的value 是否存在于 SET 中
func (c *sCache) SIsMember(ctx context.Context, key string, value interface{}) (int64, error) {
	if c.redis == nil {
		return 0, fmt.Errorf("redis is not set")
	}
	exists, err := c.redis.SIsMember(ctx, key, value)
	if err != nil {
		return 0, fmt.Errorf("SISMEMBER 错误: %v", err)
	}
	return exists, nil
}

// SMembers 获取 SET 中的所有value
func (c *sCache) SMembers(ctx context.Context, key string) (gvar.Vars, error) {
	if c.redis == nil {
		return nil, fmt.Errorf("redis is not set")
	}
	values, err := c.redis.SMembers(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("SMEMBERS 错误: %v", err)
	}
	return values, nil
}

// SRem 从 SET 中移除指定的value
func (c *sCache) SRem(ctx context.Context, key string, value interface{}) error {
	if c.redis == nil {
		return fmt.Errorf("redis is not set")
	}
	_, err := c.redis.SRem(ctx, key, value)
	if err != nil {
		return fmt.Errorf("SREM 错误: %v", err)
	}
	return nil
}

// IncrBy 增加指定的value
func (c *sCache) IncrBy(ctx context.Context, key string, value interface{}) (result interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if c.redis == nil {
			err = fmt.Errorf("redis is not set")
			return
		}
		key = fmt.Sprintf("IncrBy_%s", key)

		isFloat := regexp.MustCompile(`^-?\d+\.\d+$`)
		if isFloat.MatchString(fmt.Sprint(value)) {
			result, err = c.redis.IncrByFloat(ctx, library.GetTenantCacheKey(ctx, key), gconv.Float64(value))
		} else {
			valueInt := gconv.Int64(value)
			result, err = c.redis.IncrBy(ctx, library.GetTenantCacheKey(ctx, key), valueInt)
		}
		library.ErrIsNil(ctx, err)
		// 异步更新数据库
		config := &entity.SysConfig{
			ConfigKey:   key,
			ConfigValue: fmt.Sprintf("%v", result),
			Version:     gconv.Int64(result),
			TenantId:    int64(library.GetTenantID(ctx)),
		}

		err = queue.QueueManager().Publish(consts.StreamSubjectConfigUpdate, config)
	})
	return
}

// IncrByFloat 增加指定的value
func (c *sCache) IncrByFloat(ctx context.Context, key string, value interface{}) (result interface{}, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if c.redis == nil {
			err = fmt.Errorf("redis is not set")
			return
		}
		key = fmt.Sprintf("IncrByFloat_%s", key)

		result, err = c.redis.IncrByFloat(ctx, library.GetTenantCacheKey(ctx, key), gconv.Float64(value))
		library.ErrIsNil(ctx, err)
		// 异步更新数据库
		config := &entity.SysConfig{
			ConfigKey:   key,
			ConfigValue: fmt.Sprintf("%v", result),
			Version:     gconv.Int64(result),
			TenantId:    int64(library.GetTenantID(ctx)),
		}

		err = queue.QueueManager().Publish(consts.StreamSubjectConfigUpdate, config)
	})
	return
}

const (
	lockKeyPrefix = "dlock:"        // 分布式锁的key前缀
	watchDogTime  = 3 * time.Second // 看门狗检查时间间隔
	maxRenewals   = 100             // 最大续期次数，比如 100 次 * 5秒 = 500秒后强制释放
	minLockTTL    = 5               // 最小锁过期时间（秒）
)

// TryLock 尝试获取分布式锁，返回锁ID
func (c *sCache) TryLock(ctx context.Context, key string, ttl int) (string, error) {
	if c.redis == nil {
		return "", fmt.Errorf("redis is not set")
	}

	// 确保 TTL 不小于最小值
	if ttl < minLockTTL {
		ttl = minLockTTL
	}

	lockKey := fmt.Sprintf("%s%s", lockKeyPrefix, key)
	lockId, _ := nanoid.New() // 生成锁ID
	duration := time.Duration(ttl) * time.Second

	// 使用 SET 命令配合 NX 和 EX 选项来设置锁
	success, err := c.redis.Do(ctx, "SET", lockKey, lockId, "NX", "EX", ttl)
	if err != nil {
		return "", fmt.Errorf("获取锁失败: %v", err)
	}

	if success == nil {
		return "", fmt.Errorf("锁已被占用")
	}

	// 启动自动续期
	go c.watchDog(ctx, lockKey, lockId, duration)

	return lockId, nil
}

// watchDog 自动续期处理
func (c *sCache) watchDog(ctx context.Context, lockKey string, lockId string, duration time.Duration) {
	ticker := time.NewTicker(watchDogTime)
	defer ticker.Stop()

	renewalCount := 0

	for {
		select {
		case <-ticker.C:
			// 超过最大续期次数，停止续期
			if renewalCount >= maxRenewals {
				g.Log().Warningf(ctx, "锁 %s 已达到最大续期次数限制", lockKey)
				return
			}

			// 检查是否仍持有锁
			val, err := c.redis.Get(ctx, lockKey)
			if err != nil || val.String() != lockId {
				return
			}

			// 续期
			_, err = c.redis.Do(ctx, "EXPIRE", lockKey, int(duration.Seconds()))
			if err != nil {
				g.Log().Errorf(ctx, "锁续期失败: %v", err)
				return
			}

			renewalCount++

		case <-ctx.Done():
			return
		}
	}
}

// UnLock 释放锁
func (c *sCache) UnLock(ctx context.Context, key string, lockId string) error {
	if c.redis == nil {
		return fmt.Errorf("redis is not set")
	}

	lockKey := fmt.Sprintf("%s%s", lockKeyPrefix, key)

	// Lua脚本：确保只能释放自己的锁
	script := `
		if redis.call('get', KEYS[1]) == ARGV[1] then
			return redis.call('del', KEYS[1])
		end
		return 0
	`

	_, err := c.redis.Do(ctx, "EVAL", script, 1, lockKey, lockId)
	if err != nil {
		return fmt.Errorf("释放锁失败: %v", err)
	}

	return nil
}

// IsLocked 检查指定key是否已被锁定
func (c *sCache) IsLocked(ctx context.Context, key string) (bool, error) {
	if c.redis == nil {
		return false, fmt.Errorf("redis is not set")
	}

	lockKey := fmt.Sprintf("%s%s", lockKeyPrefix, key)
	exists, err := c.redis.Exists(ctx, lockKey)
	if err != nil {
		return false, fmt.Errorf("检查锁状态失败: %v", err)
	}

	return exists == 1, nil
}
