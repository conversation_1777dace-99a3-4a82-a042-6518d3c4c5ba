package formExporttemplate

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFormExportTemplate(new(sFormExportTemplate))
}

type sFormExportTemplate struct{}

// 获取导出模板列表
func (s *sFormExportTemplate) GetFormExportTemplateList(ctx context.Context, req *form.FormExportTemplateListReq) (res *form.FormExportTemplateListRes, err error) {
	res = new(form.FormExportTemplateListRes)
	m := dao.TenantCtx(dao.FormExportTemplate, ctx)
	m = m.Where(dao.FormExportTemplate.Columns().FormTemplateId, req.FormTemplateId)
	if req.Name != "" {
		m = m.Where(dao.FormExportTemplate.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取导出模板总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取导出模板数据失败")
	})
	return
}

// 获取启用导出模板列表
func (s *sFormExportTemplate) GetEnabledRules(ctx context.Context, formTemplateId int64) (res []*entity.FormExportTemplate, err error) {
	m := dao.TenantCtx(dao.FormExportTemplate, ctx)
	m = m.Where(dao.FormExportTemplate.Columns().FormTemplateId, formTemplateId)
	m = m.Where(dao.FormExportTemplate.Columns().Status, 1)
	order := "id asc"
	err = m.Order(order).Scan(&res)
	return
}

// 保存导出模板数据
func (s *sFormExportTemplate) Save(ctx context.Context, req *form.FormExportTemplateSaveReq) (res *form.FormExportTemplateSaveRes, err error) {

	res = new(form.FormExportTemplateSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {

		formExportTemplate := new(do.FormExportTemplate)

		if len(req.FieldMapping) <= 0 {
			library.Fail(ctx, "字段映射不能为空")
			return
		}

		if req.Id > 0 {
			var editFormExportTemplate *entity.FormExportTemplate
			editFormExportTemplate, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editFormExportTemplate, "导出模板信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			formExportTemplate.CreatedBy = library.GetUserId(ctx)
			formExportTemplate.Status = 1
		}
		formExportTemplate.Name = req.Name
		formExportTemplate.FormTemplateId = req.FormTemplateId
		formExportTemplate.FieldMapping = req.FieldMapping

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.FormExportTemplate, ctx).WherePri(req.Id).Update(formExportTemplate)
			library.ErrIsNil(ctx, err, "修改导出模板失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.FormExportTemplate, ctx).Data(formExportTemplate).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入导出模板失败")
			res.Id = newid
		}

	})
	return

}

// 删除导出模板
func (s *sFormExportTemplate) Delete(ctx context.Context, req *form.FormExportTemplateDeleteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(req.Id), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "导出模板不存在")
			return
		}
		_, err = dao.TenantCtx(dao.FormExportTemplate, ctx).WherePri(req.Id).Delete()
		library.ErrIsNil(ctx, err, "删除导出模板失败")

	})
	return
}

// 获得详细信息
func (s *sFormExportTemplate) GetInfoByID(ctx context.Context, automationId uint64, with bool) (formExportTemplate *entity.FormExportTemplate, err error) {
	m := dao.TenantCtx(dao.FormExportTemplate, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&formExportTemplate)
	return
}

// 修改状态
func (s *sFormExportTemplate) UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(automationId), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "导出模板不存在")
			return
		}

		_, err = dao.TenantCtx(dao.FormExportTemplate, ctx).WherePri(automationId).Update(&do.FormExportTemplate{Status: lo.Ternary(enabled, 1, 999)})
		if err != nil {
			g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
			library.Fail(ctx, err.Error())
		}

	})
	return
}
