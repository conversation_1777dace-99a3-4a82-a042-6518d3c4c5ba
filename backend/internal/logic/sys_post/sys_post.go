package SysPost

import (
	"backend/api/v1/system"
	"backend/internal/consts"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/gogf/gf/text/gstr"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterSysPost(new(sSysPost))
}

type sSysPost struct {
}

// 根据条件获取岗位列表
func (s *sSysPost) GetList(ctx context.Context, req *system.PostSearchReq) (list []*with.SysPost, err error) {
	g.Log().Info(ctx, fmt.Sprintf("req:%+v", req))

	list, err = s.GetListCache(ctx)
	if err != nil {
		return
	}
	filters := make([]library.FilterFunc[*with.SysPost], 0)
	filters = append(filters, func(item *with.SysPost) bool {
		if req.PostName != "" && !gstr.ContainsI(item.PostName, req.PostName) {
			return false
		}
		return true
	})
	filters = append(filters, func(item *with.SysPost) bool {
		if req.PostCode != "" && !gstr.ContainsI(item.PostCode, req.PostCode) {
			return false
		}
		return true
	})
	filters = append(filters, func(item *with.SysPost) bool {
		createAt := req.CreatedAt
		switch t := createAt.(type) {
		case []interface{}:
			// 时间区间判断
			if len(t) != 2 {
				return false
			}

			startTime, err := gtime.StrToTime(gconv.String(t[0]))
			if err != nil {
				return false
			}
			endTime, err := gtime.StrToTime(gconv.String(t[1]))
			if err != nil {
				return false
			}

			// 结束时间修改为每天的结束
			if endTime.Time.Format("15:04:05") == "00:00:00" {
				endTime = endTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			}
			// g.Log().Info(ctx, fmt.Sprintf("startTime:%+v, endTime:%+v", startTime, endTime))
			itemTime := item.CreatedAt.Time
			if !itemTime.After(startTime.Time) || !itemTime.Before(endTime.Time) {
				return false
			}
			return true
		}
		return true
	})

	list = library.FilterList(list, filters)
	// 根据节点路径排序
	sort.Slice(list, func(i, j int) bool {
		return fmt.Sprintf("%s%d", list[i].NodePath, list[i].Id) < fmt.Sprintf("%s%d", list[j].NodePath, list[j].Id)

	})
	return
}

// 获取所有岗位列表，岗位数据一般不多，可以从缓存中获取
func (s *sSysPost) GetListCache(ctx context.Context) (list []*with.SysPost, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		cList, err := service.Cache().GetOrSetFunc(ctx, library.GetTenantCacheKey(ctx, consts.KeyPostList), func(ctx context.Context) (value interface{}, err error) {
			err = dao.TenantCtx(dao.SysPost, ctx).WithAll().Scan(&list)
			if err != nil {
				return
			}
			value = list
			return
		}, consts.DefaultCacheExpireTime)
		library.ErrIsNil(ctx, err)
		if cList != nil {
			err := gconv.Struct(cList, &list)
			library.ErrIsNil(ctx, err)
		}
	})
	return
}

// Save 岗位保存
func (s *sSysPost) Save(ctx context.Context, req *system.PostSaveReq) (err error) {
	// 获得岗位树节点路径
	err = g.Try(ctx, func(ctx context.Context) {

		postDto := &do.SysPost{
			Id:        req.PostID,
			NodePath:  s.GetNodePath(ctx, req.ParentID),
			ParentId:  req.ParentID,
			PostName:  req.PostName,
			ListOrder: req.ListOrder,
			WithRoles: req.WithRoles,
			Remark:    req.Remark,
		}
		id := gconv.Int64(req.PostID)
		if req.PostID <= 0 {
			id, err = s.Add(ctx, postDto)
		} else {
			var oldPost *with.SysPost
			oldPost, err = s.GetInfoByID(ctx, int64(req.PostID))
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(oldPost, "岗位不存在")
			err = s.Edit(ctx, postDto)
		}
		library.ErrIsNil(ctx, err)

		// 修改关联关系
		dao.TenantCtx(dao.SysPostRole, ctx).Where(dao.SysPostRole.Columns().PostId+" = ?", id).Delete()
		if len(req.WithRoles) > 0 {
			for _, roleId := range req.WithRoles {
				_, err = dao.TenantCtx(dao.SysPostRole, ctx).Insert(do.SysPostRole{
					PostId: id,
					RoleId: roleId,
				})
				library.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// GetNodePath 获得岗位树节点路径
func (s *sSysPost) GetNodePath(ctx context.Context, parentId uint64) (nodePath string) {
	if parentId > 0 {
		parentInfo, err := s.GetInfoByID(ctx, gconv.Int64(parentId))
		if err == nil && parentInfo != nil {
			nodePath = parentInfo.NodePath
		}
	}
	nodePath = fmt.Sprintf("%s%d,", nodePath, parentId)
	return
}

// Add 添加岗位
func (s *sSysPost) Add(ctx context.Context, req *do.SysPost) (lastid int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		postCode := library.GenerateID("R{20060102150406}{RanNum(8)}", 0)
		lastid, err = dao.TenantCtx(dao.SysPost, ctx).InsertAndGetId(do.SysPost{
			PostCode:  postCode,
			NodePath:  req.NodePath,
			ParentId:  req.ParentId,
			PostName:  req.PostName,
			ListOrder: req.ListOrder,
			WithRoles: req.WithRoles,
			Remark:    req.Remark,
			CreatedBy: library.GetUserId(ctx),
		})
		library.ErrIsNil(ctx, err, "添加岗位失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyPostList))
	})
	return
}

// Edit 岗位修改
func (s *sSysPost) Edit(ctx context.Context, req *do.SysPost) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.SysPost, ctx).WherePri(req.Id).Update(do.SysPost{
			NodePath:  req.NodePath,
			ParentId:  req.ParentId,
			PostName:  req.PostName,
			ListOrder: req.ListOrder,
			WithRoles: req.WithRoles,
			Remark:    req.Remark,
		})
		library.ErrIsNil(ctx, err, "修改岗位失败")
		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyPostList))
	})
	return
}

// Delete 删除岗位
func (s *sSysPost) Delete(ctx context.Context, postId int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []*with.SysPost
		list, err = s.GetListCache(ctx)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(list, "无岗位数据")
		// 获得子岗位
		g.Log().Info(ctx, "postId:"+gconv.String(postId))
		subList := s.GetSubPost(list, postId)
		ids := make([]int64, 0)
		for _, post := range subList {
			ids = append(ids, post.Id)
		}
		ids = append(ids, postId)

		_, err = dao.TenantCtx(dao.SysPost, ctx).Where(dao.SysPost.Columns().Id+" IN (?)", ids).Delete()
		library.ErrIsNil(ctx, err, "删除岗位失败")

		// 删除缓存
		service.Cache().Remove(ctx, library.GetTenantCacheKey(ctx, consts.KeyPostList))
	})
	return
}

// 递归获取所有岗位和子岗位的id
func (s *sSysPost) GetPostIds(ctx context.Context, postId int64) (ids []int64, err error) {
	list, err := s.GetListCache(ctx)
	if err != nil {
		return
	}
	subList := s.GetSubPost(list, postId)
	for _, post := range subList {
		ids = append(ids, post.Id)
	}
	ids = append(ids, postId)
	return
}

// 递归获得所有子岗位
func (s *sSysPost) GetSubPost(list []*with.SysPost, postId int64) (subList []*with.SysPost) {
	subList = make([]*with.SysPost, 0)
	for _, post := range list {
		if post.ParentId == postId {
			subList = append(subList, post)
			_subList := s.GetSubPost(list, post.Id)
			subList = append(subList, _subList...)
		}
	}
	return
}

// 根据ID获取岗位信息
func (s *sSysPost) GetInfoByID(ctx context.Context, postId int64) (post *with.SysPost, err error) {
	list, err := s.GetListCache(ctx)

	if err != nil {
		return
	}

	post = library.FindOne(list, []library.FilterFunc[*with.SysPost]{
		func(item *with.SysPost) bool {
			return item.Id == postId
		},
	})
	return
}

// 根据ids获取岗位列表
func (s *sSysPost) GetPostListByIDs(ctx context.Context, ids []int64) (list []*with.SysPost, err error) {
	allList, err := s.GetListCache(ctx)
	if err != nil {
		return
	}
	list = lo.Filter(allList, func(item *with.SysPost, _ int) bool {
		return lo.Contains(ids, item.Id)
	})
	return
}
