package projectrole

import (
	"context"

	"backend/api/v1/project"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterProjectRole(new(sProjectRole))
}

type sProjectRole struct{}

// 获取角色列表
func (s *sProjectRole) GetProjectRoleList(ctx context.Context, req *project.AdProjectRoleListReq) (res *project.AdProjectRoleListRes, err error) {
	res = new(project.AdProjectRoleListRes)
	m := dao.TenantCtx(dao.AdProjectRole, ctx)
	if req.Name != "" {
		m = m.Where(dao.AdProjectRole.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取角色总数失败")
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List) //.Fields(dao.SysProjectRole.Columns())
		library.ErrIsNil(ctx, err, "获取角色数据失败")
	})
	return
}

// 保存角色数据
func (s *sProjectRole) Save(ctx context.Context, req *project.AdProjectRoleSaveReq) (res *project.AdProjectRoleSaveRes, err error) {

	res = new(project.AdProjectRoleSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {
		ProjectRole := new(do.AdProjectRole)
		if req.Id > 0 {
			var editProjectRole *entity.AdProjectRole
			editProjectRole, err = s.GetInfoByID(ctx, uint64(req.Id))
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editProjectRole, "角色信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			ProjectRole.Code = service.Generate().GenerateID(ctx, "PROR-{AUTO_INCREMENT({ProjectRole-20060102},8)}")
			ProjectRole.CreatedBy = library.GetUserId(ctx)
		}
		ProjectRole.Name = req.Name
		ProjectRole.IsDefault = req.IsDefault
		ProjectRole.Instruction = req.Instruction

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.AdProjectRole, ctx).WherePri(req.Id).Update(ProjectRole)
			library.ErrIsNil(ctx, err, "修改角色失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.AdProjectRole, ctx).Data(ProjectRole).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入角色失败")
			res.Id = newid
		}
		// 清除角色列表缓存
	})
	return

}

// 删除角色
func (s *sProjectRole) Delete(ctx context.Context, req *project.AdProjectRoleDeleteReq) (err error) {
	_, err = dao.TenantCtx(dao.AdProjectRole, ctx).WherePri(req.Id).Delete()
	library.ErrIsNil(ctx, err, "删除角色失败")
	// 清除角色列表缓存
	return
}

// 获得详细信息
func (s *sProjectRole) GetInfoByID(ctx context.Context, projectRoleId uint64) (ProjectRole *entity.AdProjectRole, err error) {
	err = dao.TenantCtx(dao.AdProjectRole, ctx).WherePri(projectRoleId).Scan(&ProjectRole)
	return
}

// 获得最大ID
func (s *sProjectRole) GetMaxID(ctx context.Context) (maxId uint64, err error) {
	_maxid, err := dao.TenantCtx(dao.AdProjectRole, ctx).Max(dao.AdProjectRole.Columns().Id)
	if err != nil {
		return
	}
	maxId = gconv.Uint64(_maxid)
	return
}
