package project

import (
	"context"

	"backend/api/v1/project"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/with"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterProject(new(sProject))
}

type sProject struct{}

// 获取项目列表
func (s *sProject) GetProjectList(ctx context.Context, req *project.AdProjectListReq) (res *project.AdProjectListRes, err error) {
	res = new(project.AdProjectListRes)
	m := dao.TenantCtx(dao.AdProject, ctx)
	if req.Name != "" {
		m = m.Where(dao.AdProject.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取项目总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List) //.Fields(dao.SysProject.Columns())
		library.ErrIsNil(ctx, err, "获取项目数据失败")
	})
	return
}

// 保存项目数据
func (s *sProject) Save(ctx context.Context, req *project.AdProjectSaveReq) (res *project.AdProjectSaveRes, err error) {

	res = new(project.AdProjectSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {
		Project := new(do.AdProject)
		if req.Id > 0 {
			var editProject *with.AdProject
			editProject, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editProject, "项目信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			Project.Code = service.Generate().GenerateID(ctx, "PRO-{AUTO_INCREMENT({Project-20060102},8)}")
			Project.CreatedBy = library.GetUserId(ctx)
		}
		Project.Name = req.Name
		Project.Leader = req.Leader
		Project.Instruction = req.Instruction
		Project.Source = req.Source

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.AdProject, ctx).WherePri(req.Id).Update(Project)
			library.ErrIsNil(ctx, err, "修改项目失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.AdProject, ctx).Data(Project).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入项目失败")
			res.Id = newid
		}
		// 清除项目列表缓存
	})
	return

}

// 删除项目
func (s *sProject) Delete(ctx context.Context, req *project.AdProjectDeleteReq) (err error) {
	_, err = dao.TenantCtx(dao.AdProject, ctx).WherePri(req.Id).Delete()
	library.ErrIsNil(ctx, err, "删除项目失败")
	// 清除项目列表缓存
	return
}

// 获得详细信息
func (s *sProject) GetInfoByID(ctx context.Context, projectId uint64, with bool) (Project *with.AdProject, err error) {
	m := dao.TenantCtx(dao.AdProject, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(projectId).Scan(&Project)
	return
}

// 根据项目名获取项目信息
func (s *sProject) GetProjectByName(ctx context.Context, name string) (Project *with.AdProject, err error) {
	m := dao.TenantCtx(dao.AdProject, ctx)
	err = m.Where(dao.AdProject.Columns().Name, name).Limit(1).Scan(&Project)
	return
}

// 获得最大ID
func (s *sProject) GetMaxID(ctx context.Context) (maxId uint64, err error) {
	_maxid, err := dao.TenantCtx(dao.AdProject, ctx).Max(dao.AdProject.Columns().Id)
	if err != nil {
		return
	}
	maxId = gconv.Uint64(_maxid)
	return
}
