package flowtemplate

import (
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterFlowTemplate(new(sFlowTemplate))
}

type sFlowTemplate struct {
}

// 保存流程模板
func (s *sFlowTemplate) Save(ctx context.Context, input_id int64, input *do.FlowTemplate) (flow_template_id int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		createUser, err := service.SysUser().GetInfoByID(ctx, int64(library.GetUserId(ctx)))
		library.ErrIsNil(ctx, err, "获得创建人信息失败")

		// 验证流程设置数据格式
		err = s.CheckFlowSchema(ctx, gconv.String(input.FlowSchema), createUser)
		library.ErrIsNil(ctx, err)

		m := dao.TenantCtx(dao.FlowTemplate, ctx)
		if input_id <= 0 {
			input.CreatedBy = createUser.Id
			last_id, err := m.InsertAndGetId(input)
			library.ErrIsNil(ctx, err, "保存流程模板失败")
			flow_template_id = last_id
		} else {
			_, err = dao.TenantCtx(dao.FlowTemplate, ctx).WherePri(input_id).Update(input)
			library.ErrIsNil(ctx, err, "修改流程模板失败")
			flow_template_id = input_id
		}

		// 删除缓存

	})
	return
}

// 验证流程设置数据格式
func (s *sFlowTemplate) CheckFlowSchema(ctx context.Context, input string, createUser *with.SysUser) (err error) {
	var flow_schema dto.FlowSchema
	err = json.Unmarshal([]byte(input), &flow_schema)
	if err != nil {
		g.Log().Info(ctx, library.SDump("CheckFlowSchema err", err))
		return fmt.Errorf("流程设计数据格式不正确")
	}
	// // 必须有一个开始节点和一个结束节点
	// if len(flow_schema.Nodes) < 2 ||
	// 	len(lo.Filter(flow_schema.Nodes, func(node *dto.FlowSchemaNode, _ int) bool { return node.Shape == EnumNodeType.StartNode })) != 1 ||
	// 	len(lo.Filter(flow_schema.Nodes, func(node *dto.FlowSchemaNode, _ int) bool { return node.Shape == EnumNodeType.EndNode })) != 1 {
	// 	err = fmt.Errorf("工作流程必须有一个且只能有一个开始节点和一个结束节点")
	// 	return
	// }

	_, err = service.FlowInstance().GenerateSteps(ctx, &flow_schema, nil, createUser, nil)

	return
}

// 验证附加数据是否符合格式
func (s *sFlowTemplate) ValidateExtraData(ctx context.Context, flowShcema string, extraData *dto.InstanceExtraData) (err error) {
	var flow_schema dto.FlowSchema
	err = json.Unmarshal([]byte(flowShcema), &flow_schema)
	if err != nil {
		g.Log().Info(ctx, library.SDump("ValidateExtraData err", err))
		return fmt.Errorf("流程设计数据格式不正确")
	}
	if len(flow_schema.Nodes) <= 0 {
		return fmt.Errorf("流程设计数据格式不正确,未找到节点")
	}
	failNodes := lo.Filter(flow_schema.Nodes, func(node *dto.FlowSchemaNode, _ int) bool {
		if node.Data != nil && node.Data.Auditor != nil && node.Data.Auditor.Value == enum.AuditorType_ByApplicantChoice && node.Data.Auditor.SelfChoice {
			if extraData == nil || extraData.SelectedApprovers == nil || len(extraData.SelectedApprovers) <= 0 {
				return true
			}
			nodeSelectedApprovers, hasNodeSelectedApprovers := lo.Find(extraData.SelectedApprovers, func(approver *dto.InstanceFlowNodeApprovers) bool {
				return approver.Id == node.Id
			})
			if !hasNodeSelectedApprovers || len(nodeSelectedApprovers.Approver) <= 0 {
				return true
			}
			return false
		}
		return false
	})

	if len(failNodes) > 0 {
		return fmt.Errorf("节点[%s]至少需要自选一个人员", failNodes[0].Data.Label)
	}
	return
}

// 获得流程模板
func (s *sFlowTemplate) GetDetail(ctx context.Context, id int64) (template *entity.FlowTemplate, err error) {
	dao.TenantCtx(dao.FlowTemplate, ctx).WherePri(id).Scan(&template)
	return
}

// 获得流转条件中，受支持的参数列表
func (s *sFlowTemplate) GetSupportedParams(ctx context.Context, form_template_id int64) (result []*dto.FLowEdgeParams, err error) {
	result = make([]*dto.FLowEdgeParams, 0)
	if form_template_id > 0 {
		var form_columns []*dto.FLowEdgeParamColumns
		// 添加表单字段参数
		form_columns, err = s.GetSupportedFormParams(ctx, form_template_id)
		if err != nil {
			return
		}
		result = append(result, &dto.FLowEdgeParams{
			Name:    "fieldsValue",
			Label:   "当前表单",
			Columns: form_columns,
		})
	}
	// 添加当前用户相关参数
	user_columns, err := s.GetSupportedUserParams(ctx)
	if err != nil {
		return
	}
	result = append(result, &dto.FLowEdgeParams{
		Name:    "currentUser",
		Label:   "当前用户",
		Columns: user_columns,
	})
	return
}

// 获得表单模板中，受支持的参数列表
func (s *sFlowTemplate) GetSupportedFormParams(ctx context.Context, form_template_id int64) (result []*dto.FLowEdgeParamColumns, err error) {
	// 获得表单模板信息
	_, form_columns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, form_template_id)
	if err != nil {
		return
	}

	result = make([]*dto.FLowEdgeParamColumns, 0)
	// 遍历表单字段，添加到参数列表中
	for _, column := range form_columns {
		result = append(result, &dto.FLowEdgeParamColumns{
			ColumnName: column.Id,
			Label:      column.Label,
			ValueType:  enum.ParamValueType_Default,
		})
		// 递归遍历子字段
		// g.Log().Info(ctx, library.SDump(column.Children))
		if len(column.Children) > 0 {
			for _, child := range column.Children {
				result = append(result, &dto.FLowEdgeParamColumns{
					ColumnName: fmt.Sprintf("%s.%s", column.Id, child.Id),
					Label:      fmt.Sprintf("%s.%s", column.Label, child.Label),
					ValueType:  enum.ParamValueType_Default,
				})
			}
		}
	}

	return
}

// 获得当前用户相关参数列表
func (s *sFlowTemplate) GetSupportedUserParams(ctx context.Context) (result []*dto.FLowEdgeParamColumns, err error) {
	result = make([]*dto.FLowEdgeParamColumns, 0)
	result = append(result, &dto.FLowEdgeParamColumns{
		ColumnName: "userid",
		Label:      "发起人",
		ValueType:  enum.ParamValueType_User,
	})
	result = append(result, &dto.FLowEdgeParamColumns{
		ColumnName: "depts",
		Label:      "发起人部门",
		ValueType:  enum.ParamValueType_Dept,
	})
	result = append(result, &dto.FLowEdgeParamColumns{
		ColumnName: "posts",
		Label:      "发起人岗位",
		ValueType:  enum.ParamValueType_Post,
	})
	return
}
