package formprint

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"maps"
	"os"
	"os/exec"
	"regexp"
	"strings"

	"backend/api/v1/form"
	"backend/api/v1/print"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/library"

	"backend/internal/service"

	"github.com/aidarkhanov/nanoid/v2"
	"github.com/dop251/goja"
	"github.com/gogf/gf/os/gfile" // This might also need to be v2 if used with v2 types, but gtime is the immediate issue.
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime" // Corrected to v2
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

func init() {
	service.RegisterPrint(new(sPrint))
}

type sPrint struct{}

// 获取打印模版列表
func (s *sPrint) GetPrintTemplateList(ctx context.Context, req *print.ListReq) (res *print.ListRes, err error) {
	res = new(print.ListRes)
	m := dao.TenantCtx(dao.FormPrintTemplate, ctx)
	m = m.Where(dao.FormPrintTemplate.Columns().FormTemplateId, req.FormTemplateId)
	if req.Name != "" {
		m = m.Where(dao.FormPrintTemplate.Columns().Name+" like ?", "%"+req.Name+"%")
	}

	order := "id asc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取打印模版总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List) //.Fields(dao.SysPrint.Columns())
		library.ErrIsNil(ctx, err, "获取打印模版数据失败")
	})
	return
}

// 保存打印模版数据
func (s *sPrint) Save(ctx context.Context, req *print.FormPrintTemplateSaveReq) (res *print.FormPrintTemplateSaveRes, err error) {

	res = new(print.FormPrintTemplateSaveRes)
	err = g.Try(ctx, func(ctx context.Context) {

		PrintTemplate := new(do.FormPrintTemplate)
		if req.Id > 0 {
			var editPrintTemplate *entity.FormPrintTemplate
			editPrintTemplate, err = s.GetInfoByID(ctx, uint64(req.Id), false)
			library.ErrIsNil(ctx, err)
			library.ValueIsNil(editPrintTemplate, "打印模版信息不存在")
		} else {
			library.ErrIsNil(ctx, err)
			PrintTemplate.CreatedBy = library.GetUserId(ctx)
			PrintTemplate.Status = 1
		}
		PrintTemplate.Name = req.Name
		PrintTemplate.FormTemplateId = req.FormTemplateId
		PrintTemplate.TemplateContent = req.TemplateContent
		PrintTemplate.PrintSize = req.PrintSize
		PrintTemplate.PrintRotation = req.PrintRotation
		PrintTemplate.EnterWithData = req.EnterWithData
		PrintTemplate.Padding = req.Padding
		PrintTemplate.Width = req.Width
		PrintTemplate.Height = req.Height
		PrintTemplate.SqlQueries = req.SqlQueries

		if req.Id > 0 {
			_, err := dao.TenantCtx(dao.FormPrintTemplate, ctx).WherePri(req.Id).Update(PrintTemplate)
			library.ErrIsNil(ctx, err, "修改打印模版失败")
			res.Id = req.Id
		} else {
			newid, err := dao.TenantCtx(dao.FormPrintTemplate, ctx).Data(PrintTemplate).InsertAndGetId()
			library.ErrIsNil(ctx, err, "插入打印模版失败")
			res.Id = newid
		}

	})
	return

}

// 删除打印模版
func (s *sPrint) Delete(ctx context.Context, req *print.FormPrintTemplateDeleteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(req.Id), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "配置信息不存在")
			return
		}
		_, err = dao.TenantCtx(dao.FormPrintTemplate, ctx).WherePri(req.Id).Delete()
		library.ErrIsNil(ctx, err, "删除打印模版失败")

	})
	return
}

// 获得详细信息
func (s *sPrint) GetInfoByID(ctx context.Context, automationId uint64, with bool) (PrintTemplate *entity.FormPrintTemplate, err error) {
	m := dao.TenantCtx(dao.FormPrintTemplate, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(automationId).Scan(&PrintTemplate)
	return
}

// 修改状态
func (s *sPrint) UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, uint64(automationId), false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "打印模版不存在")
			return
		}

		_, err = dao.TenantCtx(dao.FormPrintTemplate, ctx).WherePri(automationId).Update(&do.FormPrintTemplate{Status: lo.Ternary(enabled, 1, 999)})
		if err != nil {
			g.Log().Info(ctx, library.SDump("CheckAMFlowSchema err", err))
			library.Fail(ctx, err.Error())
		}

	})
	return
}

// 修改是否允许打印前编辑状态
func (s *sPrint) UpdateAllowEditStatus(ctx context.Context, templateId int64, allowEdit bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		templateInfo, errGT := s.GetInfoByID(ctx, uint64(templateId), false)
		library.ErrIsNil(ctx, errGT)
		if templateInfo == nil {
			library.Fail(ctx, "打印模版不存在")
			return
		}

		// 使用正确的字段名 AllowEditBefore
		updateData := &do.FormPrintTemplate{AllowEditBefore: lo.Ternary(allowEdit, 1, 0)}

		_, err = dao.TenantCtx(dao.FormPrintTemplate, ctx).WherePri(templateId).Update(updateData)
		if err != nil {
			g.Log().Error(ctx, "UpdateAllowEditStatus failed", err)
			library.Fail(ctx, "更新打印前编辑状态失败: "+err.Error())
		}
	})
	return
}

func (s *sPrint) GenerateFormToPDF(ctx context.Context, req *print.PrintFormReq, noCheckPermission bool) (res *print.PrintFormRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得上传文件的配置
		printPath := g.Cfg().MustGet(ctx, "print.path").String()
		if printPath == "" {
			library.ErrIsNil(ctx, fmt.Errorf("print.path 未配置"))
		}

		// 获得打印模板信息
		printTemplate, err := s.GetInfoByID(ctx, uint64(req.FormPrintTemplateId), true)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(printTemplate, "打印模板信息不存在")

		// 验证打印权限
		if !noCheckPermission {
			library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, printTemplate.FormTemplateId, enum.SysPermission_Print), enum.SysPermission_Print.Error())
		}

		// 获得表单模板信息
		formTemplate, formColumns, tableDesign, _, err := service.FormTemplate().GetDatabaseDesign(ctx, printTemplate.FormTemplateId)
		library.ErrIsNil(ctx, err)
		// 获得表单数据
		formData, err := service.FormData().GetFormData(ctx, formTemplate.FormTableName, req.FormId)
		library.ErrIsNil(ctx, err)
		library.ValueIsNil(formData, "表单数据不存在")
		// 判断是否允许未审核的表单打印
		var allowUnAudit bool
		if tableDesign != nil && tableDesign.ActionSettings != nil {
			if printSettings, ok := tableDesign.ActionSettings["actionPrint"]; ok {
				if allow, exists := printSettings["actionPrintAllowUnAudit"]; exists {
					allowUnAudit = gconv.Bool(allow)
				}
			}
		}
		// 如果不允许未审核打印,则检查流程状态
		if !allowUnAudit {
			if gconv.Int64(formData["flow_status"]) != gconv.Int64(enum.FlowStatus_Finish) {
				library.ErrIsNil(ctx, fmt.Errorf("表单未审批通过，无法打印"))
			}
		}
		__global_identifier, err := service.FormData().GenerateGlobalId(ctx, 0, printTemplate.FormTemplateId, gconv.Int64(formData["id"]))
		library.ErrIsNil(ctx, err)
		formData["__global_identifier"] = __global_identifier
		formData["code_prefix"] = library.CodePrefixEncode(gconv.Int32(printTemplate.FormTemplateId), 0)

		// 解析 enterWithData 为切片
		var withTableDataMaps []*dto.WithTableData
		err = json.Unmarshal([]byte(printTemplate.EnterWithData), &withTableDataMaps)
		if err != nil {
			library.ErrIsNil(ctx, fmt.Errorf("关联数据格式不正确"))
		}

		// 获取关联表字段信息
		withFormColumns, withFormTableInfo, err := s.GetWithFormColumns(ctx, withTableDataMaps)
		library.ErrIsNil(ctx, err)
		mergedFormColumns := append(dto.FixedFields, formColumns...)
		withFormColumns["fieldsValue"] = mergedFormColumns
		withFormTableInfo["fieldsValue"] = &dto.SupportTable{
			TableName: formTemplate.FormTableName,
			Type:      0,
		}

		// 获取关联表数据
		withTableData, err := getWithTableDataMap(ctx, formData, withFormColumns, withTableDataMaps, "table", 0)
		library.ErrIsNil(ctx, err)

		// 执行SQL查询并添加到数据上下文
		sqlQueryResults, err := s.ExecuteSqlQueries(ctx, printTemplate.SqlQueries, formData, withTableData)
		library.ErrIsNil(ctx, err)

		// 构建SQL查询配置映射
		var sqlQueries []map[string]interface{}
		if printTemplate.SqlQueries != "" {
			json.Unmarshal([]byte(printTemplate.SqlQueries), &sqlQueries)
		}
		sqlQueryConfigs := make(map[string]interface{})
		for _, sqlQuery := range sqlQueries {
			if queryId := gconv.String(sqlQuery["id"]); queryId != "" {
				sqlQueryConfigs[queryId] = sqlQuery
			}
		}

		templateContent, err := HandleTemplateContent(ctx, printTemplate.TemplateContent, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, sqlQueryResults, sqlQueryConfigs)
		library.ErrIsNil(ctx, err)

		// html 内容转换为 pdf 文件
		dateDirName := gtime.Now().Format("Ymd")
		pdfDirName := gfile.Join(printPath, dateDirName)
		library.IsNotExistMkdirPath(ctx, pdfDirName)
		pdfFileName := library.RandSeq(32) + ".pdf"

		err = GeneratePDF(ctx, templateContent, gfile.Join(pdfDirName, pdfFileName), printTemplate.PrintSize, printTemplate.PrintRotation, printTemplate.Padding, printTemplate.Width, printTemplate.Height, true)
		library.ErrIsNil(ctx, err)

		res = &print.PrintFormRes{
			PdfUrl: fmt.Sprintf("/print_pdf/%s/%s", dateDirName, pdfFileName),
		}

		// 创建打印记录
		service.FormPrintLogs().CreateByIds(ctx, printTemplate.FormTemplateId, req.FormId, int64(printTemplate.Id))
	})
	return
}

// GenerateFormToHtml generates HTML content for a given form and print template.
func (s *sPrint) GenerateFormToHtml(ctx context.Context, req *print.PrintFormHtmlReq) (res *print.PrintFormHtmlRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获得打印模板信息
		printTemplate, errGT := s.GetInfoByID(ctx, uint64(req.FormPrintTemplateId), true)
		library.ErrIsNil(ctx, errGT, "获取打印模板失败")
		library.ValueIsNil(printTemplate, "打印模板信息不存在")

		// 验证打印权限 (assuming noCheckPermission is false for HTML generation by default, or add to req if needed)
		library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, printTemplate.FormTemplateId, enum.SysPermission_Print), enum.SysPermission_Print.Error())

		// 获得表单模板信息
		formTemplate, formColumns, tableDesign, _, errFDS := service.FormTemplate().GetDatabaseDesign(ctx, printTemplate.FormTemplateId)
		library.ErrIsNil(ctx, errFDS, "获取表单设计失败")

		// 获得表单数据
		formData, errFD := service.FormData().GetFormData(ctx, formTemplate.FormTableName, req.FormId)
		library.ErrIsNil(ctx, errFD, "获取表单数据失败")
		library.ValueIsNil(formData, "表单数据不存在")
		// 判断是否允许打印前编辑
		if printTemplate.AllowEditBefore == 0 {
			library.ErrIsNil(ctx, fmt.Errorf("不允许打印前编辑"))
		}
		// 判断是否允许未审核的表单打印
		var allowUnAudit bool
		if tableDesign != nil && tableDesign.ActionSettings != nil {
			if printSettings, ok := tableDesign.ActionSettings["actionPrint"]; ok {
				if allow, exists := printSettings["actionPrintAllowUnAudit"]; exists {
					allowUnAudit = gconv.Bool(allow)
				}
			}
		}
		// 如果不允许未审核打印,则检查流程状态
		if !allowUnAudit {
			if gconv.Int64(formData["flow_status"]) != gconv.Int64(enum.FlowStatus_Finish) {
				library.ErrIsNil(ctx, fmt.Errorf("表单未审批通过，无法生成打印内容"))
			}
		}

		__global_identifier, errGI := service.FormData().GenerateGlobalId(ctx, 0, printTemplate.FormTemplateId, gconv.Int64(formData["id"]))
		library.ErrIsNil(ctx, errGI, "生成全局标识失败")
		formData["__global_identifier"] = __global_identifier
		formData["code_prefix"] = library.CodePrefixEncode(gconv.Int32(printTemplate.FormTemplateId), 0)

		// 解析 enterWithData 为切片
		var withTableDataMaps []*dto.WithTableData
		errUM := json.Unmarshal([]byte(printTemplate.EnterWithData), &withTableDataMaps)
		if errUM != nil {
			library.ErrIsNil(ctx, fmt.Errorf("关联数据格式不正确: %v", errUM))
		}

		// 获取关联表字段信息
		withFormColumns, withFormTableInfo, errWFC := s.GetWithFormColumns(ctx, withTableDataMaps)
		library.ErrIsNil(ctx, errWFC, "获取关联表字段信息失败")
		mergedFormColumns := append(dto.FixedFields, formColumns...)
		withFormColumns["fieldsValue"] = mergedFormColumns
		withFormTableInfo["fieldsValue"] = &dto.SupportTable{
			TableName: formTemplate.FormTableName,
			Type:      0,
		}

		// 获取关联表数据
		withTableData, errWTD := getWithTableDataMap(ctx, formData, withFormColumns, withTableDataMaps, "table", 0)
		library.ErrIsNil(ctx, errWTD, "获取关联表数据失败")

		// 执行SQL查询并添加到数据上下文
		sqlQueryResults, errSQL := s.ExecuteSqlQueries(ctx, printTemplate.SqlQueries, formData, withTableData)
		library.ErrIsNil(ctx, errSQL, "执行SQL查询失败")

		// 构建SQL查询配置映射
		var sqlQueries []map[string]interface{}
		if printTemplate.SqlQueries != "" {
			json.Unmarshal([]byte(printTemplate.SqlQueries), &sqlQueries)
		}
		sqlQueryConfigs := make(map[string]interface{})
		for _, sqlQuery := range sqlQueries {
			if queryId := gconv.String(sqlQuery["id"]); queryId != "" {
				sqlQueryConfigs[queryId] = sqlQuery
			}
		}

		templateContent, errHTC := HandleTemplateContent(ctx, printTemplate.TemplateContent, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, sqlQueryResults, sqlQueryConfigs)
		library.ErrIsNil(ctx, errHTC, "处理模板内容失败")

		res = &print.PrintFormHtmlRes{
			Html: templateContent,
		}
	})
	return
}

// GeneratePdfFromHtmlContent generates a PDF from provided HTML content.
func (s *sPrint) GeneratePdfFromHtmlContent(ctx context.Context, req *print.GeneratePdfFromHtmlReq) (res *print.GeneratePdfFromHtmlRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Infof(ctx, "GeneratePdfFromHtmlContent called for FormId: %d, TemplateId: %d", req.FormId, req.FormPrintTemplateId)

		printPath := g.Cfg().MustGet(ctx, "print.path").String()
		if printPath == "" {
			library.ErrIsNil(ctx, fmt.Errorf("print.path 未配置"))
			return
		}

		var printSize, printRotation, padding string
		var width, height int

		if req.FormPrintTemplateId <= 0 {
			library.ErrIsNil(ctx, fmt.Errorf("打印模板id不能为空"))
			return
		}

		printTemplate, errGT := s.GetInfoByID(ctx, uint64(req.FormPrintTemplateId), false) // Don't need 'with' data here
		if errGT != nil || printTemplate == nil {
			library.ErrIsNil(ctx, fmt.Errorf("打印模板信息不存在"))
			return
		}
		printSize = printTemplate.PrintSize
		printRotation = printTemplate.PrintRotation
		padding = printTemplate.Padding
		width = printTemplate.Width
		height = printTemplate.Height

		dateDirName := gtime.Now().Format("Ymd")
		pdfDirName := gfile.Join(printPath, dateDirName)
		library.IsNotExistMkdirPath(ctx, pdfDirName) // Ensure directory exists

		// Sanitize FormId and FormPrintTemplateId for filename to avoid issues if they are 0 or invalid
		formIdStr := "custom"
		if req.FormId > 0 {
			formIdStr = gconv.String(req.FormId)
		}
		templateIdStr := "custom"
		if req.FormPrintTemplateId > 0 {
			templateIdStr = gconv.String(req.FormPrintTemplateId)
		}

		pdfFileName := fmt.Sprintf("form_%s_tpl_%s_modified_%s.pdf", formIdStr, templateIdStr, library.RandSeq(16))

		filePath := gfile.Join(pdfDirName, pdfFileName)
		g.Log().Debugf(ctx, "Attempting to generate PDF at: %s with HTML content: %s", filePath, req.HtmlContent)

		errGen := GeneratePDF(ctx, req.HtmlContent, filePath, printSize, printRotation, padding, width, height, true)
		library.ErrIsNil(ctx, errGen, "生成PDF文件失败")

		res = &print.GeneratePdfFromHtmlRes{
			PdfUrl: fmt.Sprintf("/print_pdf/%s/%s", dateDirName, pdfFileName), // Assuming /print_pdf/ is the public route for printPath
		}

		service.FormPrintLogs().CreateByIds(ctx, printTemplate.FormTemplateId, req.FormId, int64(printTemplate.Id))

	})
	return
}

func (s *sPrint) GetWithFormColumns(ctx context.Context, withTableDataMaps []*dto.WithTableData) (withFormColumns map[string][]*dto.FormColumn, withFormTableInfo map[string]*dto.SupportTable, err error) {
	withFormColumns = make(map[string][]*dto.FormColumn)
	withFormTableInfo = make(map[string]*dto.SupportTable)
	// 遍历每个 WithTableData 对象
	for _, withTableData := range withTableDataMaps {
		withFormTableInfo[withTableData.ID] = &dto.SupportTable{
			TableName: withTableData.WithTable.TableName,
			Type:      lo.Ternary(withTableData.WithTable.FormTemplateID > 0, 0, 1),
		}
		formColumns, err := service.FormTemplate().GetRelatedColumnsByTableName(ctx, withFormTableInfo[withTableData.ID].TableName, withFormTableInfo[withTableData.ID].Type, true)

		if err != nil {
			return nil, nil, err
		}
		withFormColumns[withTableData.ID] = formColumns
		// if withTableData.WithTable.FormTemplateID > 0 {
		// 	_, formColumns, _, _, err := service.FormTemplate().GetDatabaseDesign(ctx, int64(withTableData.WithTable.FormTemplateID))
		// 	if err != nil {
		// 		return nil, nil, err
		// 	}
		// 	withFormColumns[withTableData.ID] = formColumns

		// }
	}
	return
}

func getWithTableDataMap(ctx context.Context, formData map[string]interface{}, withFormColumns map[string][]*dto.FormColumn, withTableDataMaps []*dto.WithTableData, mount string, rowIndex int) (withTableDataMap map[string]map[string]interface{}, err error) {
	withTableDataMap = make(map[string]map[string]interface{})
	withTableDataMap["fieldsValue"] = formData
	// 遍历每个 WithTableData 对象
	for _, withTableData := range withTableDataMaps {
		if withTableData.Mount != mount {
			continue
		}
		// 构建查询条件
		filter := make(map[string]interface{})
		for _, condition := range withTableData.WithCondition {
			currentData := withTableDataMap[condition.Value.GroupKey]
			formColumns := withFormColumns[condition.Value.GroupKey]
			currentColumns := formColumns
			if condition.Value.Parent != "" {
				parentDataVar, hasParentDataVar := formData[condition.Value.Parent].(*gvar.Var)
				var parentDataSlice []map[string]interface{}
				if hasParentDataVar {
					json.Unmarshal([]byte(parentDataVar.String()), &parentDataSlice)
					if len(parentDataSlice) > rowIndex {
						currentData = parentDataSlice[rowIndex]
					}
				}
				parentColumn, _ := lo.Find(formColumns, func(column *dto.FormColumn) bool { return column.Id == condition.Value.Parent })
				if parentColumn != nil {
					currentColumns = parentColumn.Children
				}
			}
			fieldColumn, hasFieldColumn := lo.Find(currentColumns, func(column *dto.FormColumn) bool { return column.Id == condition.Value.FieldKey })
			fieldValue, exists := currentData[condition.Value.FieldKey]
			if exists && hasFieldColumn {
				formatValue, formatErr := fieldColumn.FormatValue(ctx, fieldValue)
				if formatErr != nil {
					g.Log().Info(ctx, fmt.Sprintf("GetWithTableDataMap FormatValue err :%v", formatErr))
					continue
				}
				filter[condition.Column.ColumnName] = formatValue
			}
		}

		// 调用 GetListCustomEx 方法获取数据
		res, err := service.FormData().GetListCustomEx(ctx, filter, withTableData.WithTable.TableName)
		if err != nil {
			return nil, err
		}

		// 只取第一条数据进行映射
		if len(res) > 0 {
			firstItem := res[0]
			withTableDataMap[withTableData.ID] = firstItem
		}
	}

	return withTableDataMap, nil
}

// HandleTableRowLoops 处理包含表格字段的行循环（新增函数）
func HandleTableRowLoops(ctx context.Context, templateContent string, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withTableDataMaps []*dto.WithTableData, withFormTableInfo map[string]*dto.SupportTable, sqlQueryResults map[string]interface{}, sqlQueryConfigs map[string]interface{}) (string, error) {

	// 使用正则表达式匹配表格行
	trRegex := regexp.MustCompile(`<tr[^>]*>(.*?)</tr>`)

	return trRegex.ReplaceAllStringFunc(templateContent, func(trMatch string) string {
		g.Log().Infof(ctx, "trMatch: %s", trMatch)
		// 检查当前行是否包含表格字段占位符
		tableFieldInfo := extractTableFieldFromRow(ctx, trMatch)
		if tableFieldInfo == nil {
			// 没有表格字段，返回原始行
			return trMatch
		}

		// 有表格字段，需要循环处理
		return processTableRowLoop(ctx, trMatch, tableFieldInfo, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, sqlQueryResults, sqlQueryConfigs)
	}), nil
}

// extractTableFieldFromRow 从行中提取表格字段信息
func extractTableFieldFromRow(ctx context.Context, rowContent string) *TableFieldInfo {
	// 查找行中的所有占位符，使用字符类匹配来正确处理包含下划线的字段名
	placeholderRegex := regexp.MustCompile(`__TABLE_FIELD__([a-zA-Z0-9_]+)__([a-zA-Z0-9_]+)__([a-zA-Z0-9_]+)__`)
	matches := placeholderRegex.FindAllStringSubmatch(rowContent, -1)

	if len(matches) == 0 {
		return nil
	}

	// 取第一个表格字段作为循环依据（假设同一行的表格字段都来自同一个表）
	firstMatch := matches[0]
	return &TableFieldInfo{
		TagKey:    firstMatch[1],
		TableName: firstMatch[2],
		Fields:    extractAllTableFieldsFromRow(rowContent),
	}
}

// TableFieldInfo 表格字段信息结构
type TableFieldInfo struct {
	TagKey    string
	TableName string
	Fields    []TableFieldDetail
}

// TableFieldDetail 表格字段详情
type TableFieldDetail struct {
	TagKey      string
	TableName   string
	ColumnName  string
	Placeholder string
}

// extractAllTableFieldsFromRow 提取行中所有的表格字段
func extractAllTableFieldsFromRow(rowContent string) []TableFieldDetail {
	var fields []TableFieldDetail
	placeholderRegex := regexp.MustCompile(`__TABLE_FIELD__([a-zA-Z0-9_]+)__([a-zA-Z0-9_]+)__([a-zA-Z0-9_]+)__`)
	matches := placeholderRegex.FindAllStringSubmatch(rowContent, -1)

	for _, match := range matches {
		fields = append(fields, TableFieldDetail{
			TagKey:      match[1],
			TableName:   match[2],
			ColumnName:  match[3],
			Placeholder: match[0],
		})
	}

	return fields
}

// CellInfo 单元格信息
type CellInfo struct {
	Content   string // 单元格内容
	IsFixed   bool   // 是否为固定内容（不含表格字段）
	HasFields bool   // 是否包含表格字段
}

// analyzeCellStructure 分析行中的单元格结构
func analyzeCellStructure(rowContent string) []CellInfo {
	var cells []CellInfo

	// 使用正则表达式匹配所有td标签
	tdRegex := regexp.MustCompile(`<td[^>]*>(.*?)</td>`)
	tdMatches := tdRegex.FindAllStringSubmatch(rowContent, -1)

	for _, match := range tdMatches {
		if len(match) >= 2 {
			cellContent := match[1]
			hasTableField := strings.Contains(cellContent, "__TABLE_FIELD__")

			cells = append(cells, CellInfo{
				Content:   cellContent,
				IsFixed:   !hasTableField,
				HasFields: hasTableField,
			})
		}
	}

	return cells
}

// buildRowWithMerge 构建带有行合并的表格行
func buildRowWithMerge(originalRow string, cellStructure []CellInfo, rowIndex int, totalRows int, fieldReplacements map[string]string) string {
	// 使用正则表达式匹配所有td标签
	tdRegex := regexp.MustCompile(`<td[^>]*>(.*?)</td>`)

	var cellIndex int = 0
	result := tdRegex.ReplaceAllStringFunc(originalRow, func(tdMatch string) string {
		if cellIndex >= len(cellStructure) {
			cellIndex++
			return tdMatch
		}

		cellInfo := cellStructure[cellIndex]
		cellIndex++

		if cellInfo.IsFixed {
			// 固定内容单元格
			if rowIndex == 0 {
				// 第一行：添加rowspan属性
				if totalRows > 1 {
					// 先移除已存在的rowspan属性，然后添加新的rowspan
					result := regexp.MustCompile(`\s+rowspan\s*=\s*"[^"]*"`).ReplaceAllString(tdMatch, "")
					return regexp.MustCompile(`<td([^>]*?)>`).ReplaceAllString(result, fmt.Sprintf(`<td$1 rowspan="%d">`, totalRows))
				}
				return tdMatch
			} else {
				// 后续行：移除此单元格
				return ""
			}
		} else {
			// 动态内容单元格：替换表格字段占位符
			result := tdMatch
			for placeholder, replacement := range fieldReplacements {
				result = strings.Replace(result, placeholder, replacement, -1)
			}
			return result
		}
	})

	return result
}

// processTableRowLoop 处理表格行循环（重写以支持行合并）
func processTableRowLoop(ctx context.Context, originalRow string, tableFieldInfo *TableFieldInfo, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withTableDataMaps []*dto.WithTableData, withFormTableInfo map[string]*dto.SupportTable, sqlQueryResults map[string]interface{}, sqlQueryConfigs map[string]interface{}) string {

	// 获取表格数据
	data, exists := withTableData[tableFieldInfo.TagKey]
	if !exists {
		g.Log().Info(ctx, fmt.Sprintf("processTableRowLoop: no data found for tagKey: %s", tableFieldInfo.TagKey))
		return originalRow
	}

	tableJsonStr, ok := data[tableFieldInfo.TableName].(*gvar.Var)
	if !ok || tableJsonStr.String() == "" {
		g.Log().Info(ctx, fmt.Sprintf("processTableRowLoop: no table data found for table: %s", tableFieldInfo.TableName))
		return originalRow
	}

	var tableRows []map[string]interface{}
	if err := json.Unmarshal([]byte(tableJsonStr.String()), &tableRows); err != nil {
		g.Log().Error(ctx, fmt.Sprintf("processTableRowLoop: failed to unmarshal table data: %v", err))
		return originalRow
	}

	if len(tableRows) == 0 {
		return originalRow
	}

	// 获取表单列信息
	formColumns, exists := withFormColumns[tableFieldInfo.TagKey]
	if !exists {
		g.Log().Info(ctx, fmt.Sprintf("processTableRowLoop: formColumns not found for tagKey: %s", tableFieldInfo.TagKey))
		return originalRow
	}

	// 找到表格字段的列信息
	tableColumnInfo, exists := lo.Find(formColumns, func(column *dto.FormColumn) bool {
		return column.Id == tableFieldInfo.TableName
	})
	if !exists || tableColumnInfo == nil {
		g.Log().Info(ctx, fmt.Sprintf("processTableRowLoop: tableColumnInfo not found for table: %s", tableFieldInfo.TableName))
		return originalRow
	}

	// 分析单元格结构
	cellStructure := analyzeCellStructure(originalRow)
	totalRows := len(tableRows)

	// 生成循环行
	var resultRows []string
	for rowIndex, row := range tableRows {
		// 获取当前行的关联数据
		rowWithTableData, err := getWithTableDataMap(ctx, data, withFormColumns, withTableDataMaps, tableFieldInfo.TableName, rowIndex)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("processTableRowLoop: failed to get row data: %v", err))
			continue
		}
		maps.Copy(rowWithTableData, withTableData)

		// 准备字段替换映射
		fieldReplacements := make(map[string]string)
		for _, field := range tableFieldInfo.Fields {
			var fieldValue interface{}
			var fieldColumn *dto.FormColumn
			if field.TagKey == "fieldsValue" {
				if field.ColumnName == "serial_number" {
					fieldValue = rowIndex + 1
				} else {
					fieldValue = row[field.ColumnName]
					fieldColumn, _ = lo.Find(tableColumnInfo.Children, func(child *dto.FormColumn) bool {
						return child.Id == field.ColumnName
					})
				}

			} else {
				withTableRow := rowWithTableData[field.TagKey]
				if withTableRow != nil {
					fieldValue = withTableRow[field.ColumnName]
					withFormColumn := withFormColumns[field.TagKey]
					if len(withFormColumn) > 0 {
						fieldColumn, _ = lo.Find(withFormColumn, func(column *dto.FormColumn) bool {
							return column.Id == field.ColumnName
						})
					}
				}
			}

			// 格式化字段值
			if fieldColumn != nil {
				formattedValue, err := service.FormData().FormatColumnLabel(ctx, fieldColumn, fieldValue)
				if err != nil {
					g.Log().Info(ctx, fmt.Sprintf("processTableRowLoop: formatValue error: %s", err.Error()))
					fieldValue = "&nbsp;"
				} else {
					fieldValue = formattedValue
				}
			}

			fieldReplacements[field.Placeholder] = gconv.String(fieldValue)
		}

		// 构建当前行（考虑合并）
		currentRow := buildRowWithMerge(originalRow, cellStructure, rowIndex, totalRows, fieldReplacements)

		if currentRow != "" {
			resultRows = append(resultRows, currentRow)
		}
	}

	return strings.Join(resultRows, "")
}

// 对printTemplate.TemplateContent中的变量进行处理
func HandleTemplateContent(ctx context.Context, templateContent string, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withTableDataMaps []*dto.WithTableData, withFormTableInfo map[string]*dto.SupportTable, sqlQueryResults map[string]interface{}, sqlQueryConfigs map[string]interface{}) (res string, err error) {
	// 第一步：先处理a标签
	re := regexp.MustCompile(`(?:<p[^>]*>)?<a\s+[^>]*href="([^"]*)"[^>]*>.*?</a>(?:</p>)?`)

	res = re.ReplaceAllStringFunc(templateContent, func(aTag string) string {
		// 提取href内容
		hrefMatch := re.FindStringSubmatch(aTag)
		if len(hrefMatch) < 2 {
			return aTag
		}
		href := hrefMatch[1]

		// 调用函数进行变量替换
		processedHref := HandleHrefContent(ctx, href, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, sqlQueryResults, sqlQueryConfigs)

		// 替换整个a标签为处理后的内容
		return processedHref
	})

	// 第二步：处理表格行循环（在a标签处理后进行）
	res, err = HandleTableRowLoops(ctx, res, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, sqlQueryResults, sqlQueryConfigs)
	if err != nil {
		return "", err
	}

	// 第三步：处理img标签和alt内容
	reAlt := regexp.MustCompile(`<img\s+[^>]*alt="([^"]*)"[^>]*\/?>`)
	reStyle := regexp.MustCompile(`style="([^"]*)"`)
	// 使用正则表达式查找并处理alt内容
	res = reAlt.ReplaceAllStringFunc(res, func(imgTag string) string {
		// 提取alt内容
		altMatch := reAlt.FindStringSubmatch(imgTag)
		if len(altMatch) < 2 {
			return imgTag
		}
		alt := altMatch[1]
		// 提取style属性
		styleMatch := reStyle.FindStringSubmatch(imgTag)
		var style string
		if len(styleMatch) > 1 {
			style = styleMatch[1] // 提取style的内容
		}

		// 调用函数进行变量替换
		processedAlt := HandleHrefContent(ctx, alt, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, sqlQueryResults, sqlQueryConfigs)
		if style != "" {
			processedAlt = strings.Replace(processedAlt, "/>", ` style="`+style+`" />`, 1)
		}
		// 替换整个img标签为处理后的内容
		return processedAlt
	})

	return
}

// 对href内容进行变量替换
func HandleHrefContent(ctx context.Context, href string, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withTableDataMaps []*dto.WithTableData, withFormTableInfo map[string]*dto.SupportTable, sqlQueryResults map[string]interface{}, sqlQueryConfigs map[string]interface{}) (res string) {
	res = "&nbsp;"
	// 对href内容进行base解码

	decodedBytes, err := base64.StdEncoding.DecodeString(href)
	if err != nil {
		g.Log().Info(ctx, fmt.Sprintf("HandleHrefContent base64 decode err :%v", err))
		return
	}
	var hrefMap map[string]interface{}
	err = json.Unmarshal(decodedBytes, &hrefMap)
	if err != nil {
		g.Log().Info(ctx, fmt.Sprintf("HandleHrefContent json unmarshal err :%v", err))
		return
	}

	varType, has := hrefMap["type"]
	if !has {
		g.Log().Info(ctx, "HandleHrefContent type not found in hrefMap")
		return
	}
	vm, err := service.Javascript().GetVM(map[string]interface{}{})
	library.ErrIsNil(ctx, err)

	if varType == "key" {
		res = gconv.String(HandleKeyContent(ctx, hrefMap, withFormColumns, withTableData, withFormTableInfo, vm))
	} else if varType == "table" {
		tableData, ok := hrefMap["data"].(map[string]interface{})
		if !ok {
			g.Log().Info(ctx, "HandleHrefContent table data is not a valid map")
			return
		}

		// 调用 HandleTableContent 生成HTML表格
		tableHTML, err := HandleTableContent(ctx, tableData, withFormColumns, withTableData, withTableDataMaps, withFormTableInfo, vm)
		if err != nil {
			g.Log().Info(ctx, fmt.Sprintf("HandleHrefContent error generating table HTML: %v", err))
			return
		}

		// 返回生成的HTML表格
		res = tableHTML
	} else if varType == "table_field" {
		// 处理表格字段占位符（新增）
		res = gconv.String(HandleTableFieldContent(ctx, hrefMap, withFormColumns, withTableData, withFormTableInfo, vm))
	} else if varType == "sql_query" {
		// 处理SQL查询占位符
		res = HandleSqlQueryContent(ctx, hrefMap, sqlQueryResults, sqlQueryConfigs)
	}
	return
}

// 处理SQL查询占位符内容
func HandleSqlQueryContent(ctx context.Context, hrefMap map[string]interface{}, sqlQueryResults map[string]interface{}, sqlQueryConfigs map[string]interface{}) string {
	sqlId, ok := hrefMap["sqlId"].(string)
	if !ok || sqlId == "" {
		g.Log().Info(ctx, "HandleSqlQueryContent: sqlId not found or invalid")
		return "&nbsp;"
	}

	sqlType, ok := hrefMap["sqlType"].(string)
	if !ok {
		sqlType = "table" // 默认为表格类型
	}

	// 从SQL查询结果中获取数据
	result, exists := sqlQueryResults[sqlId]
	if !exists {
		g.Log().Info(ctx, fmt.Sprintf("HandleSqlQueryContent: no result found for sqlId: %s", sqlId))
		return "&nbsp;"
	}

	if sqlType == "table" {
		// 获取SQL查询配置
		var columnConfigs []map[string]interface{}
		if sqlConfig, configExists := sqlQueryConfigs[sqlId]; configExists {
			if configMap, ok := sqlConfig.(map[string]interface{}); ok {
				if columns, ok := configMap["columns"].([]interface{}); ok {
					for _, col := range columns {
						if colMap, ok := col.(map[string]interface{}); ok {
							columnConfigs = append(columnConfigs, colMap)
						}
					}
				}
			}
		}

		// 表格类型的SQL查询结果
		return HandleSqlTableResult(ctx, result, columnConfigs)
	} else {
		// 单值类型的SQL查询结果
		return HandleSqlValueResult(ctx, result)
	}
}

// 处理SQL表格查询结果
func HandleSqlTableResult(ctx context.Context, result interface{}, columnConfigs []map[string]interface{}) string {
	resultList, ok := result.([]map[string]interface{})
	if !ok {
		g.Log().Info(ctx, "HandleSqlTableResult: result is not a valid table format")
		return "&nbsp;"
	}

	if len(resultList) == 0 {
		return "<table  style='width: 100%;' border='1'><tr><td>暂无数据</td></tr></table>"
	}

	var buffer strings.Builder
	buffer.WriteString("<table border='1' style='width: 100%; '>")

	// 如果有列配置，按照配置来显示
	if len(columnConfigs) > 0 {
		// 按照配置的顺序和显示设置来生成表头
		buffer.WriteString("<tr>")
		for _, colConfig := range columnConfigs {
			show, _ := colConfig["show"].(bool)
			if show {
				alias := gconv.String(colConfig["alias"])
				if alias == "" {
					alias = gconv.String(colConfig["key"])
				}
				buffer.WriteString("<th style='font-weight: 400; '>" + alias + "\u200B</th>")
			}
		}
		buffer.WriteString("</tr>")

		// 按照配置的顺序和显示设置来生成数据行
		for _, row := range resultList {
			buffer.WriteString("<tr>")
			for _, colConfig := range columnConfigs {
				show, _ := colConfig["show"].(bool)
				if show {
					key := gconv.String(colConfig["key"])
					value := row[key]
					buffer.WriteString("<td style=' text-align:center;'>" + gconv.String(value) + "</td>")
				}
			}
			buffer.WriteString("</tr>")
		}
	} else {
		// 如果没有列配置，使用原来的逻辑
		firstRow := resultList[0]
		buffer.WriteString("<tr>")
		for key := range firstRow {
			buffer.WriteString("<th style='font-weight: 400; '>" + key + "\u200B</th>")
		}
		buffer.WriteString("</tr>")

		// 添加数据行
		for _, row := range resultList {
			buffer.WriteString("<tr>")
			for _, value := range row {
				buffer.WriteString("<td  style=' text-align:center;'>" + gconv.String(value) + "</td>")
			}
			buffer.WriteString("</tr>")
		}
	}

	buffer.WriteString("</table>")
	return buffer.String()
}

// 处理SQL单值查询结果
func HandleSqlValueResult(ctx context.Context, result interface{}) string {
	if result == nil {
		return "&nbsp;"
	}
	return gconv.String(result)
}

func HandleTableContent(ctx context.Context, tableData map[string]interface{}, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withTableDataMaps []*dto.WithTableData, withFormTableInfo map[string]*dto.SupportTable, vm *goja.Runtime) (string, error) {
	tableName, fields, tagKey, err := extractTableMetadata(tableData)
	if err != nil {
		return "", err
	}
	data, has := withTableData[tagKey]
	if !has {
		return "", fmt.Errorf("HandleTableContent: no data found for tagKey: %s", tagKey)
	}
	tableJsonStr, ok := data[tableName].(*gvar.Var)
	if !ok || tableJsonStr.String() == "" {
		return "", fmt.Errorf("HandleTableContent: no data found for table: %s", tableName)
	}

	var tableRows []map[string]interface{}
	if err := json.Unmarshal([]byte(tableJsonStr.String()), &tableRows); err != nil {
		return "", fmt.Errorf("HandleTableContent: failed to unmarshal JSON data: %v", err)
	}
	formColumns, ok := withFormColumns[tagKey]
	if !ok {
		return "", fmt.Errorf("HandleTableContent: formColumns not found for tagKey: %s", tagKey)
	}
	// 从formColumns找到对应表名的columnInfo
	columnInfo, has := lo.Find(formColumns, func(column *dto.FormColumn) bool {
		return column.Id == tableName
	})
	if !has || columnInfo == nil {
		return "", fmt.Errorf("HandleTableContent: columnInfo not found for table: %s", tableName)
	}

	// 构造HTML表格
	var buffer strings.Builder
	buffer.WriteString("<table  style='width: 100%;' border='1'><tr>")

	// 添加表头，使用 Children 中的 Label 作为表头名称
	for _, field := range fields {
		fieldTagKey := field.TagKey
		fieldColumnName := field.Column
		if fieldColumnName == "serial_number" {
			buffer.WriteString("<th style='font-weight: 400;'>序号\u200B</th>")
			continue
		} else {
			if !g.IsEmpty(field.Alias) {
				buffer.WriteString("<th style='font-weight: 400;'>" + field.Alias + "\u200B</th>")
				continue
			}
		}

		var fieldColumns []*dto.FormColumn
		var has bool
		if field.IsChildren {
			has = true
			fieldColumns = columnInfo.Children
		} else {
			fieldColumns, has = withFormColumns[fieldTagKey]
		}
		var childColumnInfo *dto.FormColumn = nil
		if has {
			childColumnInfo, _ = lo.Find(fieldColumns, func(child *dto.FormColumn) bool {
				return child.Id == fieldColumnName
			})
		}
		if childColumnInfo != nil {
			buffer.WriteString("<th style='font-weight: 400;'>" + childColumnInfo.Label + "\u200B</th>")
		} else {
			buffer.WriteString("<th style='font-weight: 400;'>" + fieldColumnName + "\u200B</th>") // 如果找不到对应的 Label，使用字段名作为表头
		}
	}
	buffer.WriteString("</tr>")

	// 添加表格数据
	for rowIndex, row := range tableRows {
		// 当前行对应的关联数据
		rowWithTableData, err := getWithTableDataMap(ctx, data, withFormColumns, withTableDataMaps, tableName, rowIndex)
		if err != nil {
			continue
		}
		maps.Copy(rowWithTableData, withTableData)

		buffer.WriteString("<tr>")
		for _, field := range fields {

			var childColumnInfo *dto.FormColumn = nil
			var childColumnValue interface{} = nil
			var fieldColumns []*dto.FormColumn

			var has bool

			if field.Column == "serial_number" {
				childColumnValue = rowIndex + 1
			} else {
				if field.IsChildren {
					has = true
					fieldColumns = columnInfo.Children
					childColumnValue = row[field.Column]
				} else {
					fieldColumns, has = withFormColumns[field.TagKey]
					tagData := rowWithTableData[field.TagKey]
					childColumnValue = tagData[field.Column]
				}
				if has {
					childColumnInfo, _ = lo.Find(fieldColumns, func(child *dto.FormColumn) bool {
						return child.Id == field.Column
					})
				}
			}

			if !g.IsEmpty(field.FuncCode) && field.FuncCode.CodeContent != "" {
				tableInfo, hasTableInfo := withFormTableInfo[field.TagKey]
				if hasTableInfo {
					formatRes, err := service.Javascript().FormatMappedValue(ctx, vm, tableInfo.TableName, tableInfo.Type, formColumns, childColumnValue, field.FuncCode)
					if err != nil {
						return "", fmt.Errorf("HandleTableContent: FormatMappedValue err: %s", err.Error())
					}
					childColumnValue = formatRes
				}
			} else if has && childColumnInfo != nil {
				formattedValue, err := service.FormData().FormatColumnLabel(ctx, childColumnInfo, childColumnValue)
				if err != nil {
					g.Log().Info(ctx, "HandleTableContent formatValue err :"+err.Error())
					buffer.WriteString("<td style=' text-align:center;'>&nbsp;</td>")
					continue
				}
				childColumnValue = formattedValue
			}

			buffer.WriteString("<td style=' text-align:center;'>" + gconv.String(childColumnValue) + "</td>")
		}
		buffer.WriteString("</tr>")
	}
	buffer.WriteString("</table>")

	return buffer.String(), nil
}

func extractTableMetadata(tableData map[string]interface{}) (string, []*dto.PrintTableMap, string, error) {
	tableName, ok := tableData["table"].(string)
	if !ok || tableName == "" {
		return "", nil, "", fmt.Errorf("HandleTableContent: table name is invalid")
	}

	fieldsMap, ok := tableData["fields"].([]interface{})
	if !ok || len(fieldsMap) == 0 {
		return "", nil, "", fmt.Errorf("HandleTableContent: fields are invalid")
	}

	var fields []*dto.PrintTableMap
	err := library.ToAny(fieldsMap, &fields)
	if err != nil {
		return "", nil, "", fmt.Errorf("HandleTableContent: fields are invalid" + err.Error())
	}
	tagKey, ok := tableData["tagKey"].(string)
	if !ok || tagKey == "" {
		return "", nil, "", fmt.Errorf("HandleTableContent: tagKey is invalid")
	}

	return tableName, fields, tagKey, nil
}

func HandleKeyContent(ctx context.Context, hrefMap map[string]interface{}, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withFormTableInfo map[string]*dto.SupportTable, vm *goja.Runtime) (res interface{}) {
	res = "&nbsp;"
	// 获得变量名
	varMap, has := hrefMap["data"].(map[string]interface{})
	if !has {
		g.Log().Info(ctx, "HandleKeyContent var not found in hrefMap")
		return
	}
	var printMap dto.PrintMap
	err := library.ToStruct(varMap, &printMap)
	if err != nil {
		g.Log().Info(ctx, "HandleKeyContent toStruct err :"+err.Error())
		return
	}
	tagKey := printMap.TagKey
	varName := printMap.Column
	// 获得变量值
	data, has := withTableData[tagKey]
	if !has {
		g.Log().Info(ctx, " HandleKeyContent varValue not found in withTableData : "+gconv.String(tagKey))
		return
	}
	vm.Set("__currentRecord", data)

	// 获得列信息
	formColumns, has := withFormColumns[tagKey]
	if !has {
		g.Log().Info(ctx, " HandleKeyContent formColumns not found in withFormColumns : "+gconv.String(tagKey))
		return
	}

	res, has = data[varName]
	if !has {
		g.Log().Info(ctx, " HandleKeyContent varValue not found in data : "+gconv.String(varName))
		return
	}
	tableInfo, has := withFormTableInfo[tagKey]
	if !has {
		g.Log().Info(ctx, " HandleKeyContent tableInfo not found in withFormTableInfo : "+gconv.String(tagKey))
		return
	}
	if !g.IsEmpty(printMap.ContentFormatCode) && printMap.ContentFormatCode.CodeContent != "" {
		// 自定义格式化代码
		res, err = service.Javascript().FormatMappedValue(ctx, vm, tableInfo.TableName, tableInfo.Type, formColumns, res, printMap.ContentFormatCode)
		if err != nil {
			g.Log().Info(ctx, "HandleKeyContent FormatMappedValue err :"+err.Error())
			return
		}
	} else {
		// 根据列信息和变量值，格式化值的显示内容
		columnInfo, _ := lo.Find(formColumns, func(column *dto.FormColumn) bool {
			return column.Id == varName
		})
		if columnInfo != nil {
			formattedValue, err := service.FormData().FormatColumnLabel(ctx, columnInfo, res)
			if err != nil {
				g.Log().Info(ctx, " HandleKeyContent formatValue err :"+err.Error())
				return
			}
			res = formattedValue
		}
	}

	contentPresentation := printMap.ContentPresentation
	if contentPresentation == "2" || contentPresentation == "3" {
		var imageSrc string
		switch contentPresentation {
		case "2":
			imageSrc, _ = library.BarcodeBase64(gconv.String(res), 600, 200)
		case "3":
			imageSrc, _ = library.QRCodeBase64(gconv.String(res), 512)
		}
		res = `<img src="data:image/png;base64,` + imageSrc + `" alt="` + gconv.String(res) + `"  />`
	}
	// contentFormat := varMap["contentFormat"].(string)
	return
}

// 处理表格字段占位符内容（新增函数）
func HandleTableFieldContent(ctx context.Context, hrefMap map[string]interface{}, withFormColumns map[string][]*dto.FormColumn, withTableData map[string]map[string]interface{}, withFormTableInfo map[string]*dto.SupportTable, vm *goja.Runtime) (res interface{}) {
	res = "&nbsp;"

	// 获取表格字段数据
	dataMap, has := hrefMap["data"].(map[string]interface{})
	if !has {
		g.Log().Info(ctx, "HandleTableFieldContent data not found in hrefMap")
		return
	}

	tagKey := gconv.String(dataMap["tagKey"])
	tableName := gconv.String(dataMap["tableName"])
	columnName := gconv.String(dataMap["column"])

	if tagKey == "" || tableName == "" || columnName == "" {
		g.Log().Info(ctx, "HandleTableFieldContent missing required parameters")
		return
	}

	// 这是表格字段的占位符，不直接返回值
	// 实际的值替换会在HandleTemplateContent中的循环处理时进行
	// 这里返回占位符标记，用于后续识别
	res = fmt.Sprintf("__TABLE_FIELD__%s__%s__%s__", tagKey, tableName, columnName)
	return
}

// 将 HTML 内容转换为 PDF 文件
func GeneratePDF(ctx context.Context, htmlContent, outputFile string, printSize string, printRotation string, padding string, width int, height int, formatHtml bool) error {
	// printSize = lo.Ternary(printSize != "A4", "A5", "A4")
	printRotation = lo.Ternary(printRotation != "vertical", "Landscape", "Portrait")
	switch printSize {
	case "A4":
		width = 210
	case "A5":
		width = 148
	}
	margin := []string{"--margin-top", "10mm", "--margin-right", "10mm", "--margin-bottom", "10mm", "--margin-left", "10mm"}

	if printRotation == "Landscape" {
		margin = []string{"--margin-top", "10mm", "--margin-right", "11mm", "--margin-bottom", "10mm", "--margin-left", "11mm"}
	}
	// 正则验证padding 是否是数字
	re := regexp.MustCompile(`^\d+$`)
	if re.MatchString(padding) {
		margin = []string{"--margin-top", padding + "mm", "--margin-right", padding + "mm", "--margin-bottom", padding + "mm", "--margin-left", padding + "mm"}
	}

	if formatHtml {
		// 删除所有 <td> 标签内的 border-* 属性
		htmlContent = regexp.MustCompile(`></td>`).ReplaceAllString(htmlContent, `>&nbsp;</td>`)
		// 正则表达式匹配带有 border-width 的 table 标签
		tableRe := regexp.MustCompile(`<table([^>]*)style="([^"]*border-width:\s*[1-9]\d*px;[^"]*)"`)

		htmlContent = tableRe.ReplaceAllStringFunc(htmlContent, func(match string) string {
			replaced := regexp.MustCompile(`border-width:\s*[1-9]\d*px;?`).ReplaceAllString(match, "")
			return strings.Replace(replaced, `<table`, `<table border="1"`, 1)
		})

		// 正则删除掉 border-color 属性
		htmlContent = regexp.MustCompile(`border-color:[^;]+;?`).ReplaceAllString(htmlContent, "")
		htmlContent = regexp.MustCompile(`border-style:[^;]+;?`).ReplaceAllString(htmlContent, "")
		htmlContent = regexp.MustCompile(`border-width:[^;]+;?`).ReplaceAllString(htmlContent, "")

		// 先获取所有表格
		tableCaptureRe := regexp.MustCompile("(?is)(<table[^>]*>.*?</table>)")
		tables := tableCaptureRe.FindAllString(htmlContent, -1)

		// 遍历所有表格，处理包含零宽字符的表格
		for _, table := range tables {
			if strings.Contains(table, "\u200B") {

				// 处理表格：移除table-layout:fixed并添加border="1"
				processedTable := regexp.MustCompile(`table-layout:\s*fixed;?`).ReplaceAllString(table, "")

				// 删除colgroup标签及其内容
				processedTable = regexp.MustCompile(`<colgroup[^>]*>.*?</colgroup>`).ReplaceAllString(processedTable, "")

				// 添加border="1"属性（如果尚未存在）
				if !strings.Contains(processedTable, "border=") {
					processedTable = regexp.MustCompile(`<table`).ReplaceAllString(processedTable, `<table border="1"`)
				}

				// 用处理后的表格替换原始表格
				htmlContent = strings.Replace(htmlContent, table, processedTable, 1)

			}
		}
	}

	htmlContent = `<!DOCTYPE html>
	<html lang="en">
	<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.15" />
	<title> Document </title>
		<style>
		td,th { line-height:21px; padding: 3px 5px;}
		table {
		margin-top:-1px;
		display: table;
		box-sizing: border-box;
		text-indent: initial;
		unicode-bidi: isolate;
		border-color: #333;
		border-collapse: collapse;
		border-spacing: 0;
		border-width:0;
		}
		table[border]:not([border="0"]):not([style*=border-width]) td,
		table[border]:not([border="0"]):not([style*=border-width]) th {
		border-width: 1px;
		}
		
		p {
		padding: 0;
		margin: 10px 0;
		}

		p table {
		margin:-11px 0 -10px 0
		}
		body,html {
		padding:0;
		margin:0;
		}
	</style>
	</head>

	<body style="font-size:16px;">
	<div style="width:` + gconv.String(float64(width)*5.05) + `px;">` + htmlContent + `</div>
	</body>

	</html>`

	// 生成随机文件名
	nanoid, _ := nanoid.New()
	tempFileName := nanoid + ".html"
	tempFilePath := os.TempDir() + "/" + tempFileName

	// 保存 HTML 内容到临时文件
	err := os.WriteFile(tempFilePath, []byte(htmlContent), 0644)
	if err != nil {
		return fmt.Errorf("failed to save HTML to temp file: %v", err)
	}

	g.Log().Info(nil, "GeneratePDF tempFilePath: "+tempFilePath)
	fmt.Printf("HTML saved to temporary file: %s\n", tempFilePath)

	// 使用 wkhtmltopdf 命令
	var cmd *exec.Cmd
	args := []string{"--encoding", "UTF-8"}
	if printSize == "custom" {
		args = append(args, "--page-width")
		args = append(args, gconv.String(width)+"mm")
		args = append(args, "--page-height")
		args = append(args, gconv.String(height)+"mm")
	} else {
		args = append(args, "--page-size")
		args = append(args, printSize)
	}

	args = append(args, "--orientation")
	args = append(args, printRotation)

	args = append(args,
		"--dpi", "300",
		"--no-pdf-compression",
	)
	args = append(args, margin...)
	args = append(args, "-", outputFile)
	cmd = exec.Command("wkhtmltopdf", args...)

	// g.Log().Info(gctx.New(), "GeneratePDF htmlContent:\n "+htmlContent)
	// 设置标准输入为 HTML 内容
	stdin, err := cmd.StdinPipe()
	if err != nil {
		return fmt.Errorf("failed to get stdin: %v", err)
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start command: %v", err)
	}

	// 向标准输入写入 HTML 内容
	_, err = stdin.Write([]byte(htmlContent))
	if err != nil {
		return fmt.Errorf("failed to write to stdin: %v", err)
	}
	stdin.Close()

	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		return fmt.Errorf("command finished with error: %v", err)
	}

	return nil
}

// 执行SQL查询并返回结果
func (s *sPrint) ExecuteSqlQueries(ctx context.Context, sqlQueriesJson string, formData map[string]interface{}, withTableData map[string]map[string]interface{}) (map[string]interface{}, error) {
	results := make(map[string]interface{})

	if sqlQueriesJson == "" {
		return results, nil
	}

	// 解析SQL查询配置
	var sqlQueries []map[string]interface{}
	err := json.Unmarshal([]byte(sqlQueriesJson), &sqlQueries)
	if err != nil {
		g.Log().Error(ctx, "ExecuteSqlQueries: failed to parse sqlQueries JSON", err)
		return results, nil // 不阻断打印流程，只记录错误
	}

	// 执行每个SQL查询
	for _, queryConfig := range sqlQueries {
		queryId, ok := queryConfig["id"].(string)
		if !ok {
			continue
		}

		sql, ok := queryConfig["sql"].(string)
		if !ok || sql == "" {
			continue
		}

		queryType, ok := queryConfig["type"].(string)
		if !ok {
			queryType = "table" // 默认为表格类型
		}

		// 获取参数配置
		var parameters []form.SqlParameter
		if params, ok := queryConfig["parameters"].([]interface{}); ok {
			for _, param := range params {
				if paramMap, ok := param.(map[string]interface{}); ok {
					sqlParam := form.SqlParameter{
						Parameter:    gconv.String(paramMap["parameter"]),
						FieldPath:    gconv.String(paramMap["fieldPath"]),
						DefaultValue: gconv.String(paramMap["defaultValue"]),
					}

					// 处理自定义函数代码
					if funcCode, ok := paramMap["funcCode"].(map[string]interface{}); ok {
						var scriptCode dto.ScriptCode
						err := library.ToStruct(funcCode, &scriptCode)
						if err == nil {
							sqlParam.FuncCode = &scriptCode
						}
					}

					parameters = append(parameters, sqlParam)
				}
			}
		}

		// 使用现有的SQL查询服务，传递withTableData
		req := &form.SqlQueryExecuteReq{
			Sql:           sql,
			Type:          queryType,
			Parameters:    parameters,
			FormData:      g.Map(formData),
			WithTableData: withTableData,
		}

		result, err := service.SqlQuery().ExecuteQuery(ctx, req)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("ExecuteSqlQueries: failed to execute SQL query %s", queryId), err)
			continue // 单个查询失败不阻断整个流程
		}

		results[queryId] = result.Data
	}

	return results, nil
}
