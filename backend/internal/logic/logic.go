// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package logic

import (
	_ "backend/internal/logic/am_config"
	_ "backend/internal/logic/am_config_history"
	_ "backend/internal/logic/cache"
	_ "backend/internal/logic/code_history"
	_ "backend/internal/logic/doc_process"
	_ "backend/internal/logic/excel_service"
	_ "backend/internal/logic/flow_instance"
	_ "backend/internal/logic/flow_instance_history"
	_ "backend/internal/logic/flow_instance_history_auditor"
	_ "backend/internal/logic/flow_template"
	_ "backend/internal/logic/form_check_rule"
	_ "backend/internal/logic/form_countersign_logs"
	_ "backend/internal/logic/form_data"
	_ "backend/internal/logic/form_export_history"
	_ "backend/internal/logic/form_export_template"
	_ "backend/internal/logic/form_import_history"
	_ "backend/internal/logic/form_import_template"
	_ "backend/internal/logic/form_print"
	_ "backend/internal/logic/form_print_logs"
	_ "backend/internal/logic/form_template"
	_ "backend/internal/logic/generate"
	_ "backend/internal/logic/sql_query"
	_ "backend/internal/logic/javascript"
	_ "backend/internal/logic/log"
	_ "backend/internal/logic/project"
	_ "backend/internal/logic/project_role"
	_ "backend/internal/logic/superset"
	_ "backend/internal/logic/sys_config"
	_ "backend/internal/logic/sys_dept"
	_ "backend/internal/logic/sys_menu"
	_ "backend/internal/logic/sys_post"
	_ "backend/internal/logic/sys_role"
	_ "backend/internal/logic/sys_user"
	_ "backend/internal/logic/sys_user_role"
	_ "backend/internal/logic/upload_file"
)
