package javascript

import (
	"backend/api/v1/form"
	"backend/internal/consts"
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/dop251/goja"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gres"
	"github.com/gogf/gf/v2/util/gconv"
)

type Context struct{}

// IncrByFloat 方法：接收 incrKey 和 currentNumber 参数，并返回一个累加后的数值
func (c *Context) IncrByFloat(incrKey string, currentNumber float64) (value interface{}, err error) {
	ctx := context.Background()
	return service.Cache().IncrByFloat(ctx, incrKey, currentNumber)
}
func (c *Context) IncrByFloatMD5(incrKey string, currentNumber float64) (value interface{}, err error) {
	ctx := context.Background()
	incrKey = gmd5.MustEncrypt(incrKey)
	return service.Cache().IncrByFloat(ctx, incrKey, currentNumber)
}

// getDatas 方法：接收 tableName 参数并返回一个数据列表
func (c *Context) GetDatas(params dto.GetDatasReq) *ghttp.DefaultHandlerResponse {

	ctx := context.Background()
	var data *form.TableDataListRes
	err := g.Try(ctx, func(ctx context.Context) {
		jsonParams, err := json.Marshal(params)
		library.ErrIsNil(ctx, err)
		g.Log().Info(ctx, "GetDatas:", string(jsonParams))
		cacheKey := library.GetTenantCacheKey(ctx, gmd5.MustEncrypt(consts.KeyTableDataList+string(jsonParams)))
		// 查询数据根据条件缓存十秒钟，加快批量处理的速度
		cacheRes, err := service.Cache().GetOrSetFunc(ctx, cacheKey, func(ctx context.Context) (value interface{}, err error) {
			data, err := service.FormData().TableDataList(ctx, &form.TableDataListReq{
				TableName:  params.TableName,
				TableType:  params.TableType,
				Filter:     params.Filter,
				RuleFilter: params.RuleFilter,
				PageReq: model.PageReq{
					PageSize: params.PageSize,
					PageNum:  params.PageNum,
				},
			}, true)
			if err != nil {
				return
			}
			value = data
			return
		}, 10*time.Second)
		library.ErrIsNil(ctx, err)
		err = gconv.Struct(cacheRes, &data)
		library.ErrIsNil(ctx, err)
	})

	response := &ghttp.DefaultHandlerResponse{}
	if err != nil {
		response.Code = 500
		response.Message = err.Error()
	} else {
		response.Code = 0
		response.Data = data
	}
	return response
}

// 根据id获取用户信息
func (c *Context) GetUserById(id int64) *with.SysUser {
	ctx := context.Background()
	user, err := service.SysUser().GetInfoByID(ctx, id)
	library.ErrIsNil(ctx, err)
	return user
}

// 根据用户名获取用户信息
func (c *Context) GetUserByUsername(username string) *with.SysUser {
	ctx := context.Background()
	user, err := service.SysUser().GetUserByAccount(ctx, username)
	library.ErrIsNil(ctx, err)
	return user
}

// 根据项目名获取项目信息
func (c *Context) GetProjectByName(name string) *with.AdProject {
	ctx := context.Background()
	project, err := service.Project().GetProjectByName(ctx, name)
	library.ErrIsNil(ctx, err)
	return project
}

func (c *Context) Log(content interface{}) {
	ctx := context.Background()
	g.Log().Info(ctx, library.SDump("Javascript log", content))
}

// 执行一段js代码，并返回执行结果
func executeScriptWithTimeout(vm *goja.Runtime, script string, timeout time.Duration) (goja.Value, error) {
	// 创建带有超时的 context
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	done := make(chan struct{})
	var result goja.Value
	var err error

	// 在一个 goroutine 中执行脚本
	go func() {
		result, err = vm.RunString(script)
		close(done)
	}()

	// 等待脚本执行完成或超时
	select {
	case <-done:
		// 返回结果和可能的错误
		return result, err
	case <-ctx.Done():
		vm.Interrupt("timeout")
		// 返回超时错误
		return nil, fmt.Errorf("script execution timed out")
		// 纪录超时日志和脚本内容

	}
}
func init() {
	service.RegisterJavascript(new(sJavascript))
}

type sJavascript struct {
}

// 获得一个注入了基础库的 goja.Runtime 实例
func (s *sJavascript) GetVM(props map[string]interface{}) (vm *goja.Runtime, err error) {
	vm = goja.New()
	// 注入基础库
	files := gres.ScanDirFile("resource/lib/javascript", "*.js")
	for _, file := range files {
		_, err = vm.RunString(string(file.Content()))
	}
	vm.Set("ctx", &Context{})
	vm.Set("__service", &Context{})
	for k, v := range props {
		vm.Set(k, v)
	}
	return
}

func (s *sJavascript) SetProps(ctx context.Context, vm *goja.Runtime, props map[string]interface{}) (err error) {
	for k, v := range props {
		vm.Set(k, v)
	}
	return
}

// 运行一段js代码，并返回执行结果
func (s *sJavascript) Run(ctx context.Context, funcCode dto.ScriptCode, timeout time.Duration, vm *goja.Runtime) (result goja.Value, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if vm == nil {
			vm, err = s.GetVM(nil)
			library.ErrIsNil(ctx, err)
		}
		// 注入ref

		// 执行脚本
		result, err = executeScriptWithTimeout(vm, fmt.Sprintf(`
(()=>{
%s
})()
		`, funcCode.CodeContent), timeout)
		library.ErrIsNil(ctx, err)
	})
	return
}

// 运行一段js代码，返回验证结果 bool 值
func (s *sJavascript) RunBool(ctx context.Context, formTableName string, formTableType int, formColumns []*dto.FormColumn, funcCode dto.ScriptCode, timeout time.Duration, vm *goja.Runtime) (result bool, err error) {

	// 获得_columnMap引用
	columnMap, err := s.BuildOriginColumnMap(ctx, formTableName, formTableType, funcCode.ColumnMap, formColumns)
	if err != nil {
		return
	}
	vm.Set("__columnMap", columnMap)
	runResult, err := s.Run(ctx, funcCode, timeout, vm)
	if err != nil {
		return
	}
	result = gconv.Bool(runResult)
	return
}

// 格式化映射值的函数
func (s *sJavascript) FormatMappedValue(ctx context.Context, vm *goja.Runtime, formTableName string, formTableType int, formColumns []*dto.FormColumn, value interface{}, funcCode dto.ScriptCode) (result interface{}, err error) {

	if g.IsEmpty(funcCode) || funcCode.CodeContent == "" {
		return value, nil
	}
	// 获得_columnMap引用
	columnMap, err := s.BuildOriginColumnMap(ctx, formTableName, formTableType, funcCode.ColumnMap, formColumns)
	if err != nil {
		return
	}
	vm.Set("__current", value)
	vm.Set("__columnMap", columnMap)
	// 执行脚本
	runResult, err := s.Run(ctx, funcCode, time.Second*5, vm)
	if err != nil {
		return
	}
	result = runResult.Export()
	return
}

func (s *sJavascript) BuildOriginColumnMap(ctx context.Context, formTableName string, formTableType int, columnMapRules []dto.ColumnMapRule, formColumns []*dto.FormColumn) (columnMap map[string]interface{}, err error) {
	columnMap = make(map[string]interface{})
	columnMap["formTableName"] = formTableName
	columnMap["formTableType"] = formTableType
	columns := make(map[string]interface{})
	err = g.Try(ctx, func(ctx context.Context) {
		for _, columnMapRule := range columnMapRules {
			// 规则类型 0: 普通字段 1: 子表字段 2：其他表（非关联）
			if columnMapRule.Type == 0 {
				sourceName := gconv.String(columnMapRule.Source)
				columns[columnMapRule.Target] = sourceName
			} else if columnMapRule.Type == 1 && len(columnMapRule.SubRules) > 0 {
				columnMapSubTableColumns := make(map[string]interface{})
				for _, subRule := range columnMapRule.SubRules {
					sourceSubName := gconv.String(subRule.Source)
					columnMapSubTableColumns[subRule.Target] = sourceSubName
				}
				columnMapSubTableColumns["formTableName"] = gconv.String(columnMapRule.Source)
				columns[columnMapRule.Target] = columnMapSubTableColumns
			} else if columnMapRule.Type == 2 {
				source := gconv.Map(columnMapRule.Source)
				tableName := gconv.String(source["table_name"])
				tableType := gconv.Int(source["type"])

				subColumnMap, err := s.BuildOriginColumnMap(ctx, tableName, tableType, columnMapRule.SubRules, formColumns)
				library.ErrIsNil(ctx, err)
				columns[columnMapRule.Target] = subColumnMap

			}
		}
		columnMap["columns"] = columns
	})
	return
}
