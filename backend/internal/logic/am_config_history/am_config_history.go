package amconfig

import (
	"context"
	"fmt"

	"backend/api/v1/automation"
	"backend/internal/dao"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/library"

	"backend/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

func init() {
	service.RegisterAmConfigHistory(new(sAmConfigHistory))
}

type sAmConfigHistory struct{}

// 获取自动化执行纪录列表
func (s *sAmConfigHistory) GetAutoMationList(ctx context.Context, req *automation.AmConfigHistoryListReq) (res *automation.AmConfigHistoryListRes, err error) {
	res = new(automation.AmConfigHistoryListRes)
	m := dao.TenantCtx(dao.AmConfigHistory, ctx)

	if req.AmConfigId > 0 {
		m = m.Where(dao.AmConfigHistory.Columns().AmConfigId, req.AmConfigId)
	}
	if req.StartTime != "" {
		m = m.Where(dao.AmConfigHistory.Columns().CreatedAt+" >= ", req.StartTime)
	}
	if req.EndTime != "" {
		m = m.Where(dao.AmConfigHistory.Columns().CreatedAt+" <= ", req.EndTime)
	}
	if req.Status > 0 {
		m = m.Where(dao.AmConfigHistory.Columns().Status, req.Status)
	}

	order := "id desc"
	err = g.Try(ctx, func(ctx context.Context) {
		res.Total, err = m.Count()
		library.ErrIsNil(ctx, err, "获取自动化执行纪录总数失败")
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).FieldsEx(dao.AmConfigHistory.Columns().NodeLog).Scan(&res.List)
		library.ErrIsNil(ctx, err, "获取自动化执行纪录数据失败")
	})
	return
}

// 保存自动化执行纪录数据
func (s *sAmConfigHistory) New(ctx context.Context, automationId int64, triggerData map[string]interface{}, sequence int64) (res *do.AmConfigHistory, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amConfigHistory := new(do.AmConfigHistory)
		amConfigHistory.AmConfigId = uint64(automationId)
		amConfigHistory.ResultSnap = triggerData
		amConfigHistory.CreatedAt = gtime.Now()
		amConfigHistory.Status = enum.AmStatus_Running
		if sequence > 0 {
			amConfigHistory.SequenceId = fmt.Sprintf("%d_%d", automationId, sequence)
		}
		newid, err := dao.TenantCtx(dao.AmConfigHistory, ctx).Data(amConfigHistory).InsertAndGetId()
		library.ErrIsNil(ctx, err, "创建新的自动化执行纪录失败")
		amConfigHistory.Id = newid
		res = amConfigHistory
	})
	return
}

// 保存自动化执行纪录数据
func (s *sAmConfigHistory) Update(ctx context.Context, amConfigHistory *do.AmConfigHistory) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.TenantCtx(dao.AmConfigHistory, ctx).WherePri(amConfigHistory.Id).Update(amConfigHistory)
		library.ErrIsNil(ctx, err, "更新自动化执行纪录失败")
	})
	return
}

// 删除自动化执行纪录
func (s *sAmConfigHistory) Delete(ctx context.Context, historyId uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		amconfig, err := s.GetInfoByID(ctx, historyId, false)
		library.ErrIsNil(ctx, err)
		if amconfig == nil {
			library.Fail(ctx, "信息不存在")
			return
		}
		_, err = dao.TenantCtx(dao.AmConfigHistory, ctx).WherePri(historyId).Delete()
		library.ErrIsNil(ctx, err, "删除自动化执行纪录失败")
	})
	return
}

// 获得详细信息
func (s *sAmConfigHistory) GetInfoByID(ctx context.Context, historyId uint64, with bool) (AutoMationHistory *entity.AmConfigHistory, err error) {
	m := dao.TenantCtx(dao.AmConfigHistory, ctx)
	if with {
		m = m.WithAll()
	}
	err = m.WherePri(historyId).Scan(&AutoMationHistory)
	return
}
