package v1

import (
	"context"
	"fmt"

	"backend/api/v1/form"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerForm) FormTemlateSaveFormDesign(ctx context.Context, req *form.FormTemlateSaveFormDesignReq) (res *form.FormTemlateSaveFormDesignRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	res, err = service.FormTemplate().SaveFormDesign(ctx, req)
	return
}
func (c *ControllerForm) FormTemlateSaveFormDefault(ctx context.Context, req *form.FormTemlateSaveFormDefaultReq) (res *form.FormTemlateSaveFormDefaultRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	res, err = service.FormTemplate().SaveFormDefault(ctx, req)
	return
}
func (c *ControllerForm) FormTemlateList(ctx context.Context, req *form.FormTemlateListReq) (res *form.FormTemlateListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_View), enum.SysPermission_View.Error())
	res, err = service.FormTemplate().GetList(ctx, req)
	return
}
func (c *ControllerForm) FormTemlateDelete(ctx context.Context, req *form.FormTemlateDeleteReq) (res *form.FormTemlateDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.FormTemplate().Delete(ctx, req.Id)
	return
}
func (c *ControllerForm) FormTemlateDetail(ctx context.Context, req *form.FormTemlateDetailReq) (res *form.FormTemlateDetailRes, err error) {
	// checkResult := service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_View)
	// if !checkResult {
	// 	checkResult = service.SysUser().CheckFormPermission(ctx, int64(req.Id), enum.SysPermission_View)
	// }
	// if !checkResult {
	// 	// 增加一层审批权限验证
	// }
	// library.CheckFail(ctx, checkResult, enum.SysPermission_View.Error())
	formTemplate, err := service.FormTemplate().GetFormTemplateInfo(ctx, int64(req.Id))

	if err != nil {
		return
	}
	if formTemplate == nil {
		err = fmt.Errorf("表单模板不存在")
		return
	}

	res = &form.FormTemlateDetailRes{
		FormTemplate: formTemplate,
	}

	return
}
func (c *ControllerForm) SupportTableList(ctx context.Context, req *form.SupportTableListReq) (res *form.SupportTableListRes, err error) {
	res, err = service.FormTemplate().GetRelatedTables(ctx, req)
	return
}

func (c *ControllerForm) SupportFieldsList(ctx context.Context, req *form.SupportFieldsListReq) (res *form.SupportFieldsListRes, err error) {
	res, err = service.FormTemplate().GetRelatedFields(ctx, req)
	return
}
func (c *ControllerForm) SupportColumnList(ctx context.Context, req *form.SupportColumnListReq) (res *form.SupportColumnListRes, err error) {
	res, err = service.FormTemplate().GetRelatedColumns(ctx, req)
	return
}

func (c *ControllerForm) FormTemlateSaveExtKey(ctx context.Context, req *form.FormTemlateSaveExtKeyReq) (res *form.FormTemlateSaveExtKeyRes, err error) {
	err = service.FormTemplate().SetTemplateIdByExpandKey(ctx, req.ExtKey, req.Id)
	return
}
func (c *ControllerForm) FormTemlatePermissionSetting(ctx context.Context, req *form.FormTemlatePermissionSettingReq) (res *form.FormTemlatePermissionSettingRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form_Permission, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.FormTemplate().PermissionSettle(ctx, req)
	return
}
func (c *ControllerForm) FormTemlatePermissionList(ctx context.Context, req *form.FormTemlatePermissionListReq) (res *form.FormTemlatePermissionListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form_Permission, enum.SysPermission_View), enum.SysPermission_View.Error())
	listRes, err := service.FormTemplate().GetList(ctx, &form.FormTemlateListReq{
		ListReqStruct: req.ListReqStruct,
	})
	if err != nil {
		return
	}
	res = &form.FormTemlatePermissionListRes{
		ListResStruct: listRes.ListResStruct,
	}
	return
}
