package v1

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/service"
)

func (c *ControllerForm) FormImportHistoryList(ctx context.Context, req *form.FormImportHistoryListReq) (res *form.FormImportHistoryListRes, err error) {
	return service.FormImportHistory().GetFormImportHistoryList(ctx, req)
}
func (c *ControllerForm) FormImportHistoryDetail(ctx context.Context, req *form.FormImportHistoryDetailReq) (res *form.FormImportHistoryDetailRes, err error) {
	info, err := service.FormImportHistory().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return nil, err
	}
	res = &form.FormImportHistoryDetailRes{
		FormImportHistory: info,
	}
	return
}
func (c *ControllerForm) FormImportHistoryNew(ctx context.Context, req *form.FormImportHistoryNewReq) (res *form.FormImportHistoryNewRes, err error) {
	return service.FormImportHistory().New(ctx, req)
}
