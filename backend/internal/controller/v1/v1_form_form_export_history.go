package v1

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/service"
)

func (c *ControllerForm) FormExportHistoryList(ctx context.Context, req *form.FormExportHistoryListReq) (res *form.FormExportHistoryListRes, err error) {
	return service.FormExportHistory().GetFormExportHistoryList(ctx, req)
}
func (c *ControllerForm) FormExportHistoryDetail(ctx context.Context, req *form.FormExportHistoryDetailReq) (res *form.FormExportHistoryDetailRes, err error) {
	info, err := service.FormExportHistory().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return nil, err
	}
	res = &form.FormExportHistoryDetailRes{
		FormExportHistory: info,
	}
	return
}
func (c *ControllerForm) FormExportHistoryNew(ctx context.Context, req *form.FormExportHistoryNewReq) (res *form.FormExportHistoryNewRes, err error) {

	return service.FormExportHistory().New(ctx, req)
}
