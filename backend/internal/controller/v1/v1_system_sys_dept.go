package v1

import (
	"context"

	"backend/api/v1/system"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/v2/util/gconv"
)

func (c *ControllerSystem) DeptSearch(ctx context.Context, req *system.DeptSearchReq) (res *system.DeptSearchRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_View), enum.SysPermission_View.Error())
	res = new(system.DeptSearchRes)
	res.DeptList, err = service.SysDept().GetList(ctx, req)
	return
}
func (c *ControllerSystem) DeptSave(ctx context.Context, req *system.DeptSaveReq) (res *system.DeptSaveRes, err error) {
	if req.DeptID > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	err = service.SysDept().Save(ctx, req)
	return
}
func (c *ControllerSystem) DeptDelete(ctx context.Context, req *system.DeptDeleteReq) (res *system.DeptDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysDept().Delete(ctx, int64(req.Id))
	return
}
func (c *ControllerSystem) DeptInfo(ctx context.Context, req *system.DeptInfoReq) (res *system.DeptInfoRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_View), enum.SysPermission_View.Error())
	dept, err := service.SysDept().GetInfoByID(ctx, int64(req.Id))

	if err != nil {
		return
	}

	if dept == nil {
		return
	}
	err = gconv.Struct(dept, &res)
	return

}
