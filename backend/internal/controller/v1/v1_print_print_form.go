package v1

import (
	"context"

	"backend/api/v1/print"
	"backend/internal/service"
)

func (c *ControllerPrint) PrintForm(ctx context.Context, req *print.PrintFormReq) (res *print.PrintFormRes, err error) {
	return service.Print().GenerateFormToPDF(ctx, req, false)
}

// PrintFormHtml retrieves the HTML content for a form print template.
func (c *ControllerPrint) PrintFormHtml(ctx context.Context, req *print.PrintFormHtmlReq) (res *print.PrintFormHtmlRes, err error) {
	return service.Print().GenerateFormToHtml(ctx, req)
}

// GeneratePdfFromHtml generates a PDF from the provided HTML content.
func (c *ControllerPrint) GeneratePdfFromHtml(ctx context.Context, req *print.GeneratePdfFromHtmlReq) (res *print.GeneratePdfFromHtmlRes, err error) {
	return service.Print().GeneratePdfFromHtmlContent(ctx, req)
}
