package v1

import (
	"context"

	"backend/api/v1/system"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/v2/util/gconv"
)

func (c *ControllerSystem) MenuSearch(ctx context.Context, req *system.MenuSearchReq) (res *system.MenuSearchRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Menu, enum.SysPermission_View), enum.SysPermission_View.Error())
	res = new(system.MenuSearchRes)
	res.MenuList, err = service.SysMenu().GetList(ctx, req)
	return
}
func (c *ControllerSystem) MenuSave(ctx context.Context, req *system.MenuSaveReq) (res *system.MenuSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Menu, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Menu, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	err = service.SysMenu().Save(ctx, req)
	return
}
func (c *ControllerSystem) MenuDelete(ctx context.Context, req *system.MenuDeleteReq) (res *system.MenuDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Menu, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysMenu().Delete(ctx, int64(req.Id))
	return
}
func (c *ControllerSystem) MenuInfo(ctx context.Context, req *system.MenuInfoReq) (res *system.MenuInfoRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Menu, enum.SysPermission_View), enum.SysPermission_View.Error())
	post, err := service.SysMenu().GetInfoByID(ctx, int64(req.Id))
	if err != nil {
		return
	}

	if post == nil {
		return
	}
	res = &system.MenuInfoRes{
		SysMenu: &with.SysMenu{},
	}
	err = gconv.Struct(post, &res)
	return
}
func (c *ControllerSystem) MenuTSearch(ctx context.Context, req *system.MenuTSearchReq) (res *system.MenuTSearchRes, err error) {
	res = new(system.MenuTSearchRes)
	res.MenuList, err = service.SysMenu().TGetList(ctx)
	return
}
func (c *ControllerSystem) MenuMove(ctx context.Context, req *system.MenuMoveReq) (res *system.MenuMoveRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Menu, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysMenu().MoveSort(ctx, req.Id, req.Direction)
	return
}
