package v1

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/service"
)

func (c *ControllerForm) FormImportTemplateList(ctx context.Context, req *form.FormImportTemplateListReq) (res *form.FormImportTemplateListRes, err error) {
	res, err = service.FormImportTemplate().GetFormImportTemplateList(ctx, req)
	return
}
func (c *ControllerForm) FormImportTemplateDetail(ctx context.Context, req *form.FormImportTemplateDetailReq) (res *form.FormImportTemplateDetailRes, err error) {
	info, err := service.FormImportTemplate().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &form.FormImportTemplateDetailRes{
		FormImportTemplate: info,
	}
	return
}
func (c *ControllerForm) FormImportTemplateDelete(ctx context.Context, req *form.FormImportTemplateDeleteReq) (res *form.FormImportTemplateDeleteRes, err error) {
	err = service.FormImportTemplate().Delete(ctx, req)
	return
}
func (c *ControllerForm) FormImportTemplateSave(ctx context.Context, req *form.FormImportTemplateSaveReq) (res *form.FormImportTemplateSaveRes, err error) {
	res, err = service.FormImportTemplate().Save(ctx, req)
	return
}
func (c *ControllerForm) FormImportTemplateStatus(ctx context.Context, req *form.FormImportTemplateStatusReq) (res *form.FormImportTemplateStatusRes, err error) {
	err = service.FormImportTemplate().UpdateStatus(ctx, req.Id, req.Enabled)
	return
}
