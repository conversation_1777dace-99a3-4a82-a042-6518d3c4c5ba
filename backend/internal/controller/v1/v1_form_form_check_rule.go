package v1

import (
	"backend/api/v1/form"
	"backend/internal/service"
	"context"
)

func (c *ControllerForm) FormCheckRuleList(ctx context.Context, req *form.FormCheckRuleListReq) (res *form.FormCheckRuleListRes, err error) {
	res, err = service.FormCheckRule().GetFormCheckRuleList(ctx, req)
	return
}
func (c *ControllerForm) FormCheckRuleDetail(ctx context.Context, req *form.FormCheckRuleDetailReq) (res *form.FormCheckRuleDetailRes, err error) {
	info, err := service.FormCheckRule().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &form.FormCheckRuleDetailRes{
		FormCheckRule: info,
	}
	return
}
func (c *ControllerForm) FormCheckRuleDelete(ctx context.Context, req *form.FormCheckRuleDeleteReq) (res *form.FormCheckRuleDeleteRes, err error) {
	err = service.FormCheckRule().Delete(ctx, req)
	return
}
func (c *ControllerForm) FormCheckRuleSave(ctx context.Context, req *form.FormCheckRuleSaveReq) (res *form.FormCheckRuleSaveRes, err error) {
	res, err = service.FormCheckRule().Save(ctx, req)
	return
}
func (c *ControllerForm) FormCheckRuleStatus(ctx context.Context, req *form.FormCheckRuleStatusReq) (res *form.FormCheckRuleStatusRes, err error) {
	err = service.FormCheckRule().UpdateStatus(ctx, req.Id, req.Enabled)
	return
}
