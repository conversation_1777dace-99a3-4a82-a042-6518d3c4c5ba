// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package v1

import (
	v1 "backend/api/v1"
)

type ControllerDemo struct{}

func NewDemo() v1.IV1Demo {
	return &ControllerDemo{}
}

type ControllerSaas struct{}

func NewSaas() v1.IV1Saas {
	return &ControllerSaas{}
}

type ControllerSystem struct{}

func NewSystem() v1.IV1System {
	return &ControllerSystem{}
}

type ControllerLogin struct{}

func NewLogin() v1.IV1Login {
	return &ControllerLogin{}
}

type ControllerFile struct{}

func NewFile() v1.IV1File {
	return &ControllerFile{}
}

type ControllerFlow struct{}

func NewFlow() v1.IV1Flow {
	return &ControllerFlow{}
}

type ControllerProject struct{}

func NewProject() v1.IV1Project {
	return &ControllerProject{}
}

type ControllerForm struct{}

func NewForm() v1.IV1Form {
	return &ControllerForm{}
}

type ControllerAutomation struct{}

func NewAutomation() v1.IV1Automation {
	return &ControllerAutomation{}
}

type ControllerPrint struct{}

func NewPrint() v1.IV1Print {
	return &ControllerPrint{}
}

type ControllerCode struct{}

func NewCode() v1.IV1Code {
	return &ControllerCode{}
}

type ControllerSuperset struct{}

func NewSuperset() v1.IV1Superset {
	return &ControllerSuperset{}
}
