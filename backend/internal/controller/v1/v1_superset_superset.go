package v1

import (
	"context"

	"backend/api/v1/superset"
	"backend/internal/service"
)

func (c *ControllerSuperset) AccessToken(ctx context.Context, req *superset.AccessTokenReq) (res *superset.AccessTokenRes, err error) {
	// 需要额外的权限管理处理（这个地方又安全性问题）
	// 调用 superset 服务获取访问令牌
	accessToken, err := service.Superset().GetAccessToken(ctx)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &superset.AccessTokenRes{
		AccessToken: accessToken,
	}

	return res, nil
}
func (c *ControllerSuperset) EmbeddedGuestToken(ctx context.Context, req *superset.EmbeddedGuestTokenReq) (res *superset.EmbeddedGuestTokenRes, err error) {
	// 调用 superset 服务获取嵌入的 guest token
	token, uuid, url, expiresAt, err := service.Superset().GetEmbeddedGuestToken(ctx, req.DashboardID)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &superset.EmbeddedGuestTokenRes{
		Token:     token,
		URL:       url,
		ExpiresAt: expiresAt,
		UUID:      uuid,
	}

	return res, nil
}

func (c *ControllerSuperset) GuestToken(ctx context.Context, req *superset.GuestTokenReq) (res *superset.GuestTokenRes, err error) {
	// 调用 superset 服务获取 guest token
	token, url, expiresAt, err := service.Superset().GetGuestToken(ctx, req.ResourceID, req.ResourceType)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &superset.GuestTokenRes{
		Token:     token,
		URL:       url,
		ExpiresAt: expiresAt,
	}

	return res, nil
}

func (c *ControllerSuperset) DashboardList(ctx context.Context, req *superset.DashboardListReq) (res *superset.DashboardListRes, err error) {
	// 创建请求参数
	dashboardReq := service.DashboardListRequest{
		Page:     req.PageNum,
		PageSize: req.PageSize,
	}

	// 调用 superset 服务获取仪表盘列表
	dashboardRes, err := service.Superset().GetDashboardList(ctx, dashboardReq)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &superset.DashboardListRes{
		List: dashboardRes.Result,
	}
	res.Total = dashboardRes.Count
	res.CurrentPage = req.PageNum

	return res, nil
}
