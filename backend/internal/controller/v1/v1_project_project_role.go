package v1

import (
	"context"

	"backend/api/v1/project"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerProject) AdProjectRoleList(ctx context.Context, req *project.AdProjectRoleListReq) (res *project.AdProjectRoleListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project_Role, enum.SysPermission_View), enum.SysPermission_View.Error())
	res, err = service.ProjectRole().GetProjectRoleList(ctx, req)
	return
}
func (c *ControllerProject) AdProjectRoleDetail(ctx context.Context, req *project.AdProjectRoleDetailReq) (res *project.AdProjectRoleDetailRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project_Role, enum.SysPermission_View), enum.SysPermission_View.Error())
	role, err := service.ProjectRole().GetInfoByID(ctx, uint64(req.Id))
	if err != nil {
		return
	}
	res = &project.AdProjectRoleDetailRes{
		AdProjectRole: role,
	}
	return
}
func (c *ControllerProject) AdProjectRoleDelete(ctx context.Context, req *project.AdProjectRoleDeleteReq) (res *project.AdProjectRoleDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project_Role, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.ProjectRole().Delete(ctx, req)
	return
}
func (c *ControllerProject) AdProjectRoleSave(ctx context.Context, req *project.AdProjectRoleSaveReq) (res *project.AdProjectRoleSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project_Role, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project_Role, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	res, err = service.ProjectRole().Save(ctx, req)
	return
}
