package v1

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/service"
)

func (c *ControllerForm) FormExportTemplateList(ctx context.Context, req *form.FormExportTemplateListReq) (res *form.FormExportTemplateListRes, err error) {
	res, err = service.FormExportTemplate().GetFormExportTemplateList(ctx, req)
	return
}
func (c *ControllerForm) FormExportTemplateDetail(ctx context.Context, req *form.FormExportTemplateDetailReq) (res *form.FormExportTemplateDetailRes, err error) {
	info, err := service.FormExportTemplate().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &form.FormExportTemplateDetailRes{
		FormExportTemplate: info,
	}
	return
}
func (c *ControllerForm) FormExportTemplateDelete(ctx context.Context, req *form.FormExportTemplateDeleteReq) (res *form.FormExportTemplateDeleteRes, err error) {
	err = service.FormExportTemplate().Delete(ctx, req)
	return
}
func (c *ControllerForm) FormExportTemplateSave(ctx context.Context, req *form.FormExportTemplateSaveReq) (res *form.FormExportTemplateSaveRes, err error) {
	res, err = service.FormExportTemplate().Save(ctx, req)
	return
}
func (c *ControllerForm) FormExportTemplateStatus(ctx context.Context, req *form.FormExportTemplateStatusReq) (res *form.FormExportTemplateStatusRes, err error) {
	err = service.FormExportTemplate().UpdateStatus(ctx, req.Id, req.Enabled)
	return
}
