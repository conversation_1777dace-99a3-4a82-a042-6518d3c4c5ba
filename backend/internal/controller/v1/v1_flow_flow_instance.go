package v1

import (
	"context"

	"backend/api/v1/flow"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"

	"github.com/samber/lo"
)

func (c *ControllerFlow) Create(ctx context.Context, req *flow.CreateReq) (res *flow.CreateRes, err error) {
	// 创建流程实例
	_, err = service.FlowInstance().Create(ctx, req.FormTemplateId, req.FormId, &req.ExtraData, false, enum.FlowInstanceType_Create, int64(library.GetUserId(ctx)))
	if err != nil {
		return
	}
	return
}
func (c *ControllerFlow) Approve(ctx context.Context, req *flow.ApproveReq) (res *flow.ApproveRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_Flow_Pending, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.FlowInstanceHistoryAuditor().UserApprove(ctx, req)
	return
}
func (c *ControllerFlow) MyList(ctx context.Context, req *flow.MyListReq) (res *flow.MyListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_Flow_Pending, enum.SysPermission_View), enum.SysPermission_View.Error())
	myListReq := lo.Ternary(req == nil, new(dto.HistoryAuditorListReq), req.HistoryAuditorListReq)
	myListReq.ApprovalUser = int64(library.GetUserId(ctx))
	myListReq.ApprovalStatus = enum.NodeActionType_Pending
	res = new(flow.MyListRes)
	res.HistoryAuditorListRes, err = service.FlowInstanceHistoryAuditor().GetList(ctx, myListReq)
	return
}
func (c *ControllerFlow) MyCcList(ctx context.Context, req *flow.MyCcListReq) (res *flow.MyCcListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_Flow_Cc, enum.SysPermission_View), enum.SysPermission_View.Error())
	MyCcListReq := lo.Ternary(req == nil, new(dto.HistoryAuditorListReq), req.HistoryAuditorListReq)
	MyCcListReq.ApprovalUser = int64(library.GetUserId(ctx))
	MyCcListReq.NodeType = enum.NodeType_CcNode
	res = new(flow.MyCcListRes)
	res.HistoryAuditorListRes, err = service.FlowInstanceHistoryAuditor().GetList(ctx, MyCcListReq)
	return
}
func (c *ControllerFlow) MyStartList(ctx context.Context, req *flow.MyStartListReq) (res *flow.MyStartListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_Flow_Initiated, enum.SysPermission_View), enum.SysPermission_View.Error())
	MyStartListReq := lo.Ternary(req == nil, new(dto.HistoryAuditorListReq), req.HistoryAuditorListReq)
	MyStartListReq.ApprovalUser = int64(library.GetUserId(ctx))
	MyStartListReq.NodeType = enum.NodeType_StartNode
	MyStartListReq.IsAutoPass = 1
	res = new(flow.MyStartListRes)
	res.HistoryAuditorListRes, err = service.FlowInstanceHistoryAuditor().GetList(ctx, MyStartListReq)
	return
}
func (c *ControllerFlow) MyDoneList(ctx context.Context, req *flow.MyDoneListReq) (res *flow.MyDoneListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_Flow_Completed, enum.SysPermission_View), enum.SysPermission_View.Error())
	MyDoneListReq := lo.Ternary(req == nil, new(dto.HistoryAuditorListReq), req.HistoryAuditorListReq)
	MyDoneListReq.ApprovalUser = int64(library.GetUserId(ctx))
	MyDoneListReq.ApprovalStatusType = 1
	MyDoneListReq.ApprovalStatus = enum.NodeActionType_Pending
	MyDoneListReq.IsAutoPass = 0
	res = new(flow.MyDoneListRes)
	res.HistoryAuditorListRes, err = service.FlowInstanceHistoryAuditor().GetList(ctx, MyDoneListReq)
	return
}
func (c *ControllerFlow) Get(ctx context.Context, req *flow.GetReq) (res *flow.GetRes, err error) {
	flowInstance, err := service.FlowInstance().GetInstanceByForm(ctx, req.FormTemplateId, req.FormId)
	if err != nil {
		return
	}
	res = &flow.GetRes{
		FlowInstance: flowInstance,
	}
	return
}
func (c *ControllerFlow) AuditInstances(ctx context.Context, req *flow.AuditInstancesReq) (res *flow.AuditInstancesRes, err error) {
	list, err := service.FlowInstance().GetAllAuditInstances(ctx, req.FormTemplateId, req.FormId)
	if err != nil {
		return
	}
	res = &flow.AuditInstancesRes{
		List: list,
	}
	return
}
func (c *ControllerFlow) InstanceInfo(ctx context.Context, req *flow.InstanceInfoReq) (res *flow.InstanceInfoRes, err error) {
	instance, err := service.FlowInstance().GetInstanceInfo(ctx, req.InstanceId)
	if err != nil {
		return
	}
	res = &flow.InstanceInfoRes{
		FlowInstance: instance,
	}
	return
}
