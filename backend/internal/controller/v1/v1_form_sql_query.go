package v1

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/service"
)

// 测试SQL查询
func (c *ControllerForm) SqlQueryTest(ctx context.Context, req *form.SqlQueryTestReq) (res *form.SqlQueryTestRes, err error) {
	// 注意：这里可以根据需要添加权限检查
	// library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_View), enum.SysPermission_View.Error())

	res, err = service.SqlQuery().TestQuery(ctx, req)
	return
}

// 执行SQL查询
func (c *ControllerForm) SqlQueryExecute(ctx context.Context, req *form.SqlQueryExecuteReq) (res *form.SqlQueryExecuteRes, err error) {
	// 注意：这里可以根据需要添加权限检查
	// library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_View), enum.SysPermission_View.Error())

	res, err = service.SqlQuery().ExecuteQuery(ctx, req)
	return
}

// 验证SQL语法
func (c *ControllerForm) SqlQueryValidate(ctx context.Context, req *form.SqlQueryValidateReq) (res *form.SqlQueryValidateRes, err error) {
	// 注意：这里可以根据需要添加权限检查
	// library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_View), enum.SysPermission_View.Error())

	res, err = service.SqlQuery().ValidateSQL(ctx, req)
	return
}

// 获取数据库结构
func (c *ControllerForm) SqlQuerySchema(ctx context.Context, req *form.SqlQuerySchemaReq) (res *form.SqlQuerySchemaRes, err error) {
	// 注意：这里可以根据需要添加权限检查
	// library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Form, enum.SysPermission_View), enum.SysPermission_View.Error())

	res, err = service.SqlQuery().GetDatabaseSchema(ctx, req)
	return
}
