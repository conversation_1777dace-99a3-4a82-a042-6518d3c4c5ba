package v1

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	"backend/api/v1/system"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerSystem) SysUserList(ctx context.Context, req *system.SysUserListReq) (res *system.SysUserListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_View), enum.SysPermission_View.Error())
	res, err = service.SysUser().GetList(ctx, req)
	return
}
func (c *ControllerSystem) SysUserSave(ctx context.Context, req *system.SysUserSaveReq) (res *system.SysUserSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	userId, err := service.SysUser().Save(ctx, req)
	if err != nil {
		return
	}
	res = &system.SysUserSaveRes{
		UserId: userId,
	}
	return
}
func (c *ControllerSystem) SysUserDelete(ctx context.Context, req *system.SysUserDeleteReq) (res *system.SysUserDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysUser().Delete(ctx, req.Id)
	return
}
func (c *ControllerSystem) SysUserUpdateStatus(ctx context.Context, req *system.SysUserUpdateStatusReq) (res *system.SysUserUpdateStatusRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
func (c *ControllerSystem) SysUserResetPwd(ctx context.Context, req *system.SysUserResetPwdReq) (res *system.SysUserResetPwdRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysUser().EditPassword(ctx, req.Id, "")
	return
}
func (c *ControllerSystem) SysUserUpdatePwd(ctx context.Context, req *system.SysUserUpdatePwdReq) (res *system.SysUserUpdatePwdRes, err error) {
	user, err := service.SysUser().GetInfoByID(ctx, gconv.Int64(library.GetUserId(ctx)))
	if err != nil {
		return
	}
	g.Log().Info(ctx, library.SDump("user", req))
	if user.PasswordChanged == 1 && g.IsEmpty(req.OldPwd) {
		err = fmt.Errorf("旧密码不能为空")
		return
	}

	if !g.IsEmpty(req.OldPwd) && library.EnPwd(req.OldPwd, user.Usersale) != user.Password {
		err = fmt.Errorf("旧密码错误")
		return
	}
	err = service.SysUser().EditPassword(ctx, gconv.Int64(library.GetUserId(ctx)), req.NewPwd)
	return
}
func (c *ControllerSystem) SysUserDetail(ctx context.Context, req *system.SysUserDetailReq) (res *system.SysUserDetailRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Dept_User, enum.SysPermission_View), enum.SysPermission_View.Error())
	user, err := service.SysUser().GetInfoByID(ctx, int64(req.Id))
	if err != nil {
		return
	}

	if user == nil {
		return
	}
	err = gconv.Struct(user, &res)
	return
}
func (c *ControllerSystem) CurrentSysUser(ctx context.Context, req *system.CurrentSysUserReq) (res *system.CurrentSysUserRes, err error) {
	res, err = service.SysUser().GetCurrentUser(ctx)
	return
}
