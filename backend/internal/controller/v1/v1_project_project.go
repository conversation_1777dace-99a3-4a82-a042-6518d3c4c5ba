package v1

import (
	"context"

	"backend/api/v1/project"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerProject) AdProjectList(ctx context.Context, req *project.AdProjectListReq) (res *project.AdProjectListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project, enum.SysPermission_View), enum.SysPermission_View.Error())
	res, err = service.Project().GetProjectList(ctx, req)
	return
}
func (c *ControllerProject) AdProjectDetail(ctx context.Context, req *project.AdProjectDetailReq) (res *project.AdProjectDetailRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project, enum.SysPermission_View), enum.SysPermission_View.Error())
	info, err := service.Project().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &project.AdProjectDetailRes{
		AdProject: info,
	}
	return
}
func (c *ControllerProject) AdProjectDelete(ctx context.Context, req *project.AdProjectDeleteReq) (res *project.AdProjectDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.Project().Delete(ctx, req)
	return
}
func (c *ControllerProject) AdProjectSave(ctx context.Context, req *project.AdProjectSaveReq) (res *project.AdProjectSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemBase_Project, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	res, err = service.Project().Save(ctx, req)
	return
}
