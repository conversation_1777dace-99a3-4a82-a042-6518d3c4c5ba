package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"backend/api/v1/demo"
	"backend/internal/consts"
	"backend/internal/service"
	"backend/queue"
	"backend/websocket"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gctx"
	"github.com/gogf/gf/util/gconv"
)

func (c *ControllerDemo) Hello(ctx context.Context, req *demo.HelloReq) (res *demo.HelloRes, err error) {
	queue.QueueManager().Publish(consts.StreamSubjectFlowFinish, "hello world")
	return
}
func (c *ControllerDemo) HelloConfig(ctx context.Context, req *demo.HelloConfigReq) (res *demo.HelloConfigRes, err error) {
	_ = service.SysConfig().SetValue(ctx, "hello", "world", 0)
	value, err := service.SysConfig().GetValue(ctx, "hello")
	if err != nil {
		return
	}
	res = &demo.HelloConfigRes{
		Value: value,
	}
	return
}
func (c *ControllerDemo) HelloCache(ctx context.Context, req *demo.HelloCacheReq) (res *demo.HelloCacheRes, err error) {
	_ = service.Cache().Set(ctx, "hello2", "world", 0)

	value, err := service.Cache().Get(ctx, "hello2")

	service.Cache().SAdd(ctx, "hello3", "1", "2", "3", "4", "5")
	service.Cache().SAdd(ctx, "hello3", "5")

	if err != nil {
		return
	}
	vars, err := service.Cache().SMembers(ctx, "hello3")
	vars4, err := service.Cache().SMembers(ctx, "hello4")

	if err != nil {
		return
	}

	res = &demo.HelloCacheRes{
		Value:    gconv.String(value),
		Members:  vars.Int64s(),
		Members2: vars4.Int64s(),
	}
	return
}
func (c *ControllerDemo) HelloNextId(ctx context.Context, req *demo.HelloNextIdReq) (res *demo.HelloNextIdRes, err error) {
	id, err := service.Cache().GetNextIdString(ctx, "hello", 5)
	if err != nil {
		return
	}
	res = &demo.HelloNextIdRes{
		Value: id,
	}
	return
}
func (c *ControllerDemo) StartInstance(ctx context.Context, req *demo.StartInstanceReq) (res *demo.StartInstanceRes, err error) {
	instance, err := service.FlowInstance().GetInstance(ctx, 4)
	if err != nil {
		return
	}
	queue.QueueManager().Publish(consts.StreamSubjectFlowCreated, instance)
	return
}
func (c *ControllerDemo) HelloDemo(ctx context.Context, req *demo.HelloDemoReq) (res *demo.HelloDemoRes, err error) {

	// instance := &dto.FlowInstance{
	// 	FinishdStepsUn: make([]string, 0),
	// }
	for i := 0; i < 10; i++ {
		time.Sleep(200 * time.Millisecond)
		instance, _ := service.FlowInstance().GetInstance(ctx, 51)
		g.Log().Info(ctx, fmt.Sprintf("info.FinishdStepsUn:%v", instance.FinishdStepsUn))
		go test(instance)
	}
	return
}

func test(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	g.Log().Info(gctx.New(), fmt.Sprintf("发送主题 数据 [%s] ", string(jsonData)))
	return nil
}
func (c *ControllerDemo) HelloFlowCreatedReload(ctx context.Context, req *demo.HelloFlowCreatedReloadReq) (res *demo.HelloFlowCreatedReloadRes, err error) {
	instance, err := service.FlowInstance().GetInstance(ctx, req.Id)
	if err != nil {
		return
	}
	if instance == nil {
		return
	}
	err = queue.QueueManager().Publish(consts.StreamSubjectFlowCreated, map[string]interface{}{
		"id": req.Id,
	})
	if err != nil {
		return
	}
	return
}
func (c *ControllerDemo) HelloQueueTest(ctx context.Context, req *demo.HelloQueueTestReq) (res *demo.HelloQueueTestRes, err error) {
	if req.Key != "andiguang@240527" {
		return
	}
	queue.QueueManager().Publish(req.Subject, req.Data)
	return
}
func (c *ControllerDemo) HelloWebsocketBroadcast(ctx context.Context, req *demo.HelloWebsocketBroadcastReq) (res *demo.HelloWebsocketBroadcastRes, err error) {
	websocket.GetServer().SendMessageToAll([]byte(req.Message))
	return
}
