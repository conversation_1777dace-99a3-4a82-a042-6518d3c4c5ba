package v1

import (
	"context"
	"fmt"

	"backend/api/v1/form"
	model "backend/internal/model/common"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/util/gconv"
	"github.com/samber/lo"
)

func (c *ControllerForm) FormDataList(ctx context.Context, req *form.FormDataListReq) (res *form.FormDataListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, req.FormId, enum.SysPermission_View), enum.SysPermission_View.Error())
	unlimitedSearch := service.SysUser().CheckFormPermission(ctx, req.FormId, enum.SysPermission_UnlimitedSearch)
	res, err = service.FormData().GetList(ctx, req, unlimitedSearch, nil)
	return
}
func (c *ControllerForm) FormDataSave(ctx context.Context, req *form.FormDataSaveReq) (res *form.FormDataSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, req.FormId, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, req.FormId, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	req.AllowUpdatePartly = false
	res, err = service.FormData().Save(ctx, req, nil)
	return
}
func (c *ControllerForm) ColumnWithList(ctx context.Context, req *form.ColumnWithListReq) (res *form.ColumnWithListRes, err error) {
	res, err = service.FormData().GetColumnWithList(ctx, req)
	return
}

func (c *ControllerForm) ColumnWithInfo(ctx context.Context, req *form.ColumnWithInfoReq) (res *form.ColumnWithInfoRes, err error) {
	res, err = service.FormData().GetColumnWithInfo(ctx, req)
	return
}

func (c *ControllerForm) FormDataDelete(ctx context.Context, req *form.FormDataDeleteReq) (res *form.FormDataDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, req.FormId, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.FormData().Delete(ctx, req.FormId, req.Id)
	return
}
func (c *ControllerForm) FormDataInfo(ctx context.Context, req *form.FormDataInfoReq) (res *form.FormDataInfoRes, err error) {
	info, _, err := service.FormData().GetDetail(ctx, req.FormId, req.Id)
	if err != nil {
		return
	}
	res = &form.FormDataInfoRes{
		Info: info,
	}
	return
}
func (c *ControllerForm) FormDataListCustom(ctx context.Context, req *form.FormDataListCustomReq) (res *form.FormDataListCustomRes, err error) {
	res, err = service.FormData().GetListCustom(ctx, req)
	return
}

func (c *ControllerForm) FormExpandInfo(ctx context.Context, req *form.FormExpandInfoReq) (res *form.FormExpandInfoRes, err error) {
	expandInfo, err := service.FormData().GetExpandInfo(ctx, req.ExpandKey, req.ExpandDataId)
	if err != nil {
		return
	}

	res = &form.FormExpandInfoRes{
		ExpandInfo: expandInfo,
	}
	return
}

func (c *ControllerForm) TableDataList(ctx context.Context, req *form.TableDataListReq) (res *form.TableDataListRes, err error) {
	supportTables, err := service.FormTemplate().GetSupportTablesCache(ctx)
	if err != nil {
		return
	}
	supportTable, hasTable := lo.Find(supportTables, func(s *dto.SupportTable) bool {
		return s.TableName == gconv.String(req.TableName) && s.Type == req.TableType
	})
	if !hasTable {
		err = fmt.Errorf("表[%s]查询不受支持", req.TableName)
		return
	}

	if supportTable.Type == 0 {
		// 验证权限
		library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, supportTable.FormTemplateId, enum.SysPermission_View), enum.SysPermission_View.Error())
		var formDataListRes *form.FormDataListRes
		formDataListRes, err = service.FormData().GetList(ctx, &form.FormDataListReq{
			CommonFormDataReq: form.CommonFormDataReq{
				FormId:     supportTable.FormTemplateId,
				Filter:     req.Filter,
				RuleFilter: req.RuleFilter,
			},
		}, true, nil)
		if err != nil {
			return
		}
		res = &form.TableDataListRes{
			List:    formDataListRes.List,
			ListRes: formDataListRes.ListRes,
		}
		return
	} else if supportTable.PermissionMark != "" {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, supportTable.PermissionMark, enum.SysPermission_View), enum.SysPermission_View.Error())
		var list []map[string]interface{}
		var total int
		list, total, err = service.FormData().GetCustomTableDatas(ctx, supportTable.TableName, req.Filter, req.RuleFilter, req.PageNum, req.PageSize)
		if err != nil {
			return
		}
		res = &form.TableDataListRes{
			List: list,
			ListRes: model.ListRes{
				CurrentPage: req.PageNum,
				Total:       total,
			},
		}
		return
	}
	return
}
func (c *ControllerForm) FormDataInvalidate(ctx context.Context, req *form.FormDataInvalidateReq) (res *form.FormDataInvalidateRes, err error) {
	err = service.FormData().Invalidate(ctx, req.ExtraData, req.FormId, req.Id)
	return
}
func (c *ControllerForm) FormDataListSum(ctx context.Context, req *form.FormDataListSumReq) (res *form.FormDataListSumRes, err error) {
	res, err = service.FormData().GetFieldSum(ctx, req, true, nil)
	return
}

func (c *ControllerForm) FormDataUpdateField(ctx context.Context, req *form.FormDataUpdateFieldReq) (res *form.FormDataUpdateFieldRes, err error) {
	// 检查编辑权限
	library.CheckFail(ctx, service.SysUser().CheckFormPermission(ctx, req.FormId, enum.SysPermission_Change), enum.SysPermission_Change.Error())

	err = service.FormData().UpdateSingleField(ctx, req)
	if err != nil {
		return
	}

	res = &form.FormDataUpdateFieldRes{}
	return
}
