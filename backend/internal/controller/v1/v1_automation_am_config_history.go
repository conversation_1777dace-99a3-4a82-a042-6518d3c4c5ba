package v1

import (
	"context"

	"backend/api/v1/automation"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerAutomation) AmConfigHistoryList(ctx context.Context, req *automation.AmConfigHistoryListReq) (res *automation.AmConfigHistoryListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_View), enum.SysPermission_View.Error())
	return service.AmConfigHistory().GetAutoMationList(ctx, req)
}
func (c *ControllerAutomation) AmConfigHistoryDetail(ctx context.Context, req *automation.AmConfigHistoryDetailReq) (res *automation.AmConfigHistoryDetailRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_View), enum.SysPermission_View.Error())
	info, err := service.AmConfigHistory().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &automation.AmConfigHistoryDetailRes{
		AmConfigHistory: info,
	}
	return
}
