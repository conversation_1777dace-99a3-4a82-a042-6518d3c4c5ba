package v1

import (
	"context"

	"backend/api/v1/file"
	"backend/internal/service"
)

func (c *ControllerFile) ImageUpload(ctx context.Context, req *file.ImageUploadReq) (res *file.ImageUploadRes, err error) {

	urls, err := service.UploadFile().UploadImage(ctx, req.File)
	if err != nil {
		return
	}

	res = &file.ImageUploadRes{
		Urls: urls,
	}
	return
}
func (c *ControllerFile) FileUpload(ctx context.Context, req *file.FileUploadReq) (res *file.FileUploadRes, err error) {

	// g.Log().Info(ctx, fmt.Sprintf("FileUpload req: %d", len(req.File)))

	urls, err := service.UploadFile().UploadFile(ctx, req.File)
	if err != nil {
		return
	}

	res = &file.FileUploadRes{
		Urls: urls,
	}
	return
}
