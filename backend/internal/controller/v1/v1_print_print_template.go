package v1

import (
	"context"

	"backend/api/v1/print"
	"backend/internal/service"
)

func (c *ControllerPrint) List(ctx context.Context, req *print.ListReq) (res *print.ListRes, err error) {
	res, err = service.Print().GetPrintTemplateList(ctx, req)
	return
}
func (c *ControllerPrint) FormPrintTemplateDetail(ctx context.Context, req *print.FormPrintTemplateDetailReq) (res *print.FormPrintTemplateDetailRes, err error) {
	info, err := service.Print().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &print.FormPrintTemplateDetailRes{
		FormPrintTemplate: info,
	}
	return
}
func (c *ControllerPrint) FormPrintTemplateDelete(ctx context.Context, req *print.FormPrintTemplateDeleteReq) (res *print.FormPrintTemplateDeleteRes, err error) {
	err = service.Print().Delete(ctx, req)
	return
}
func (c *ControllerPrint) FormPrintTemplateSave(ctx context.Context, req *print.FormPrintTemplateSaveReq) (res *print.FormPrintTemplateSaveRes, err error) {
	res, err = service.Print().Save(ctx, req)
	return
}
func (c *ControllerPrint) FormPrintTemplateStatus(ctx context.Context, req *print.FormPrintTemplateStatusReq) (res *print.FormPrintTemplateStatusRes, err error) {
	err = service.Print().UpdateStatus(ctx, req.Id, req.Enabled)
	return
}

func (c *ControllerPrint) FormPrintTemplateAllowEditStatus(ctx context.Context, req *print.FormPrintTemplateAllowEditStatusReq) (res *print.FormPrintTemplateAllowEditStatusRes, err error) {
	err = service.Print().UpdateAllowEditStatus(ctx, req.Id, req.AllowEdit)
	res = &print.FormPrintTemplateAllowEditStatusRes{}
	return
}
