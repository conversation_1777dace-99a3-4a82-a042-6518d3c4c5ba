package v1

import (
	"context"

	"backend/api/v1/automation"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerAutomation) List(ctx context.Context, req *automation.ListReq) (res *automation.ListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_View), enum.SysPermission_View.Error())
	res, err = service.AmConfig().GetAutoMationList(ctx, req)
	return
}
func (c *ControllerAutomation) AmConfigDetail(ctx context.Context, req *automation.AmConfigDetailReq) (res *automation.AmConfigDetailRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_View), enum.SysPermission_View.Error())
	info, err := service.AmConfig().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &automation.AmConfigDetailRes{
		AmConfig: info,
	}
	return
}
func (c *ControllerAutomation) AmConfigDelete(ctx context.Context, req *automation.AmConfigDeleteReq) (res *automation.AmConfigDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.AmConfig().Delete(ctx, req)
	return
}
func (c *ControllerAutomation) AmConfigSave(ctx context.Context, req *automation.AmConfigSaveReq) (res *automation.AmConfigSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	res, err = service.AmConfig().Save(ctx, req)
	return
}
func (c *ControllerAutomation) AmConfigStatus(ctx context.Context, req *automation.AmConfigStatusReq) (res *automation.AmConfigStatusRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Automation, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.AmConfig().UpdateStatus(ctx, req.Id, req.Enabled)
	return
}
func (c *ControllerAutomation) Depend(ctx context.Context, req *automation.DependReq) (res *automation.DependRes, err error) {
	res, err = service.AmConfig().GetDepend(ctx, req)
	return
}

func (c *ControllerAutomation) AmConfigRetry(ctx context.Context, req *automation.AmConfigRetryReq) (res *automation.AmConfigRetryRes, err error) {
	err = service.AmConfig().Retry(ctx, req.HistoryId)
	return
}
