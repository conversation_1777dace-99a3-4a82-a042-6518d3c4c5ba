package v1

import (
	"context"

	"backend/api/v1/system"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"

	"github.com/gogf/gf/v2/util/gconv"
)

func (c *ControllerSystem) PostSearch(ctx context.Context, req *system.PostSearchReq) (res *system.PostSearchRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Post, enum.SysPermission_View), enum.SysPermission_View.Error())
	res = new(system.PostSearchRes)
	res.PostList, err = service.SysPost().GetList(ctx, req)
	return
}
func (c *ControllerSystem) PostSave(ctx context.Context, req *system.PostSaveReq) (res *system.PostSaveRes, err error) {
	if req.PostID > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Post, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Post, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	err = service.SysPost().Save(ctx, req)
	return
}
func (c *ControllerSystem) PostDelete(ctx context.Context, req *system.PostDeleteReq) (res *system.PostDeleteRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Post, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysPost().Delete(ctx, int64(req.Id))
	return
}
func (c *ControllerSystem) PostInfo(ctx context.Context, req *system.PostInfoReq) (res *system.PostInfoRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Post, enum.SysPermission_View), enum.SysPermission_View.Error())
	post, err := service.SysPost().GetInfoByID(ctx, int64(req.Id))
	if err != nil {
		return
	}

	if post == nil {
		return
	}
	err = gconv.Struct(post, &res)
	return
}
