package v1

import (
	"context"

	"github.com/gogf/gf/v2/util/gconv"

	"backend/api/v1/system"
	"backend/internal/model/enum"
	"backend/internal/service"
	"backend/library"
)

func (c *ControllerSystem) SysRoleList(ctx context.Context, req *system.SysRoleListReq) (res *system.SysRoleListRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Role, enum.SysPermission_View), enum.SysPermission_View.Error())
	res, err = service.SysRole().GetRoleList(ctx, req)
	if err != nil {
		return
	}

	if res.List == nil {
		for _, item := range res.List {
			var userLen int
			userLen, err = service.SysUserRole().GetRoleUserLen(ctx, int64(item.Id))
			if err != nil {
				break
			}
			item.Users = uint(userLen)
		}
	}
	return
}
func (c *ControllerSystem) SysRoleSave(ctx context.Context, req *system.SysRoleSaveReq) (res *system.SysRoleSaveRes, err error) {
	if req.Id > 0 {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Role, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	} else {
		library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Role, enum.SysPermission_New), enum.SysPermission_New.Error())
	}
	res, err = service.SysRole().Save(ctx, req)
	return
}
func (c *ControllerSystem) SysRoleDel(ctx context.Context, req *system.SysRoleDelReq) (res *system.SysRoleDelRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Role, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysRole().Delete(ctx, req)
	return
}
func (c *ControllerSystem) SysRoleDetail(ctx context.Context, req *system.SysRoleDetailReq) (res *system.SysRoleDetailRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Role, enum.SysPermission_View), enum.SysPermission_View.Error())
	role, err := service.SysRole().GetInfoByID(ctx, uint64(req.Id))
	if err != nil {
		return
	}
	gconv.Struct(role, &res)
	return
}
func (c *ControllerSystem) SysRoleSetPerms(ctx context.Context, req *system.SysRoleSetPermsReq) (res *system.SysRoleSetPermsRes, err error) {
	library.CheckFail(ctx, service.SysUser().CheckMenuPermission(ctx, enum.SysPermissionMark_SystemSettle_Role, enum.SysPermission_Change), enum.SysPermission_Change.Error())
	err = service.SysRole().SetRolePermissions(ctx, req)
	return
}
func (c *ControllerSystem) SysRoleListAll(ctx context.Context, req *system.SysRoleListAllReq) (res *system.SysRoleListAllRes, err error) {
	res = new(system.SysRoleListAllRes)
	list, err := service.SysRole().GetListCache(ctx)
	if err != nil {
		return
	}
	if list == nil {
		return
	}

	err = gconv.Struct(list, &res.List)

	return
}
