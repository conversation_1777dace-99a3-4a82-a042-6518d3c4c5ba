package v1

import (
	"backend/internal/consts"
	"backend/internal/service"
	"backend/library"
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerAutomationApiTrigger struct{}

func NewAutomationApiTrigger() *ControllerAutomationApiTrigger {
	return &ControllerAutomationApiTrigger{}
}

func (c *ControllerAutomationApiTrigger) Api(r *ghttp.Request) {
	ctx := r.GetCtx()
	var affectedRows int64
	err := g.Try(ctx, func(ctx context.Context) {
		// 获取请求头中的 token
		token := r.Header.Get("token")

		// 获取完整的请求路径
		fullPath := r.URL.Path
		// 移除基础路径，只保留自定义路由段
		customRoute := strings.TrimPrefix(fullPath, "/api/v1/automation/trigger/")

		// 获取 POST 请求中的 JSON 数据
		jsonData := make(g.Map)
		data, err := r.GetJson()
		if err != nil {
			library.ErrIsNil(ctx, fmt.Errorf("invalid JSON data: %s", err.Error()))
		}
		if err = gconv.Struct(data, &jsonData); err != nil {
			library.ErrIsNil(ctx, fmt.Errorf("failed to parse JSON data: %s", err.Error()))
		}
		// TODO: 触发对应的自动化流程
		smemberKey := fmt.Sprintf("%s_%s", consts.TriggerAPI, customRoute)
		affectedRows, err = service.AmConfig().TriggerBySmemberKey(ctx, smemberKey, map[string]interface{}{"token": token, "params": jsonData}, 0)
		library.ErrIsNil(ctx, err)
	})
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code":         500,
			"message":      err.Error(),
			"affectedRows": affectedRows,
		})
		return
	}
	r.Response.WriteJsonExit(g.Map{
		"code":         200,
		"message":      "Automation triggered successfully",
		"affectedRows": affectedRows,
	})
}
