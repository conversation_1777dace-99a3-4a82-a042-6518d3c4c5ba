package v1

import (
	"context"

	"backend/api/v1/code"
	"backend/internal/service"
)

func (c *ControllerCode) CodeHistoryList(ctx context.Context, req *code.CodeHistoryListReq) (res *code.CodeHistoryListRes, err error) {
	res, err = service.CodeHistory().GetCodeHistoryList(ctx, req)
	return
}
func (c *ControllerCode) CodeHistoryDetail(ctx context.Context, req *code.CodeHistoryDetailReq) (res *code.CodeHistoryDetailRes, err error) {
	info, err := service.CodeHistory().GetInfoByID(ctx, uint64(req.Id), false)
	if err != nil {
		return
	}

	res = &code.CodeHistoryDetailRes{
		CodeHistory: info,
	}
	return
}
func (c *ControllerCode) CodeHistoryDelete(ctx context.Context, req *code.CodeHistoryDeleteReq) (res *code.CodeHistoryDeleteRes, err error) {
	err = service.CodeHistory().Delete(ctx, req)
	return
}
func (c *ControllerCode) CodeHistorySave(ctx context.Context, req *code.CodeHistorySaveReq) (res *code.CodeHistorySaveRes, err error) {
	res, err = service.CodeHistory().Save(ctx, req)
	return
}
