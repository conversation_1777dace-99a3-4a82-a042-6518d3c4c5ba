package v1

import (
	"context"

	"backend/api/v1/form"
	"backend/internal/service"
)

func (c *ControllerForm) FormCountersignLogCreateByGlobalId(ctx context.Context, req *form.FormCountersignLogCreateByGlobalIdReq) (res *form.FormCountersignLogCreateByGlobalIdRes, err error) {
	// 通过全局ID创建回签记录
	insertId, err := service.FormCountersignLogs().CreateByGlobalId(ctx, req.GlobalId, "")
	if err != nil {
		return nil, err
	}

	res = &form.FormCountersignLogCreateByGlobalIdRes{
		Id: insertId,
	}
	return
}

func (c *ControllerForm) FormCountersignLogCreate(ctx context.Context, req *form.FormCountersignLogCreateReq) (res *form.FormCountersignLogCreateRes, err error) {
	// 通过表单模板ID和数据ID创建回签记录
	insertId, err := service.FormCountersignLogs().CreateByIds(ctx, req.FormId, req.DataId, "", "")
	if err != nil {
		return nil, err
	}

	res = &form.FormCountersignLogCreateRes{
		Id: insertId,
	}
	return
}

func (c *ControllerForm) FormCountersignLogDelete(ctx context.Context, req *form.FormCountersignLogDeleteReq) (res *form.FormCountersignLogDeleteRes, err error) {
	// 根据表单模板ID和数据ID获取回签记录
	countersignLog, err := service.FormCountersignLogs().GetByFormId(ctx, req.FormId, req.DataId)
	if err != nil {
		return nil, err
	}

	if countersignLog != nil {
		// 删除记录
		err = service.FormCountersignLogs().Delete(ctx, countersignLog.Id)
		if err != nil {
			return nil, err
		}

		res = &form.FormCountersignLogDeleteRes{}
	}

	return
}

func (c *ControllerForm) FormCountersignLogList(ctx context.Context, req *form.FormCountersignLogListReq) (res *form.FormCountersignLogListRes, err error) {
	return service.FormCountersignLogs().GetProjectList(ctx, req)
}
