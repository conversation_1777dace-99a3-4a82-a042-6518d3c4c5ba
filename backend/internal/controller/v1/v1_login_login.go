package v1

import (
	"context"
	"time"

	"backend/api/v1/login"
	"backend/internal/consts"
	"backend/internal/model/dto"
	"backend/internal/model/with"
	"backend/internal/service"
	"backend/library"
	"backend/queue"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

func (c *ControllerLogin) LoginAccount(ctx context.Context, req *login.LoginAccountReq) (res *login.LoginAccountRes, err error) {

	var user *with.SysUser
	err = g.Try(ctx, func(ctx context.Context) {
		// 验证码(暂时不做)

		// 验证账号密码是否正确
		user, err = service.SysUser().LoginValidate(ctx, req.Username, req.Password)
		library.ErrIsNil(ctx, err)

		// 生成jwttoken
		tokenString, err := service.SysUser().GetUserJwtToken(ctx, user)

		library.ErrIsNil(ctx, err)

		// 生成 refreshToken
		refreshTokenString, err := service.SysUser().GetJwtRefreshToken(ctx, user, req.AutoLogin)

		library.ErrIsNil(ctx, err)

		// 返回jwt
		res = &login.LoginAccountRes{
			Token:        tokenString,
			RefreshToken: refreshTokenString,
		}

	})

	// 记录登录日志
	loginLog := &dto.QueueUserLoginLog{}
	loginLog.Username = req.Username
	loginLog.IP = g.RequestFromCtx(ctx).GetClientIp()
	loginLog.UserAgent = ghttp.RequestFromCtx(ctx).Header.Get("User-Agent")
	loginLog.LoginTime = gtime.NewFromTime(time.Now())
	if err != nil {
		// 登录失败
		loginLog.Success = false
		loginLog.Message = err.Error()
		loginLog.Password = req.Password
	} else {
		// 登录成功
		loginLog.Success = true
		if user != nil {
			loginLog.UserId = user.Id
		}
	}

	queue.QueueManager().Publish(consts.StreamSubjectLoginLog, loginLog)

	return
}

func (c *ControllerLogin) RefreshToken(ctx context.Context, req *login.RefreshTokenReq) (res *login.RefreshTokenRes, err error) {
	newToken, err := service.SysUser().RefreshToken(ctx, req.RefreshToken)
	if err != nil {
		return
	}
	res = &login.RefreshTokenRes{
		Token: newToken,
	}
	return
}
