package cmd

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"

	v1 "backend/internal/controller/v1"
	"backend/internal/middleware"
	"backend/internal/service"
	"backend/library"
	"backend/queue"
	"backend/websocket"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {

			// 开启异步日志，提高性能
			g.Log().SetAsync(true)

			// 启动队列管理器
			go func() {
				_err := queue.QueueManager().Start()
				if _err != nil {
					g.Log().Error(ctx, "queue manager start error", _err)
				}
			}()

			g.<PERSON>g().Info(ctx, "正在同步数据库配置到缓存")
			// 同步数据库配置到缓存
			_ = service.Cache().InitConfig(ctx)
			g.Log().Info(ctx, "同步数据库配置到缓存完成")

			// 初始化自动化配置触发器
			_ = service.AmConfig().InitTrigger(ctx)
			g.Log().Info(ctx, "初始化自动化配置触发器完成")

			// 注册路由
			s := g.Server()

			// 设置最大请求体大小
			s.SetClientMaxBodySize(1024 * 1024 * 200)
			// 注册静态路由
			registerStaticPath := func(pathKey, mappathKey string) {
				path := g.Cfg().MustGet(ctx, pathKey).String()
				mappath := g.Cfg().MustGet(ctx, mappathKey).String()
				if !g.IsEmpty(path) && !g.IsEmpty(mappath) {
					library.IsNotExistMkdirPath(ctx, path)
					s.AddStaticPath(mappath, path)
				}
			}
			registerStaticPath("upload.path", "upload.mappath") // 注册上传文件的静态路由
			registerStaticPath("print.path", "print.mappath")   // 注册打印文件的静态路由
			registerStaticPath("export.path", "export.mappath") // 注册导出的文件的静态路由
			s.AddStaticPath("/lowcode", "resource/lowcode")     // 注册低代码的静态路由
			s.AddStaticPath("/foo", "resource/frontend")        // 注册低代码的静态路由

			// 注册websocket路由
			websocket.Start()
			s.BindHandler("/ws/connect", func(r *ghttp.Request) {
				websocket.GetServer().Connect(ctx, r)
			})

			groupHandle := func(group *ghttp.RouterGroup) {
				group.Middleware(ghttp.MiddlewareHandlerResponse)
				group.Middleware(middleware.Auth)
				group.Bind(
					v1.NewDemo(),
					v1.NewSaas(),
					v1.NewSystem(),
					v1.NewLogin(),
					v1.NewForm(),
					v1.NewFile(),
					v1.NewFlow(),
					v1.NewProject(),
					v1.NewAutomation(),
					v1.NewPrint(),
					v1.NewCode(),
					v1.NewSuperset(),
				)
			}
			// 注册api接口路由
			s.Group("/api", groupHandle)
			s.Group("/api/v1", groupHandle)
			// 注册自动化触发器路由
			s.BindHandler("POST:/api/automation/trigger/*", v1.NewAutomationApiTrigger().Api)
			s.BindHandler("POST:/api/v1/automation/trigger/*", v1.NewAutomationApiTrigger().Api)

			// 注册UI路由
			s.SetServerRoot("resource/frontend")
			s.SetIndexFiles([]string{"index.html"})
			s.BindStatusHandler(404, func(r *ghttp.Request) {
				path := r.URL.Path
				if !strings.HasPrefix(path, "/api/") {
					r.Response.ServeFile("resource/frontend/index.html")
				}
			})

			s.Run()
			// routes := s.GetRoutes()

			return nil
		},
	}
)
