package consts

type localCode struct {
	code    int
	message string
	err     error
}

func (c localCode) Code() int {
	return c.code
}

func (c localCode) Message() string {
	return c.message
}

func (c localCode) Detail() interface{} {
	return c.err
}

var (
	CodeInvalidToken      = localCode{401, "Invalid Token", nil}      // 无效Token
	CodeTokenParseError   = localCode{401, "Token Parse Error", nil}  // token解析失败
	CodeNoLoginUserInfo   = localCode{401, "No Login User Info", nil} // 无法获得用户信息
	CodeInvalidParams     = localCode{400, "Invalid Params", nil}     // 无效参数
	CodeNoRoutePermission = localCode{403, "权限不足", nil}               // 无路由权限
)
