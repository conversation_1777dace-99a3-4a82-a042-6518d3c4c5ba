package consts

import "time"

const (
	TENANT_ID           = "tenant_id"         // 记录在数据库用于区分租户的字段名
	CTX_TENANT_ID       = "tenant_id"         // 上下文中用于记录租户ID的键名
	CTX_USER_ID         = "current_user_id"   // 上下文中用于记录当前登录用户ID的键名
	CTX_USER            = "current_user"      // 上下文中用于记录当前登录用户的键名
	DEFAULT_PASSWORD    = "andiguang888"      // 默认密码
	ACCOUNT_CODE_START  = 10000               // 账号默认编码开始数
	SYSTEM_SALT         = "andkj"             // 系统盐
	JWTKEY              = "andiguang20240606" // JWT密钥
	JWT_EXPIRATION_TIME = 5 * time.Minute     // JWT过期时间
)
