package consts

const (
	StreamName                       = "ADG_CRM"                               // 队列名称
	StreamSubjects                   = StreamName + ".*"                       // 主题名称分组
	StreamSubjectFlowCreated         = StreamName + ".FLOW_Created"            // 流程创建
	StreamSubjectFlowFinish          = StreamName + ".FLOW_FINISH"             // 流程结束
	StreamSubjectFlowUpdateStatus    = StreamName + ".FLOW_UPDATE_STATUS"      // 流程状态更新
	StreamSubjectFlowAudit           = StreamName + ".FLOW_AUDIT"              // 流程审核
	StreamFormDataCreated            = StreamName + ".FORM_DATA_CREATED"       // 新建表单数据
	StreamFormDataChanged            = StreamName + ".FORM_DATA_CHANGED"       // 表单数据变化
	StreamFormDataApplyInvalid       = StreamName + ".FORM_DATA_APPLY_INVALID" // 表单申请作废
	StreamSubjectNoticeSend          = StreamName + ".NOTICE_SEND"             // 通知发送
	StreamSubjectOrderPayed          = StreamName + ".ORDER_PAYED"             // 订单
	StreamSubjectConfigUpdate        = StreamName + ".CONFIG_UPDATE"           // 配置更新
	StreamSubjectLoginLog            = StreamName + ".LOGIN_LOG"               // 登录记录
	StreamSubjectExcelImportCreated  = StreamName + ".EXCEL_IMPORT_Created"    // Excel导入文件通知
	StreamSubjectExcelImportProgress = StreamName + ".EXCEL_IMPORT_Progress"   // Excel导入进度
	StreamSubjectExcelExportCreated  = StreamName + ".EXCEL_EXPORT_Created"    // Excel导出文件通知
	StreamSubjectExcelExportProgress = StreamName + ".EXCEL_EXPORT_Progress"   // Excel导出进度
	// 客户主题名

	StreamFormFlowFinish         = StreamName + ".FORM_FLOW_FINISH"         // 表单审批成功
	StreamFormInvalid            = StreamName + ".FORM_INVALID"             // 表单作废成功
	StreamFormFlowStarted        = StreamName + ".FORM_FLOW_STARTED"        // 表单审批流经过了开始节点
	StreamFormFlowEndOrReturn    = StreamName + ".FORM_FLOW_END_OR_RETURN"  // 表单审批流结束 或者 退回了开始节点
	StreamFormFlowRejectOrCancel = StreamName + ".FORM_FLOW_REJECTORCANCEL" // 表单审批拒绝或者撤销

)
