// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"context"
)

type (
	IFormPrintLogs interface {
		// Create 创建打印记录
		Create(ctx context.Context, in *do.FormPrintLogs) (insertid int64, err error)
		// CreateByIds 通过表单模板ID和数据ID创建打印记录
		CreateByIds(ctx context.Context, formTemplateId, formDataId int64, formPrintTemplateId int64) (insertid int64, err error)
		// GetById 根据ID获取打印记录
		GetById(ctx context.Context, id int64) (out *entity.FormPrintLogs, err error)
		// GetByFormDataId 根据表单数据ID获取打印记录
		GetByFormId(ctx context.Context, formTemplateId, formDataId int64) (out *entity.FormPrintLogs, err error)
		// GetByFormTemplateId 根据表单模板ID获取打印记录
		GetByFormTemplateId(ctx context.Context, formTemplateId int64) (out []*entity.FormPrintLogs, err error)
		// Update 更新打印记录
		Update(ctx context.Context, in *do.FormPrintLogs) (err error)
		// Delete 删除打印记录
		Delete(ctx context.Context, id int64) (err error)
	}
)

var (
	localFormPrintLogs IFormPrintLogs
)

func FormPrintLogs() IFormPrintLogs {
	if localFormPrintLogs == nil {
		panic("implement not found for interface IFormPrintLogs, forgot register?")
	}
	return localFormPrintLogs
}

func RegisterFormPrintLogs(i IFormPrintLogs) {
	localFormPrintLogs = i
}
