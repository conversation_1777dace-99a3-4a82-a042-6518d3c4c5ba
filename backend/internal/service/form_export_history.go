// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/library"
	"context"

	"github.com/dop251/goja"
)

type (
	IFormExportHistory interface {
		// 获取导出数据文件列表
		GetFormExportHistoryList(ctx context.Context, req *form.FormExportHistoryListReq) (res *form.FormExportHistoryListRes, err error)
		// 新的数据导出
		New(ctx context.Context, req *form.FormExportHistoryNewReq) (res *form.FormExportHistoryNewRes, err error)
		// 触发导出数据文件
		TriggerExportFile(ctx context.Context, id int64) (err error)
		// 处理导出数据任务
		ProcessExportFile(ctx context.Context, historyInfo *entity.FormExportHistory) (err error)
		BuildMapColumns(ctx context.Context, fieldMapping []*dto.ColumnMapRule) (exportColumns []library.ColumnConfig, err error)
		FormatDataListByFieldMapping(ctx context.Context, vm *goja.Runtime, formTableName string, formTableType int, formColumns []*dto.FormColumn, dataList []map[string]interface{}, fieldMapping []*dto.ColumnMapRule) (formatDataList []map[string]interface{}, err error)
		// 更新导出数据文件的方法
		Update(ctx context.Context, historyInfo *entity.FormExportHistory) (err error)
		// 修改导出数据文件状态
		UpdateStatus(ctx context.Context, id int64, status enum.ExportStatus) (err error)
		// 删除导出数据文件
		Delete(ctx context.Context, id uint64) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (FormExportHistory *entity.FormExportHistory, err error)
	}
)

var (
	localFormExportHistory IFormExportHistory
)

func FormExportHistory() IFormExportHistory {
	if localFormExportHistory == nil {
		panic("implement not found for interface IFormExportHistory, forgot register?")
	}
	return localFormExportHistory
}

func RegisterFormExportHistory(i IFormExportHistory) {
	localFormExportHistory = i
}
