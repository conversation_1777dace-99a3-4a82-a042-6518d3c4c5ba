// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/system"
	"backend/internal/model/entity"
	"context"
)

type (
	ISysRole interface {
		// 获取角色列表
		GetRoleList(ctx context.Context, req *system.SysRoleListReq) (res *system.SysRoleListRes, err error)
		// 保存角色数据
		Save(ctx context.Context, req *system.SysRoleSaveReq) (res *system.SysRoleSaveRes, err error)
		// 删除角色
		Delete(ctx context.Context, req *system.SysRoleDelReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, roleId uint64) (role *entity.SysRole, err error)
		// 获得最大ID
		GetMaxID(ctx context.Context) (maxId uint64, err error)
		// 获取角色列表缓存
		GetListCache(ctx context.Context) (list []*entity.SysRole, err error)
		// 设置角色权限
		SetRolePermissions(ctx context.Context, req *system.SysRoleSetPermsReq) (err error)
	}
)

var (
	localSysRole ISysRole
)

func SysRole() ISysRole {
	if localSysRole == nil {
		panic("implement not found for interface ISysRole, forgot register?")
	}
	return localSysRole
}

func RegisterSysRole(i ISysRole) {
	localSysRole = i
}
