package service

import (
	"context"
)

type (
	ISuperset interface {
		// 获取Superset的guest token
		GetGuestToken(ctx context.Context, resourceID string, dashboard string) (token string, url string, expiresAt int64, err error)
		// 获取Superset的访问令牌
		GetAccessToken(ctx context.Context) (accessToken string, err error)
		// 获取仪表盘列表
		GetDashboardList(ctx context.Context, req DashboardListRequest) (*DashboardListResponse, error)
		// 获取仪表盘的嵌入式配置
		GetEmbeddedConfig(ctx context.Context, dashboardID string) (*EmbeddedConfig, error)
		// 设置仪表盘的嵌入式配置
		SetEmbeddedConfig(ctx context.Context, dashboardID string) (*EmbeddedConfig, error)
		// 通过仪表盘ID获取嵌入的guest token
		GetEmbeddedGuestToken(ctx context.Context, dashboardID string) (token string, uuid string, url string, expiresAt int64, err error)
	}

	// DashboardListRequest 获取仪表盘列表的请求参数
	DashboardListRequest struct {
		Page           int      `json:"page"`                      // 页码，从0开始
		PageSize       int      `json:"page_size"`                 // 每页数量
		OrderColumn    string   `json:"order_column,omitempty"`    // 排序列
		OrderDirection string   `json:"order_direction,omitempty"` // 排序方向
		SelectColumns  []string `json:"select_columns,omitempty"`  // 选择的列
	}

	// DashboardInfo 仪表盘基本信息
	DashboardInfo struct {
		ID    int    `json:"id"`              // 仪表盘ID
		Title string `json:"dashboard_title"` // 仪表盘标题
		Slug  string `json:"slug"`            // 仪表盘别名
		UUID  string `json:"uuid"`            // 仪表盘UUID
	}

	// DashboardListResponse 仪表盘列表响应
	DashboardListResponse struct {
		Count  int             `json:"count"`  // 总数量
		Result []DashboardInfo `json:"result"` // 结果列表
	}

	// EmbeddedConfig 嵌入式配置信息
	EmbeddedConfig struct {
		AllowedDomains []string `json:"allowed_domains"` // 允许的域名
		DashboardID    string   `json:"dashboard_id"`    // 仪表盘ID
		UUID           string   `json:"uuid"`            // UUID
	}
)

var (
	localSuperset ISuperset
)

func Superset() ISuperset {
	if localSuperset == nil {
		panic("implement not found for interface ISuperset, forgot register?")
	}
	return localSuperset
}

func RegisterSuperset(i ISuperset) {
	localSuperset = i
}
