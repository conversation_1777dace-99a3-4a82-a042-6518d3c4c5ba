// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/library"
	"context"

	"github.com/dop251/goja"
	"github.com/gogf/gf/v2/os/gtime"
)

type (
	IFormData interface {
		// 保存表单数据
		Save(ctx context.Context, req *form.FormDataSaveReq, vm *goja.Runtime, createUser ...int64) (res *form.FormDataSaveRes, err error)
		// 同步流程的审核状态到表单
		SyncFlowStatus(ctx context.Context, table_name string, form_data_id int64, flow_status enum.FlowStatus, flow_status_version *gtime.Time, flow_instance_type enum.FlowInstanceType) (affected int64, err error)
		// 更新表单的流程状态
		UpdateFlowStatus(ctx context.Context, table_name string, form_data_id int64, flow_status enum.FlowStatus, flow_status_version *gtime.Time, flow_instance_type enum.FlowInstanceType) (affected int64, err error)
		// 更新表单数据
		UpdateFormData(ctx context.Context, table_name string, form_data_id int64, form_data map[string]interface{}) (err error)
		// 获取表单数据详情
		GetDetail(ctx context.Context, FormTemId int64, id int64) (formData map[string]interface{}, template *with.FormTemplate, err error)
		GetDetailByFilter(ctx context.Context, FormTemId int64, filter map[string]interface{}) (formData map[string]interface{}, err error)
		GetDetailByRuleFilter(ctx context.Context, FormTemId int64, ruleFilter map[string]interface{}) (formData map[string]interface{}, err error)
		GetFormDataByFilter(ctx context.Context, formTableName string, filter map[string]interface{}, formColumns library.MySlice[*dto.FormColumn]) (formData map[string]interface{}, err error)
		GetFormData(ctx context.Context, formTableName string, id int64) (formData map[string]interface{}, err error)
		PopulateCreatedUser(ctx context.Context, formData *map[string]interface{})
		// 删除
		Delete(ctx context.Context, FormTemId int64, id int64) (err error)
		// 申请作废表单数据
		Invalidate(ctx context.Context, extraData *dto.InstanceExtraData, FormTemId int64, id int64) (err error)
		// 更新作废状态和作废理由信息
		UpdateInvalidStatus(ctx context.Context, tableName string, id int64, extraData *dto.InstanceExtraDataAttachment) (err error)
		// 查询是否存在关联数据
		CheckRelatedData(ctx context.Context, templateInfo *with.FormTemplate, formData map[string]interface{}, includeMainTableIgnore bool) (relatedData []*dto.FormRelatedData, err error)
		GetListByRules(ctx context.Context, formType int, formTableName string, FormTemplateId int64, formColumns []*dto.FormColumn, ruleFilter map[string]interface{}) (formDatas []map[string]interface{}, err error)
		// 获得某个表单的某个字段关联数据列表
		GetColumnWithList(ctx context.Context, req *form.ColumnWithListReq) (res *form.ColumnWithListRes, err error)
		GetCustomTableDatas(ctx context.Context, tableName string, filter map[string]interface{}, ruleFilter map[string]interface{}, pageNum int, pageSize int) (result []map[string]interface{}, total int, err error)
		// 获得某个表单的某个字段关联的表单信息
		GetColumnWithInfo(ctx context.Context, req *form.ColumnWithInfoReq) (res *form.ColumnWithInfoRes, err error)
		// 新增方法：获取某个字段的总和
		GetFieldSum(ctx context.Context, req *form.FormDataListSumReq, unlimitedSearch bool, loginUser *dto.UserClaims, noCheckPermission ...bool) (res *form.FormDataListSumRes, err error)
		// 获取表单数据列表
		GetList(ctx context.Context, req *form.FormDataListReq, unlimitedSearch bool, loginUser *dto.UserClaims, noCheckPermission ...bool) (res *form.FormDataListRes, err error)
		// 获得自定义表单数据列表
		GetListCustom(ctx context.Context, req *form.FormDataListCustomReq) (res *form.FormDataListCustomRes, err error)
		// 获得自定义表单数据列表
		GetListCustomEx(ctx context.Context, filter map[string]interface{}, tableName string) (res []map[string]interface{}, err error)
		// 获得拓展key对应的模板id和数据id等信息
		GetExpandInfo(ctx context.Context, expand_key string, expand_data_id int64) (expandInfo *dto.ExpandInfo, err error)
		TableDataList(ctx context.Context, req *form.TableDataListReq, noCheckPermission ...bool) (res *form.TableDataListRes, err error)
		// 生成一个全局唯一的id
		GenerateGlobalId(ctx context.Context, formType int32, formTemId int64, formDataId int64) (id string, err error)
		// 根据全局唯一Id拆解出模板id和数据id
		ParseGlobalId(ctx context.Context, globalId string) (formType int32, formTemId int32, formDataId int64, err error)
		// 格式化列的label
		FormatColumnLabel(ctx context.Context, columnInfo *dto.FormColumn, value interface{}) (result interface{}, err error)
		// 更新表单单个字段
		UpdateSingleField(ctx context.Context, req *form.FormDataUpdateFieldReq) (err error)
	}
)

var (
	localFormData IFormData
)

func FormData() IFormData {
	if localFormData == nil {
		panic("implement not found for interface IFormData, forgot register?")
	}
	return localFormData
}

func RegisterFormData(i IFormData) {
	localFormData = i
}
