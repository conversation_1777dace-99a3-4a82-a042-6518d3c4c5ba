// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	IExcelService interface {
		// 解析Excel内容
		ParseExcelContent(ctx context.Context, filePath string) (err error)
		// 将数据转成Excel
		ConvertDataToExcel(ctx context.Context, data interface{}) (filePath string, err error)
		// 将Excel内容转成map切片
		ExcelToMapSlice(ctx context.Context, filePath string, startRow int) (result []map[string]string, err error)
	}
)

var (
	localExcelService IExcelService
)

func ExcelService() IExcelService {
	if localExcelService == nil {
		panic("implement not found for interface IExcelService, forgot register?")
	}
	return localExcelService
}

func RegisterExcelService(i IExcelService) {
	localExcelService = i
}
