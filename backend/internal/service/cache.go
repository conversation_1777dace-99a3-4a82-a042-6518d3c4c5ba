package service

import (
	"context"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/os/gcache"
)

type (
	ICache interface {
		gcache.Adapter
		InitConfig(ctx context.Context) (err error)
		GetNextId(ctx context.Context, key string) (id int64, err error)
		GetNextIdString(ctx context.Context, key string, n int) (id string, err error)

		SAdd(ctx context.Context, key string, values ...interface{}) error
		SIsMember(ctx context.Context, key string, value interface{}) (int64, error)
		SMembers(ctx context.Context, key string) (gvar.Vars, error)
		SRem(ctx context.Context, key string, value interface{}) error
		IncrBy(ctx context.Context, key string, value interface{}) (result interface{}, err error)
		IncrByFloat(ctx context.Context, key string, value interface{}) (result interface{}, err error)
		TryLock(ctx context.Context, key string, ttl int) (string, error)
		UnLock(ctx context.Context, key string, lockId string) error
	}
)

var (
	localCache ICache
)

func Cache() ICache {
	if localCache == nil {
		panic("implement not found for interface ICache, forgot register?")
	}
	return localCache
}

func RegisterCache(i ICache) {
	localCache = i
}
