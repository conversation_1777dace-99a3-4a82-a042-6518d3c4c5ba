// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/system"
	"backend/internal/model/do"
	"backend/internal/model/with"
	"context"
)

type (
	ISysDept interface {
		// 根据条件获取部门列表
		GetList(ctx context.Context, req *system.DeptSearchReq) (list []*with.SysDept, err error)
		// 获取所有部门列表，部门数据一般不多，可以从缓存中获取
		GetListCache(ctx context.Context) (list []*with.SysDept, err error)
		// Save 部门保存
		Save(ctx context.Context, req *system.DeptSaveReq) (err error)
		// Add 添加部门
		Add(ctx context.Context, input *do.SysDept) (err error)
		// Edit 部门修改
		Edit(ctx context.Context, input *do.SysDept) (err error)
		// Delete 删除部门
		Delete(ctx context.Context, deptId int64) (err error)
		// 递归获取所有部门和子部门的id
		GetDeptIds(ctx context.Context, deptId int64) (ids []int64, err error)
		// 递归获得所有子部门
		GetSubDept(list []*with.SysDept, deptId int64) (subList []*with.SysDept)
		// 根据ID获取部门信息
		GetInfoByID(ctx context.Context, deptId int64) (dept *with.SysDept, err error)
		// 根据ids获取部门列表
		GetDeptListByIDs(ctx context.Context, ids []int64) (list []*with.SysDept, err error)
	}
)

var (
	localSysDept ISysDept
)

func SysDept() ISysDept {
	if localSysDept == nil {
		panic("implement not found for interface ISysDept, forgot register?")
	}
	return localSysDept
}

func RegisterSysDept(i ISysDept) {
	localSysDept = i
}
