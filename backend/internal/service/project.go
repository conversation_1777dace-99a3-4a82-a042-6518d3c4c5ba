// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/project"
	"backend/internal/model/with"
	"context"
)

type (
	IProject interface {
		// 获取项目列表
		GetProjectList(ctx context.Context, req *project.AdProjectListReq) (res *project.AdProjectListRes, err error)
		// 保存项目数据
		Save(ctx context.Context, req *project.AdProjectSaveReq) (res *project.AdProjectSaveRes, err error)
		// 删除项目
		Delete(ctx context.Context, req *project.AdProjectDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, projectId uint64, with bool) (Project *with.AdProject, err error)
		// 根据项目名获取项目信息
		GetProjectByName(ctx context.Context, name string) (Project *with.AdProject, err error)
		// 获得最大ID
		GetMaxID(ctx context.Context) (maxId uint64, err error)
	}
)

var (
	localProject IProject
)

func Project() IProject {
	if localProject == nil {
		panic("implement not found for interface IProject, forgot register?")
	}
	return localProject
}

func RegisterProject(i IProject) {
	localProject = i
}
