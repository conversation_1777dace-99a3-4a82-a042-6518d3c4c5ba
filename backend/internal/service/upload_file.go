// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IUploadFile interface {
		// 上传图片
		UploadImage(ctx context.Context, file []*ghttp.UploadFile) (res []string, err error)
		// 上传文件
		UploadFile(ctx context.Context, file []*ghttp.UploadFile) (res []string, err error)
	}
)

var (
	localUploadFile IUploadFile
)

func UploadFile() IUploadFile {
	if localUploadFile == nil {
		panic("implement not found for interface IUploadFile, forgot register?")
	}
	return localUploadFile
}

func RegisterUploadFile(i IUploadFile) {
	localUploadFile = i
}
