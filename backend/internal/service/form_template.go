// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/with"
	"backend/library"
	"context"
)

type (
	IFormTemplate interface {
		// 保存表单的设计结构
		SaveFormDesign(ctx context.Context, input *form.FormTemlateSaveFormDesignReq) (res *form.FormTemlateSaveFormDesignRes, err error)
		// 保存表单的基础设置字段数据（包含列表设计、表单设置）
		SaveFormDefault(ctx context.Context, input *form.FormTemlateSaveFormDefaultReq) (res *form.FormTemlateSaveFormDefaultRes, err error)
		// Add
		Add(ctx context.Context, input *do.FormTemplate) (id int64, err error)
		// Edit 修改
		Edit(ctx context.Context, input *do.FormTemplate) (id int64, err error)
		// 获得与xx表有关联的模板列表
		GetRelatedFormTemplateList(ctx context.Context, tableName string) (res []*entity.FormTemplate, err error)
		// 获得表单模板信息
		GetFormTemplateInfo(ctx context.Context, id int64) (res *with.FormTemplate, err error)
		// 获得表单模板信息
		GetFormTemplateInfoCache(ctx context.Context, id int64) (res *with.FormTemplate, err error)
		// 获得一个有序且唯一的表单名称
		GetUniqueFormName(ctx context.Context) (name string, err error)
		// 验证表名是否已经存在
		TableNameExists(ctx context.Context, formTableName string) (isExists bool, err error)
		GetTablesCache(ctx context.Context) (tables []string, err error)
		// 创建一个新表
		CreateTable(ctx context.Context, tableName string) (err error)
		// 更新的时候，需要添加表字段
		AddFields(ctx context.Context, formTableName string, fields []*dto.FormColumn) (err error)
		// 添加耽搁字段
		AddField(ctx context.Context, formTableName string, field *dto.FormColumn) (err error)
		// 检查字段是否存在
		FieldExists(ctx context.Context, formTableName string, fieldName string) (bool, error)
		// 获取表单模板列表
		GetList(ctx context.Context, req *form.FormTemlateListReq) (res *form.FormTemlateListRes, err error)
		// 获得表单用于数据库相关的设计结构（包括列信息、表格信息）
		GetDatabaseDesign(ctx context.Context, id int64) (templateInfo *with.FormTemplate, formColumns library.MySlice[*dto.FormColumn], tableDesign *dto.FormTableDesign, permissions *dto.FormPermissions, err error)
		// 删除
		Delete(ctx context.Context, id int64) (err error)
		// 获得所有支持关联的表
		GetSupportTablesCache(ctx context.Context) (res []*dto.SupportTable, err error)
		// 支持的关联表
		GetRelatedTables(ctx context.Context, req *form.SupportTableListReq) (res *form.SupportTableListRes, err error)
		// 获得表单模板信息
		GetFormTemplateByTableName(ctx context.Context, form_table_name string) (res *entity.FormTemplate, err error)
		GetFormTemplateRelatedColumns(ctx context.Context, form_table_name string, show_fixed_fields ...bool) (res []*dto.FormColumn, err error)
		GetCustomTableRelatedColumns(ctx context.Context, form_table_name string) (res []*dto.FormColumn, err error)
		// 表支持的关联列
		GetRelatedColumns(ctx context.Context, req *form.SupportColumnListReq) (res *form.SupportColumnListRes, err error)
		GetRelatedColumnsByTableName(ctx context.Context, table_name string, table_type int, show_fixed_fields ...bool) (res []*dto.FormColumn, err error)
		// 表支持的关联字段(模板表)
		GetFormTemplateRelatedFields(ctx context.Context, form_table_name string) (res []*dto.SupportColumn, err error)
		// 表支持的关联字段（自定义表）
		GetCustomTableRelatedFields(ctx context.Context, form_table_name string) (res []*dto.SupportColumn, err error)
		// 表支持的关联字段
		GetRelatedFields(ctx context.Context, req *form.SupportFieldsListReq) (res *form.SupportFieldsListRes, err error)
		// 获得拓展key对应的模板id
		GetTemplateIdByExpandKey(ctx context.Context, expand_key string) (template_id int64, err error)
		// 设置拓展key对应的模板id
		SetTemplateIdByExpandKey(ctx context.Context, expand_key string, template_id int64) (err error)
		// 更新表单模版的权限设置
		PermissionSettle(ctx context.Context, req *form.FormTemlatePermissionSettingReq) (err error)
		// 构建脚本引用
		BuildScriptRef(ctx context.Context, formTableName string, formTableType int, formColumns []*dto.FormColumn, formData map[string]interface{}, columnMapRules []dto.ColumnMapRule) (scriptRef map[string]interface{}, err error)
	}
)

var (
	localFormTemplate IFormTemplate
)

func FormTemplate() IFormTemplate {
	if localFormTemplate == nil {
		panic("implement not found for interface IFormTemplate, forgot register?")
	}
	return localFormTemplate
}

func RegisterFormTemplate(i IFormTemplate) {
	localFormTemplate = i
}
