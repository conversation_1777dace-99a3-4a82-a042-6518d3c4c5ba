// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/with"
	"context"
)

type (
	IFlowTemplate interface {
		// 保存流程模板
		Save(ctx context.Context, input_id int64, input *do.FlowTemplate) (flow_template_id int64, err error)
		// 验证流程设置数据格式
		CheckFlowSchema(ctx context.Context, input string, createUser *with.SysUser) (err error)
		// 验证附加数据是否符合格式
		ValidateExtraData(ctx context.Context, flowShcema string, extraData *dto.InstanceExtraData) (err error)
		// 获得流程模板
		GetDetail(ctx context.Context, id int64) (template *entity.FlowTemplate, err error)
		// 获得流转条件中，受支持的参数列表
		GetSupportedParams(ctx context.Context, form_template_id int64) (result []*dto.FLowEdgeParams, err error)
		// 获得表单模板中，受支持的参数列表
		GetSupportedFormParams(ctx context.Context, form_template_id int64) (result []*dto.FLowEdgeParamColumns, err error)
		// 获得当前用户相关参数列表
		GetSupportedUserParams(ctx context.Context) (result []*dto.FLowEdgeParamColumns, err error)
	}
)

var (
	localFlowTemplate IFlowTemplate
)

func FlowTemplate() IFlowTemplate {
	if localFlowTemplate == nil {
		panic("implement not found for interface IFlowTemplate, forgot register?")
	}
	return localFlowTemplate
}

func RegisterFlowTemplate(i IFlowTemplate) {
	localFlowTemplate = i
}
