// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/flow"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"context"
)

type (
	IFlowInstanceHistoryAuditor interface {
		// 获得流程实例审核人历史详细信息
		GetInfoByID(ctx context.Context, flow_instance_history_auditor_id int64) (historyAuditorEntity *entity.FlowInstanceHistoryAuditor, err error)
		// 创建一个流程实例审核人历史
		Create(ctx context.Context, history *entity.FlowInstanceHistory, approver *dto.InstanceFlowAuditor) (historyAuditorEntity *entity.FlowInstanceHistoryAuditor, err error)
		// 验证审核人是否已经存在审核通过的节点记录
		IsExistAgreeLog(ctx context.Context, flow_instance_id int64, user_id int64) (isExist bool, err error)
		GetLastLogByUser(ctx context.Context, flow_instance_id int64, user_id int64) (lastLog *entity.FlowInstanceHistoryAuditor, err error)
		// 获得某个节点实例的审核人历史列表（根据状态）
		GetAuditorsByStatus(ctx context.Context, flow_instance_history_id int64, approval_status enum.NodeActionType) (historyAuditors []*entity.FlowInstanceHistoryAuditor, err error)
		// 更新当前节点未审核的审批人状态为已跳过
		SkipPendingAuditors(ctx context.Context, flow_instance_history_id int64) error
		// 撤销某节点实例的所有未审核纪录
		CancelPendingAuditors(ctx context.Context, flow_instance_history_id int64) (err error)
		// 撤销某流程实例的所有未审核纪录
		CancelAllPendingAuditors(ctx context.Context, instance_id int64) (err error)
		// 撤销某流程实例的中StepId在cancelStepIds的未审核纪录
		CancelAllPendingAuditorsByStepIds(ctx context.Context, instance_id int64, cancelStepIds []string) (err error)
		// 撤销审核
		UserCancel(ctx context.Context, req *flow.ApproveReq) (err error)
		// 步骤处理
		UserApprove(ctx context.Context, req *flow.ApproveReq) (err error)
		// 查询历史
		GetList(ctx context.Context, req *dto.HistoryAuditorListReq) (res *dto.HistoryAuditorListRes, err error)
	}
)

var (
	localFlowInstanceHistoryAuditor IFlowInstanceHistoryAuditor
)

func FlowInstanceHistoryAuditor() IFlowInstanceHistoryAuditor {
	if localFlowInstanceHistoryAuditor == nil {
		panic("implement not found for interface IFlowInstanceHistoryAuditor, forgot register?")
	}
	return localFlowInstanceHistoryAuditor
}

func RegisterFlowInstanceHistoryAuditor(i IFlowInstanceHistoryAuditor) {
	localFlowInstanceHistoryAuditor = i
}
