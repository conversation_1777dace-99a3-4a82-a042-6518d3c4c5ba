// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"context"
)

type (
	IFlowInstanceHistory interface {
		// 获得流程实例审核历史详细信息
		GetInfoByID(ctx context.Context, flow_instance_history_id int64) (instance *entity.FlowInstanceHistory, err error)
		GetInfoByInstanceAndStepId(ctx context.Context, instance_id int64, step_id string, pre_history_id int64) (instance *entity.FlowInstanceHistory, err error)
		// 创建流程审核历史
		Create(ctx context.Context, instance_id int64, flow_node *dto.InstanceFlowNode) (history *entity.FlowInstanceHistory, err error)
		UpdateStatus(ctx context.Context, flow_instance_history_id int64, status int) (err error)
		// 撤销某流程实例的所有未审核纪录
		CancelAllPending(ctx context.Context, instance_id int64) (err error)
	}
)

var (
	localFlowInstanceHistory IFlowInstanceHistory
)

func FlowInstanceHistory() IFlowInstanceHistory {
	if localFlowInstanceHistory == nil {
		panic("implement not found for interface IFlowInstanceHistory, forgot register?")
	}
	return localFlowInstanceHistory
}

func RegisterFlowInstanceHistory(i IFlowInstanceHistory) {
	localFlowInstanceHistory = i
}
