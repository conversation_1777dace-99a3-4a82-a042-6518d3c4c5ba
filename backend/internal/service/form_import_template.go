// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/entity"
	"context"
)

type (
	IFormImportTemplate interface {
		// 获取导入模板列表
		GetFormImportTemplateList(ctx context.Context, req *form.FormImportTemplateListReq) (res *form.FormImportTemplateListRes, err error)
		// 获取启用导入模板列表
		GetEnabledRules(ctx context.Context, formTemplateId int64) (res []*entity.FormImportTemplate, err error)
		// 保存导入模板数据
		Save(ctx context.Context, req *form.FormImportTemplateSaveReq) (res *form.FormImportTemplateSaveRes, err error)
		// 删除导入模板
		Delete(ctx context.Context, req *form.FormImportTemplateDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (formImportTemplate *entity.FormImportTemplate, err error)
		// 修改状态
		UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error)
	}
)

var (
	localFormImportTemplate IFormImportTemplate
)

func FormImportTemplate() IFormImportTemplate {
	if localFormImportTemplate == nil {
		panic("implement not found for interface IFormImportTemplate, forgot register?")
	}
	return localFormImportTemplate
}

func RegisterFormImportTemplate(i IFormImportTemplate) {
	localFormImportTemplate = i
}
