// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/internal/model/dto"
	"context"
	"time"

	"github.com/dop251/goja"
)

type (
	IJavascript interface {
		// 获得一个注入了基础库的 goja.Runtime 实例
		GetVM(props map[string]interface{}) (vm *goja.Runtime, err error)
		SetProps(ctx context.Context, vm *goja.Runtime, props map[string]interface{}) (err error)
		// 运行一段js代码，并返回执行结果
		Run(ctx context.Context, funcCode dto.ScriptCode, timeout time.Duration, vm *goja.Runtime) (result goja.Value, err error)
		// 运行一段js代码，返回验证结果 bool 值
		RunBool(ctx context.Context, formTableName string, formTableType int, formColumns []*dto.FormColumn, funcCode dto.ScriptCode, timeout time.Duration, vm *goja.Runtime) (result bool, err error)
		// 格式化映射值的函数
		FormatMappedValue(ctx context.Context, vm *goja.Runtime, formTableName string, formTableType int, formColumns []*dto.FormColumn, value interface{}, funcCode dto.ScriptCode) (result interface{}, err error)
		BuildOriginColumnMap(ctx context.Context, formTableName string, formTableType int, columnMapRules []dto.ColumnMapRule, formColumns []*dto.FormColumn) (columnMap map[string]interface{}, err error)
	}
)

var (
	localJavascript IJavascript
)

func Javascript() IJavascript {
	if localJavascript == nil {
		panic("implement not found for interface IJavascript, forgot register?")
	}
	return localJavascript
}

func RegisterJavascript(i IJavascript) {
	localJavascript = i
}
