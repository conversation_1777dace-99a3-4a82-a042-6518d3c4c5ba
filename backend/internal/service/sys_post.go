// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/system"
	"backend/internal/model/do"
	"backend/internal/model/with"
	"context"
)

type (
	ISysPost interface {
		// 根据条件获取岗位列表
		GetList(ctx context.Context, req *system.PostSearchReq) (list []*with.SysPost, err error)
		// 获取所有岗位列表，岗位数据一般不多，可以从缓存中获取
		GetListCache(ctx context.Context) (list []*with.SysPost, err error)
		// Save 岗位保存
		Save(ctx context.Context, req *system.PostSaveReq) (err error)
		// GetNodePath 获得岗位树节点路径
		GetNodePath(ctx context.Context, parentId uint64) (nodePath string)
		// Add 添加岗位
		Add(ctx context.Context, req *do.SysPost) (lastid int64, err error)
		// Edit 岗位修改
		Edit(ctx context.Context, req *do.SysPost) (err error)
		// Delete 删除岗位
		Delete(ctx context.Context, postId int64) (err error)
		// 递归获取所有岗位和子岗位的id
		GetPostIds(ctx context.Context, postId int64) (ids []int64, err error)
		// 递归获得所有子岗位
		GetSubPost(list []*with.SysPost, postId int64) (subList []*with.SysPost)
		// 根据ID获取岗位信息
		GetInfoByID(ctx context.Context, postId int64) (post *with.SysPost, err error)
		// 根据ids获取岗位列表
		GetPostListByIDs(ctx context.Context, ids []int64) (list []*with.SysPost, err error)
	}
)

var (
	localSysPost ISysPost
)

func SysPost() ISysPost {
	if localSysPost == nil {
		panic("implement not found for interface ISysPost, forgot register?")
	}
	return localSysPost
}

func RegisterSysPost(i ISysPost) {
	localSysPost = i
}
