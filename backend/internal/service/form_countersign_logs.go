// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"context"
)

type (
	IFormCountersignLogs interface {
		// Create 创建回签记录
		Create(ctx context.Context, in *do.FormCountersignLogs) (insertid int64, err error)
		// CreateByIds 通过表单模板ID和数据ID创建回签记录
		CreateByIds(ctx context.Context, formTemplateId, formDataId int64, content, remark string) (insertid int64, err error)
		// CreateByGlobalId 通过全局ID创建回签记录
		CreateByGlobalId(ctx context.Context, globalId string, content string) (insertid int64, err error)
		// GetById 根据ID获取回签记录
		GetById(ctx context.Context, id int64) (out *entity.FormCountersignLogs, err error)
		// GetByFormDataId 根据表单数据ID获取回签记录
		GetByFormId(ctx context.Context, formTemplateId, formDataId int64) (out *entity.FormCountersignLogs, err error)
		// GetByFormTemplateId 根据表单模板ID获取回签记录
		GetByFormTemplateId(ctx context.Context, formTemplateId int64) (out []*entity.FormCountersignLogs, err error)
		// Update 更新回签记录
		Update(ctx context.Context, in *do.FormCountersignLogs) (err error)
		// Delete 删除回签记录
		Delete(ctx context.Context, id int64) (err error)
		// 获取列表
		GetProjectList(ctx context.Context, req *form.FormCountersignLogListReq) (res *form.FormCountersignLogListRes, err error)
	}
)

var (
	localFormCountersignLogs IFormCountersignLogs
)

func FormCountersignLogs() IFormCountersignLogs {
	if localFormCountersignLogs == nil {
		panic("implement not found for interface IFormCountersignLogs, forgot register?")
	}
	return localFormCountersignLogs
}

func RegisterFormCountersignLogs(i IFormCountersignLogs) {
	localFormCountersignLogs = i
}
