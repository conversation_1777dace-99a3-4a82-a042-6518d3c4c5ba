// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/project"
	"backend/internal/model/entity"
	"context"
)

type (
	IProjectRole interface {
		// 获取角色列表
		GetProjectRoleList(ctx context.Context, req *project.AdProjectRoleListReq) (res *project.AdProjectRoleListRes, err error)
		// 保存角色数据
		Save(ctx context.Context, req *project.AdProjectRoleSaveReq) (res *project.AdProjectRoleSaveRes, err error)
		// 删除角色
		Delete(ctx context.Context, req *project.AdProjectRoleDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, projectRoleId uint64) (ProjectRole *entity.AdProjectRole, err error)
		// 获得最大ID
		GetMaxID(ctx context.Context) (maxId uint64, err error)
	}
)

var (
	localProjectRole IProjectRole
)

func ProjectRole() IProjectRole {
	if localProjectRole == nil {
		panic("implement not found for interface IProjectRole, forgot register?")
	}
	return localProjectRole
}

func RegisterProjectRole(i IProjectRole) {
	localProjectRole = i
}
