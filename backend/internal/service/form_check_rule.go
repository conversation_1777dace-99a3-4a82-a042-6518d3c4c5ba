// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/entity"
	"context"
)

type (
	IFormCheckRule interface {
		// 获取验证规则列表
		GetFormCheckRuleList(ctx context.Context, req *form.FormCheckRuleListReq) (res *form.FormCheckRuleListRes, err error)
		// 获取启用验证规则列表
		GetEnabledRules(ctx context.Context, formTemplateId int64) (res []*entity.FormCheckRule, err error)
		// 保存验证规则数据
		Save(ctx context.Context, req *form.FormCheckRuleSaveReq) (res *form.FormCheckRuleSaveRes, err error)
		// 删除验证规则
		Delete(ctx context.Context, req *form.FormCheckRuleDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (PrintTemplate *entity.FormCheckRule, err error)
		// 修改状态
		UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error)
	}
)

var (
	localFormCheckRule IFormCheckRule
)

func FormCheckRule() IFormCheckRule {
	if localFormCheckRule == nil {
		panic("implement not found for interface IFormCheckRule, forgot register?")
	}
	return localFormCheckRule
}

func RegisterFormCheckRule(i IFormCheckRule) {
	localFormCheckRule = i
}
