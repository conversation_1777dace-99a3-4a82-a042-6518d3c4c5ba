// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	IGenerate interface {
		// 生成指定格式的ID ，例如
		// format = "R{20060102150406}{RanNum(5)}{RanStr(5)}{AUTO_INCREMENT({xxxx},5)}" , 或者 "DD{20060102}{AUTO_INCREMENT({xxxx},5)}"
		GenerateID(ctx context.Context, format string) (newid string)
	}
)

var (
	localGenerate IGenerate
)

func Generate() IGenerate {
	if localGenerate == nil {
		panic("implement not found for interface IGenerate, forgot register?")
	}
	return localGenerate
}

func RegisterGenerate(i IGenerate) {
	localGenerate = i
}
