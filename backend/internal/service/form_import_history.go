// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"context"
)

type (
	IFormImportHistory interface {
		// 获取导入数据文件列表
		GetFormImportHistoryList(ctx context.Context, req *form.FormImportHistoryListReq) (res *form.FormImportHistoryListRes, err error)
		// 新的数据导入
		New(ctx context.Context, req *form.FormImportHistoryNewReq) (res *form.FormImportHistoryNewRes, err error)
		// 触发导入数据文件
		TriggerImportFile(ctx context.Context, id int64) (err error)
		// 处理导入数据文件
		ProcessImportFile(ctx context.Context, historyInfo *entity.FormImportHistory) (err error)
		// 更新导入数据文件的方法
		Update(ctx context.Context, historyInfo *entity.FormImportHistory) (err error)
		// 修改导入数据文件状态
		UpdateStatus(ctx context.Context, id int64, status enum.ImportStatus) (err error)
		// 删除导入数据文件
		Delete(ctx context.Context, id uint64) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (FormImportHistory *entity.FormImportHistory, err error)
	}
)

var (
	localFormImportHistory IFormImportHistory
)

func FormImportHistory() IFormImportHistory {
	if localFormImportHistory == nil {
		panic("implement not found for interface IFormImportHistory, forgot register?")
	}
	return localFormImportHistory
}

func RegisterFormImportHistory(i IFormImportHistory) {
	localFormImportHistory = i
}
