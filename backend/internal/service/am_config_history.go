// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/automation"
	"backend/internal/model/do"
	"backend/internal/model/entity"
	"context"
)

type (
	IAmConfigHistory interface {
		// 获取自动化执行纪录列表
		GetAutoMationList(ctx context.Context, req *automation.AmConfigHistoryListReq) (res *automation.AmConfigHistoryListRes, err error)
		// 保存自动化执行纪录数据
		New(ctx context.Context, automationId int64, triggerData map[string]interface{}, sequence int64) (res *do.AmConfigHistory, err error)
		// 保存自动化执行纪录数据
		Update(ctx context.Context, amConfigHistory *do.AmConfigHistory) (err error)
		// 删除自动化执行纪录
		Delete(ctx context.Context, historyId uint64) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, historyId uint64, with bool) (AutoMationHistory *entity.AmConfigHistory, err error)
	}
)

var (
	localAmConfigHistory IAmConfigHistory
)

func AmConfigHistory() IAmConfigHistory {
	if localAmConfigHistory == nil {
		panic("implement not found for interface IAmConfigHistory, forgot register?")
	}
	return localAmConfigHistory
}

func RegisterAmConfigHistory(i IAmConfigHistory) {
	localAmConfigHistory = i
}
