// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/code"
	"backend/internal/model/entity"
	"context"
)

type (
	ICodeHistory interface {
		// 获取代码库列表
		GetCodeHistoryList(ctx context.Context, req *code.CodeHistoryListReq) (res *code.CodeHistoryListRes, err error)
		// 保存代码库数据
		Save(ctx context.Context, req *code.CodeHistorySaveReq) (res *code.CodeHistorySaveRes, err error)
		// 删除代码库
		Delete(ctx context.Context, req *code.CodeHistoryDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (PrintTemplate *entity.CodeHistory, err error)
	}
)

var (
	localCodeHistory ICodeHistory
)

func CodeHistory() ICodeHistory {
	if localCodeHistory == nil {
		panic("implement not found for interface ICodeHistory, forgot register?")
	}
	return localCodeHistory
}

func RegisterCodeHistory(i ICodeHistory) {
	localCodeHistory = i
}
