// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/system"
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"context"

	"github.com/gogf/gf/v2/os/gtime"
	"golang.org/x/time/rate"
)

type (
	ISysUser interface {
		GetListByProjectRoles(ctx context.Context, projectId int64, projectRoleIds []int64) (users []*with.SysUserOutline, err error)
		GetListByPostId(ctx context.Context, postId int64) (users []*with.SysUser, err error)
		GetListByUserIds(ctx context.Context, userIds []int64) (users []*with.SysUser, err error)
		// 检索用户信息
		GetList(ctx context.Context, req *system.SysUserListReq) (res *system.SysUserListRes, err error)
		// Save 保存用户信息
		Save(ctx context.Context, req *system.SysUserSaveReq) (userId int64, err error)
		// Add 添加
		Add(ctx context.Context, user *do.SysUser) (lastid int64, err error)
		// Edit 修改
		Edit(ctx context.Context, user *do.SysUser) (err error)
		// 删除
		Delete(ctx context.Context, userid int64) (err error)
		// 重置用户密码 @password 为空时，使用默认密码
		EditPassword(ctx context.Context, id int64, password string) (err error)
		// 修改部门关联关系
		EditWithDept(ctx context.Context, userId int64, withIds []uint64) (err error)
		// 修改岗位关联关系
		EditWithPost(ctx context.Context, userId int64, withIds []uint64) (err error)
		// 修改项目关联关系
		EditWithProject(ctx context.Context, userId int64, withProjects []*dto.AdProjectUser) (err error)
		// 修改角色关联关系
		EditWithRole(ctx context.Context, userId int64, withIds []uint64) (err error)
		// 获取用户信息
		GetInfoByID(ctx context.Context, id int64) (user *with.SysUser, err error)
		// 根据账号获取用户信息
		GetUserByAccount(ctx context.Context, account string) (user *with.SysUser, err error)
		// 获取用户的令牌桶
		GetUserLimiter(ctx context.Context, userName string) *rate.Limiter
		// 用户登录验证
		LoginValidate(ctx context.Context, userName string, password string) (user *with.SysUser, err error)
		// 根据用户所属角色，计算所拥有的菜单权限ID
		ComputeAllowMenuIds(ctx context.Context, user *with.SysUser) (menuIds []int64, err error)
		// 模拟用户的UserClaims
		MockGetUserClaims(ctx context.Context, userid int64) (userClaims *dto.UserClaims, err error)
		// 获得用户的jwttoken
		GetUserJwtToken(ctx context.Context, user *with.SysUser) (jwtToken string, err error)
		GetJwtRefreshToken(ctx context.Context, user *with.SysUser, autoLogin bool) (jwtRefreshToken string, err error)
		// 根据refreshToken 刷新jwttoken
		RefreshToken(ctx context.Context, refreshToken string) (newToken string, err error)
		// 更新用户的登录ip和时间
		UpdateUserLoginInfo(ctx context.Context, userid int64, ip string, time *gtime.Time) (err error)
		// 从ctx中获得存储的当前登录用户
		GetLoginUser(ctx context.Context) (user *dto.UserClaims)
		// 计算登录用户的功能权限
		ComputeUserPermissions(ctx context.Context) (permissions []*dto.SysPermissions, err error)
		CheckFormPermission(ctx context.Context, formId int64, permissionName enum.SysPermission) (result bool)
		CheckMenuPermission(ctx context.Context, mark enum.SysPermissionMark, permissionName enum.SysPermission) (result bool)
		// 验证用户是否拥有某个功能权限
		CheckPermission(ctx context.Context, permissions []*dto.SysPermissions, permissionType enum.SysPermissionType, permissionKey interface{}, permissionName enum.SysPermission) (result bool, err error)
		// 计算角色的功能权限
		ComputeRolesPermissions(ctx context.Context, roleIds []int64) (permissions []*dto.SysPermissions, err error)
		// 构造前端需要的CurrentUser
		GetCurrentUser(ctx context.Context) (currentUser *system.CurrentSysUserRes, err error)
		// 获得岗位关联的用户id列表
		GetPostUserIds(ctx context.Context, postIds []int64) (userIds []int64, err error)
		// 获得部门关联的用户id列表
		GetDeptUserIds(ctx context.Context, deptIds []int64) (userIds []int64, err error)
		// 获得当前用户所管理的部门id列表
		GetLeaderDeptIds(ctx context.Context, userId int64) (deptIds []int64, err error)
		// 获得当前用户所管理的项目id列表
		GetLeaderProjectIds(ctx context.Context, userId int64) (deptIds []int64, err error)
		// GetRoleProjectIds 获得角色关联的项目id列表
		GetRoleProjectIds(ctx context.Context, userId int64, roleIds []int64) (projectIds []int64, err error)
		// GetUserProperty 获取用户属性值（用于参数绑定等场景）
		GetUserProperty(ctx context.Context, loginUser *dto.UserClaims, property string) interface{}
	}
)

var (
	localSysUser ISysUser
)

func SysUser() ISysUser {
	if localSysUser == nil {
		panic("implement not found for interface ISysUser, forgot register?")
	}
	return localSysUser
}

func RegisterSysUser(i ISysUser) {
	localSysUser = i
}
