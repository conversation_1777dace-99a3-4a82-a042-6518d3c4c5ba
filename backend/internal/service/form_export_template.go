// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/form"
	"backend/internal/model/entity"
	"context"
)

type (
	IFormExportTemplate interface {
		// 获取导出模板列表
		GetFormExportTemplateList(ctx context.Context, req *form.FormExportTemplateListReq) (res *form.FormExportTemplateListRes, err error)
		// 获取启用导出模板列表
		GetEnabledRules(ctx context.Context, formTemplateId int64) (res []*entity.FormExportTemplate, err error)
		// 保存导出模板数据
		Save(ctx context.Context, req *form.FormExportTemplateSaveReq) (res *form.FormExportTemplateSaveRes, err error)
		// 删除导出模板
		Delete(ctx context.Context, req *form.FormExportTemplateDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (formExportTemplate *entity.FormExportTemplate, err error)
		// 修改状态
		UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error)
	}
)

var (
	localFormExportTemplate IFormExportTemplate
)

func FormExportTemplate() IFormExportTemplate {
	if localFormExportTemplate == nil {
		panic("implement not found for interface IFormExportTemplate, forgot register?")
	}
	return localFormExportTemplate
}

func RegisterFormExportTemplate(i IFormExportTemplate) {
	localFormExportTemplate = i
}
