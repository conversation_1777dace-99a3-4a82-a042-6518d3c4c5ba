// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

type (
	IDocProcess interface {
		// 创建流程实例
		CreateProcessInstance(processID int, creator string, params map[string]interface{}) (processInstanceID int, err error)
		// 进入下一个流程节点
		GoNextNode(processInstanceID int, operator string, params map[string]interface{}) (nodeID int, err error)
		// 返回上一个流程节点
		GoPrevNode(processInstanceID int, operator string, params map[string]interface{}) (nodeID int, err error)
		// 节点操作
		NodeOperation(nodeID int, operation string, params map[string]interface{}) (err error)
		// 创建or修改流程模板
		SaveProcessTemplate(templateName string, creator string, params map[string]interface{}) (processTemplateID int, err error)
	}
)

var (
	localDocProcess IDocProcess
)

func DocProcess() IDocProcess {
	if localDocProcess == nil {
		panic("implement not found for interface IDocProcess, forgot register?")
	}
	return localDocProcess
}

func RegisterDocProcess(i IDocProcess) {
	localDocProcess = i
}
