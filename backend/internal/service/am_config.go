// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/automation"
	"backend/internal/model/entity"
	"context"
)

type (
	IAmConfig interface {
		// 获取自动化配置列表
		GetAutoMationList(ctx context.Context, req *automation.ListReq) (res *automation.ListRes, err error)
		// 获取自动化配置的依赖
		GetDepend(ctx context.Context, req *automation.DependReq) (res *automation.DependRes, err error)
		// 获得所有启动中的自动化配置
		GetAllRunning(ctx context.Context) (res []*entity.AmConfig, err error)
		// 保存自动化配置数据
		Save(ctx context.Context, req *automation.AmConfigSaveReq) (res *automation.AmConfigSaveRes, err error)
		// 删除自动化配置
		Delete(ctx context.Context, req *automation.AmConfigDeleteReq) (err error)
		// 系统系统时初始化触发器
		InitTrigger(ctx context.Context) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (AutoMation *entity.AmConfig, err error)
		// 修改状态
		UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error)
		// 根据smemberKey触发自动化配置
		TriggerBySmemberKey(ctx context.Context, smemberKey string, triggerData map[string]interface{}, sequence int64) (affectedRows int64, err error)
		// 前向重试
		Retry(ctx context.Context, amConfigHistoryId int64) (err error)
		// 触发自动化流程
		Trigger(ctx context.Context, automationId int64, version int64, triggerData map[string]interface{}, sequence int64) (code int, err error)
	}
)

var (
	localAmConfig IAmConfig
)

func AmConfig() IAmConfig {
	if localAmConfig == nil {
		panic("implement not found for interface IAmConfig, forgot register?")
	}
	return localAmConfig
}

func RegisterAmConfig(i IAmConfig) {
	localAmConfig = i
}
