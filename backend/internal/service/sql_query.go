package service

import (
	"backend/api/v1/form"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type (
	ISqlQuery interface {
		// 测试SQL查询
		TestQuery(ctx context.Context, req *form.SqlQueryTestReq) (*form.SqlQueryTestRes, error)
		// 执行SQL查询
		ExecuteQuery(ctx context.Context, req *form.SqlQueryExecuteReq) (*form.SqlQueryExecuteRes, error)
		// 验证SQL语法
		ValidateSQL(ctx context.Context, req *form.SqlQueryValidateReq) (*form.SqlQueryValidateRes, error)
		// 获取数据库结构
		GetDatabaseSchema(ctx context.Context, req *form.SqlQuerySchemaReq) (*form.SqlQuerySchemaRes, error)
		// 替换SQL中的参数占位符
		ReplaceParameters(sql string, parameters []form.SqlParameter, formData g.Map, withTableData map[string]map[string]interface{}) (string, error)
		// 从表单数据中获取字段值
		GetValueFromFormData(formData g.Map, fieldPath string, withTableData map[string]map[string]interface{}) string
	}
)

var (
	localSqlQuery ISqlQuery
)

func SqlQuery() ISqlQuery {
	if localSqlQuery == nil {
		panic("implement not found for interface ISqlQuery, forgot register?")
	}
	return localSqlQuery
}

func RegisterSqlQuery(i ISqlQuery) {
	localSqlQuery = i
}
