// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	ISysUserRole interface {
		// 获得角色绑定的用户数量
		GetRoleUserLen(ctx context.Context, roleId int64) (count int, err error)
	}
)

var (
	localSysUserRole ISysUserRole
)

func SysUserRole() ISysUserRole {
	if localSysUserRole == nil {
		panic("implement not found for interface ISysUserRole, forgot register?")
	}
	return localSysUserRole
}

func RegisterSysUserRole(i ISysUserRole) {
	localSysUserRole = i
}
