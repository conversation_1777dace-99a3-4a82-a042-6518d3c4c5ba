// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/internal/model/do"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"context"
)

type (
	IFlowInstance interface {
		// 新建流程实例
		Create(ctx context.Context, form_template_id int64, form_id int64, extraData *dto.InstanceExtraData, noOpenError bool, instanceType enum.FlowInstanceType, createdBy int64) (instance *dto.FlowInstance, err error)
		// 审批流转
		StartNextApproval(ctx context.Context, historyAuditor *entity.FlowInstanceHistoryAuditor, instance *dto.FlowInstance) (err error)
		// 节点审核完成
		HistoryFinish(ctx context.Context, instance *dto.FlowInstance, history *entity.FlowInstanceHistory) (err error)
		// 更新流程实例状态
		UpdateInstanceStatus(ctx context.Context, instance_id int64, flow_stated enum.FlowStatus, oldInstance *dto.FlowInstance) (err error)
		// 更新流程实例信息
		UpdateInstance(ctx context.Context, instance_id int64, info *do.FlowInstance) (affected int64, err error)
		// 结束流程实例
		EndInstance(ctx context.Context, instance *dto.FlowInstance, flow_status enum.FlowStatus) (err error)
		// 实例首次启动
		StartInstanceFirst(ctx context.Context, instance *dto.FlowInstance) (history *entity.FlowInstanceHistory, historyAuditors []*entity.FlowInstanceHistoryAuditor, err error)
		// 节点首次启动
		StartNodeFirst(ctx context.Context, instance *dto.FlowInstance, node *dto.InstanceFlowNode) (history *entity.FlowInstanceHistory, historyAuditors []*entity.FlowInstanceHistoryAuditor, err error)
		// 执行模拟流程
		GenerateSteps(ctx context.Context, flowSchema *dto.FlowSchema, form_data map[string]interface{}, createUser *with.SysUser, extraData *dto.InstanceExtraData) (flowSteps *dto.FlowSchemaInstance, err error)
		// 根据当前节点获取下一个节点
		GetNextNode(ctx context.Context, flowSchema *dto.FlowSchema, form_data map[string]interface{}, currentNode *dto.InstanceFlowNode, createUser *with.SysUser, extraData *dto.InstanceExtraData) (nextNodes []*dto.InstanceFlowNode, err error)
		// 根据连接线获取下一个节点
		GetNextNodeByEdge(ctx context.Context, nodes []*dto.FlowSchemaNode, edge *dto.FlowSchemaEdge, createUser *with.SysUser, extraData *dto.InstanceExtraData, form_data map[string]interface{}) (node *dto.InstanceFlowNode, err error)
		// 获得节点审批人
		GetNodeApprovers(ctx context.Context, node *dto.FlowSchemaNodeData, createUser *with.SysUser, extraData *dto.InstanceExtraData, form_data map[string]interface{}) ([]*dto.InstanceFlowAuditor, error)
		// 条件评估
		EvaluateCondition(ctx context.Context, condition *dto.SettleCondition, formData map[string]interface{}, createUser *with.SysUser) (result bool, err error)
		GetInstance(ctx context.Context, flow_instance_id int64) (instance *dto.FlowInstance, err error)
		// 根据表单模板id和表单数据id获得流程实例信息
		GetInstanceByForm(ctx context.Context, form_template_id int64, form_data_id int64) (instance *with.FlowInstance, err error)
		GetAllAuditInstances(ctx context.Context, formTemplateId int64, formId int64) (list []*dto.InstanceTypeSummary, err error)
		GetInstanceInfo(ctx context.Context, instanceId int64) (instance *with.FlowInstance, err error)
		// 获得流程实例详细信息
		GetInfoByID(ctx context.Context, flow_instance_id int64) (instance *with.FlowInstance, err error)
		// 获得流程实例详细信息-带缓存
		GetInfoCache(ctx context.Context, flow_instance_id int64) (instance *with.FlowInstance, err error)
		// 根据流转参数设置，获得映射的参数值
		GetMappedParamValue(ctx context.Context, param *dto.FLowEdgeParam, formData map[string]interface{}, createUser *with.SysUser) (value interface{}, err error)
		// 根据流转参数设置，获得映射的参数值(表单值相关)
		GetMappedParamFormValue(ctx context.Context, formData map[string]interface{}, param *dto.FLowEdgeParamColumns) (value interface{}, err error)
		// 根据流转参数设置，获得映射的参数值(表单值相关)
		GetMappedParamCurrentUser(ctx context.Context, createUser *with.SysUser, param *dto.FLowEdgeParamColumns) (value interface{}, err error)
	}
)

var (
	localFlowInstance IFlowInstance
)

func FlowInstance() IFlowInstance {
	if localFlowInstance == nil {
		panic("implement not found for interface IFlowInstance, forgot register?")
	}
	return localFlowInstance
}

func RegisterFlowInstance(i IFlowInstance) {
	localFlowInstance = i
}
