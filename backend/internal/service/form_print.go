// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/print"
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"context"
)

type (
	IPrint interface {
		// 获取打印模版列表
		GetPrintTemplateList(ctx context.Context, req *print.ListReq) (res *print.ListRes, err error)
		// 保存打印模版数据
		Save(ctx context.Context, req *print.FormPrintTemplateSaveReq) (res *print.FormPrintTemplateSaveRes, err error)
		// 删除打印模版
		Delete(ctx context.Context, req *print.FormPrintTemplateDeleteReq) (err error)
		// 获得详细信息
		GetInfoByID(ctx context.Context, automationId uint64, with bool) (PrintTemplate *entity.FormPrintTemplate, err error)
		// 修改状态
		UpdateStatus(ctx context.Context, automationId int64, enabled bool) (err error)
		// 修改是否允许打印前编辑状态
		UpdateAllowEditStatus(ctx context.Context, templateId int64, allowEdit bool) (err error)
		GenerateFormToPDF(ctx context.Context, req *print.PrintFormReq, noCheckPermission bool) (res *print.PrintFormRes, err error)
		// GenerateFormToHtml generates HTML content for a given form and print template.
		GenerateFormToHtml(ctx context.Context, req *print.PrintFormHtmlReq) (res *print.PrintFormHtmlRes, err error)
		// GeneratePdfFromHtmlContent generates a PDF from provided HTML content.
		GeneratePdfFromHtmlContent(ctx context.Context, req *print.GeneratePdfFromHtmlReq) (res *print.GeneratePdfFromHtmlRes, err error)
		GetWithFormColumns(ctx context.Context, withTableDataMaps []*dto.WithTableData) (withFormColumns map[string][]*dto.FormColumn, withFormTableInfo map[string]*dto.SupportTable, err error)
	}
)

var (
	localPrint IPrint
)

func Print() IPrint {
	if localPrint == nil {
		panic("implement not found for interface IPrint, forgot register?")
	}
	return localPrint
}

func RegisterPrint(i IPrint) {
	localPrint = i
}
