// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/api/v1/system"
	"backend/internal/model/with"
	"context"
)

type (
	ISysMenu interface {
		TGetList(ctx context.Context) (list []*with.SysMenu, err error)
		GetBindFormIds(ctx context.Context) (ids []int64, err error)
		// 根据条件获取菜单列表
		GetList(ctx context.Context, req *system.MenuSearchReq) (list []*with.SysMenu, err error)
		// 获取所有菜单列表，菜单数据一般不多，可以从缓存中获取
		GetListCache(ctx context.Context) (list []*with.SysMenu, err error)
		// Save 菜单保存
		Save(ctx context.Context, req *system.MenuSaveReq) (err error)
		// Add 添加菜单
		Add(ctx context.Context, req *system.MenuSaveReq) (err error)
		// 移动一个菜单的顺序
		MoveSort(ctx context.Context, id int64, direction string) (err error)
		// 交换两个菜单的顺序
		SwapSort(ctx context.Context, activeId int64, overId int64) (err error)
		// Edit 菜单修改
		Edit(ctx context.Context, req *system.MenuSaveReq) (err error)
		// Delete 删除菜单
		Delete(ctx context.Context, menuId int64) (err error)
		// 递归获取所有菜单和子菜单的id
		GetMenuIds(ctx context.Context, menuId int64) (ids []int64, err error)
		// 递归获得所有子菜单
		GetSubMenu(list []*with.SysMenu, menuId int64) (subList []*with.SysMenu)
		// 根据ID获取菜单信息
		GetInfoByID(ctx context.Context, menuId int64) (menu *with.SysMenu, err error)
		// 获得在ID列表中的所有菜单
		GetListInIds(ctx context.Context, ids []int64) (list []*with.SysMenu, err error)
		// 获得不在ID列表中的所有菜单
		GetListNotInIds(ctx context.Context, ids []int64) (list []*with.SysMenu, err error)
		// 获得不在ID列表中的所有菜单关联的API
		GetApiAssociationNotInIds(ctx context.Context, ids []int64) (apiList []string, err error)
	}
)

var (
	localSysMenu ISysMenu
)

func SysMenu() ISysMenu {
	if localSysMenu == nil {
		panic("implement not found for interface ISysMenu, forgot register?")
	}
	return localSysMenu
}

func RegisterSysMenu(i ISysMenu) {
	localSysMenu = i
}
