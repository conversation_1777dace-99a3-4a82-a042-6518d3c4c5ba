// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"backend/internal/model/entity"
	"context"
)

type (
	ISysConfig interface {
		// 获得所有的配置信息
		GetAllConfig(ctx context.Context) (list []*entity.SysConfig, err error)
		GetConfig(ctx context.Context, configKey string) (config *entity.SysConfig, err error)
		// 获得一个配置
		GetValue(ctx context.Context, configKey string) (configValue string, err error)
		// 设置一个配置
		SetValue(ctx context.Context, configKey string, configValue string, version int64, removeCaches ...bool) (err error)
		// 删除一个配置
		Delete(ctx context.Context, configKey string) (err error)
	}
)

var (
	localSysConfig ISysConfig
)

func SysConfig() ISysConfig {
	if localSysConfig == nil {
		panic("implement not found for interface ISysConfig, forgot register?")
	}
	return localSysConfig
}

func RegisterSysConfig(i ISysConfig) {
	localSysConfig = i
}
