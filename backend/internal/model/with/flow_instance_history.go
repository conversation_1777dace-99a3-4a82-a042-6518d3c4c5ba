package with

import (
	"backend/internal/model/entity"

	"github.com/gogf/gf/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// 这里的快照数据暂不使用
type FlowInstanceHistoryShort struct {
	gmeta.Meta `orm:"table:flow_instance_history"`
	Id         int64       `json:"id"             orm:"id"               ` // id
	StepName   string      `json:"stepName"       orm:"step_name"        ` // 步骤名
	Status     int         `json:"status"         orm:"status"           ` // 当前状态
	CreatedAt  *gtime.Time `json:"createdAt"      orm:"created_at"       ` // 创建时间
	FinishdAt  *gtime.Time `json:"finishdAt"      orm:"finishd_at"       ` // 完成时间

}

type FlowInstanceHistory struct {
	gmeta.Meta `orm:"table:flow_instance_history"`
	*entity.FlowInstanceHistory
	Auditors []*FlowInstanceHistoryAuditor `json:"auditors" orm:"with:flow_instance_history_id=id"` // 审批人列表
}
