package with

import (
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/util/gmeta"
)

type SysPost struct {
	gmeta.Meta `orm:"table:sys_post"`
	*entity.SysPost
	ParentPost *entity.SysPost `orm:"with:id=parent_id" json:"parentPost" `
	Roles      []*SysPostRole  `orm:"with:post_id=id" json:"roles"`
}

type SysPostRole struct {
	gmeta.Meta `orm:"table:sys_post_role"`
	*entity.SysPostRole
	Role *entity.SysRole `orm:"with:id=role_id" json:"role" `
}
