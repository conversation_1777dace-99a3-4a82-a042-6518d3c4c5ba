package with

import (
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/util/gmeta"
)

type FLowTemplate struct {
	gmeta.Meta `orm:"table:flow_template"`
	Id         int64  `json:"id"          orm:"id"          ` // id
	FlowSchema string `json:"flowSchema"  orm:"flow_schema" ` // 流程设计整体结构
	FlowStatus string `json:"flowStatus"  orm:"flow_status" ` // 状态;1 正常 999 停止
}

type FormTemplate struct {
	gmeta.Meta `orm:"table:form_template"`
	*entity.FormTemplate
	CreatedUser     *SysUserOutline           `orm:"with:id=created_by" json:"createdUser" `
	FlowTemplate    *FLowTemplate             `orm:"with:id=flow_template_id" json:"flowTemplate" `
	PrintTemplates  []*FormPrintTemplateShort `orm:"with:form_template_id=id" json:"printTemplates" `
	ImportTemplates []*ImportTemplate         `orm:"with:form_template_id=id" json:"importTemplates" `
	ExportTemplates []*ExportTemplate         `orm:"with:form_template_id=id" json:"exportTemplates" `
	// RuleCodes      []*FormPrintTemplateRuleCode `orm:"with:form_template_id=id" json:"ruleCodes" `
}

// 表单模板简要信息
type FormTemplateShort struct {
	gmeta.Meta `orm:"table:form_template"`
	Id         int64  `json:"id"               orm:"id"                 ` // id
	FormTitle  string `json:"formTitle"        orm:"form_title"         ` // 表单名
}

type ImportTemplate struct {
	gmeta.Meta `orm:"table:form_import_template"`
	Id         int    `json:"id"              orm:"id"               ` // id
	Name       string `json:"name"            orm:"name"             ` // 模板名
	Status     int    `json:"status"          orm:"status"           ` // 状态;1 正常 999 停止
}

type ExportTemplate struct {
	gmeta.Meta `orm:"table:form_export_template"`
	Id         int    `json:"id"              orm:"id"               ` // id
	Name       string `json:"name"            orm:"name"             ` // 模板名
	Status     int    `json:"status"          orm:"status"           ` // 状态;1 正常 999 停止
}
type FormPrintTemplateShort struct {
	gmeta.Meta      `orm:"table:form_print_template"`
	Id              int    `json:"id"              orm:"id"               `  // id
	Name            string `json:"name"            orm:"name"             `  // 模板名
	Status          int    `json:"status"          orm:"status"           `  // 状态;1 正常 999 停止
	AllowEditBefore int    `json:"allowEditBefore" orm:"allow_edit_before" ` // 是否允许打印前编辑

}

type FormPrintTemplateRuleCode struct {
	gmeta.Meta  `orm:"table:form_check_rule"`
	Id          int    `json:"id"              orm:"id"               ` // id
	CodeContent string `json:"codeContent"    orm:"code_content"     `  // 代码内容
	FailMsg     string `json:"failMsg"        orm:"fail_msg"         `  // 验证失败提示内容
	Status      int    `json:"status"          orm:"status"           ` // 状态;1 正常 999 停止
}
