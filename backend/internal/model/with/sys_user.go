package with

import (
	"backend/internal/model/entity"

	"github.com/gogf/gf/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

type SysUser struct {
	gmeta.Meta `orm:"table:sys_user"`
	*entity.SysUser
	Roles []*SysUserRole `orm:"with:user_id=id" json:"roles"`
	Depts []*SysUserDept `orm:"with:user_id=id" json:"depts"`
	Posts []*SysUserPost `orm:"with:user_id=id" json:"posts"`
}

type SysUserOutline struct {
	gmeta.Meta      `orm:"table:sys_user"`
	Id              int64       `json:"id"              orm:"id"               ` // id
	Code            string      `json:"code"            orm:"code"             ` // 员工编号
	SystemAccount   string      `json:"systemAccount"   orm:"system_account"   ` // 系统账号
	Username        string      `json:"username"        orm:"username"         ` // 用户名
	Avatar          string      `json:"avatar"          orm:"avatar"           ` // 头像
	Email           string      `json:"email"           orm:"email"            ` // 邮箱
	ContactPhone    string      `json:"contactPhone"    orm:"contact_phone"    ` // 联系电话
	HireDate        *gtime.Time `json:"hireDate"        orm:"hire_date"        ` // 入职日期
	ResignationDate *gtime.Time `json:"resignationDate" orm:"resignation_date" ` // 离职日期
	Status          int         `json:"status"          orm:"status"           ` // 状态
	WithDepts       string      `json:"withDepts"       orm:"with_depts"       ` // 所在部门
	WithPosts       string      `json:"withPosts"       orm:"with_posts"       ` // 所属岗位
	WithRoles       string      `json:"withRoles"       orm:"with_roles"       ` // 拥有的角色
	WithCompanys    string      `json:"withCompanys"    orm:"with_companys"    ` // 关联的公司
	WithProjects    string      `json:"withProjects"    orm:"with_projects"    ` // 关联的项目
	Gender          int         `json:"gender"          orm:"gender"           ` // 性别
	Birthday        *gtime.Time `json:"birthday"        orm:"birthday"         ` // 生日
	ContactAddress  string      `json:"contactAddress"  orm:"contact_address"  ` // 联系地址
	Description     string      `json:"description"     orm:"description"      ` // 描述信息
	CreatedBy       int64       `json:"createdBy"       orm:"created_by"       ` // 创建人
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       ` // 创建时间
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       ` // 更新时间

}

type SysUserShort struct {
	gmeta.Meta    `orm:"table:sys_user"`
	Id            int64  `json:"id"              orm:"id"               ` // id
	Code          string `json:"code"            orm:"code"             ` // 员工编号
	SystemAccount string `json:"systemAccount"   orm:"system_account"   ` // 系统账号
	Username      string `json:"username"        orm:"username"         ` // 用户名
	Avatar        string `json:"avatar"          orm:"avatar"           ` // 头像
}

type SysUserRole struct {
	gmeta.Meta `orm:"table:sys_user_role"`
	*entity.SysUserRole
	Role *entity.SysRole `orm:"with:id=role_id" json:"role" `
}

type SysUserDept struct {
	gmeta.Meta `orm:"table:sys_user_dept"`
	*entity.SysUserDept
	Dept *entity.SysDept `orm:"with:id=dept_id" json:"dept" `
}

type SysUserPost struct {
	gmeta.Meta `orm:"table:sys_user_post"`
	*entity.SysUserPost
	Post *entity.SysPost `orm:"with:id=post_id" json:"post" `
}

type SysProjectUser struct {
	gmeta.Meta `orm:"table:sys_user_project"`
	*entity.SysUserProject
	User *SysUserOutline `orm:"with:id=user_id" json:"user" `
}
