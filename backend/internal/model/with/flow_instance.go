package with

import (
	"backend/internal/model/entity"

	"github.com/gogf/gf/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

type FlowInstance struct {
	gmeta.Meta `orm:"table:flow_instance"`
	*entity.FlowInstance
	CreatedUser *SysUser               `orm:"with:id=created_by" json:"createdUser" `
	Historys    []*FlowInstanceHistory `orm:"with:flow_instance_id=id" json:"historys"`
}

// 这里的快照数据暂不使用
type FlowInstanceShort struct {
	gmeta.Meta        `orm:"table:flow_instance"`
	Id                int64        `json:"id"                    orm:"id"                       ` // id
	FormTemplateId    int64        `json:"formTemplateId"    orm:"form_template_id"    `          // 绑定表单模板id
	FormTemplateTitle string       `json:"formTemplateTitle" orm:"form_template_title" `          // 绑定表单模板标题
	FormTableName     string       `json:"formTableName"     orm:"form_table_name"     `          // 绑定表单表名称
	FormTableId       int64        `json:"formTableId"       orm:"form_table_id"       `          // 绑定表单表id
	CurrentSteps      string       `json:"currentSteps"      orm:"current_steps"       `          // 当前正在进行的步骤
	InstanceState     int          `json:"instanceState"     orm:"instance_state"      `          // 实例状态
	CreatedBy         int64        `json:"createdBy"         orm:"created_by"          `          // 创建人
	CreateUser        SysUserShort `json:"createUser"        orm:"with:id=CreatedBy"        `     // 创建人信息
	CreatedAt         *gtime.Time  `json:"createdAt"         orm:"created_at"          `          // 创建时间
	FinishdAt         *gtime.Time  `json:"finishdAt"         orm:"finishd_at"          `          // 完成时间
	FlowInstanceType  int          `json:"flowInstanceType"  orm:"flow_instance_type"  `          // 流程实例类型 	1创建申请 2作废申请
}
