package with

import (
	"backend/internal/model/entity"

	"github.com/gogf/gf/v2/util/gmeta"
)

type FlowInstanceHistoryAuditorList struct {
	gmeta.Meta `orm:"table:flow_instance_history_auditor"`
	*entity.FlowInstanceHistoryAuditor
	FlowInstance        *FlowInstanceShort        `json:"flowInstance" orm:"with:id=flow_instance_id"`                // 流程实例
	FlowInstanceHistory *FlowInstanceHistoryShort `json:"flowInstanceHistory" orm:"with:id=flow_instance_history_id"` //节点实例
}

type FlowInstanceHistoryAuditor struct {
	gmeta.Meta `orm:"table:flow_instance_history_auditor"`
	*entity.FlowInstanceHistoryAuditor
	AuditorUser *SysUserShort `json:"auditorUser" orm:"with:id=user_id"` // 审批人
}
