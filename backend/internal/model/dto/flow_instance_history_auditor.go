package dto

import (
	"backend/internal/model/common"
	"backend/internal/model/enum"
	"backend/internal/model/with"

	"github.com/gogf/gf/v2/frame/g"
)

type HistoryAuditorListReq struct {
	FormTemplateId     int64               `json:"form_template_id" summary:"单据类型"  `
	CreatedBy          int64               `json:"created_by" summary:"发起人"  `
	FlowInstanceState  int                 `json:"flow_instance_state" summary:"流程实例状态"`
	InstanceStatus     string              `json:"instance_status" summary:"实例审批状态" `
	NodeStatus         string              `json:"node_status" summary:"节点审批状态" `
	ApprovalStatusType int                 `json:"approval_status_type" summary:"审批人审批状态匹配类型" `
	ApprovalStatus     enum.NodeActionType `json:"approval_status" summary:"审批人审批状态" `
	ApprovalUser       int64               `json:"approval_user" summary:"审核人"  `
	NodeType           enum.NodeType       `json:"node_type" summary:"节点类型"  `
	IsAutoPass         int                 `json:"is_auto_pass" summary:"是否是自动通过的"`
	FlowInstanceType   int                 `json:"flow_instance_type" summary:"流程实例类型"`
	common.PageReq
}

type HistoryAuditorListRes struct {
	g.Meta `mime:"application/json"`
	List   []*with.FlowInstanceHistoryAuditorList `json:"list"` //
	common.ListRes
}
