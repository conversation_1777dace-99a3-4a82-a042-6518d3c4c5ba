package dto

import (
	"backend/internal/model/enum"
)

// 流转条件参数
type FLowEdgeParam struct {
	Name   string                `json:"name"`
	Label  string                `json:"label"`
	Column *FLowEdgeParamColumns `json:"column"`
}

// 流转条件参数
type FLowEdgeParams struct {
	Name    string                  `json:"name"`
	Label   string                  `json:"label"`
	Columns []*FLowEdgeParamColumns `json:"columns"`
}

// 流转条件参数列
type FLowEdgeParamColumns struct {
	ColumnName string              `json:"column_name"`
	Label      string              `json:"label"`
	ValueType  enum.ParamValueType `json:"value_type"`
}

type FLowSchemaEdgeNode struct {
	Cell string `json:"cell"` // 节点ID
}
type FlowSchemaEdgeData struct {
	Label         string               `json:"label"`
	Props         []*SettleProp        `json:"props"`
	Selected      bool                 `json:"selected"`
	FlowCondition *SettleFlowCondition `json:"flowCondition"`
}
type FlowSchemaEdge struct {
	Id       string              `json:"id"`       // 唯一标识
	Animated bool                `json:"animated"` // 是否有动画效果
	Shape    string              `json:"shape"`    // 形状
	Label    interface{}         `json:"label"`    // 标签
	Data     *FlowSchemaEdgeData `json:"data"`     // 额外数据
	Source   *FLowSchemaEdgeNode `json:"source"`   // 源节点
	Target   *FLowSchemaEdgeNode `json:"target"`   // 目标节点
	ZIndex   int                 `json:"zIndex"`   // 层级
}
type FlowSchemaNodeData struct {
	Id               string                 `json:"id"`
	Label            string                 `json:"label"`
	Props            []*SettleProp          `json:"props"`
	Auditor          *SettleAuditor         `json:"auditor"`
	Reverse          *SettleReverse         `json:"reverse"`
	Timeout          *SettleTimeout         `json:"timeout"`
	Selected         bool                   `json:"selected"`
	Condition        enum.NodeCondition     `json:"condition"`
	AuditorIsNull    enum.NodeAuditorIsNull `json:"auditor_isnull"`
	ShowOtherOpinion bool                   `json:"showOtherOpinion"`
}
type FlowSchemaNode struct {
	Id    string              `json:"id"`    // 唯一标识
	Data  *FlowSchemaNodeData `json:"data"`  // 额外数据
	Shape enum.NodeType       `json:"shape"` // 形状
	//....
}

// 流程模板设计结构
type FlowSchema struct {
	AllowComment    bool              `json:"allowComment"`    // 是否允许评论
	AllowRevoke     bool              `json:"allowRevoke"`     // 是否允许撤回
	ApproveType     interface{}       `json:"approveType"`     // 审批类型
	PreviewCcNode   bool              `json:"previewCcNode"`   // 是否预览抄送节点
	RepeatApprove   bool              `json:"repeatApprove"`   // 重复审批人自动通过
	Edges           []*FlowSchemaEdge `json:"edges"`           // 连接线
	Nodes           []*FlowSchemaNode `json:"nodes"`           // 节点
	WithFormColumns []string          `json:"withFormColumns"` // 流转关联的表单字段
	HasSelectedDept bool              `json:"hasSelectedDept"` // 是否有需要选择部门的节点（用于用户在多个部门时提示用户选择部门审核）
	// 需要选择审核人的节点
}
