package dto

import (
	"backend/internal/model/enum"
	"backend/library"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

/*
 * 流程设置器相关的DTO
 */

type SettleCondition struct {
	ID         string         `json:"id"`
	Operator   enum.Operator  `json:"operator"`
	LeftParam  *FLowEdgeParam `json:"leftParam"`
	ValueType  string         `json:"valueType"`
	RightParam interface{}    `json:"rightParam"`
}

type SettleConditionGroup struct {
	ID            string        `json:"id"`
	Conditions    []interface{} `json:"conditions"`
	GroupRelation string        `json:"groupRelation"`
}

func (s *SettleConditionGroup) EvaluateConditionGroup(ctx context.Context, evaluateCondition EvaluateCondition) (result bool, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		result = s.GroupRelation == "AND"
		for _, condition := range s.Conditions {
			conditionResult, err := evaluateCondition(ctx, condition)
			library.ErrIsNil(ctx, err)
			if s.GroupRelation == "AND" {
				result = result && conditionResult
				if !result {
					return
				}
			} else if s.GroupRelation == "OR" {
				result = result || conditionResult
				if result {
					return
				}
			}
		}
	})
	return
}

type SettleFlowConditionDetail struct {
	ID                     string                  `json:"id"`
	ConditionGroups        []*SettleConditionGroup `json:"conditionGroups"`
	ConditionGroupRelation string                  `json:"conditionGroupRelation"`
}

type EvaluateCondition func(ctx context.Context, condition interface{}) (result bool, err error)

func (s *SettleFlowConditionDetail) EvaluateFlowConditionDetail(ctx context.Context, evaluateCondition EvaluateCondition) (result bool, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		result = s.ConditionGroupRelation == "AND"
		for _, group := range s.ConditionGroups {
			groupResult, err := group.EvaluateConditionGroup(ctx, evaluateCondition)
			library.ErrIsNil(ctx, err)
			if s.ConditionGroupRelation == "AND" {
				result = result && groupResult
				if !result {
					return
				}
			} else if s.ConditionGroupRelation == "OR" {
				result = result || groupResult
				if result {
					return
				}
			}
		}
	})
	return
}

type SettleFlowCondition struct {
	Condition *SettleFlowConditionDetail `json:"condition"`
	Type      string                     `json:"type"`
}

type SettleProp struct {
	Name         string      `json:"name"`
	Title        string      `json:"title"`
	Setter       interface{} `json:"setter"`
	PropType     string      `json:"propType"`
	DefaultValue interface{} `json:"defaultValue"`
}

type SettleOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type SettleRadioGroupSetterProps struct {
	Options []*SettleOption `json:"options"`
}

type SettleRadioGroupSetter struct {
	Name  string                       `json:"name"`
	Props *SettleRadioGroupSetterProps `json:"props"`
}

type SettleDefaultValueTimeout struct {
	Time  int `json:"time"`
	Unit  int `json:"unit"`
	Value int `json:"value"`
}

type SettleDefaultValueReverse struct {
	Time  int  `json:"time"`
	Value bool `json:"value"`
}

type SettleProjectRoleItem struct {
	Id          int64       `json:"id"          orm:"id"          ` // id
	Code        string      `json:"code"        orm:"code"        ` // 编号
	Name        string      `json:"name"        orm:"name"        ` // 角色名
	IsDefault   int         `json:"isDefault"   orm:"is_default"  ` // 是否是默认角色
	Instruction string      `json:"instruction" orm:"instruction" ` // 角色说明
	Value       interface{} `json:"value"`                          // 值
	Label       interface{} `json:"label"`                          // 标签
}
type SettleProjectRole struct {
	Roles      []*SettleProjectRoleItem `json:"roles"`
	ColumnName string                   `json:"column_name"`
}

type SettleSequentialDeptLeader struct {
	Type  enum.SequentialDeptLeaderType `json:"type"`
	Level int                           `json:"level"` // 指定向上级别
	Rule  enum.DeptLeaderRule           `json:"rule"`  // 提取规则
}
type SettleDeptLeader struct {
	Level int                 `json:"up_level"` // 向上级别
	Rule  enum.DeptLeaderRule `json:"rule"`     // 提取规则
}

type SettleSpecifiedUsers struct {
	Id       int64  `json:"id"`
	Username string `json:"username"`
}

type SettleAuditor struct {
	Time                 int                         `json:"time"`
	Value                enum.AuditorType            `json:"value"`                  // 审批人类型
	FieldUsers           []string                    `json:"field_users"`            // 表单字段中关联的人
	DeptLeader           *SettleDeptLeader           `json:"dept_leader"`            // 直属部门负责人
	ProjectRole          *SettleProjectRole          `json:"project_role"`           // 项目角色
	ProjectLeader        string                      `json:"project_leader"`         // 项目负责人
	SpecifiedUsers       []*SettleSpecifiedUsers     `json:"specified_users"`        // 指定用户
	SpecifiedPosition    []int64                     `json:"specified_position"`     // 指定岗位
	SpecifiedDeptLeader  []int64                     `json:"specified_dept_leader"`  // 指定部门负责人
	SequentialDeptLeader *SettleSequentialDeptLeader `json:"sequential_dept_leader"` // 逐级部门负责人
	SelfChoice           bool                        `json:"self_choice"`            // 是否至少自选一人
}

type SettleReverse struct {
	Time  int  `json:"time"`
	Value bool `json:"value"`
}

type SettleTimeout struct {
	Time  int `json:"time"`
	Unit  int `json:"unit"`
	Value int `json:"value"`
}
