package dto

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"backend/internal/model/enum"
	"backend/internal/model/with"
	"backend/library"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

var AllowedColumnTypes = library.MySlice[enum.ColumnType]{enum.ColumnType_Varchar, enum.ColumnType_Char, enum.ColumnType_Int, enum.ColumnType_BIGINT, enum.ColumnType_Float, enum.ColumnType_Double, enum.ColumnType_Decimal, enum.ColumnType_Text, enum.ColumnType_Date, enum.ColumnType_Time, enum.ColumnType_DateTime, enum.ColumnType_Timestamp, enum.ColumnType_Json, enum.ColumnType_Tinyint}

type ExpandInfo struct {
	FormTemplateId int64 `json:"form_template_id"` // 表单模板ID
	FormDataId     int64 `json:"form_data_id"`     // 表单ID
}

type FormColumn struct {
	Id               string                       `json:"id"`                 // 字段Id，唯一标识
	ComponentName    string                       `json:"component_name"`     // 组件名称
	Label            string                       `json:"label"`              // 字段标题
	ColumnType       string                       `json:"column_type"`        // 对应的数据库字段类型
	Required         bool                         `json:"required"`           // 是否必填
	Rules            []string                     `json:"rules"`              // 验证规则
	RulesFailMessage string                       `json:"rules_fail_message"` // 规则验证失败提示
	Props            map[string]interface{}       `json:"props"`              // 组件属性
	Children         library.MySlice[*FormColumn] `json:"children"`           // 子字段
	IsFixed          bool                         `json:"is_fixed"`           // 是否为固定字段
}

type RuleFilterValue struct {
	Operator enum.Operator `json:"operator"` // 操作符
	Value    interface{}   `json:"value"`    // 值
}

type FormFileItem struct {
	UId    string `json:"uid"`    // 文件唯一标识
	Name   string `json:"name"`   // 文件名
	Url    string `json:"url"`    // 文件链接
	Status string `json:"status"` // 状态
	Error  string `json:"error"`  // 错误信息
}

type FormWithInfo struct {
	Type                    int    `json:"type"`                       // 关联类型 0: 表单模板表 1: 自定义表
	TableName               string `json:"table_name"`                 // 关联的表名称
	FormTemplateID          int64  `json:"form_template_id"`           // 关联的模版表的ID
	RelatedColumnName       string `json:"related_column_name"`        // 关联表的字段名称
	CurrentColumnName       string `json:"current_column_name"`        // 当前表的字段名称
	CurrentColumnType       int    `json:"current_column_type"`        // 当前表的字段类型 1 单选 2 多选
	CurrentParentColumnName string `json:"current_parent_column_name"` // 当前表的父级字段名称
	AllowMainTableIgnore    bool   `json:"allow_main_table_ignore"`    // 允许主表忽略关联
}

type FormRelatedData struct {
	TableName            string                 `json:"table_name"`              // 关联的表名称
	FormTitle            string                 `json:"form_title"`              // 表单标题
	FormData             map[string]interface{} `json:"form_data"`               // 关联的表单数据
	AllowMainTableIgnore bool                   `json:"allow_main_table_ignore"` // 允许主表忽略关联
	// ...
}

func (f *FormColumn) GetColumnTypeOutline() string {
	// 将f.ColumnType转换为小写
	ColumnTypeOutline := strings.ToLower(f.ColumnType)
	// 去除长度设置
	re := regexp.MustCompile(`\(\d+\)`)
	ColumnTypeOutline = re.ReplaceAllString(ColumnTypeOutline, "")
	return ColumnTypeOutline
}

// 验证字段的值格式是否符合预期要求
func (f *FormColumn) ValidateVal(ctx context.Context, value interface{}) (processedValue interface{}, err error) {
	// 因为级联公式显示隐藏组件的问题，这里暂时禁用空值验证（如果某个列是必填，但是因为公式隐藏了，理论上它不需要进行验证了，但后台仍然会验证导致数据无法添加）
	// 后续想办法处理
	// if f.Required && g.IsEmpty(value) {
	// 	err = fmt.Errorf("'%s'必须填写", f.Label)
	// 	return
	// }
	// 默认返回原始值
	processedValue = value
	err = f.ValidateType()
	if err != nil {
		return
	}
	// 空值不验证格式
	if g.IsEmpty(value) {
		return
	}

	if varValue, ok := value.(*gvar.Var); ok {
		value = varValue.Val()
	}

	if f.ComponentName == "Field.UploadImage" || f.ComponentName == "Field.UploadFile" {
		var fileValues []*FormFileItem
		// 文件类型的数据验证
		if str_err := gconv.Structs(value, &fileValues); str_err != nil {
			g.Log().Info(ctx, library.SDump("validate_val str_err", str_err))
			err = fmt.Errorf("字段'%s'的值格式不正确", f.Label)
			return
		}

		for _, file := range fileValues {
			if file.Error != "" {
				err = fmt.Errorf("字段'%s' 未上传成功，错误：%s", f.Label, file.Error)
				return
			} else if file.Status != "done" {
				err = fmt.Errorf("字段'%s' 未上传成功", f.Label)
				return
			}
		}
	} else if f.ComponentName == "Field.Number" {
		// 验证是否是数字类型
		if _, parseErr := strconv.ParseFloat(gconv.String(value), 64); parseErr != nil {
			err = fmt.Errorf("字段'%s'的值类型应为数字", f.Label)
			return
		}
	} else {
		// 验证value 是否符合当前字段的类型要求
		switch f.GetColumnTypeOutline() {
		case "varchar", "char", "text":
			if gvarValue, ok := value.(*gvar.Var); ok {
				value = gvarValue.String()
			}
			if _, ok := value.(string); !ok {
				err = fmt.Errorf("字段'%s'的值类型应为string", f.Label)
			}
		case "json":

			switch value := value.(type) {
			case string:
				var jsonValue interface{}
				unmarshalErr := json.Unmarshal([]byte(value), &jsonValue)
				if unmarshalErr != nil {
					err = fmt.Errorf("字段'%s'的值类型应为JSON对象或数组", f.Label)
				}
			case map[string]interface{}, []interface{}, []map[string]interface{}:
				// 正确的 JSON 类型
			default:
				err = fmt.Errorf("字段'%s'的值类型应为JSON对象或数组", f.Label)
			}
		case "int", "bigint":
			if _, ok := value.(int64); !ok {
				err = fmt.Errorf("字段'%s'的值类型应为int64", f.Label)
			}
		case "float", "double", "decimal":
			if _, ok := value.(float64); !ok {
				err = fmt.Errorf("字段'%s'的值类型应为float64", f.Label)
			}
		case "date", "time", "datetime", "timestamp":
			formats := generateDateFormats()
			valid := false
			switch convValue := value.(type) {
			case *gtime.Time:
				valid = true
			case string:
				completedDate := completeDate(convValue)
				// 如果日期被修改了，设置valid为true表示可以接受这种格式
				if completedDate != convValue {
					processedValue = completedDate // 更新value为补全后的日期
					valid = true
				} else {
					// 使用标准格式尝试解析
					for _, format := range formats {
						_, err := time.Parse(format, convValue)
						if err == nil {
							valid = true
							break
						}
					}
				}

			case *gvar.Var:
				valueTime := convValue.Time()
				if !valueTime.IsZero() {
					valid = true
				}
			}

			if !valid {
				g.Log().Info(ctx, fmt.Sprintf("错误：%v", err))
				err = fmt.Errorf("字段'%s'的值类型应为正确的时间格式", f.Label)
			}
		}
	}
	// 验证是否符合Rules规则
	if !g.IsEmpty(f.Rules) {
		// TODO: 验证规则
	}

	return
}

// 完善日期字符串，对于只有年月的日期添加默认天数，对于只有年的日期添加默认月日
func completeDate(dateStr string) string {
	// 检测是否只有年格式 (2025)
	yearRegex := regexp.MustCompile(`^(\d{4})$`)
	if matches := yearRegex.FindStringSubmatch(dateStr); len(matches) == 2 {
		year := matches[1]
		// 添加默认的月日 "01-01"
		return fmt.Sprintf("%s-01-01", year)
	}

	// 检测是否只有年月格式 (2006-01 或 2006/01 或 2006-1 或 2006/1)
	yearMonthRegex := regexp.MustCompile(`^(\d{4})[-/](\d{1,2})$`)
	if matches := yearMonthRegex.FindStringSubmatch(dateStr); len(matches) == 3 {
		year := matches[1]
		month := matches[2]
		if len(month) == 1 {
			month = "0" + month
		}
		// 添加默认的天数 "01"
		return fmt.Sprintf("%s-%s-01", year, month)
	}
	return dateStr
}

// 生成所有可能的日期格式
func generateDateFormats() []string {
	baseFormats := []string{
		"2006-01-02",
		"2006/01/02",
		"2006-01-02 15:04",
		"2006/01/02 15:04",
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
		"2006-01-02T15:04:05Z07:00",
	}

	var formats []string
	for _, base := range baseFormats {
		formats = append(formats, base)
		formats = append(formats, strings.Replace(base, "-", "/", -1))
		formats = append(formats, strings.Replace(base, "/", "-", -1))
		formats = append(formats, strings.Replace(base, "01", "1", -1))
		formats = append(formats, strings.Replace(base, "02", "2", -1))
		formats = append(formats, strings.Replace(base, "2006-01-02", "2006-1-2", -1))
		formats = append(formats, strings.Replace(base, "2006/01/02", "2006/1/2", -1))
	}

	return formats
}
func (f *FormColumn) ValidateType() (err error) {
	if AllowedColumnTypes.FindIndex([]library.FilterFunc[enum.ColumnType]{func(v enum.ColumnType) bool {
		return v == enum.ColumnType(f.GetColumnTypeOutline())
	}}) < 0 {
		err = fmt.Errorf("字段'%s'的类型'%s'不支持", f.Label, f.ColumnType)
		return
	}
	return
}
func (s *FormColumn) FormatValue(ctx context.Context, value interface{}) (interface{}, error) {
	switch s.ComponentName {
	case "Field.WithSingleSelect", "Field.SingleSelect", "Field.RadioGroup":
		return formatSingleSelect(value)
	case "Field.WithMultipleSelect", "Field.MultipleSelect", "Field.CheckboxGroup":
		return formatMultipleSelect(value, "value")
	case "Field.UserSelect", "Field.DeptSelect", "Field.PostSelect":
		return formatArrayIdSelect(value)
	case "Field.DatePicker":
		return formatDatePicker(s, value)
	default:
		return value, nil
	}
}

func formatArrayIdSelect(value interface{}) (interface{}, error) {

	var result map[string]interface{}
	switch value := value.(type) {
	case map[string]interface{}:
		result = value
	case *gvar.Var:
		unErr := json.Unmarshal([]byte(value.String()), &result)
		if unErr != nil {
			var userArr []SysUserBrief
			unErr := json.Unmarshal([]byte(value.String()), &userArr)
			if unErr != nil {
				return nil, unErr
			}
			if len(userArr) > 0 {
				return userArr[0].ID, nil
			}
		}
	case *with.SysUser:
		return value.Id, nil
	case with.SysUser:
		return value.Id, nil
	}
	if label, ok := result["id"]; ok {
		return label, nil
	}
	return nil, fmt.Errorf("invalid value type for formatArrayIdSelect")
}

func formatSingleSelect(value interface{}) (interface{}, error) {

	var result map[string]interface{}
	switch value := value.(type) {
	case map[string]interface{}:
		result = value
	case *gvar.Var:
		unErr := json.Unmarshal([]byte(value.String()), &result)
		if unErr != nil {
			return nil, unErr
		}
	}
	if label, ok := result["value"]; ok {
		return label, nil
	}
	return nil, fmt.Errorf("invalid value type for formatSingleSelect")
}
func formatMultipleSelect(value interface{}, valueKey string) (interface{}, error) {
	var result []map[string]interface{}
	switch value := value.(type) {
	case []map[string]interface{}:
		result = value
	case *gvar.Var:
		unErr := json.Unmarshal([]byte(value.String()), &result)
		if unErr != nil {
			return nil, unErr
		}
	}
	values := make([]interface{}, 0)

	for _, item := range result {
		if label, ok := item[valueKey]; ok {
			values = append(values, label)
		}
	}
	return values, nil
}

func (s *FormColumn) FormatLabel(ctx context.Context, value interface{}) (interface{}, error) {
	switch s.ComponentName {
	case "Field.WithSingleSelect":
		return formatWithSingleSelect(value)
	case "Field.WithMultipleSelect":
		return formatWithMultipleSelect(value, "label")
	case "Field.SingleSelect", "Field.MultipleSelect", "Field.CheckboxGroup", "Field.RadioGroup":
		return formatSelectWithProps(s, value)
	case "Field.UploadFile", "Field.UploadImage":
		return formatUpload(value)
	case "Field.DatePicker":
		return formatDatePicker(s, value)
	case "Field.UserSelect":
		return formatUserSelect(value)
	case "Field.Input":
		return value, nil
	default:
		return value, nil
	}
}

func formatUserSelect(value interface{}) (interface{}, error) {
	var result []map[string]interface{}
	switch value := value.(type) {
	case *with.SysUser:
		return value.Username, nil
	case []map[string]interface{}:
		result = value
	case *gvar.Var:
		unErr := json.Unmarshal([]byte(value.String()), &result)
		if unErr != nil {
			return nil, unErr
		}
	}
	labels := []string{}
	for _, item := range result {
		if label, ok := item["username"].(string); ok {
			labels = append(labels, label)
		}
	}
	return strings.Join(labels, ", "), nil
}
func formatWithSingleSelect(value interface{}) (interface{}, error) {

	var result map[string]interface{}
	switch value := value.(type) {
	case map[string]interface{}:
		result = value
	case *gvar.Var:
		unErr := json.Unmarshal([]byte(value.String()), &result)
		if unErr != nil {
			return nil, unErr
		}
	}
	if label, ok := result["label"].(string); ok {
		return label, nil
	}
	return nil, fmt.Errorf("invalid value type for WithSelect")
}

func formatWithMultipleSelect(value interface{}, labelName string) (interface{}, error) {
	var result []map[string]interface{}
	switch value := value.(type) {
	case []map[string]interface{}:
		result = value
	case *gvar.Var:
		unErr := json.Unmarshal([]byte(value.String()), &result)
		if unErr != nil {
			return nil, unErr
		}
	}
	labels := []string{}
	for _, item := range result {
		if label, ok := item[labelName].(string); ok {
			labels = append(labels, label)
		}
	}
	return strings.Join(labels, ", "), nil
}

// 根据Props处理Field.SingleSelect、Field.MultipleSelect、Field.CheckboxGroup、Field.RadioGroup
func formatSelectWithProps(columnInfo *FormColumn, value interface{}) (interface{}, error) {
	result := gconv.String(value)
	if props := columnInfo.Props; props != nil {
		options, ok := props["options"].([]interface{})
		if !ok {
			return nil, fmt.Errorf("invalid props for select field")
		}
		if strings.HasPrefix(columnInfo.ColumnType, "json") {
			var selectedValues []interface{}
			if err := json.Unmarshal([]byte(result), &selectedValues); err != nil {
				return nil, err
			}
			labels := []string{}
			for _, val := range selectedValues {
				for _, option := range options {
					if optMap, ok := option.(map[string]interface{}); ok {
						if optMap["value"] == val {
							labels = append(labels, optMap["label"].(string))
						}
					}
				}
			}
			return strings.Join(labels, ", "), nil
		} else {
			for _, option := range options {
				if optMap, ok := option.(map[string]interface{}); ok {
					if optMap["value"] == result {
						return optMap["label"], nil
					}
				}
			}
		}
	}
	return value, nil
}

// 处理Field.UploadFile和Field.UploadImage
func formatUpload(value interface{}) (interface{}, error) {
	var result []map[string]interface{}
	if err := parseJSON(value, &result); err != nil {
		return nil, err
	}
	links := []string{}
	for _, item := range result {
		if name, ok := item["name"].(string); ok {
			if url, ok := item["url"].(string); ok {
				links = append(links, fmt.Sprintf("<a href=\"%s\">%s</a>", url, name))
			}
		}
	}
	return strings.Join(links, "<br/>"), nil
}

// 处理Field.DatePicker
func formatDatePicker(columnInfo *FormColumn, value interface{}) (interface{}, error) {
	dateFormat := "2006-01-02"
	if format, ok := columnInfo.Props["datetype"].(string); ok {
		switch format {
		case "datetime":
			dateFormat = "2006-01-02 15:04:05"
		case "year":
			dateFormat = "2006"
		case "month":
			dateFormat = "2006-01"
		}
	}
	var t time.Time
	if strVar, ok := value.(*gvar.Var); ok {
		switch timeVal := strVar.Val().(type) {
		case *gtime.Time:
			t = timeVal.Time
		case string:
			t, _ = time.Parse("2006-01-02 15:04:05", timeVal)

		}
		return t.Format(dateFormat), nil
	}
	return value, nil
}

// 工具函数：解析JSON
func parseJSON(input interface{}, output interface{}) error {
	if str, ok := input.(string); ok && str != "" {
		if err := json.Unmarshal([]byte(str), output); err != nil {
			return err
		}
		return nil
	}
	return fmt.Errorf("input is not a valid JSON string")
}

type FormTableDesign struct {
	Actiions       library.MySlice[string]               `json:"actions"`        // 表格操作按钮
	Columns        library.MySlice[*FormTableDesignItem] `json:"columns"`        // 表格列
	Filter         library.MySlice[*FormTableDesignItem] `json:"filter"`         // 表格过滤器
	ActionSettings map[string]map[string]interface{}     `json:"actionSettings"` // 表格操作按钮设置
}

type FormTableDesignItem struct {
	Id           string                 `json:"id"`
	Switch       bool                   `json:"switch"`
	Children     []*FormTableDesignItem `json:"children"`
	NeedSum      bool                   `json:"needSum"`      // 是否需要求和
	SearchSettle *SearchSettle          `json:"searchSettle"` // 搜索设置
	Width        int                    `json:"width"`        // 宽度
}

type SearchSettle struct {
	SearchIgnoreWords []string `json:"searchIgnoreWords"` // 搜索忽略字段
	MinSearchLength   int      `json:"minSearchLength"`   // 最小搜索长度
}

// 处理搜索相关的设置
func (s *FormTableDesignItem) HandleSearchWords(keyWork interface{}) interface{} {
	result := keyWork
	if s.SearchSettle != nil {
		if len(s.SearchSettle.SearchIgnoreWords) > 0 {
			for _, v := range s.SearchSettle.SearchIgnoreWords {
				result = strings.ReplaceAll(gconv.String(result), v, "")
			}
		}
		if s.SearchSettle.MinSearchLength > 0 {
			if utf8.RuneCountInString(gconv.String(result)) < s.SearchSettle.MinSearchLength {
				result = ""
			}
		}
	}
	return result
}

type SupportTable struct {
	TableName      string                 `json:"table_name"`       // 表名
	FormTemplateId int64                  `json:"form_template_id"` // 表单模板ID
	Label          string                 `json:"label"`            // 表名显示名称
	Type           int                    `json:"type"`             // 表类型 0: 表单模板表 1: 自定义表
	PermissionMark enum.SysPermissionMark `json:"permission_mark"`  // 权限标识
}

type SupportColumn struct {
	ColumnName       string `json:"column_name" orm:"COLUMN_NAME"`               // 列名
	Label            string `json:"label"       orm:"COLUMN_COMMENT"`            // 列名显示名称
	ColumnType       string `json:"column_type" orm:"COLUMN_TYPE"`               // 列类型
	ColumnRenderType string `json:"column_render_type" orm:"COLUMN_RENDER_TYPE"` // 列渲染类型
}

type FillRule struct {
	Source   string      `json:"source"`   // 规则来源
	Target   string      `json:"target"`   // 规则目标
	Type     int         `json:"type"`     // 规则类型 0: 普通字段 1: 子表字段
	SubRules []*FillRule `json:"subRules"` // 子规则
}

type FormPermissions struct {
	Self       FormPermission `json:"self"`
	Dept       FormPermission `json:"dept"`
	DeptLeader FormPermission `json:"dept_leader"`
	FieldUsers struct {
		Enabled bool         `json:"enabled"`
		Data    []FieldUsers `json:"data"`
	} `json:"field_users"`
	SpecifiedUsers struct {
		Enabled bool             `json:"enabled"`
		Data    []FormUserDetail `json:"data"`
	} `json:"specified_users"`
	SpecifiedPosition struct {
		Enabled bool               `json:"enabled"`
		Data    []FormPositionData `json:"data"`
	} `json:"specified_position"`
	ProjectLeader struct {
		Enabled bool              `json:"enabled"`
		Data    FormProjectLeader `json:"data"`
	} `json:"project_leader"`
	ProjectRole struct {
		Enabled bool         `json:"enabled"`
		Data    FormRoleData `json:"data"`
	} `json:"project_role"`
	CustomCondition struct {
		Enabled bool                `json:"enabled"`
		Data    CustomConditionData `json:"data"`
	} `json:"custom_condition"`
}

type FieldUsers struct {
	Label string `json:"label"`
	Value string `json:"value"`
}
type FormPermission struct {
	Enabled bool `json:"enabled"`
}

type FormUserDetail struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
}

type FormPositionData struct {
	ID    int64  `json:"id"`
	Label string `json:"label"`
}

type FormProjectLeader struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type FormRoleData struct {
	List       []FormRoleDataItem `json:"list"`
	ColumnName FormProjectLeader  `json:"columnName"`
}
type FormRoleDataItem struct {
	Id    int64       `json:"id"`
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

type FormTemplate struct {
	ID          int             `json:"id"`
	Permissions FormPermissions `json:"permissions"`
}

type ScriptCode struct {
	CodeName    string          `json:"codeName"`
	ColumnMap   []ColumnMapRule `json:"columnMap"`
	CodeContent string          `json:"codeContent"`
}

type ColumnMapRule struct {
	Id       string          `json:"id"`       // 规则ID
	Source   interface{}     `json:"source"`   // 映射来源
	Target   string          `json:"target"`   // 映射名称
	Type     int             `json:"type"`     // 规则类型 0: 普通字段 1: 子表字段 2：其他表（非关联）3：字段分组
	FuncCode ScriptCode      `json:"funcCode"` // 自定义函数代码
	Width    int             `json:"width"`    // 字段宽度
	SubRules []ColumnMapRule `json:"subRules"` // 子规则
	IsGroup  bool            `json:"isGroup"`  // 是否为分组字段
}

type FilterRules struct {
	Filter          map[string]interface{} `p:"filter" summary:"搜索类型的表单过滤条件"`
	RuleFilter      map[string]interface{} `p:"ruleFilter" summary:"规则过滤条件"`
	UnlimitedSearch bool                   `p:"unlimitedSearch" summary:"是否可以无限制检索"`
}

type PrintMap struct {
	TagKey              string     `json:"tagKey"`
	Column              string     `json:"column"`
	ContentPresentation string     `json:"contentPresentation"`
	ContentFormat       string     `json:"contentFormat"`
	ContentFormatCode   ScriptCode `json:"contentFormatCode"`
}

type PrintTableMap struct {
	Column     string     `json:"column"`     // 键名
	TagKey     string     `json:"tagKey"`     // 标签键
	Label      string     `json:"label"`      // 标签名
	Alias      string     `json:"alias"`      // 别名
	Key        string     `json:"key"`        // 键名
	IsChildren bool       `json:"isChildren"` // 是否为子元素
	Parent     string     `json:"parent"`     // 父键名
	Select     bool       `json:"select"`     // 是否可选
	Edit       bool       `json:"edit"`       // 是否可编辑
	FuncCode   ScriptCode `json:"funcCode"`   // 自定义函数代码
}

// 自定义条件数据结构 - 新版本
type CustomConditionData struct {
	RefDatas        []RefDataConfig     `json:"refDatas"`        // 引用数据源配置
	ConditionGroups MainConditionGroups `json:"conditionGroups"` // 主条件组结构
}

// 引用数据源配置
type RefDataConfig struct {
	Id              string              `json:"id"`              // 数据源唯一ID
	TableName       string              `json:"tableName"`       // 表名
	TableLabel      string              `json:"tableLabel"`      // 表标签（显示名）
	FilterCondition MainConditionGroups `json:"filterCondition"` // 该数据源的过滤条件
}

// 主条件组结构 - 保持不变
type MainConditionGroups struct {
	Logic      string           `json:"logic"`      // 逻辑关系：AND/OR
	Conditions []ConditionGroup `json:"conditions"` // 条件组列表
}

// 条件组 - 更新结构
type ConditionGroup struct {
	Id    string          `json:"id"`    // 条件组唯一ID
	Logic string          `json:"logic"` // 逻辑关系：AND/OR
	Rules []ConditionRule `json:"rules"` // 条件规则列表
}

// 条件规则 - 完全重构
type ConditionRule struct {
	Id                   string      `json:"id"`                   // 规则唯一ID
	Field                string      `json:"field"`                // 当前表字段名
	Operator             string      `json:"operator"`             // 操作符
	ValueType            string      `json:"valueType"`            // 值类型：fixed（固定值）/refData（引用数据）
	Value                interface{} `json:"value"`                // 固定值（当valueType=fixed时使用）
	RefDataSourceId      string      `json:"refDataSourceId"`      // 引用数据源ID（当valueType=refData时使用）
	RefDataSourceFieldId string      `json:"refDataSourceFieldId"` // 引用数据源字段ID（当valueType=refData时使用）
}

// 废弃的旧结构，保留用于向后兼容
type WithTableDataMap struct {
	Alias         string        `json:"alias"`         // 表别名
	TableName     string        `json:"tableName"`     // 关联表名
	TableLabel    string        `json:"tableLabel"`    // 表标签（显示名）
	JoinType      string        `json:"joinType"`      // 连接类型：INNER/LEFT/RIGHT
	JoinCondition JoinCondition `json:"joinCondition"` // 连接条件
}

// 连接条件 - 废弃，保留用于向后兼容
type JoinCondition struct {
	CurrentField  string `json:"currentField"`  // 当前表字段（可能包含表别名如：current.field_name或t123.field_name）
	RelationField string `json:"relationField"` // 关联表字段
}
