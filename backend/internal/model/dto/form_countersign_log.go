package dto

import (
	"time"
)

// FormCountersignLog 表单回签记录DTO
type FormCountersignLog struct {
	Id        int64                  `json:"id" summary:"回签记录ID"`
	FormId    int64                  `json:"formId" summary:"表单模板ID"`
	DataId    int64                  `json:"dataId" summary:"表单数据ID"`
	GlobalId  string                 `json:"globalId" summary:"全局唯一标识ID"`
	Data      map[string]interface{} `json:"data" summary:"回签数据"`
	Remark    string                 `json:"remark" summary:"备注信息"`
	CreatedBy int64                  `json:"createdBy" summary:"创建人ID"`
	Creator   string                 `json:"creator" summary:"创建人"`
	CreatedAt time.Time              `json:"createdAt" summary:"创建时间"`
	UpdatedBy int64                  `json:"updatedBy" summary:"更新人ID"`
	Updater   string                 `json:"updater" summary:"更新人"`
	UpdatedAt time.Time              `json:"updatedAt" summary:"更新时间"`
}
