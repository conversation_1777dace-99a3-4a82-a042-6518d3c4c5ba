package dto

import (
	"backend/internal/model/enum"

	"github.com/gogf/gf/v2/util/gconv"
)

type FormDataShort struct {
	TableName      string             `json:"table_name"`
	FormTemplateId int64              `json:"form_template_id"`
	FormId         int64              `json:"form_id"`
	ExtraData      *InstanceExtraData `json:"extra_data"`
	CreatedBy      int64              `json:"created_by"`
}

type FormFlowFinish struct {
	TableName  string `json:"table_name"`
	FormDataId int64  `json:"form_data_id"`
}

type FormInvalid struct {
	TableName  string `json:"table_name"`
	FormDataId int64  `json:"form_data_id"`
}

type FormStreamInfo struct {
	TableName    string `json:"table_name"`
	FormDataId   int64  `json:"form_data_id"`
	ApprovalType string `json:"approval_type"`
}

var FixedTableFields []*FormColumn = []*FormColumn{
	{
		Id:            "__id",
		Label:         "子表唯一ID",
		ComponentName: "Field.Input",
		ColumnType:    "string",
		IsFixed:       true,
	},
}
var FixedFields []*FormColumn = []*FormColumn{
	{
		Id:            "id",
		Label:         "ID",
		ComponentName: "Field.Input",
		ColumnType:    "int",
		IsFixed:       true,
	},
	{
		Id:            "created_by",
		Label:         "创建人",
		ComponentName: "Field.UserSelect",
		ColumnType:    "int",
		IsFixed:       true,
	},
	{
		Id:            "created_user",
		Label:         "创建人信息",
		ComponentName: "Field.UserSelect",
		ColumnType:    "int",
		IsFixed:       true,
	},
	{
		Id:            "created_at",
		Label:         "创建时间",
		ComponentName: "Field.DatePicker",
		ColumnType:    "datetime",
		Props: map[string]interface{}{
			"datetype": "datetime",
		},
		IsFixed: true,
	},
	{
		Id:            "updated_at",
		Label:         "最后更新时间",
		ComponentName: "Field.DatePicker",
		ColumnType:    "datetime",
		IsFixed:       true,
	},
	{
		Id:            "flow_status",
		Label:         "审批流状态",
		ComponentName: "Field.SingleSelect",
		ColumnType:    "int",
		IsFixed:       true,
		Props: map[string]interface{}{
			"options": []interface{}{
				map[string]interface{}{
					"label": "待审核",
					"value": gconv.String(enum.FlowStatus_Pending),
				},
				map[string]interface{}{
					"label": "审核中",
					"value": gconv.String(enum.FlowStatus_Running),
				},
				map[string]interface{}{
					"label": "已通过",
					"value": gconv.String(enum.FlowStatus_Finish),
				},
				map[string]interface{}{
					"label": "已拒绝",
					"value": gconv.String(enum.FlowStatus_Reject),
				},
				map[string]interface{}{
					"label": "已撤销",
					"value": gconv.String(enum.FlowStatus_Cancel),
				},
				map[string]interface{}{
					"label": "已作废",
					"value": gconv.String(enum.FlowStatus_Invalid),
				},
			},
		},
	},
	// {
	// 	Id:            "print_status",
	// 	Label:         "打印状态",
	// 	ComponentName: "Field.SingleSelect",
	// 	ColumnType:    "int",
	// 	IsFixed:       true,
	// 	Props: map[string]interface{}{
	// 		"options": []interface{}{
	// 			map[string]interface{}{
	// 				"label": "未打印",
	// 				"value": gconv.String(enum.PrintType_Unprinted),
	// 			},
	// 			map[string]interface{}{
	// 				"label": "已打印",
	// 				"value": gconv.String(enum.PrintType_Printed),
	// 			},
	// 		},
	// 	},
	// },
	// {
	// 	Id:            "signback_status",
	// 	Label:         "回签状态",
	// 	ComponentName: "Field.SingleSelect",
	// 	ColumnType:    "int",
	// 	IsFixed:       true,
	// 	Props: map[string]interface{}{
	// 		"options": []interface{}{
	// 			map[string]interface{}{
	// 				"label": "未回签",
	// 				"value": gconv.String(enum.SignBackType_Unsigned),
	// 			},
	// 			map[string]interface{}{
	// 				"label": "已回签",
	// 				"value": gconv.String(enum.SignBackType_Signed),
	// 			},
	// 		},
	// 	},
	// },
}

type LinkedData struct {
	// TableName  string `json:"table_name" description:"关联表名"`
	TemplateId int64  `json:"template_id" description:"关联表单模板ID"`
	FieldName  string `json:"field_name" description:"关联字段名"`
}
