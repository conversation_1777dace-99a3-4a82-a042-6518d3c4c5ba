package dto

import (
	"backend/internal/model/entity"
	"backend/internal/model/enum"
	"backend/internal/model/with"
	"time"
)

// 流程实例
type FlowInstance struct {
	*with.FlowInstance
	FlowTemplateSnapUn *FlowSchema          `json:"flow_template_snap_un"`  // 流程模板快照
	FormDataSnapUn     *InstanceFormDatSnap `json:"form_data_snap_un"`      // 表单数据快照
	FlowStepsUn        *FlowSchemaInstance  `json:"flow_steps_un"`          // 表单实例步骤快照
	FormExtraDataUn    *InstanceExtraData   `json:"extra_data_un"`          // 流程实例附加数据
	FinishdStepsUn     []string             `json:"finishd_steps_un"`       // 已完成步骤结构
	CurrentStepsUn     []*InstanceFlowNode  `json:"current_steps_un"      ` // 当前正在进行的步骤
}

// 表单数据快照 (表单原始结构相关的信息按道理应该做版本管理，表单数据和实例数据都应该根据版本来取结构，但是时间不够，暂时不处理)
type InstanceFormDatSnap struct {
	FormSchema  interface{}            `json:"form_schema"`  // 表单原始结构
	FormColumns []*FormColumn          `json:"form_columns"` // 表单计算的字段信息
	FormData    map[string]interface{} `json:"form_data"`    // 表单数据
}

type FlowSchemaInstance struct {
	Steps []*InstanceFlowNode `json:"steps"` // 节点实例
	Edges []*InstanceFlowEdge `json:"edges"` // 连接线实例
}

// 节点实例
type InstanceFlowNode struct {
	Id                  string                 `json:"id"`                    // id
	PreId               string                 `json:"pre_id"`                // 上一个节点id
	PreHistoryId        int64                  `json:"pre_history_id"`        // 上一个审核节点历史纪录id
	IsReturn            int                    `json:"is_return"`             // 是否是退回节点
	Name                string                 `json:"name"`                  // 名字
	Approver            []*InstanceFlowAuditor `json:"approver"`              // 关联的审批人
	Condition           enum.NodeCondition     `json:"condition"`             // 存在多个审批人时的审批方式
	AuditorIsNull       enum.NodeAuditorIsNull `json:"auditor_isnull"`        // 审批人为空时的处理方式
	AllowSelectApprover bool                   `json:"allow_select_approver"` // 是否可以自选审批人
	Shape               enum.NodeType          `json:"shape"`                 // 形状（节点类型）
	Status              enum.ApprovalStatus    `json:"status"`                // 状态
}

// 连接线实例
type InstanceFlowEdge struct {
	Source string `json:"source"` // 源节点
	Target string `json:"target"` // 目标节点
}
type SelectdOption struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

type InstanceFlowAuditor struct {
	UserId          int64               `json:"user_id"`          // 审批人ID
	Username        string              `json:"username"`         // 审批人名称
	ApprovalStatus  enum.NodeActionType `json:"approval_status"`  // 审批状态
	ArrivalTime     time.Time           `json:"arrival_time"`     // 到达时间
	CompletionTime  time.Time           `json:"completion_time"`  // 完成时间
	ApprovalComment string              `json:"approval_comment"` // 审批意见
	Attachment      interface{}         `json:"attachment"`       // 附件
	Signature       interface{}         `json:"signature"`        // 签名
	Pics            interface{}         `json:"pics"`             // 图片
	IsAutoPass      int                 `json:"is_auto_pass"`     // 是否是自动通过的
}

// 流程实例附加数据
type InstanceExtraData struct {
	SelectedDeptId    int64                        `json:"selected_dept_id"`   // 发起人选择的部门身份
	SelectedApprovers []*InstanceFlowNodeApprovers `json:"selected_approvers"` // 发起人选择的各节点审批人
	Attachment        *InstanceExtraDataAttachment `json:"attachment"`         // 附加数据中的申请理由、附件、图片等

	TriggerType     string `json:"trigger_type"`      // 触发类型
	TriggerSourceId string `json:"trigger_source_id"` // 触发来源ID
}

// 附加数据中的申请理由、附件、图片等
type InstanceExtraDataAttachment struct {
	Reason     string `json:"reason"`     // 申请理由
	Attachment string `json:"attachment"` // 附件
	Pics       string `json:"pics"`       // 图片
}

// 节点实例关联的审批人
type InstanceFlowNodeApprovers struct {
	Id       string          `json:"id"`       // id
	Approver []*SysUserBrief `json:"approver"` // 关联的审批人
}

type DeptDepth struct {
	Dept  *with.SysDept `json:"dept"`  // 部门
	Depth int           `json:"depth"` // 部门深度
}

type NextApproval struct {
	HistoryAuditor *entity.FlowInstanceHistoryAuditor `json:"history_auditor"` // 审批人历史纪录
	Instance       *FlowInstance                      `json:"instance"`        // 流程实例
}

// 流程实例的类型概要
type InstanceTypeSummary struct {
	Id               int64 `json:"id"                orm:"id"                  ` // id
	FlowInstanceType int   `json:"flowInstanceType"  orm:"flow_instance_type"  ` // 流程实例类型 	1创建申请 2作废申请
	InstanceState    int   `json:"instanceState"     orm:"instance_state"      ` // 实例状态;1 等待审批 2 审批中 3审批通过 4审批不通过 5 作废
}
