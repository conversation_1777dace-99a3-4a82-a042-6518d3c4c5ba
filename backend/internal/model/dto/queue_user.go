package dto

import "github.com/gogf/gf/v2/os/gtime"

type QueueUserLoginLog struct {
	Success   bool        `json:"success"`    // 是否成功
	Message   string      `json:"message"`    // 错误信息
	IP        string      `json:"ip"`         // 登录IP
	UserAgent string      `json:"user_agent"` //浏览器信息
	UserId    int64       `json:"user_id"`    // 用户ID
	Username  string      `json:"username"`   // 用户名
	Password  string      `json:"password"`   // 密码
	LoginTime *gtime.Time `json:"login_time"` // 登录时间
}
