package dto

type SysPermissions struct {
	Type            string            `json:"type"`             // 类型
	Name            string            `json:"name"`             // 名称
	MenuId          int64             `json:"menu_id"`          // 菜单id
	FormId          int64             `json:"form_id"`          // 关联的表单id，如果是表单类型权限有此值
	Mark            string            `json:"mark"`             // 权限标识
	Permission      map[string]bool   `json:"permission"`       // 功能权限
	FieldPermission []FieldPermission `json:"field_permission"` // 字段权限
}

// 字段权限
type FieldPermission struct {
	Field         string            `json:"field"`          // 字段名
	HasPermission bool              `json:"has_permission"` // 是否有权限
	Children      []FieldPermission `json:"children"`       // 子字段信息
}
