package dto

import (
	"backend/automation/types"
	"backend/internal/model/enum"
)

type AMFlowSchemaNode struct {
	Id    string        `json:"id"`    // 唯一标识
	Data  *types.Meta   `json:"data"`  // 额外数据
	Shape enum.NodeType `json:"shape"` // 形状
	//....
}
type AMFLowSchemaEdgeNode struct {
	Cell string `json:"cell"` // 节点ID
	Port string `json:"port"` // 链接的点位
}
type AMFlowSchemaEdge struct {
	Source *AMFLowSchemaEdgeNode `json:"source"` // 源节点
	Target *AMFLowSchemaEdgeNode `json:"target"` // 目标节点
}

// 自动化流程模板设计结构
type AMFlowSchema struct {
	Edges []*AMFlowSchemaEdge `json:"edges"` // 连接线
	Nodes []*AMFlowSchemaNode `json:"nodes"` // 节点
}

type TriggerInput struct {
	AmConfigId int64   `json:"am_config_id"` // 自动化配置id
	Version    int64   `json:"version"`      // 版本
	Depend     []int64 `json:"depend"`       // 依赖的自动化配置id
}

type AMDepend struct {
	Id   int    `json:"id"          orm:"id"           ` // id
	Name string `json:"name"        orm:"name"         ` // 自动化名称
}
