package dto

import "github.com/gogf/gf/v2/os/gtime"

type WithTable struct {
	Type           int    `json:"type"`
	Label          string `json:"label"`
	Value          string `json:"value"`
	TableName      string `json:"table_name"`
	FormTemplateID int    `json:"form_template_id"`
}

type WithConditionValue struct {
	Parent    string `json:"parent"`
	FieldKey  string `json:"field_key"`
	GroupKey  string `json:"group_key"`
	FieldName string `json:"field_name"`
}

type WithConditionColumn struct {
	Label      string `json:"label"`
	Value      string `json:"value"`
	ColumnName string `json:"column_name"`
	ColumnType string `json:"column_type"`
}

type WithCondition struct {
	Value  *WithConditionValue  `json:"value"`
	Column *WithConditionColumn `json:"column"`
}

type WithTableData struct {
	ID            string           `json:"id"`
	Mount         string           `json:"mount"`
	WithTable     *WithTable       `json:"withTable"`
	WithCondition []*WithCondition `json:"withCondition"`
}

type PrintDetails struct {
	// 打印人 打印时间 打印次数
	PrintUser       int64       `json:"print_user"`
	PrintTime       *gtime.Time `json:"print_time"`
	PrintTemplateId int64       `json:"print_template_id"`
}
