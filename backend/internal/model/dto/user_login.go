package dto

import (
	"github.com/dgrijalva/jwt-go"
	"github.com/gogf/gf/v2/os/gtime"
)

/*
* 目前jwttoken的设计和存在的问题
* jwttoken的过期时间为短效（5分钟），过期后，需要用refreshToken刷新，在刷新时可以验证用户是被禁用
* 优势：目前token完全无状态，只需要验证token是否有效，无需多次查询数据库或缓存中间件获得current登录用户信息
* 问题：因为token完全无状态，也会引发以下问题：
* 	1、即使前端注销时清空localStorage，token依然处于游离状态，后端无法完全注销掉用户的token
* 	2、不能实现只能允许单一设备登录的功能
* 	3、无法让某个用户的token立即失效，需要等待5分钟jwttoken过期刷新的时候，才能禁止用户
* 解决方案（暂时没有实现）：
* 	和token信息上增加一个版本标记，在缓存中间件中保存当前生效的token版本。用户注销、禁止用户等操作时，修改用户的token版本，然后在验证token的时候可以对比版本号
*   这种方案某种程度上丧失了jwt的无状态特性，具体看系统业务场景和实际需求做取舍吧
 */

type UserClaims struct {
	jwt.StandardClaims
	Code              string   `json:"code"`
	Id                int64    `json:"id"     `       // id
	Username          string   `json:"username"  `    // 用户名
	TenantId          int64    `json:"tenantId" `     // 数据租户id
	DeniedPermissions []string `json:"permissions"`   // 禁止访问的接口列表
	DeniedForms       []string `json:"forms"`         // 禁止访问的表单列表
	WithDepts         []int64  `json:"withDepts"`     // 所在部门
	WithPosts         []int64  `json:"withPosts"`     // 所属岗位
	WithRoles         []int64  `json:"withRoles"`     // 拥有的角色
	WithRolesName     []string `json:"withRolesName"` // 拥有的角色名称
}

type RefreshTokenClaims struct {
	jwt.StandardClaims
	Id int64 `json:"id"`
}

type CurrentUser struct {
	Id                int64             `json:"id"`
	LastLoginIp       interface{}       `json:"lastLoginIp"`
	LastLoginTime     *gtime.Time       `json:"lastLoginTime"`
	LastUpdatePwdTime *gtime.Time       `json:"lastUpdatePwdTime"` // 最后一次修改密码的时间
	PasswordChanged   interface{}       `json:"passwordChanged"`   // 密码是否已修改
	Access            string            `json:"access"`
	Avatar            string            `json:"avatar"`
	Name              string            `json:"name"`
	ApiAssociation    []*AssMenu        `json:"apiAssociation"`
	WithDepts         interface{}       `json:"withDepts"`
	WithPosts         interface{}       `json:"withPosts"`
	WithRoles         interface{}       `json:"withRoles"`
	WithCompanys      interface{}       `json:"withCompanys"`
	WithProjects      interface{}       `json:"withProjects"`
	SysPermissions    []*SysPermissions `json:"sysPermissions"`
}

type AssMenu struct {
	Id                   int64       `json:"id"             orm:"id"              `               // id
	Code                 string      `json:"code"           orm:"code"            `               // 编号
	ParentId             int64       `json:"parentId"       orm:"parent_id"       `               // 父菜单id
	Route                string      `json:"route"          orm:"route"           `               // 路由地址
	Mark                 string      `json:"mark"           orm:"mark"            `               // 菜单标记
	Title                string      `json:"title"          orm:"title"           `               // 菜单标题
	Icon                 string      `json:"icon"           orm:"icon"            `               // 图标
	MenuType             int         `json:"menuType"       orm:"menu_type"       `               // 菜单类型
	IsDisplay            int         `json:"isDisplay"      orm:"is_display"      `               // 是否显示
	ApiAssociation       string      `json:"apiAssociation" orm:"api_association" `               // 关联的API
	MenuFormTemplateId   interface{} `json:"menuFormTemplateId"   orm:"menu_form_template_id"   ` // 绑定的表单模板id
	MenuFormTemplateType interface{} `json:"menuFormTemplateType" orm:"menu_form_template_type" ` // 绑定的表单模板页面类型
	RouteType            int         `json:"routeType"            orm:"route_type"  `
	DashboardId          int         `json:"dashboardId"          orm:"dashboard_id"  `
	DashboardIdMobile    int         `json:"dashboardIdMobile"    orm:"dashboard_id_mobile"  `
}
