package enum

type ColumnType string

const (
	ColumnType_Varchar   ColumnType = "varchar"
	ColumnType_Char      ColumnType = "char"
	ColumnType_Int       ColumnType = "int"
	ColumnType_BIGINT    ColumnType = "bigint"
	ColumnType_Float     ColumnType = "float"
	ColumnType_Double    ColumnType = "double"
	ColumnType_Decimal   ColumnType = "decimal"
	ColumnType_Text      ColumnType = "text"
	ColumnType_Date      ColumnType = "date"
	ColumnType_Time      ColumnType = "time"
	ColumnType_DateTime  ColumnType = "datetime"
	ColumnType_Timestamp ColumnType = "timestamp"
	ColumnType_Json      ColumnType = "json"
	ColumnType_Tinyint   ColumnType = "tinyint"
)
