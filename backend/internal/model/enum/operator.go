package enum

type Operator string

const (
	Operator_Equal              Operator = "=="
	Operator_NotEqual           Operator = "!="
	Operator_GreaterThan        Operator = ">"
	Operator_GreaterThanOrEqual Operator = ">="
	Operator_LessThan           Operator = "<"
	Operator_LessThanOrEqual    Operator = "<="
	Operator_In                 Operator = "in"
	Operator_ContainedIn        Operator = "contain"
	Operator_CrossContained     Operator = "cross_contain"
)
