package enum

import "fmt"

type SysPermissionType string

const (
	SysPermissionType_Menu SysPermissionType = "menu"
	SysPermissionType_Form SysPermissionType = "form"
)

type SysPermission string

const (
	SysPermission_Menu            SysPermission = "menu"
	SysPermission_View            SysPermission = "view"
	SysPermission_UnlimitedSearch SysPermission = "unlimitedSearch"
	SysPermission_New             SysPermission = "new"
	SysPermission_Change          SysPermission = "change"
	SysPermission_Print           SysPermission = "print"
	SysPermission_Export          SysPermission = "export"
	SysPermission_Import          SysPermission = "import"
)

func (s SysPermission) String() string {
	return string(s)
}

func (s SysPermission) Error(title ...string) string {
	permissionName := ""
	switch s {
	case SysPermission_View:
		permissionName = "查看"
	case SysPermission_New:
		permissionName = "新增"
	case SysPermission_Change:
		permissionName = "变更"
	case SysPermission_Print:
		permissionName = "打印"
	case SysPermission_Export:
		permissionName = "导出"
	case SysPermission_Import:
		permissionName = "导入"
	case SysPermission_UnlimitedSearch:
		permissionName = "无限制检索"
	default:
		return "权限不足"
	}
	if len(title) > 0 {
		return fmt.Sprintf("抱歉，您没有%s的%s权限", title[0], permissionName)
	}
	return fmt.Sprintf("抱歉，您没有%s的权限", permissionName)
}

type SysPermissionMark string

const (
	SysPermissionMark_SystemSettle_Menu            SysPermissionMark = "SystemSettle-Menu"
	SysPermissionMark_SystemSettle_Role            SysPermissionMark = "SystemSettle-Role"
	SysPermissionMark_SystemSettle_Form            SysPermissionMark = "SystemSettle-Form"
	SysPermissionMark_SystemSettle_Automation      SysPermissionMark = "SystemSettle-Automation"
	SysPermissionMark_SystemSettle_Form_Permission SysPermissionMark = "SystemSettle-Form-Permission"
	SysPermissionMark_SystemSettle_Post            SysPermissionMark = "SystemSettle-Post"
	SysPermissionMark_SystemSettle_Dept_User       SysPermissionMark = "SystemSettle-Dept-User"
	SysPermissionMark_SystemBase_Project_Role      SysPermissionMark = "SystemBase-Project-Role"
	SysPermissionMark_SystemBase_Project           SysPermissionMark = "SystemBase-Project"
	SysPermissionMark_Flow_Pending                 SysPermissionMark = "Flow-Pending"
	SysPermissionMark_Flow_Initiated               SysPermissionMark = "Flow-Initiated"
	SysPermissionMark_Flow_Completed               SysPermissionMark = "Flow-Completed"
	SysPermissionMark_Flow_Cc                      SysPermissionMark = "Flow-Cc"
)

func (s SysPermissionMark) String() string {
	return string(s)
}

func (s SysPermissionMark) Title() string {
	switch s {
	case SysPermissionMark_SystemSettle_Menu:
		return "菜单"
	case SysPermissionMark_SystemSettle_Role:
		return "角色权限"
	case SysPermissionMark_SystemSettle_Form:
		return "表单"
	case SysPermissionMark_SystemSettle_Automation:
		return "自动化"
	case SysPermissionMark_SystemSettle_Form_Permission:
		return "表单权限"
	case SysPermissionMark_SystemSettle_Post:
		return "岗位"
	case SysPermissionMark_SystemSettle_Dept_User:
		return "部门用户"
	case SysPermissionMark_SystemBase_Project_Role:
		return "项目角色"
	case SysPermissionMark_SystemBase_Project:
		return "项目"
	case SysPermissionMark_Flow_Pending:
		return "待办"
	case SysPermissionMark_Flow_Initiated:
		return "发起"
	case SysPermissionMark_Flow_Completed:
		return "已办"
	case SysPermissionMark_Flow_Cc:
		return "抄送"
	default:
		return s.String()
	}
}
