package enum

type ImportStatus int

const (
	ImportStatus_Pending       ImportStatus = iota + 1 // 待处理
	ImportStatus_InProgress                            // 进行中
	ImportStatus_Completed                             // 已成功
	ImportStatus_Failed                                // 失败
	ImportStatus_PartialFailed                         // 部分失败
)

type DuplicateHandlingMethod int

const (
	DuplicateHandlingMethod_CancelSingle       DuplicateHandlingMethod = iota + 1 // 取消此条数据导入
	DuplicateHandlingMethod_CancelAll                                             // 整体取消导入
	DuplicateHandlingMethod_DeleteOriginal                                        // 删除原数据
	DuplicateHandlingMethod_MergeExcelPriority                                    // 合并（Excel优先）
	DuplicateHandlingMethod_MergeDbPriority                                       // 合并（Db优先）
)

type ExportStatus int

const (
	ExportStatus_Pending    ExportStatus = iota + 1 // 待处理
	ExportStatus_InProgress                         // 进行中
	ExportStatus_Completed                          // 已成功
	ExportStatus_Failed                             // 失败
)
