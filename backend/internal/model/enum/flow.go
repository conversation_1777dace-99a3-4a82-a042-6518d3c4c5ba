package enum

// 流程实例状态
type FlowStatus int

const (
	FlowStatus_Pending      FlowStatus = 1 // 待审核
	FlowStatus_Running      FlowStatus = 2 // 审核中
	FlowStatus_Finish       FlowStatus = 3 // 已通过
	FlowStatus_Reject       FlowStatus = 4 // 已拒绝
	FlowStatus_Cancel       FlowStatus = 5 // 已撤销
	FlowStatus_ApplyInvalid FlowStatus = 6 // 申请作废中
	FlowStatus_Invalid      FlowStatus = 7 // 已作废
)

// 节点实例审核状态
type ApprovalStatus int

const (
	ApprovalStatus_Pending ApprovalStatus = 1 // 待审核
	ApprovalStatus_Running ApprovalStatus = 2 // 审核中
	ApprovalStatus_Pass    ApprovalStatus = 3 // 通过
	ApprovalStatus_Reject  ApprovalStatus = 4 // 拒绝
	ApprovalStatus_Cancel  ApprovalStatus = 5 // 撤销
	ApprovalStatus_Return  ApprovalStatus = 6 // 退回
)

type SequentialDeptLeaderType int

const (
	SequentialDeptLeaderType_Top   SequentialDeptLeaderType = 1 // 直到最上层
	SequentialDeptLeaderType_Level SequentialDeptLeaderType = 2 // 只到xx层
)

type DeptLeaderRule int

const (
	DeptLeaderRule_Up   DeptLeaderRule = 1 // 无主管时,向上查找
	DeptLeaderRule_Null DeptLeaderRule = 2 // 无主管时,置为空
)

// 审核人审批操作类型
type NodeActionType string

const (
	NodeActionType_Pending  NodeActionType = "pending"  // 待处理
	NodeActionType_Agree    NodeActionType = "agree"    // 同意
	NodeActionType_Reject   NodeActionType = "reject"   // 不同意
	NodeActionType_Return   NodeActionType = "return"   // 退回
	NodeActionType_Cancel   NodeActionType = "cancel"   // 撤销
	NodeActionType_Transfer NodeActionType = "transfer" // 转交
	NodeActionType_Skipped  NodeActionType = "skipped"  // 已跳过
)

type NodeType string

const (
	NodeType_StartNode NodeType = "StartNode" // 开始节点
	NodeType_AudioNode NodeType = "AudioNode" // 审核节点
	NodeType_CcNode    NodeType = "CcNode"    // 抄送节点
	NodeType_MeetNode  NodeType = "MeetNode"  // 汇合点
	NodeType_EndNode   NodeType = "EndNode"   // 结束节点
)

// 存在多个审批人时审批方式
type NodeCondition string

const (
	NodeCondition_Parallel NodeCondition = "1" // 1. 并行会签，可同时处理（需所有人同意）
	NodeCondition_Sequence NodeCondition = "2" // 2. 顺序会签，按匹配顺序依次审批（需所有人同意）
	NodeCondition_Or       NodeCondition = "3" // 3. 或签（只要有一个人同意即可）
)

// 审核人类型
type AuditorType string

const (
	AuditorType_SpecificPerson        AuditorType = "1" // 指定人员
	AuditorType_DeptHead              AuditorType = "2" // 部门负责人
	AuditorType_HierarchicalDeptHeads AuditorType = "3" // 逐级部门负责人
	AuditorType_SpecifiedDeptHead     AuditorType = "4" // 指定部门负责人
	AuditorType_SpecifiedPosition     AuditorType = "5" // 指定岗位
	AuditorType_ProjectLeader         AuditorType = "6" // 项目负责人
	AuditorType_ProjectRole           AuditorType = "8" // 项目角色
	AuditorType_ByApplicantChoice     AuditorType = "7" // 申请人自选
	AuditorType_FieldUsers            AuditorType = "9" // 表单字段中关联的人
)

// 节点审批人匹配为空时的处理方式
type NodeAuditorIsNull string

const (
	NodeAuditorIsNull_AutoPass   NodeAuditorIsNull = "auto_pass"   // 直接通过
	NodeAuditorIsNull_AutoReject NodeAuditorIsNull = "auto_reject" // 直接拒绝
)

type ParamValueType string

const (
	ParamValueType_Default ParamValueType = "default" // 默认类型 （一般是弹出参数选择框）
	ParamValueType_User    ParamValueType = "user"    // 用户类型
	ParamValueType_Dept    ParamValueType = "dept"    // 部门类型
	ParamValueType_Post    ParamValueType = "post"    // 岗位类型
	ParamValueType_Project ParamValueType = "project" // 项目角色类型
)

type FlowInstanceType int

const (
	FlowInstanceType_Create  FlowInstanceType = 1 // 创建申请
	FlowInstanceType_Invalid FlowInstanceType = 2 // 作废申请
)
