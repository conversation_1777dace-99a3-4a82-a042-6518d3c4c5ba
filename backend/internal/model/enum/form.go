package enum

type FormTemplateType int

const (
	FormTemplateType_Default FormTemplateType = iota // 默认模板
	FormTemplateType_Expand  FormTemplateType = 999  // 其他表的拓展字段模板
)

type PrintType int

const (
	PrintType_Unprinted PrintType = 1 // 未打印
	PrintType_Printed   PrintType = 2 // 已打印
)

type SignBackType int

const (
	SignBackType_Unsigned SignBackType = 1 // 未回签
	SignBackType_Signed   SignBackType = 2 // 已回签
)
