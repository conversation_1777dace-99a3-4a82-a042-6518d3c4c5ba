// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AmConfig is the golang structure for table am_config.
type AmConfig struct {
	Id          int         `json:"id"          orm:"id"           ` // id
	Name        string      `json:"name"        orm:"name"         ` // 自动化名称
	Description string      `json:"description" orm:"description"  ` // 自动化说明
	FormId      int64       `json:"formId"      orm:"form_id"      ` // 关联的表单id
	TriggerData string      `json:"triggerData" orm:"trigger_data" ` // 触发器数据
	FlowSchema  string      `json:"flowSchema"  orm:"flow_schema"  ` // 自动化流程设计整体结构
	Status      int         `json:"status"      orm:"status"       ` // 状态;1 正常 999 停止
	CreatedBy   string      `json:"createdBy"   orm:"created_by"   ` // 创建人
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   ` // 更新时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"   ` // 删除时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"    ` // 数据租户id
	Version     int64       `json:"version"     orm:"version"      ` // 当前版本
	Depend      string      `json:"depend"      orm:"depend"       ` // 依赖的其他流程id（定义执行顺序）
}
