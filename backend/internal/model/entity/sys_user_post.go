// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysUserPost is the golang structure for table sys_user_post.
type SysUserPost struct {
	Id     int64 `json:"id"     orm:"id"      ` // 关联ID
	UserId int64 `json:"userId" orm:"user_id" ` // 用户ID
	PostId int64 `json:"postId" orm:"post_id" ` // 岗位ID
}
