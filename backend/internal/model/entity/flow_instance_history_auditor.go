// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowInstanceHistoryAuditor is the golang structure for table flow_instance_history_auditor.
type FlowInstanceHistoryAuditor struct {
	Id                               int64       `json:"id"                               orm:"id"                                   ` // id
	FlowInstanceHistoryId            int64       `json:"flowInstanceHistoryId"            orm:"flow_instance_history_id"             ` // 审核历史id
	FlowInstanceId                   int64       `json:"flowInstanceId"                   orm:"flow_instance_id"                     ` // 流程实例id
	NodeType                         string      `json:"nodeType"                         orm:"node_type"                            ` // 节点类型（用于区分发起人、审批人、抄送人）
	UserId                           int64       `json:"userId"                           orm:"user_id"                              ` // 审核人id
	ApprovalStatus                   string      `json:"approvalStatus"                   orm:"approval_status"                      ` // 审批状态
	IsAutoPass                       int         `json:"isAutoPass"                       orm:"is_auto_pass"                         ` // 是否是自动通过
	FlowInstanceHistoryStatus        int         `json:"flowInstanceHistoryStatus"        orm:"flow_instance_history_status"         ` // 节点实例状态
	FlowInstanceHistoryStatusVersion *gtime.Time `json:"flowInstanceHistoryStatusVersion" orm:"flow_instance_history_status_version" ` // 节点实例状态同步时间
	FlowInstanceStatus               int         `json:"flowInstanceStatus"               orm:"flow_instance_status"                 ` // 流程实例状态
	FlowInstanceStatusVersion        *gtime.Time `json:"flowInstanceStatusVersion"        orm:"flow_instance_status_version"         ` // 流程实例状态同步时间
	ReturnNodeId                     string      `json:"returnNodeId"                     orm:"return_node_id"                       ` // 退回节点id
	ArrivalTime                      *gtime.Time `json:"arrivalTime"                      orm:"arrival_time"                         ` // 到达时间
	CompletionTime                   *gtime.Time `json:"completionTime"                   orm:"completion_time"                      ` // 完成时间
	ApprovalComment                  string      `json:"approvalComment"                  orm:"approval_comment"                     ` // 审批意见
	Attachment                       string      `json:"attachment"                       orm:"attachment"                           ` // 附件
	Signature                        string      `json:"signature"                        orm:"signature"                            ` // 签名
	CreatedAt                        *gtime.Time `json:"createdAt"                        orm:"created_at"                           ` // 创建时间
	UpdatedAt                        *gtime.Time `json:"updatedAt"                        orm:"updated_at"                           ` // 更新时间
	DeletedAt                        *gtime.Time `json:"deletedAt"                        orm:"deleted_at"                           ` // 删除时间
	TenantId                         int64       `json:"tenantId"                         orm:"tenant_id"                            ` // 租户id
	Pics                             string      `json:"pics"                             orm:"pics"                                 ` // 图片
}
