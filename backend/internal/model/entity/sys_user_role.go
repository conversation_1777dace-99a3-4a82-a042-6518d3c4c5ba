// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysUserRole is the golang structure for table sys_user_role.
type SysUserRole struct {
	Id     int64 `json:"id"     orm:"id"      ` // 关联ID
	UserId int64 `json:"userId" orm:"user_id" ` // 用户ID
	RoleId int64 `json:"roleId" orm:"role_id" ` // 角色ID
}
