// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysConfig is the golang structure for table sys_config.
type SysConfig struct {
	Id          int    `json:"id"          orm:"id"           ` // ID
	ConfigKey   string `json:"configKey"   orm:"config_key"   ` // 配置KEY
	ConfigValue string `json:"configValue" orm:"config_value" ` // 配置内容
	Version     int64  `json:"version"     orm:"version"      ` // 版本
	TenantId    int64  `json:"tenantId"    orm:"tenant_id"    ` // 数据租户id
}
