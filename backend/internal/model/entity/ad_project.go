// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProject is the golang structure for table ad_project.
type AdProject struct {
	Id          int         `json:"id"          orm:"id"          ` // id
	Code        string      `json:"code"        orm:"code"        ` // 编号
	Name        string      `json:"name"        orm:"name"        ` // 项目名称
	Leader      int64       `json:"leader"      orm:"leader"      ` // 负责人
	Instruction string      `json:"instruction" orm:"instruction" ` // 项目说明
	SortOrder   int64       `json:"sortOrder"   orm:"sort_order"  ` // 排序字段
	CreatedBy   int64       `json:"createdBy"   orm:"created_by"  ` // 创建人
	UpdatedBy   int64       `json:"updatedBy"   orm:"updated_by"  ` // 修改人
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  ` // 修改时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  ` // 删除时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"   ` // 数据租户id
	Source      string      `json:"source"      orm:"source"      ` // 来源
	Version     int64       `json:"version"     orm:"version"     ` // 当前版本
}
