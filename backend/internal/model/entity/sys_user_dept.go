// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysUserDept is the golang structure for table sys_user_dept.
type SysUserDept struct {
	Id     int64 `json:"id"     orm:"id"      ` // 关联ID
	UserId int64 `json:"userId" orm:"user_id" ` // 用户ID
	DeptId int64 `json:"deptId" orm:"dept_id" ` // 部门ID
}
