// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ProcessTemplate is the golang structure for table process_template.
type ProcessTemplate struct {
	Id          int64       `json:"id"          orm:"id"          ` // id
	Name        string      `json:"name"        orm:"name"        ` // 模版名
	Description string      `json:"description" orm:"description" ` // 流程说明
	Settleinfo  string      `json:"settleinfo"  orm:"settleinfo"  ` // 流程设置（JSON）
	Nodes       string      `json:"nodes"       orm:"nodes"       ` // 流程节点设置（JSON）
	CreatedBy   string      `json:"createdBy"   orm:"created_by"  ` // 创建人
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  ` // 更新时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  ` // 删除时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"   ` // 数据租户id
}
