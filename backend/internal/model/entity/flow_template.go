// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowTemplate is the golang structure for table flow_template.
type FlowTemplate struct {
	Id          int64       `json:"id"          orm:"id"          ` // id
	Name        string      `json:"name"        orm:"name"        ` // 模版名
	Description string      `json:"description" orm:"description" ` // 流程说明
	FlowSchema  string      `json:"flowSchema"  orm:"flow_schema" ` // 流程设计整体结构
	FlowStatus  string      `json:"flowStatus"  orm:"flow_status" ` // 状态;1 正常 999 停止
	CreatedBy   string      `json:"createdBy"   orm:"created_by"  ` // 创建人
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  ` // 更新时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  ` // 删除时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"   ` // 数据租户id
}
