// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysPostRole is the golang structure for table sys_post_role.
type SysPostRole struct {
	Id     int64 `json:"id"     orm:"id"      ` // 关联ID
	PostId int64 `json:"postId" orm:"post_id" ` // 岗位ID
	RoleId int64 `json:"roleId" orm:"role_id" ` // 角色ID
}
