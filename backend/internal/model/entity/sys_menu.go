// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysMenu is the golang structure for table sys_menu.
type SysMenu struct {
	Id                   int64       `json:"id"                   orm:"id"                      ` // id
	Code                 string      `json:"code"                 orm:"code"                    ` // 编号
	ParentId             int64       `json:"parentId"             orm:"parent_id"               ` // 父菜单id
	Route                string      `json:"route"                orm:"route"                   ` // 路由地址
	Mark                 string      `json:"mark"                 orm:"mark"                    ` // 菜单标记
	Title                string      `json:"title"                orm:"title"                   ` // 菜单标题
	Icon                 string      `json:"icon"                 orm:"icon"                    ` // 图标
	Remark               string      `json:"remark"               orm:"remark"                  ` // 备注
	MenuType             int         `json:"menuType"             orm:"menu_type"               ` // 菜单类型
	IsDisplay            int         `json:"isDisplay"            orm:"is_display"              ` // 是否显示
	ApiAssociation       string      `json:"apiAssociation"       orm:"api_association"         ` // 关联的API
	CreatedBy            int64       `json:"createdBy"            orm:"created_by"              ` // 创建人
	CreatedAt            *gtime.Time `json:"createdAt"            orm:"created_at"              ` // 创建时间
	UpdatedAt            *gtime.Time `json:"updatedAt"            orm:"updated_at"              ` // 更新时间
	DeletedAt            *gtime.Time `json:"deletedAt"            orm:"deleted_at"              ` // 删除时间
	TenantId             int64       `json:"tenantId"             orm:"tenant_id"               ` // 数据租户id
	MenuFormTemplateId   int64       `json:"menuFormTemplateId"   orm:"menu_form_template_id"   ` // 绑定的表单模板id
	MenuFormTemplateType int         `json:"menuFormTemplateType" orm:"menu_form_template_type" ` // 绑定的表单模板页面类型
	RouteType            int         `json:"routeType"            orm:"route_type"              ` // 路由类型
	SortOrder            int64       `json:"sortOrder"            orm:"sort_order"              ` // 排序字段
	DashboardId          int         `json:"dashboardId"          orm:"dashboard_id"            ` // 仪表板id
	DashboardIdMobile    int         `json:"dashboardIdMobile"    orm:"dashboard_id_mobile"     ` // 仪表板id-移动端
}
