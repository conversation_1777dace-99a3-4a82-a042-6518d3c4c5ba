// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AmConfigHistory is the golang structure for table am_config_history.
type AmConfigHistory struct {
	Id            int64       `json:"id"            orm:"id"             ` // id
	AmConfigId    int64       `json:"amConfigId"    orm:"am_config_id"   ` // 关联的自动化配置id
	Status        int         `json:"status"        orm:"status"         ` // 状态
	ResultCode    int         `json:"resultCode"    orm:"result_code"    ` // 执行结果;1 成功 999 失败
	ResultMsg     string      `json:"resultMsg"     orm:"result_msg"     ` // 执行结果MSG
	DataSnap      string      `json:"dataSnap"      orm:"data_snap"      ` // 数据源快照
	ResultSnap    string      `json:"resultSnap"    orm:"result_snap"    ` // 结果快照
	NodeLog       string      `json:"nodeLog"       orm:"node_log"       ` // 节点详细日志;每个节点的执行信息、堆栈信息
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     ` // 创建时间
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     ` // 更新时间
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     ` // 删除时间
	TenantId      int64       `json:"tenantId"      orm:"tenant_id"      ` // 数据租户id
	WorkflowState string      `json:"workflowState" orm:"workflow_state" ` // 执行状态数据（用于恢复执行）
	SequenceId    string      `json:"sequenceId"    orm:"sequence_id"    ` // 触发的顺序id（防止消息队列重放）
}
