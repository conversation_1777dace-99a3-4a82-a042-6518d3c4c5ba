// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProjectRole is the golang structure for table ad_project_role.
type AdProjectRole struct {
	Id          int         `json:"id"          orm:"id"          ` // id
	Code        string      `json:"code"        orm:"code"        ` // 编号
	Name        string      `json:"name"        orm:"name"        ` // 角色名
	IsDefault   int         `json:"isDefault"   orm:"is_default"  ` // 是否是默认角色
	Instruction string      `json:"instruction" orm:"instruction" ` // 角色说明
	CreatedBy   int64       `json:"createdBy"   orm:"created_by"  ` // 创建人
	UpdatedBy   int64       `json:"updatedBy"   orm:"updated_by"  ` // 修改人
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  ` // 修改时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  ` // 删除时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"   ` // 数据租户id
}
