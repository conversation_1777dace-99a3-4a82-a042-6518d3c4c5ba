// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// CodeHistory is the golang structure for table code_history.
type CodeHistory struct {
	Id          int64       `json:"id"          orm:"id"          ` // id
	Title       string      `json:"title"       orm:"title"       ` // 代码库标题
	Instruction string      `json:"instruction" orm:"instruction" ` // 代码库说明
	Content     string      `json:"content"     orm:"content"     ` // 代码内容
	CreatedBy   int64       `json:"createdBy"   orm:"created_by"  ` // 创建人
	UpdatedBy   int64       `json:"updatedBy"   orm:"updated_by"  ` // 修改人
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  ` // 修改时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  ` // 删除时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"   ` // 数据租户id
}
