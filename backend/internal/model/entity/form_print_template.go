// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FormPrintTemplate is the golang structure for table form_print_template.
type FormPrintTemplate struct {
	Id              int         `json:"id"              orm:"id"                ` // id
	Name            string      `json:"name"            orm:"name"              ` // 模板名
	Description     string      `json:"description"     orm:"description"       ` // 模板说明
	FormTemplateId  int64       `json:"formTemplateId"  orm:"form_template_id"  ` // 关联的模板id
	EnterWithData   string      `json:"enterWithData"   orm:"enter_with_data"   ` // 关联表字段设置
	TemplateContent string      `json:"templateContent" orm:"template_content"  ` // 模版内容
	PrintSize       string      `json:"printSize"       orm:"print_size"        ` // 纸张方向
	PrintRotation   string      `json:"printRotation"   orm:"print_rotation"    ` // 尺寸大小
	Status          int         `json:"status"          orm:"status"            ` // 状态;1 正常 999 停止
	CreatedBy       string      `json:"createdBy"       orm:"created_by"        ` // 创建人
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"        ` // 创建时间
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"        ` // 更新时间
	DeletedAt       *gtime.Time `json:"deletedAt"       orm:"deleted_at"        ` // 删除时间
	TenantId        int64       `json:"tenantId"        orm:"tenant_id"         ` // 数据租户id
	Padding         string      `json:"padding"         orm:"padding"           ` // 内边距
	Width           int         `json:"width"           orm:"width"             ` // 宽度
	Height          int         `json:"height"          orm:"height"            ` // 高度
	AllowEditBefore int         `json:"allowEditBefore" orm:"allow_edit_before" ` // 是否允许打印前编辑
	SqlQueries      string      `json:"sqlQueries"      orm:"sql_queries"       ` // sql数据源配置
}
