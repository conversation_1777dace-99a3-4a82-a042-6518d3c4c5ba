// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysUser is the golang structure for table sys_user.
type SysUser struct {
	Id                int64       `json:"id"                orm:"id"                   ` // id
	Code              string      `json:"code"              orm:"code"                 ` // 员工编号
	SystemAccount     string      `json:"systemAccount"     orm:"system_account"       ` // 系统账号
	Username          string      `json:"username"          orm:"username"             ` // 用户名
	Email             string      `json:"email"             orm:"email"                ` // 邮箱
	ContactPhone      string      `json:"contactPhone"      orm:"contact_phone"        ` // 联系电话
	IdNumber          string      `json:"idNumber"          orm:"id_number"            ` // 身份证号码
	HireDate          *gtime.Time `json:"hireDate"          orm:"hire_date"            ` // 入职日期
	ResignationDate   *gtime.Time `json:"resignationDate"   orm:"resignation_date"     ` // 离职日期
	Status            int         `json:"status"            orm:"status"               ` // 状态
	WithDepts         string      `json:"withDepts"         orm:"with_depts"           ` // 所在部门
	WithPosts         string      `json:"withPosts"         orm:"with_posts"           ` // 所属岗位
	WithRoles         string      `json:"withRoles"         orm:"with_roles"           ` // 拥有的角色
	WithCompanys      string      `json:"withCompanys"      orm:"with_companys"        ` // 关联的公司
	WithProjects      string      `json:"withProjects"      orm:"with_projects"        ` // 关联的项目
	Gender            int         `json:"gender"            orm:"gender"               ` // 性别
	Usersale          string      `json:"usersale"          orm:"usersale"             ` // 用户盐
	Password          string      `json:"password"          orm:"password"             ` // 密码
	Birthday          *gtime.Time `json:"birthday"          orm:"birthday"             ` // 生日
	ContactAddress    string      `json:"contactAddress"    orm:"contact_address"      ` // 联系地址
	Description       string      `json:"description"       orm:"description"          ` // 描述信息
	LastLoginIp       string      `json:"lastLoginIp"       orm:"last_login_ip"        ` // 最后登录ip
	LastLoginTime     *gtime.Time `json:"lastLoginTime"     orm:"last_login_time"      ` // 最后登录时间
	CreatedBy         int64       `json:"createdBy"         orm:"created_by"           ` // 创建人
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"           ` // 创建时间
	UpdatedAt         *gtime.Time `json:"updatedAt"         orm:"updated_at"           ` // 更新时间
	DeletedAt         *gtime.Time `json:"deletedAt"         orm:"deleted_at"           ` // 删除时间
	TenantId          int64       `json:"tenantId"          orm:"tenant_id"            ` // 数据租户id
	Avatar            string      `json:"avatar"            orm:"avatar"               ` // 用户头像
	LastUpdatePwdTime *gtime.Time `json:"lastUpdatePwdTime" orm:"last_update_pwd_time" ` // 最后一次修改密码的时间
	PasswordChanged   int         `json:"passwordChanged"   orm:"password_changed"     ` // 密码是否已修改
}
