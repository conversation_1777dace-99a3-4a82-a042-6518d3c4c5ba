// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowInstance is the golang structure for table flow_instance.
type FlowInstance struct {
	Id                int64       `json:"id"                orm:"id"                  ` // id
	FlowTemplateId    int64       `json:"flowTemplateId"    orm:"flow_template_id"    ` // 流程模板id
	FlowTemplateSnap  string      `json:"flowTemplateSnap"  orm:"flow_template_snap"  ` // 流程模板快照
	FormTemplateId    int64       `json:"formTemplateId"    orm:"form_template_id"    ` // 绑定表单模板id
	FormTemplateTitle string      `json:"formTemplateTitle" orm:"form_template_title" ` // 绑定表单模板标题
	FormTemplateSnap  string      `json:"formTemplateSnap"  orm:"form_template_snap"  ` // 绑定表单模板快照
	FormTableName     string      `json:"formTableName"     orm:"form_table_name"     ` // 绑定表单表名称
	FormTableId       int64       `json:"formTableId"       orm:"form_table_id"       ` // 绑定表单表id
	FormDataSnap      string      `json:"formDataSnap"      orm:"form_data_snap"      ` // 表单数据快照
	FormExtraData     string      `json:"formExtraData"     orm:"form_extra_data"     ` // 表单工作流附加数据
	CurrentSteps      string      `json:"currentSteps"      orm:"current_steps"       ` // 当前正在进行的步骤
	FinishdSteps      string      `json:"finishdSteps"      orm:"finishd_steps"       ` // 已经完成的步骤ID
	FlowSteps         string      `json:"flowSteps"         orm:"flow_steps"          ` // 流程预生成的所有步骤
	InstanceState     int         `json:"instanceState"     orm:"instance_state"      ` // 实例状态;1 等待审批 2 审批中 3审批通过 4审批不通过 5 作废
	CreatedBy         int64       `json:"createdBy"         orm:"created_by"          ` // 创建人
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"          ` // 创建时间
	FinishdAt         *gtime.Time `json:"finishdAt"         orm:"finishd_at"          ` // 完成时间
	UpdatedAt         *gtime.Time `json:"updatedAt"         orm:"updated_at"          ` // 更新时间
	DeletedAt         *gtime.Time `json:"deletedAt"         orm:"deleted_at"          ` // 删除时间
	TenantId          int64       `json:"tenantId"          orm:"tenant_id"           ` // 数据租户id
	FlowInstanceType  int         `json:"flowInstanceType"  orm:"flow_instance_type"  ` // 流程实例类型 	1创建申请 2作废申请
}
