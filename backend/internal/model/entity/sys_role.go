// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysRole is the golang structure for table sys_role.
type SysRole struct {
	Id          int64       `json:"id"          orm:"id"          ` //
	Code        string      `json:"code"        orm:"code"        ` // 编码
	Status      uint        `json:"status"      orm:"status"      ` // 状态;0:禁用
	ListOrder   uint        `json:"listOrder"   orm:"list_order"  ` // 排序
	Name        string      `json:"name"        orm:"name"        ` // 角色名称
	Permissions string      `json:"permissions" orm:"permissions" ` // 角色权限
	Remark      string      `json:"remark"      orm:"remark"      ` // 备注
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  ` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  ` // 更新时间
	TenantId    int64       `json:"tenantId"    orm:"tenant_id"   ` // 租户id
}
