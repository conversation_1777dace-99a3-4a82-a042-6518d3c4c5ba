// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysUserProject is the golang structure for table sys_user_project.
type SysUserProject struct {
	Id            int   `json:"id"            orm:"id"              ` // 关联ID
	UserId        int64 `json:"userId"        orm:"user_id"         ` // 用户ID
	ProjectId     int64 `json:"projectId"     orm:"project_id"      ` // 项目id
	ProjectRoleId int64 `json:"projectRoleId" orm:"project_role_id" ` // 项目角色id
}
