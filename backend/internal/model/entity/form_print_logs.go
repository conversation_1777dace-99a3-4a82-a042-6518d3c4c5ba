// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FormPrintLogs is the golang structure for table form_print_logs.
type FormPrintLogs struct {
	Id                  int64       `json:"id"                  orm:"id"                     ` // id
	FormTemplateId      int64       `json:"formTemplateId"      orm:"form_template_id"       ` // 表单模板id
	FormDataId          int64       `json:"formDataId"          orm:"form_data_id"           ` // 表单数据id
	FormPrintTemplateId int64       `json:"formPrintTemplateId" orm:"form_print_template_id" ` // 打印模板id
	PrintCount          int         `json:"printCount"          orm:"print_count"            ` // 打印次数
	LastPrintedAt       *gtime.Time `json:"lastPrintedAt"       orm:"last_printed_at"        ` // 最后打印时间
	PrintDetails        string      `json:"printDetails"        orm:"print_details"          ` // 打印明细记录
	CreatedBy           int64       `json:"createdBy"           orm:"created_by"             ` // 创建人
	CreatedAt           *gtime.Time `json:"createdAt"           orm:"created_at"             ` // 创建时间
	UpdatedAt           *gtime.Time `json:"updatedAt"           orm:"updated_at"             ` // 更新时间
	DeletedAt           *gtime.Time `json:"deletedAt"           orm:"deleted_at"             ` // 删除时间
	TenantId            int64       `json:"tenantId"            orm:"tenant_id"              ` // 数据租户id
}
