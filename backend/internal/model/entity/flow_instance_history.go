// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowInstanceHistory is the golang structure for table flow_instance_history.
type FlowInstanceHistory struct {
	Id             int64       `json:"id"             orm:"id"               ` // id
	FlowInstanceId int64       `json:"flowInstanceId" orm:"flow_instance_id" ` // 流程实例id
	StepId         string      `json:"stepId"         orm:"step_id"          ` // 步骤id
	StepName       string      `json:"stepName"       orm:"step_name"        ` // 步骤名
	NodeType       string      `json:"nodeType"       orm:"node_type"        ` // 节点类型
	Status         int         `json:"status"         orm:"status"           ` // 当前状态
	ApprovalUsers  string      `json:"approvalUsers"  orm:"approval_users"   ` // 匹配的所有审批人
	Condition      string      `json:"condition"      orm:"condition"        ` // 存在多个审批人时的审批方式
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       ` // 创建时间
	FinishdAt      *gtime.Time `json:"finishdAt"      orm:"finishd_at"       ` // 完成时间
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"       ` // 更新时间
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"       ` // 删除时间
	TenantId       int64       `json:"tenantId"       orm:"tenant_id"        ` // 租户id
	PreStepId      string      `json:"preStepId"      orm:"pre_step_id"      ` // 上一个节点的id（从哪个节点来的）
	PreHistoryId   int64       `json:"preHistoryId"   orm:"pre_history_id"   ` // 上一个节点审核历史id
	IsReturn       int         `json:"isReturn"       orm:"is_return"        ` // 是否是退回的节点
}
