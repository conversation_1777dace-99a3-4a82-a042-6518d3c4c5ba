// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysPost is the golang structure for table sys_post.
type SysPost struct {
	Id        int64       `json:"id"        orm:"id"         ` // id
	ParentId  int64       `json:"parentId"  orm:"parent_id"  ` // 父id
	PostCode  string      `json:"postCode"  orm:"post_code"  ` // 岗位编号
	PostName  string      `json:"postName"  orm:"post_name"  ` // 岗位名
	NodePath  string      `json:"nodePath"  orm:"node_path"  ` // 岗位树节点路径
	ListOrder int         `json:"listOrder" orm:"list_order" ` // 排序
	WithRoles string      `json:"withRoles" orm:"with_roles" ` // 关联角色
	Remark    string      `json:"remark"    orm:"remark"     ` // 备注
	CreatedBy int64       `json:"createdBy" orm:"created_by" ` // 创建人
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" ` // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" ` // 更新时间
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" ` // 删除时间
	TenantId  int64       `json:"tenantId"  orm:"tenant_id"  ` // 数据租户id
}
