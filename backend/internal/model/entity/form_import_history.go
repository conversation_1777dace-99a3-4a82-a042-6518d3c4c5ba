// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FormImportHistory is the golang structure for table form_import_history.
type FormImportHistory struct {
	Id                   int64       `json:"id"                   orm:"id"                      ` // id
	FormImportTemplateId int64       `json:"formImportTemplateId" orm:"form_import_template_id" ` // 导入的数据模板id
	FormTemplateId       int64       `json:"formTemplateId"       orm:"form_template_id"        ` // 导入的表单模板id
	ImportFile           string      `json:"importFile"           orm:"import_file"             ` // 导入的文件
	Status               int         `json:"status"               orm:"status"                  ` // 状态
	Result               string      `json:"result"               orm:"result"                  ` // 结果信息
	TotalCount           int64       `json:"totalCount"           orm:"total_count"             ` // 总数
	CompletedCount       int64       `json:"completedCount"       orm:"completed_count"         ` // 已处理数
	SuccessCount         int64       `json:"successCount"         orm:"success_count"           ` // 成功数
	FailureCount         int64       `json:"failureCount"         orm:"failure_count"           ` // 失败数
	FinishdAt            *gtime.Time `json:"finishdAt"            orm:"finishd_at"              ` // 完成时间
	CreatedBy            int64       `json:"createdBy"            orm:"created_by"              ` // 创建人
	CreatedAt            *gtime.Time `json:"createdAt"            orm:"created_at"              ` // 创建时间
	UpdatedAt            *gtime.Time `json:"updatedAt"            orm:"updated_at"              ` // 更新时间
	DeletedAt            *gtime.Time `json:"deletedAt"            orm:"deleted_at"              ` // 删除时间
	TenantId             int64       `json:"tenantId"             orm:"tenant_id"               ` // 数据租户id
	CurrentStep          int         `json:"currentStep"          orm:"current_step"            ` // 当前步骤
}
