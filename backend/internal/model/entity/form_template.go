// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FormTemplate is the golang structure for table form_template.
type FormTemplate struct {
	Id               int64       `json:"id"               orm:"id"                 ` // id
	FormCode         string      `json:"formCode"         orm:"form_code"          ` // 表单编号
	FormTableName    string      `json:"formTableName"    orm:"form_table_name"    ` // 表单绑定的Table名
	FormTitle        string      `json:"formTitle"        orm:"form_title"         ` // 表单名
	FormDesc         string      `json:"formDesc"         orm:"form_desc"          ` // 表单介绍
	FormSchema       string      `json:"formSchema"       orm:"form_schema"        ` // 低代码的原始结构
	FormColumns      string      `json:"formColumns"      orm:"form_columns"       ` // 低代码获取的字段列表
	HistoryColumns   string      `json:"historyColumns"   orm:"history_columns"    ` // 历史删除的字段列表
	TableDesign      string      `json:"tableDesign"      orm:"table_design"       ` // 列表设计
	OpenFlow         int         `json:"openFlow"         orm:"open_flow"          ` // 开启审核流程
	OpenLog          int         `json:"openLog"          orm:"open_log"           ` // 开启操作日志
	AllowCustomAudit int         `json:"allowCustomAudit" orm:"allow_custom_audit" ` // 允许增加自定义审核人
	AllowCustomCc    int         `json:"allowCustomCc"    orm:"allow_custom_cc"    ` // 允许增加自定义抄送人
	FlowCode         string      `json:"flowCode"         orm:"flow_code"          ` // 绑定的审核流程编号
	CreatedBy        int64       `json:"createdBy"        orm:"created_by"         ` // 创建人
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"         ` // 创建时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"         ` // 更新时间
	DeletedAt        *gtime.Time `json:"deletedAt"        orm:"deleted_at"         ` // 删除时间
	TenantId         int64       `json:"tenantId"         orm:"tenant_id"          ` // 数据租户id
	FlowTemplateId   int64       `json:"flowTemplateId"   orm:"flow_template_id"   ` // 绑定的流程模板id
	FormType         int         `json:"formType"         orm:"form_type"          ` // 表单类型
	Permissions      string      `json:"permissions"      orm:"permissions"        ` // 表单的数据行权限设置
	WithTables       string      `json:"withTables"       orm:"with_tables"        ` // 关联表信息
}
