// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FormExportHistory is the golang structure for table form_export_history.
type FormExportHistory struct {
	Id                   int64       `json:"id"                   orm:"id"                      ` // id
	FormExportTemplateId int64       `json:"formExportTemplateId" orm:"form_export_template_id" ` // 导出的数据模板id
	FormTemplateId       int64       `json:"formTemplateId"       orm:"form_template_id"        ` // 导出的表单模板id
	FilterRules          string      `json:"filterRules"          orm:"filter_rules"            ` // 数据过滤规则
	ExportFile           string      `json:"exportFile"           orm:"export_file"             ` // 导出的文件
	Status               int         `json:"status"               orm:"status"                  ` // 状态
	Result               string      `json:"result"               orm:"result"                  ` // 结果信息
	TotalCount           int64       `json:"totalCount"           orm:"total_count"             ` // 总数
	FinishdAt            *gtime.Time `json:"finishdAt"            orm:"finishd_at"              ` // 完成时间
	CreatedBy            int64       `json:"createdBy"            orm:"created_by"              ` // 创建人
	CreatedAt            *gtime.Time `json:"createdAt"            orm:"created_at"              ` // 创建时间
	UpdatedAt            *gtime.Time `json:"updatedAt"            orm:"updated_at"              ` // 更新时间
	DeletedAt            *gtime.Time `json:"deletedAt"            orm:"deleted_at"              ` // 删除时间
	TenantId             int64       `json:"tenantId"             orm:"tenant_id"               ` // 数据租户id
}
