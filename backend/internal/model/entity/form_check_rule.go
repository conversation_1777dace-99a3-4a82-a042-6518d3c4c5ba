// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FormCheckRule is the golang structure for table form_check_rule.
type FormCheckRule struct {
	Id             int64       `json:"id"             orm:"id"               ` // id
	Name           string      `json:"name"           orm:"name"             ` // 验证规则名称
	Description    string      `json:"description"    orm:"description"      ` // 规则说明
	FormTemplateId int64       `json:"formTemplateId" orm:"form_template_id" ` // 关联的模板id
	CodeContent    string      `json:"codeContent"    orm:"code_content"     ` // 代码内容
	FailMsg        string      `json:"failMsg"        orm:"fail_msg"         ` // 验证失败提示内容
	Status         int         `json:"status"         orm:"status"           ` // 状态;1 正常 999 停止
	CreatedBy      string      `json:"createdBy"      orm:"created_by"       ` // 创建人
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       ` // 创建时间
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"       ` // 更新时间
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"       ` // 删除时间
	TenantId       int64       `json:"tenantId"       orm:"tenant_id"        ` // 数据租户id
}
