// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AmConfigHistory is the golang structure of table am_config_history for DAO operations like Where/Data.
type AmConfigHistory struct {
	g.Meta        `orm:"table:am_config_history, do:true"`
	Id            interface{} // id
	AmConfigId    interface{} // 关联的自动化配置id
	Status        interface{} // 状态
	ResultCode    interface{} // 执行结果;1 成功 999 失败
	ResultMsg     interface{} // 执行结果MSG
	DataSnap      interface{} // 数据源快照
	ResultSnap    interface{} // 结果快照
	NodeLog       interface{} // 节点详细日志;每个节点的执行信息、堆栈信息
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 更新时间
	DeletedAt     *gtime.Time // 删除时间
	TenantId      interface{} // 数据租户id
	WorkflowState interface{} // 执行状态数据（用于恢复执行）
	SequenceId    interface{} // 触发的顺序id（防止消息队列重放）
}
