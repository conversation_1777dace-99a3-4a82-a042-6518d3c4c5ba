// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ProcessTemplate is the golang structure of table process_template for DAO operations like Where/Data.
type ProcessTemplate struct {
	g.Meta      `orm:"table:process_template, do:true"`
	Id          interface{} // id
	Name        interface{} // 模版名
	Description interface{} // 流程说明
	Settleinfo  interface{} // 流程设置（JSON）
	Nodes       interface{} // 流程节点设置（JSON）
	CreatedBy   interface{} // 创建人
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	DeletedAt   *gtime.Time // 删除时间
	TenantId    interface{} // 数据租户id
}
