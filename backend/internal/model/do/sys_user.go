// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysUser is the golang structure of table sys_user for DAO operations like Where/Data.
type SysUser struct {
	g.Meta            `orm:"table:sys_user, do:true"`
	Id                interface{} // id
	Code              interface{} // 员工编号
	SystemAccount     interface{} // 系统账号
	Username          interface{} // 用户名
	Email             interface{} // 邮箱
	ContactPhone      interface{} // 联系电话
	IdNumber          interface{} // 身份证号码
	HireDate          *gtime.Time // 入职日期
	ResignationDate   *gtime.Time // 离职日期
	Status            interface{} // 状态
	WithDepts         interface{} // 所在部门
	WithPosts         interface{} // 所属岗位
	WithRoles         interface{} // 拥有的角色
	WithCompanys      interface{} // 关联的公司
	WithProjects      interface{} // 关联的项目
	Gender            interface{} // 性别
	Usersale          interface{} // 用户盐
	Password          interface{} // 密码
	Birthday          *gtime.Time // 生日
	ContactAddress    interface{} // 联系地址
	Description       interface{} // 描述信息
	LastLoginIp       interface{} // 最后登录ip
	LastLoginTime     *gtime.Time // 最后登录时间
	CreatedBy         interface{} // 创建人
	CreatedAt         *gtime.Time // 创建时间
	UpdatedAt         *gtime.Time // 更新时间
	DeletedAt         *gtime.Time // 删除时间
	TenantId          interface{} // 数据租户id
	Avatar            interface{} // 用户头像
	LastUpdatePwdTime *gtime.Time // 最后一次修改密码的时间
	PasswordChanged   interface{} // 密码是否已修改
}
