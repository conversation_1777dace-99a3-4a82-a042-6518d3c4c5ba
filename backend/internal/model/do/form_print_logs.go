// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormPrintLogs is the golang structure of table form_print_logs for DAO operations like Where/Data.
type FormPrintLogs struct {
	g.Meta              `orm:"table:form_print_logs, do:true"`
	Id                  interface{} // id
	FormTemplateId      interface{} // 表单模板id
	FormDataId          interface{} // 表单数据id
	FormPrintTemplateId interface{} // 打印模板id
	PrintCount          interface{} // 打印次数
	LastPrintedAt       *gtime.Time // 最后打印时间
	PrintDetails        interface{} // 打印明细记录
	CreatedBy           interface{} // 创建人
	CreatedAt           *gtime.Time // 创建时间
	UpdatedAt           *gtime.Time // 更新时间
	DeletedAt           *gtime.Time // 删除时间
	TenantId            interface{} // 数据租户id
}
