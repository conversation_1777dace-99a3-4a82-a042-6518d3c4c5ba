// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysRole is the golang structure of table sys_role for DAO operations like Where/Data.
type SysRole struct {
	g.Meta      `orm:"table:sys_role, do:true"`
	Id          interface{} //
	Code        interface{} // 编码
	Status      interface{} // 状态;0:禁用
	ListOrder   interface{} // 排序
	Name        interface{} // 角色名称
	Permissions interface{} // 角色权限
	Remark      interface{} // 备注
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	TenantId    interface{} // 租户id
}
