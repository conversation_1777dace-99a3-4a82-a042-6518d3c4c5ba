// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowInstance is the golang structure of table flow_instance for DAO operations like Where/Data.
type FlowInstance struct {
	g.Meta            `orm:"table:flow_instance, do:true"`
	Id                interface{} // id
	FlowTemplateId    interface{} // 流程模板id
	FlowTemplateSnap  interface{} // 流程模板快照
	FormTemplateId    interface{} // 绑定表单模板id
	FormTemplateTitle interface{} // 绑定表单模板标题
	FormTemplateSnap  interface{} // 绑定表单模板快照
	FormTableName     interface{} // 绑定表单表名称
	FormTableId       interface{} // 绑定表单表id
	FormDataSnap      interface{} // 表单数据快照
	FormExtraData     interface{} // 表单工作流附加数据
	CurrentSteps      interface{} // 当前正在进行的步骤
	FinishdSteps      interface{} // 已经完成的步骤ID
	FlowSteps         interface{} // 流程预生成的所有步骤
	InstanceState     interface{} // 实例状态;1 等待审批 2 审批中 3审批通过 4审批不通过 5 作废
	CreatedBy         interface{} // 创建人
	CreatedAt         *gtime.Time // 创建时间
	FinishdAt         *gtime.Time // 完成时间
	UpdatedAt         *gtime.Time // 更新时间
	DeletedAt         *gtime.Time // 删除时间
	TenantId          interface{} // 数据租户id
	FlowInstanceType  interface{} // 流程实例类型 	1创建申请 2作废申请
}
