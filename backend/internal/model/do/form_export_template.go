// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormExportTemplate is the golang structure of table form_export_template for DAO operations like Where/Data.
type FormExportTemplate struct {
	g.Meta         `orm:"table:form_export_template, do:true"`
	Id             interface{} // id
	Name           interface{} // 模板名称
	Description    interface{} // 规则说明
	FormTemplateId interface{} // 关联的模板id
	FieldMapping   interface{} // 字段映射
	Status         interface{} // 状态;1 正常 999 停止
	CreatedBy      interface{} // 创建人
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 更新时间
	DeletedAt      *gtime.Time // 删除时间
	TenantId       interface{} // 数据租户id
}
