// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysPost is the golang structure of table sys_post for DAO operations like Where/Data.
type SysPost struct {
	g.Meta    `orm:"table:sys_post, do:true"`
	Id        interface{} // id
	ParentId  interface{} // 父id
	PostCode  interface{} // 岗位编号
	PostName  interface{} // 岗位名
	NodePath  interface{} // 岗位树节点路径
	ListOrder interface{} // 排序
	WithRoles interface{} // 关联角色
	Remark    interface{} // 备注
	CreatedBy interface{} // 创建人
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 删除时间
	TenantId  interface{} // 数据租户id
}
