// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysMenu is the golang structure of table sys_menu for DAO operations like Where/Data.
type SysMenu struct {
	g.Meta               `orm:"table:sys_menu, do:true"`
	Id                   interface{} // id
	Code                 interface{} // 编号
	ParentId             interface{} // 父菜单id
	Route                interface{} // 路由地址
	Mark                 interface{} // 菜单标记
	Title                interface{} // 菜单标题
	Icon                 interface{} // 图标
	Remark               interface{} // 备注
	MenuType             interface{} // 菜单类型
	IsDisplay            interface{} // 是否显示
	ApiAssociation       interface{} // 关联的API
	CreatedBy            interface{} // 创建人
	CreatedAt            *gtime.Time // 创建时间
	UpdatedAt            *gtime.Time // 更新时间
	DeletedAt            *gtime.Time // 删除时间
	TenantId             interface{} // 数据租户id
	MenuFormTemplateId   interface{} // 绑定的表单模板id
	MenuFormTemplateType interface{} // 绑定的表单模板页面类型
	RouteType            interface{} // 路由类型
	SortOrder            interface{} // 排序字段
	DashboardId          interface{} // 仪表板id
	DashboardIdMobile    interface{} // 仪表板id-移动端
}
