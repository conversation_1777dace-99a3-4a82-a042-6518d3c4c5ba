// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SysConfig is the golang structure of table sys_config for DAO operations like Where/Data.
type SysConfig struct {
	g.Meta      `orm:"table:sys_config, do:true"`
	Id          interface{} // ID
	ConfigKey   interface{} // 配置KEY
	ConfigValue interface{} // 配置内容
	Version     interface{} // 版本
	TenantId    interface{} // 数据租户id
}
