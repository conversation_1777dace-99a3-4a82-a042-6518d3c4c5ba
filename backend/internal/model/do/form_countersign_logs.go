// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormCountersignLogs is the golang structure of table form_countersign_logs for DAO operations like Where/Data.
type FormCountersignLogs struct {
	g.Meta             `orm:"table:form_countersign_logs, do:true"`
	Id                 interface{} // id
	FormTemplateId     interface{} // 表单模板id
	FormDataId         interface{} // 表单数据id
	CountersignContent interface{} // 回签内容
	CountersignRemark  interface{} // 回签备注
	CreatedBy          interface{} // 创建人
	CreatedAt          *gtime.Time // 创建时间
	UpdatedAt          *gtime.Time // 更新时间
	DeletedAt          *gtime.Time // 删除时间
	TenantId           interface{} // 数据租户id
}
