// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FlowTemplateForm is the golang structure of table flow_template_form for DAO operations like Where/Data.
type FlowTemplateForm struct {
	g.Meta         `orm:"table:flow_template_form, do:true"`
	Id             interface{} // id
	FlowTemplateId interface{} // 流程模板id
	FormTemplateId interface{} // 表单模板id
}
