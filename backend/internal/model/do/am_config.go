// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AmConfig is the golang structure of table am_config for DAO operations like Where/Data.
type AmConfig struct {
	g.Meta      `orm:"table:am_config, do:true"`
	Id          interface{} // id
	Name        interface{} // 自动化名称
	Description interface{} // 自动化说明
	FormId      interface{} // 关联的表单id
	TriggerData interface{} // 触发器数据
	FlowSchema  interface{} // 自动化流程设计整体结构
	Status      interface{} // 状态;1 正常 999 停止
	CreatedBy   interface{} // 创建人
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	DeletedAt   *gtime.Time // 删除时间
	TenantId    interface{} // 数据租户id
	Version     interface{} // 当前版本
	Depend      interface{} // 依赖的其他流程id（定义执行顺序）
}
