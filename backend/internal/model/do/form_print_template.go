// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormPrintTemplate is the golang structure of table form_print_template for DAO operations like Where/Data.
type FormPrintTemplate struct {
	g.Meta          `orm:"table:form_print_template, do:true"`
	Id              interface{} // id
	Name            interface{} // 模板名
	Description     interface{} // 模板说明
	FormTemplateId  interface{} // 关联的模板id
	EnterWithData   interface{} // 关联表字段设置
	TemplateContent interface{} // 模版内容
	PrintSize       interface{} // 纸张方向
	PrintRotation   interface{} // 尺寸大小
	Status          interface{} // 状态;1 正常 999 停止
	CreatedBy       interface{} // 创建人
	CreatedAt       *gtime.Time // 创建时间
	UpdatedAt       *gtime.Time // 更新时间
	DeletedAt       *gtime.Time // 删除时间
	TenantId        interface{} // 数据租户id
	Padding         interface{} // 内边距
	Width           interface{} // 宽度
	Height          interface{} // 高度
	AllowEditBefore interface{} // 是否允许打印前编辑
	SqlQueries      interface{} // sql数据源配置
}
