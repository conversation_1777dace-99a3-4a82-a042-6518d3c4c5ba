// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormImportHistory is the golang structure of table form_import_history for DAO operations like Where/Data.
type FormImportHistory struct {
	g.Meta               `orm:"table:form_import_history, do:true"`
	Id                   interface{} // id
	FormImportTemplateId interface{} // 导入的数据模板id
	FormTemplateId       interface{} // 导入的表单模板id
	ImportFile           interface{} // 导入的文件
	Status               interface{} // 状态
	Result               interface{} // 结果信息
	TotalCount           interface{} // 总数
	CompletedCount       interface{} // 已处理数
	SuccessCount         interface{} // 成功数
	FailureCount         interface{} // 失败数
	FinishdAt            *gtime.Time // 完成时间
	CreatedBy            interface{} // 创建人
	CreatedAt            *gtime.Time // 创建时间
	UpdatedAt            *gtime.Time // 更新时间
	DeletedAt            *gtime.Time // 删除时间
	TenantId             interface{} // 数据租户id
	CurrentStep          interface{} // 当前步骤
}
