// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormTemplate is the golang structure of table form_template for DAO operations like Where/Data.
type FormTemplate struct {
	g.Meta           `orm:"table:form_template, do:true"`
	Id               interface{} // id
	FormCode         interface{} // 表单编号
	FormTableName    interface{} // 表单绑定的Table名
	FormTitle        interface{} // 表单名
	FormDesc         interface{} // 表单介绍
	FormSchema       interface{} // 低代码的原始结构
	FormColumns      interface{} // 低代码获取的字段列表
	HistoryColumns   interface{} // 历史删除的字段列表
	TableDesign      interface{} // 列表设计
	OpenFlow         interface{} // 开启审核流程
	OpenLog          interface{} // 开启操作日志
	AllowCustomAudit interface{} // 允许增加自定义审核人
	AllowCustomCc    interface{} // 允许增加自定义抄送人
	FlowCode         interface{} // 绑定的审核流程编号
	CreatedBy        interface{} // 创建人
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
	DeletedAt        *gtime.Time // 删除时间
	TenantId         interface{} // 数据租户id
	FlowTemplateId   interface{} // 绑定的流程模板id
	FormType         interface{} // 表单类型
	Permissions      interface{} // 表单的数据行权限设置
	WithTables       interface{} // 关联表信息
}
