// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProject is the golang structure of table ad_project for DAO operations like Where/Data.
type AdProject struct {
	g.Meta      `orm:"table:ad_project, do:true"`
	Id          interface{} // id
	Code        interface{} // 编号
	Name        interface{} // 项目名称
	Leader      interface{} // 负责人
	Instruction interface{} // 项目说明
	SortOrder   interface{} // 排序字段
	CreatedBy   interface{} // 创建人
	UpdatedBy   interface{} // 修改人
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 修改时间
	DeletedAt   *gtime.Time // 删除时间
	TenantId    interface{} // 数据租户id
	Source      interface{} // 来源
	Version     interface{} // 当前版本
}
