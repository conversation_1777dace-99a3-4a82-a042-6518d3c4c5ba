// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SysUserProject is the golang structure of table sys_user_project for DAO operations like Where/Data.
type SysUserProject struct {
	g.Meta        `orm:"table:sys_user_project, do:true"`
	Id            interface{} // 关联ID
	UserId        interface{} // 用户ID
	ProjectId     interface{} // 项目id
	ProjectRoleId interface{} // 项目角色id
}
