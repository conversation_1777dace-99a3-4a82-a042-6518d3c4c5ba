// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowTemplate is the golang structure of table flow_template for DAO operations like Where/Data.
type FlowTemplate struct {
	g.Meta      `orm:"table:flow_template, do:true"`
	Id          interface{} // id
	Name        interface{} // 模版名
	Description interface{} // 流程说明
	FlowSchema  interface{} // 流程设计整体结构
	FlowStatus  interface{} // 状态;1 正常 999 停止
	CreatedBy   interface{} // 创建人
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	DeletedAt   *gtime.Time // 删除时间
	TenantId    interface{} // 数据租户id
}
