// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SysPostRole is the golang structure of table sys_post_role for DAO operations like Where/Data.
type SysPostRole struct {
	g.Meta `orm:"table:sys_post_role, do:true"`
	Id     interface{} // 关联ID
	PostId interface{} // 岗位ID
	RoleId interface{} // 角色ID
}
