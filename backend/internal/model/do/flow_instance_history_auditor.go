// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowInstanceHistoryAuditor is the golang structure of table flow_instance_history_auditor for DAO operations like Where/Data.
type FlowInstanceHistoryAuditor struct {
	g.Meta                           `orm:"table:flow_instance_history_auditor, do:true"`
	Id                               interface{} // id
	FlowInstanceHistoryId            interface{} // 审核历史id
	FlowInstanceId                   interface{} // 流程实例id
	NodeType                         interface{} // 节点类型（用于区分发起人、审批人、抄送人）
	UserId                           interface{} // 审核人id
	ApprovalStatus                   interface{} // 审批状态
	IsAutoPass                       interface{} // 是否是自动通过
	FlowInstanceHistoryStatus        interface{} // 节点实例状态
	FlowInstanceHistoryStatusVersion *gtime.Time // 节点实例状态同步时间
	FlowInstanceStatus               interface{} // 流程实例状态
	FlowInstanceStatusVersion        *gtime.Time // 流程实例状态同步时间
	ReturnNodeId                     interface{} // 退回节点id
	ArrivalTime                      *gtime.Time // 到达时间
	CompletionTime                   *gtime.Time // 完成时间
	ApprovalComment                  interface{} // 审批意见
	Attachment                       interface{} // 附件
	Signature                        interface{} // 签名
	CreatedAt                        *gtime.Time // 创建时间
	UpdatedAt                        *gtime.Time // 更新时间
	DeletedAt                        *gtime.Time // 删除时间
	TenantId                         interface{} // 租户id
	Pics                             interface{} // 图片
}
