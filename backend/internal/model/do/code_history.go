// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CodeHistory is the golang structure of table code_history for DAO operations like Where/Data.
type CodeHistory struct {
	g.Meta      `orm:"table:code_history, do:true"`
	Id          interface{} // id
	Title       interface{} // 代码库标题
	Instruction interface{} // 代码库说明
	Content     interface{} // 代码内容
	CreatedBy   interface{} // 创建人
	UpdatedBy   interface{} // 修改人
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 修改时间
	DeletedAt   *gtime.Time // 删除时间
	TenantId    interface{} // 数据租户id
}
