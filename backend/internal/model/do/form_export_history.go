// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormExportHistory is the golang structure of table form_export_history for DAO operations like Where/Data.
type FormExportHistory struct {
	g.Meta               `orm:"table:form_export_history, do:true"`
	Id                   interface{} // id
	FormExportTemplateId interface{} // 导出的数据模板id
	FormTemplateId       interface{} // 导出的表单模板id
	FilterRules          interface{} // 数据过滤规则
	ExportFile           interface{} // 导出的文件
	Status               interface{} // 状态
	Result               interface{} // 结果信息
	TotalCount           interface{} // 总数
	FinishdAt            *gtime.Time // 完成时间
	CreatedBy            interface{} // 创建人
	CreatedAt            *gtime.Time // 创建时间
	UpdatedAt            *gtime.Time // 更新时间
	DeletedAt            *gtime.Time // 删除时间
	TenantId             interface{} // 数据租户id
}
