// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FlowInstanceHistory is the golang structure of table flow_instance_history for DAO operations like Where/Data.
type FlowInstanceHistory struct {
	g.Meta         `orm:"table:flow_instance_history, do:true"`
	Id             interface{} // id
	FlowInstanceId interface{} // 流程实例id
	StepId         interface{} // 步骤id
	StepName       interface{} // 步骤名
	NodeType       interface{} // 节点类型
	Status         interface{} // 当前状态
	ApprovalUsers  interface{} // 匹配的所有审批人
	Condition      interface{} // 存在多个审批人时的审批方式
	CreatedAt      *gtime.Time // 创建时间
	FinishdAt      *gtime.Time // 完成时间
	UpdatedAt      *gtime.Time // 更新时间
	DeletedAt      *gtime.Time // 删除时间
	TenantId       interface{} // 租户id
	PreStepId      interface{} // 上一个节点的id（从哪个节点来的）
	PreHistoryId   interface{} // 上一个节点审核历史id
	IsReturn       interface{} // 是否是退回的节点
}
