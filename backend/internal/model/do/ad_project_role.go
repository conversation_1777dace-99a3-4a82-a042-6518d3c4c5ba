// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProjectRole is the golang structure of table ad_project_role for DAO operations like Where/Data.
type AdProjectRole struct {
	g.Meta      `orm:"table:ad_project_role, do:true"`
	Id          interface{} // id
	Code        interface{} // 编号
	Name        interface{} // 角色名
	IsDefault   interface{} // 是否是默认角色
	Instruction interface{} // 角色说明
	CreatedBy   interface{} // 创建人
	UpdatedBy   interface{} // 修改人
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 修改时间
	DeletedAt   *gtime.Time // 删除时间
	TenantId    interface{} // 数据租户id
}
