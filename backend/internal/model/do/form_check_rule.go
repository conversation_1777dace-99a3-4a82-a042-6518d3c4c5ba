// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FormCheckRule is the golang structure of table form_check_rule for DAO operations like Where/Data.
type FormCheckRule struct {
	g.Meta         `orm:"table:form_check_rule, do:true"`
	Id             interface{} // id
	Name           interface{} // 验证规则名称
	Description    interface{} // 规则说明
	FormTemplateId interface{} // 关联的模板id
	CodeContent    interface{} // 代码内容
	FailMsg        interface{} // 验证失败提示内容
	Status         interface{} // 状态;1 正常 999 停止
	CreatedBy      interface{} // 创建人
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 更新时间
	DeletedAt      *gtime.Time // 删除时间
	TenantId       interface{} // 数据租户id
}
