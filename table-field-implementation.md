# 打印模板表格字段级插入功能实现方案

## 功能背景

当前项目的打印模板功能只支持整表插入（将整个子表或SQL查询结果作为一个表格插入），现在需要增加表格字段级插入功能，允许用户在固定的表格DOM结构中插入表格数据的特定字段，并在后台自动处理行循环。

## 技术方案

### 核心思路

1. **自动循环检测**：后端检测到包含 `table_field` 类型占位符的 `<tr>` 时，自动将该行标记为循环行
2. **占位符格式**：继续使用现有的 base64 + a标签方式，新增 `table_field` 类型
3. **智能处理**：根据数据源自动生成对应数量的行，保持向后兼容

### 实现流程

```
用户操作 → 选择表格字段 → 生成占位符 → 后端检测 → 行循环处理 → 生成PDF
```

## 技术实现

### 前端修改

#### 1. 增加表格字段选项 (`edit.jsx`)

**位置**: `buildAllSelParams` 函数
**修改内容**:
- 为表格类型字段添加子菜单选项
- 格式：`table_field_{tableId}.{fieldId}`

```javascript
// 为表格字段添加子菜单
if (item.component_name === "Field.Table" && item.children && item.children.length > 0) {
    menuChildren.push({ key: item.id, label: `${item.label}(整表)` });
    menuChildren.push({
        type: 'group',
        label: `${item.label} 字段`,
        children: item.children.map(child => ({
            key: `table_field_${item.id}.${child.id}`,
            label: `└ ${child.label}`
        }))
    });
}
```

#### 2. 处理表格字段插入 (`edit.jsx`)

**位置**: `tableParamsItemsClick` 函数
**修改内容**:
- 新增 `table_field_` 前缀的处理逻辑
- 生成 `table_field` 类型的占位符

```javascript
else if (key.startsWith('table_field_')) {
    const tableFieldKey = key.replace('table_field_', '');
    const [tableName, fieldName] = tableFieldKey.split('.');
    
    // 直接插入表格字段占位符
    editor.insertNode({
        type: 'link',
        url: Buffer.from(JSON.stringify({
            type: "table_field",
            data: {
                tagKey: tagKey,
                tableName: tableName,
                column: fieldName,
                tableLabel: tableColumn.label,
                fieldLabel: fieldColumn.label
            }
        })).toString('base64'),
        children: [{ text: `${tagItem.label}.${tableColumn.label}.${fieldColumn.label}` }]
    });
}
```

### 后端修改

#### 1. 新增 `table_field` 类型处理 (`print.go`)

**位置**: `HandleHrefContent` 函数
**修改内容**:
- 新增 `table_field` 类型的条件判断
- 调用 `HandleTableFieldContent` 处理

```go
} else if varType == "table_field" {
    // 处理表格字段占位符（新增）
    res = gconv.String(HandleTableFieldContent(ctx, hrefMap, withFormColumns, withTableData, withFormTableInfo, vm))
}
```

#### 2. 新增 `HandleTableFieldContent` 函数

**功能**: 处理表格字段占位符，返回特殊标记供后续循环处理识别

```go
func HandleTableFieldContent(ctx context.Context, hrefMap map[string]interface{}, ...) (res interface{}) {
    // 提取参数
    tagKey := gconv.String(dataMap["tagKey"])
    tableName := gconv.String(dataMap["tableName"])
    columnName := gconv.String(dataMap["column"])
    
    // 返回占位符标记，用于后续识别
    res = fmt.Sprintf("__TABLE_FIELD__%s__%s__%s__", tagKey, tableName, columnName)
    return
}
```

#### 3. 新增表格行循环处理逻辑

**核心函数**: `HandleTableRowLoops`
**功能**: 检测包含表格字段的行并进行循环处理

```go
func HandleTableRowLoops(ctx context.Context, templateContent string, ...) (string, error) {
    trRegex := regexp.MustCompile(`<tr[^>]*>(.*?)</tr>`)
    
    return trRegex.ReplaceAllStringFunc(templateContent, func(trMatch string) string {
        tableFieldInfo := extractTableFieldFromRow(ctx, trMatch)
        if tableFieldInfo == nil {
            return trMatch // 没有表格字段，返回原始行
        }
        
        // 有表格字段，进行循环处理
        return processTableRowLoop(ctx, trMatch, tableFieldInfo, ...)
    }), nil
}
```

#### 4. 修改主处理函数

**位置**: `HandleTemplateContent` 函数开头
**修改内容**: 在处理a标签和img标签之前，先处理表格行循环

```go
func HandleTemplateContent(ctx context.Context, templateContent string, ...) (res string, err error) {
    // 先处理表格行循环（新增功能）
    templateContent, err = HandleTableRowLoops(ctx, templateContent, ...)
    if err != nil {
        return "", err
    }
    
    // 继续原有的处理逻辑...
}
```

## 核心数据结构

### TableFieldInfo
```go
type TableFieldInfo struct {
    TagKey    string              // 数据源标识
    TableName string              // 表格名称
    Fields    []TableFieldDetail  // 字段详情列表
}
```

### TableFieldDetail
```go
type TableFieldDetail struct {
    TagKey      string  // 数据源标识
    TableName   string  // 表格名称
    ColumnName  string  // 字段名称
    Placeholder string  // 占位符文本
}
```

## 处理流程

### 1. 占位符识别
- 用户在编辑器中插入表格字段
- 生成 base64 编码的占位符
- 存储在 a标签的 href 属性中

### 2. a标签预处理（第一步）
- `HandleHrefContent` 识别 `table_field` 类型
- `HandleTableFieldContent` 返回 `__TABLE_FIELD__` 特殊标记
- 将a标签替换为标记，为后续循环处理做准备

### 3. 行循环检测（第二步）
- `HandleTableRowLoops` 扫描所有 `<tr>` 标签
- `extractTableFieldFromRow` 检测行中是否包含 `__TABLE_FIELD__` 标记
- 如果包含，调用 `processTableRowLoop` 进行循环处理

### 4. 循环行生成
- 获取表格数据源
- 遍历每一行数据
- 为每行数据生成对应的 HTML 行
- 替换所有表格字段占位符为实际值

### 5. img标签处理（第三步）
- 处理图片标签的alt属性中的占位符
- 支持二维码、条形码等图片类型字段

**重要**：处理顺序很关键，必须先处理a标签生成标记，再进行表格行循环处理。

## 使用示例

### 用户操作
1. 在编辑器中创建表格
2. 在表格单元格中点击"表单参数"
3. 选择"当前表单" → "子表字段" → "具体字段"
4. 系统自动插入字段占位符

### 结果
```html
<!-- 用户创建的模板 -->
<table>
  <tr>
    <th>序号</th>
    <th>产品名称</th>
  </tr>
  <tr>
    <td><a href="base64_table_field_data">序号</a></td>
    <td><a href="base64_table_field_data">产品名称</a></td>
  </tr>
</table>

<!-- 后端处理后的结果 -->
<table>
  <tr>
    <th>序号</th>
    <th>产品名称</th>
  </tr>
  <tr><td>1</td><td>iPhone 15</td></tr>
  <tr><td>2</td><td>MacBook Pro</td></tr>
  <tr><td>3</td><td>iPad Air</td></tr>
</table>
```

## 兼容性说明

1. **向后兼容**: 原有的整表插入功能保持不变
2. **混合使用**: 支持在同一模板中混合使用整表插入和字段级插入
3. **数据源**: 支持当前表单、关联表、SQL查询等所有现有数据源

## 技术优势

1. **智能检测**: 自动识别需要循环的行，无需用户手动标记
2. **灵活性**: 支持在同一行中混合使用表格字段和固定内容
3. **性能**: 只对包含表格字段的行进行特殊处理，不影响其他内容
4. **扩展性**: 为未来支持更复杂的循环嵌套预留了接口

## 注意事项

1. **同行限制**: 建议同一行的表格字段来自同一个数据源，避免数据不一致
2. **性能考虑**: 大数据量时建议限制显示行数
3. **循环嵌套**: 当前版本不支持循环嵌套，如有需求需要进一步扩展

## 行合并功能（v2.0 新增）

### 功能描述
自动合并固定内容的单元格，避免重复显示：

**原始模板**：
```html
<tr>
  <td>累计未付款</td>
  <td>[未付款表.月份]</td>
</tr>
```

**智能合并后**：
```html
<tr><td rowspan="3">累计未付款</td><td>1月</td></tr>
<tr><td>2月</td></tr>
<tr><td>3月</td></tr>
```

### 实现原理
1. **单元格分析**: 自动识别固定内容和动态字段
2. **智能合并**: 固定内容添加 `rowspan`，后续行移除重复单元格
3. **无需配置**: 用户无需任何额外操作，系统自动处理

### 核心数据结构

```go
type CellInfo struct {
    Content   string // 单元格内容
    IsFixed   bool   // 是否为固定内容（不含表格字段）
    HasFields bool   // 是否包含表格字段
}
```

### 关键函数
- `analyzeCellStructure()`: 分析单元格结构
- `buildRowWithMerge()`: 构建带合并的行
- `processTableRowLoop()`: 重写的循环处理逻辑

## 后续扩展

1. **条件显示**: 支持根据条件显示/隐藏某些行
2. **自定义格式**: 支持为表格字段设置自定义格式化规则
3. **循环嵌套**: 支持多级表格的嵌套循环
4. **性能优化**: 大数据量时的分页和虚拟滚动支持
5. **高级合并**: 支持按条件合并、多列合并等复杂场景 