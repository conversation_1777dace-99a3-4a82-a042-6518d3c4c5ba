{"name": "CRM系统数据结构", "describe": "CRM系统数据结构", "avatar": "", "version": "4.9.4", "createdTime": "2024-5-19 20:38:22", "updatedTime": "2024-10-30 09:13:06", "dbConns": [], "profile": {"default": {"db": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "dbConn": "FB68FAE0-0724-4AF7-8A32-51A722FAE7B9", "entityInitFields": [{"defKey": "TENANT_ID", "defName": "租户号", "comment": "", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "refDict": "", "uiHint": "", "id": "ADB3AD14-6603-43E2-8261-114E32442B5B", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "REVISION", "defName": "乐观锁", "comment": "", "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "92BF430E-01FA-4AEF-944F-25A142632654", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811"}, {"defKey": "CREATED_BY", "defName": "创建人", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "C8BE2C7A-8251-4ADD-BB4F-411C5754DA62", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "CREATED_TIME", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "4E471FD6-3E73-4A90-B660-51598A482409", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "UPDATED_BY", "defName": "更新人", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0DC24AA9-4CD0-45D8-95CF-FA546BE343AB", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "UPDATED_TIME", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "09F64AC4-4DEE-428F-AF64-4C103884E1AC", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}], "entityInitProperties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}}, "javaHome": "", "sql": {"delimiter": ""}, "dataTypeSupports": [{"defKey": "MYSQL", "id": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E"}, {"defKey": "ORACLE", "id": "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542"}, {"defKey": "SQLServer", "id": "BFC87171-C74F-494A-B7C2-76B9C55FACC9"}, {"defKey": "PostgreSQL", "id": "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022"}, {"defKey": "DB2", "id": "89504F5D-94BF-4C9E-8B2E-44F37305FED5"}, {"defKey": "DM", "id": "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307"}, {"defKey": "GaussDB", "id": "592C7013-143D-4E7B-AF64-0D7BF1E28230"}, {"defKey": "Kingbase", "id": "77BD85E5-9D0D-4096-8427-CBA306FC9C6A"}, {"defKey": "GBase", "id": "56F4B55B-F0B8-4049-9E6B-50B95C1D793A"}, {"defKey": "MaxCompute", "id": "11D1FB71-A587-4217-89BA-611B8A1F83E0"}, {"defKey": "SQLite", "id": "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1"}, {"defKey": "Hive", "id": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2"}, {"defKey": "JAVA", "id": "797A1496-D649-4261-89B4-544132EC3F36"}, {"defKey": "JavaMybatis", "id": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B"}, {"defKey": "JavaMybatisPlus", "id": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073"}, {"defKey": "C#", "id": "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30"}, {"defKey": "Golang", "id": "B91D99E0-9B7C-416C-8737-B760957DAF09"}, {"defKey": "Rust", "id": "BDF457FD-9F98-4AC3-A705-7587B00A3BAB"}, {"defKey": "<PERSON>", "id": "483F9346-C99E-4014-A1D2-A554606BD8A3"}, {"defKey": "HighGo", "id": "ABF5836C-0B7C-4007-A41C-F869325E5842"}], "codeTemplates": [{"type": "appCode", "applyFor": "797A1496-D649-4261-89B4-544132EC3F36", " JpaBean": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@Table(name=\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    {{? field.primaryKey }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"type": "appCode", "applyFor": "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30", "Default": "using System;\nusing System.Collections.Generic;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n}}\n/*\n * <AUTHOR> http://www.chiner.com.cn\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace PDManer.Application\n{\n    public partial class {{=it.func.camel(it.entity.defKey,true) }}\n    {\n    \n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}} { get; set; }\n        $blankline\n        {{~}}\n        \n    }\n}", "SqlSugar": "using System;\nusing System.Collections.Generic;\nusing SqlSugar;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    var sqlSugartable='[SugarTable(\"{{=it.entity.defKey}}\", TableDescription = \"{{=it.func.join(it.entity.defName,it.entity.comment,';')}}\")]';\n}}\n/*\n * <AUTHOR> <EMAIL>\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace Model.DBModel\n{\n    /// <summary>\n    /// {{=it.func.join(it.entity.defName,it.entity.comment,';')}}\n    /// </summary>\n    {{=sqlSugartable}}\n    public class {{=it.entity.defKey}}\n    {\n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        {{? field.primaryKey }}\n        [SugarColumn(IsIdentity = true, IsPrimaryKey = true)]\n        {{?}}\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}}{ get; set; }\n        $blankline\n        {{~}}\n    }\n}"}, {"applyFor": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<Page<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        return ResponseEntity.ok({{=serviceVarName}}.paginQuery({{=beanVarName}}, pageRequest));\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.queryById({{=pkVarName}});\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        long total = {{=mapperName}}.count({{=beanVarName}});\n        return new PageImpl<>({{=mapperName}}.queryAllByLimit({{=beanVarName}}, pageRequest), pageRequest, total);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.update({{=beanVarName}});\n        return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\nimport java.util.List;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.data.domain.Pageable;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询指定行数据\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @param pageable 分页对象\n     * @return 对象列表\n     */\n    List<{{=beanClass}}> queryAllByLimit({{=beanClass}} {{=beanVarName}}, @Param(\"pageable\") Pageable pageable);\n\n    /** \n     * 统计总行数\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @return 总行数\n     */\n    long count({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int insert({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 批量新增数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 批量新增或按主键更新数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertOrUpdateBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 影响行数\n     */\n    int deleteById({{=pkDataType}} {{=pkVarName}});\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n    <resultMap type=\"{{=pkgName}}.entity.{{=beanClass}}\" id=\"{{=beanClass}}Map\">\n    {{~it.entity.fields:field:index}}\n        <result property=\"{{=it.func.camel(field.defKey,false)}}\" column=\"{{=field.defKey}}\" jdbcType=\"{{=field.type}}\"/>\n    {{~}}\n    </resultMap>\n    $blankline\n    <!-- 通过ID查询单条数据 -->\n    <select id=\"queryById\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </select>\n    $blankline\n    <!--分页查询指定行数据-->\n    <select id=\"queryAllByLimit\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n        limit #{pageable.offset}, #{pageable.pageSize}\n    </select>\n    $blankline\n    <!--统计总行数-->\n    <select id=\"count\" resultType=\"java.lang.Long\">\n        select count(1)\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n    </select>\n    $blankline\n    <!--新增数据-->\n    <insert id=\"insert\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values ({{=it.entity.fields.map(function(e,i){return '#{'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n    </insert>\n    $blankline\n    <!-- 批量新增数据 -->\n    <insert id=\"insertBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n    </insert>\n    $blankline\n    <!-- 批量新增或按主键更新数据 -->\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n        on duplicate key update\n        {{=it.entity.fields.map(function(e,i){return e.defKey + '=values('+e.defKey+')'}).join(',\\n\\t\\t')}}\n    </insert>\n    $blankline\n    <!-- 更新数据 -->\n    <update id=\"update\">\n        update {{=it.entity.defKey}}\n        <set>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}},\n            </if>\n        {{~}}\n        </set>\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </update>\n    $blankline\n    <!--通过主键删除-->\n    <delete id=\"deleteById\">\n        delete from {{=it.entity.defKey}} where {{=pkField}} = #{{{=pkVarName}}}\n    </delete>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport java.util.List;\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<PageImpl<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        //1.分页参数\n        long current = pageRequest.getPageNumber();\n        long size = pageRequest.getPageSize();\n\n        //2.分页查询\n        /*把Mybatis的分页对象做封装转换，MP的分页对象上有一些SQL敏感信息，还是通过spring的分页模型来封装数据吧*/\n        com.baomidou.mybatisplus.extension.plugins.pagination.Page<{{=beanClass}}> pageResult = {{=serviceVarName}}.paginQuery({{=beanVarName}}, current,size);\n\n        //3. 分页结果组装\n        List<{{=beanClass}}> dataList = pageResult.getRecords();\n        long total = pageResult.getTotal();\n        PageImpl<{{=beanClass}}> retPage = new PageImpl<{{=beanClass}}>(dataList,pageRequest,total);\n        return ResponseEntity.ok(retPage);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkFieldKey = \"UNDEFINED\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkFieldKey = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport cn.hutool.core.util.StrUtil;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;\n\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.selectById({{=pkVarName}});\n    }\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size){\n        //1. 构建动态查询条件\n        LambdaQueryWrapper<{{=beanClass}}> queryWrapper = new LambdaQueryWrapper<>();\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            queryWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n\n        //2. 执行分页查询\n        Page<{{=beanClass}}> pagin = new Page<>(current , size , true);\n        IPage<{{=beanClass}}> selectResult = {{=mapperName}}.selectByPage(pagin , queryWrapper);\n        pagin.setPages(selectResult.getPages());\n        pagin.setTotal(selectResult.getTotal());\n        pagin.setRecords(selectResult.getRecords());\n\n        //3. 返回结果\n        return pagin;\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        //1. 根据条件动态更新\n        LambdaUpdateChainWrapper<{{=beanClass}}> chainWrapper = new LambdaUpdateChainWrapper<{{=beanClass}}>({{=mapperName}});\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            chainWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n        //2. 设置主键，并更新\n        chainWrapper.set({{=beanClass}}::get{{=pkVarNameU}}, {{=beanVarName}}.get{{=pkVarNameU}}());\n        boolean ret = chainWrapper.update();\n        //3. 更新成功了，查询最最对象返回\n        if(ret){\n            return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n        }else{\n            return {{=beanVarName}};\n        }\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\n\nimport com.baomidou.mybatisplus.core.conditions.Wrapper;\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.core.toolkit.Constants;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper  extends BaseMapper<{{=beanClass}}>{\n    /** \n     * 分页查询指定行数据\n     *\n     * @param page 分页参数\n     * @param wrapper 动态查询条件\n     * @return 分页对象列表\n     */\n    IPage<{{=beanClass}}> selectByPage(IPage<{{=beanClass}}> page , @Param(Constants.WRAPPER) Wrapper<{{=beanClass}}> wrapper);\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n$blankline\n\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n     <select id=\"selectByPage\" resultType=\"{{=pkgName}}.entity.{{=beanClass}}\">\n        select * from user ${ew.customSqlSegment}\n    </select>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@TableName(\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    {{? field.primaryKey }}\n    @TableId\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    `{{=field.defKey}}` {{?field.autoIncrement}}INT AUTO_INCREMENT{{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }}{{?}} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT = '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX IF EXISTS {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}\n", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('ALTER TABLE '+before.defKey+' RENAME TO '+after.defKey);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldAdded) { \n            let ddlItem = 'ADD COLUMN `'+field.defKey+'` '+field.dbType;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            if(field.autoIncrement){\n                ddlItem += ' AUTO_INCREMENT';\n            }\n            if(field.defaultValue){\n                ddlItem += (' DEFAULT ' + field.defaultValue);\n            }\n            ddlItem += (' COMMENT \\''+field.defName+';'+field.comment+'\\'');\n            \n            if(field.index>0 && field.afterFieldKey){\n                ddlItem += (' AFTER '+field.afterFieldKey);\n            }\n            ret.push(ddlItem);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldRemoved) { \n            ret.push('DROP '+field.defKey);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' MODIFY COLUMN `'+after.defKey+'`');\n            }else{\n                changeDDL += (' CHANGE COLUMN `'+before.defKey+'` `'+after.defKey+'`');\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            let defaultValue = '';\n            if(after.defaultValue != null && after.defaultValue.length>0){\n                defaultValue = (after.defaultValue);\n            }else{\n                defaultValue = 'NULL';\n            }\n            if(defaultValue != 'NULL'){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n\n            let comment = after.defName;\n            if(after.comment){\n                comment = comment + ';' + (after.comment||'');\n            }\n            if(comment){\n                changeDDL += (' COMMENT \\''+comment+'\\';');\n            }\n            \n            ret.push(firstDDL+' '+changeDDL);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542", "type": "dbDDL", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{?field.autoIncrement}}NUMBER(11) generated by default as IDENTITY, {{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}{{?}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* -------------------------------------------------- */\n创建表：\n{{~ createEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* -------------------------------------------------- */\n删除表：\n{{~ dropEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* -------------------------------------------------- */\n修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n    {{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n    {{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}\n    {{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('\\n\\t建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('\\n\\t解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}\n{{=indexChanged?'\\n\\t更改了索引':''}}\n{{=changed?'\\n\\t更改了属性':''}}\n{{=relaArray.length>0?relaArray.join(''):''}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD (${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ddlItem += ')';\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            changeDDL += ('MODIFY ('+after.defKey+'');\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            changeDDL += ')';\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "BFC87171-C74F-494A-B7C2-76B9C55FACC9", "type": "dbDDL", "createTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U')) DROP TABLE [dbo].[{{=it.entity.defKey}}];\n\nCREATE TABLE [dbo].[{{=it.entity.defKey}}](\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{?field.autoIncrement}}INT IDENTITY(1,1) {{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}{{?}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}EXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, null, null;{{?}}\n{{~it.entity.fields:field:index}}\nEXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(field.defName,field.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, 'column', {{=field.defKey}};\n{{~}}\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U')) DROP TABLE [dbo].[{{=it.entity.defKey}}];", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`EXEC sp_rename '${before.defKey}','${after.defKey}'`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `IF ((SELECT COUNT(*) FROM ::fn_listextendedproperty('MS_Description','SCHEMA', 'dbo','TABLE', '${after.defKey}', NULL, NULL)) > 0)\n            \\n\\tEXEC sp_updateextendedproperty 'MS_Description', '${commentText}','SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            \\nELSE\n            \\n\\tEXEC sp_addextendedproperty 'MS_Description', '${commentText}', 'SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            `;\n            ret.push(myText);\n            /*ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');*/\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD [${field.defKey}] ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `EXEC sp_addextendedproperty 'MS_Description', N'${commentText}','SCHEMA', N'dbo','TABLE', N'${entity.data.baseInfo.defKey}','COLUMN', N'${field.defKey}'`;\n                ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN [${field.defKey}]`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' ALTER COLUMN ['+after.defKey+']');\n            }else{\n                let renameText = `EXEC sp_rename '[dbo].[${entity.data.baseInfo.defKey}].[${before.defKey}]','${after.defKey}','COLUMN';`;\n                ret.push(renameText);\n                continue;\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{? field.autoIncrement}}SERIAL{{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD COLUMN ${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }            \n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            if(before.dbType !== after.dbType || before.len !== after.len || before.scale !== after.scale){\n                let dbTypeDDL = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${after.defKey} TYPE ${before.dbType}`;\n                if(after.len>0){\n                    dbTypeDDL += ('('+after.len);\n                    if(parseInt(after.scale)>0){\n                        dbTypeDDL += (','+after.scale);\n                    }\n                    dbTypeDDL += ')';\n                }\n                ret.push(dbTypeDDL+';');\n            }\n            \n            if(before.defaultValue !== after.defaultValue){\n                let defaultDDL = '';\n                let defaultValue = after.defaultValue;\n                defaultValue = (defaultValue==null)?\"NULL\":(\"\"+defaultValue);\n                if(defaultValue.length>0){\n                    defaultDDL += ('SET DEFAULT ' + defaultValue);\n                }\n                let defaultTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${defaultDDL};`;\n                ret.push(defaultTpl);\n            }\n            \n            if(before.notNull !== after.notNull){\n                let notNullDDL= 'SET NULL';\n                if(after.notNull){\n                    let notNullDDL= 'SET NOT NULL';\n                }\n                let notNullTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${notNullDDL};`;\n                ret.push(notNullTpl);\n            }\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n-- 索引重建\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "dbDDL", "applyFor": "89504F5D-94BF-4C9E-8B2E-44F37305FED5", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{?field.autoIncrement}}DECIMAL(17) GENERATED ALWAYS AS IDENTITY(START WITH 1 INCREMENT BY 1),{{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}{{?}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"applyFor": "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{?field.autoIncrement}}INT IDENTITY(1,1) {{??}}{{=field.dbType}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{?}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "", "message": "", "update": ""}, {"type": "dbDDL", "applyFor": "592C7013-143D-4E7B-AF64-0D7BF1E28230", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' AUTO_INCREMENT' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "77BD85E5-9D0D-4096-8427-CBA306FC9C6A", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"applyFor": "11D1FB71-A587-4217-89BA-611B8A1F83E0", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}, {"applyFor": "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTOINCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }} --{{=it.func.join(field.defName,field.comment,';')}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  ; --{{=it.func.join(it.entity.defName,it.entity.comment,';') }}\n$blankline\n", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "dictSQLTemplate", "content": "/* 插入字典总表[{{=it.dict.defKey}}-{{=it.dict.defName}}] */\nINSERT INTO SYS_DICT(KEY_,LABEL,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=it.dict.defName}}','{{=it.dict.intro}}',1);\n/* 插入字典明细表 */\n{{~it.dict.items:item:index}}\nINSERT INTO SYS_DICT_ITEM(DICT_KEY,KEY_,LABEL,SORT_,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=item.defKey}}','{{=item.defName}}','{{=item.sort}}','{{=item.intro}}',1);\n{{~}}"}, {"applyFor": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2", "type": "dbDDL", "createTable": "/**字段名,关键字等全部用的小写*/\ndrop table if exists {{=it.entity.defKey}};\n/**补充上库名,external关键字根据建表规范看是否添加*/\ncreate [external] table if not exists {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n/**这里把varchar,char,text,date,datetime字段全部映射为string类型.tinyint unsigned,bit,Integer,tinyint,smallint,mediumint映射为int类型,int unsigned映射为bigint.其它自定义映射规则根据自己情况修改*/\n/**当长度>0只有为decimal类型或double类型时才保留长度和小数的位数*/\n{{~it.entity.fields:field:index}}\n    {{=it.func.lowerCase(field.defKey)}} {{=it.func.lowerCase(field.type)=='varchar'||it.func.lowerCase(field.type)=='char'||it.func.lowerCase(field.type)=='text'||it.func.lowerCase(field.type)=='date'||it.func.lowerCase(field.type)=='datetime' ? 'string':it.func.lowerCase(field.type)=='tinyint unsigned'||it.func.lowerCase(field.type)=='bit'||it.func.lowerCase(field.type)=='integer'||it.func.lowerCase(field.type)=='tinyint'||it.func.lowerCase(field.type)=='smallint'||it.func.lowerCase(field.type)=='mediumint' ? 'int':it.func.lowerCase(field.type)=='int unsigned' ? 'bigint':it.func.lowerCase(field.type)}}{{?field.len>0&&(it.func.lowerCase(field.type)=='decimal'||it.func.lowerCase(field.type)=='double')}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{=')'}}{{?}}{{?}} comment '{{=it.func.join(field.defName,field.comment,'')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n{{?}}\n)\n{{\n    let partitionedBy = it.entity.properties['partitioned by'];\n    partitionedBy = partitionedBy?partitionedBy:'请在扩展属性中配置[partitioned by]属性';\n}}\ncomment '{{=it.func.join(it.entity.defName,';') }}'\n/**是否分区表,分区字段名和字段注释自定义*/\n[partitioned by {{=partitionedBy}}]\n/**文件存储格式自定义*/\n[stored as orc]\n/**hdfs上的地址自定义*/\n[location xxx]\n;", "createView": "", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}, {"applyFor": "B91D99E0-9B7C-416C-8737-B760957DAF09", "type": "appCode", "content": "{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1<10?\"0\"+today.getMonth():today.getMonth();\n    var days=today.getDate()<10?\"0\"+today.getDate():today.getDate();\n    var hours = today.getHours()<10?\"0\"+today.getHours():today.getHours();         \n\tvar minutes = today.getMinutes()<10?\"0\"+today.getMinutes():today.getMinutes();      \n\tvar seconds = today.getSeconds()<10?\"0\"+today.getSeconds():today.getSeconds();    \n}}\n// Package models  {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}\n// author : http://www.liyang.love\n// date : {{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\n// desc : {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}\npackage models\n\n$blankline\n\n// {{=it.func.camel(it.entity.defKey,true) }}  {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}。\n// 说明:{{=it.entity.comment}}\n// 表名:{{=it.entity.defKey}}\n// group: {{=it.func.camel(it.entity.defKey,true) }}\n// obsolete:\n// appliesto:go 1.8+;\n// namespace:hongmouer.his.models.{{=it.func.camel(it.entity.defKey,true) }}\n// assembly: hongmouer.his.models.go\n// class:HongMouer.HIS.Models.{{=it.func.camel(it.entity.defKey,true) }}\n// version:{{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\ntype {{=it.func.camel(it.entity.defKey,true) }} struct {\n    {{~it.entity.fields:field:index}}\n    {{=formatGoLang(it.func.camel(field.defKey,true),null,field,it.entity.fields,null,1)}} {{=formatGoLang(field.type,\"type\",field,it.entity.fields,10,3)}}  `gorm:\"column:{{=field.primaryKey?\"primaryKey;\":\"\"}}{{=field.defKey}}\" json:\"{{=it.func.camel(field.defKey,true)}}\"` {{=formatGoLang(\"gorm:column:\"+field.defKey+\" json:\"+it.func.camel(field.defKey,true),null,field,it.entity.fields,null,2)}}  //type:{{=formatGoLang(field.type,\"type\",field,it.entity.fields,null,3)}}  comment:{{=formatGoLang(it.func.join(field.defName,field.comment,';'),\"defName\",field,it.entity.fields,null,4)}}  version:{{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\n    {{~}}\n}\n\n\n$blankline\n// TableName 表名:{{=it.entity.defKey}}，{{=it.entity.defName}}。\n// 说明:{{=it.entity.comment}}\nfunc (ZentaoUserInfo) TableName() string {\n\treturn \"{{=it.entity.defKey}}\"\n}\n\n{{\n\nfunction formatGoLang(str, fieldName, field, fileds, emptLength, isFiled) {\n    var maxLength = 0;\n\n    if (isFiled == 1) {\n        for (var i = 0; i < fileds.length; i++) {\n            if (getBlength(it.func.camel(fileds[i].defKey, true)) > maxLength) {\n                maxLength = getBlength(it.func.camel(fileds[i].defKey, true)) + 2;\n            }\n        }\n    } else if (isFiled == 2) {\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = \"gorm:column:\" + fileds[i].defKey + \" json:\" + it.func.camel(fileds[i].defKey, true);\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 2;\n            }\n        }\n        var empt = \"\";\n        var strLength = getBlength(str);\n        if (field.primaryKey) {\n            strLength += getBlength(\"primaryKey;\");\n        }\n        for (var j = 0; j < maxLength - strLength; j++) {\n            empt += ' ';\n        }\n        return empt;\n    } else if (isFiled == 3) {\n        /*获取某个字段的最大长度*/\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = eval(\"fileds[\" + i + \"].\" + fieldName);\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 1;\n            }\n        }\n    } else if (isFiled == 4) {\n        /*获取某个字段的最大长度*/\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = fileds[i].comment + \";\" + fileds[i].defName;\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 1;\n            }\n        }\n    }\n    else {\n        maxLength = emptLength;\n    }\n\n    var strLength = getBlength(str);\n    for (var j = 0; j < maxLength - strLength; j++) {\n        str += ' ';\n    }\n    return str;\n}\n\nfunction getBlength(str) {\n    var n = 0;\n    for (var i = str.length; i--;) {\n        n += str.charCodeAt(i) > 255 ? 2 : 1;\n    }\n    return n;\n} \n\n}}"}, {"applyFor": "BDF457FD-9F98-4AC3-A705-7587B00A3BAB", "type": "appCode", "struct": "use chrono::{DateTime, Local};\nuse serde::{Deserialize, Serialize};\n$blankline\n/// {{=it.entity.defName}}\n#[derive(Serialize, Deserialize, Debug, Clone)]\n{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    \n}}\npub struct {{=beanClass}} {\n    {{~it.entity.fields:field:index}}\n    {{\n        let fieldDateType = field.type;\n        if(!field.notNull){\n            fieldDateType = 'Option<'+fieldDateType+'>';\n        }\n    }}/// {{=field.defName}}\n    pub {{=it.func.camel(field.defKey,false)}}: {{=fieldDateType}},\n    {{~}}\n}\n"}, {"applyFor": "56F4B55B-F0B8-4049-9E6B-50B95C1D793A", "type": "dbDDL", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* -------------------------------------------------- */\n创建表：\n{{~ createEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* -------------------------------------------------- */\n删除表：\n{{~ dropEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* -------------------------------------------------- */\n修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n    {{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n    {{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}\n    {{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('\\n\\t建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('\\n\\t解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}\n{{=indexChanged?'\\n\\t更改了索引':''}}\n{{=changed?'\\n\\t更改了属性':''}}\n{{=relaArray.length>0?relaArray.join(''):''}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD (${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ddlItem += ')';\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            changeDDL += ('MODIFY ('+after.defKey+'');\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            changeDDL += ')';\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "483F9346-C99E-4014-A1D2-A554606BD8A3", "type": "dbDDL", "createTable": "{{let dorisDistributedBy = it.entity.properties['dorisDistributedBy'];\n    dorisDistributedBy = dorisDistributedBy?dorisDistributedBy:'请在表的扩展属性中配置[dorisDistributedBy]属性';\n}}CREATE TABLE IF NOT EXISTS  {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    `{{=field.defKey}}` {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : '' }}\n{{~}}\n)  COMMENT '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}'\n{{=dorisDistributedBy}} ;\n$blankline\n", "createView": "", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "", "deleteIndex": "", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}\n", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('ALTER TABLE '+before.defKey+' RENAME TO '+after.defKey);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldAdded) { \n            let ddlItem = 'ADD COLUMN `'+field.defKey+'` '+field.dbType;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            if(field.autoIncrement){\n                ddlItem += ' AUTO_INCREMENT';\n            }\n            if(field.defaultValue){\n                ddlItem += (' DEFAULT ' + field.defaultValue);\n            }\n            ddlItem += (' COMMENT \\''+field.defName+';'+field.comment+'\\'');\n            \n            if(field.index>0 && field.afterFieldKey){\n                ddlItem += (' AFTER '+field.afterFieldKey);\n            }\n            ret.push(ddlItem);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldRemoved) { \n            ret.push('DROP '+field.defKey);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' MODIFY COLUMN `'+after.defKey+'`');\n            }else{\n                changeDDL += (' CHANGE COLUMN `'+before.defKey+'` `'+after.defKey+'`');\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            let defaultValue = '';\n            if(after.defaultValue != null && after.defaultValue.length>0){\n                defaultValue = (after.defaultValue);\n            }else{\n                defaultValue = 'NULL';\n            }\n            if(defaultValue != 'NULL'){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n\n            let comment = after.defName;\n            if(after.comment){\n                comment = comment + ';' + (after.comment||'');\n            }\n            if(comment){\n                changeDDL += (' COMMENT \\''+comment+'\\';');\n            }\n            \n            ret.push(firstDDL+' '+changeDDL);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "ABF5836C-0B7C-4007-A41C-F869325E5842", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{? field.autoIncrement}}SERIAL{{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD COLUMN ${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }            \n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            if(before.dbType !== after.dbType || before.len !== after.len || before.scale !== after.scale){\n                let dbTypeDDL = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${after.defKey} TYPE ${before.dbType}`;\n                if(after.len>0){\n                    dbTypeDDL += ('('+after.len);\n                    if(parseInt(after.scale)>0){\n                        dbTypeDDL += (','+after.scale);\n                    }\n                    dbTypeDDL += ')';\n                }\n                ret.push(dbTypeDDL+';');\n            }\n            \n            if(before.defaultValue !== after.defaultValue){\n                let defaultDDL = '';\n                let defaultValue = after.defaultValue;\n                defaultValue = (defaultValue==null)?\"NULL\":(\"\"+defaultValue);\n                if(defaultValue.length>0){\n                    defaultDDL += ('SET DEFAULT ' + defaultValue);\n                }\n                let defaultTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${defaultDDL};`;\n                ret.push(defaultTpl);\n            }\n            \n            if(before.notNull !== after.notNull){\n                let notNullDDL= 'SET NULL';\n                if(after.notNull){\n                    let notNullDDL= 'SET NOT NULL';\n                }\n                let notNullTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${notNullDDL};`;\n                ret.push(notNullTpl);\n            }\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n-- 索引重建\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}], "generatorDoc": {"docTemplate": ""}, "relationFieldSize": "15", "uiHint": [{"defKey": "Input", "defName": "普通输入框", "id": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "Select", "defName": "下拉输入框", "id": "FB111359-2B73-4443-926C-08A98E446448"}, {"defKey": "CheckBox", "defName": "复选框", "id": "0CB8A6C9-1115-4FC0-B51E-5C028065082F"}, {"defKey": "RadioBox", "defName": "单选框", "id": "5C04987A-260F-4B7C-A5D5-22A181AAE9CA"}, {"defKey": "Double", "defName": "小数输入", "id": "8D5BAFE4-E15C-4707-A047-8EE59C58E70F"}, {"defKey": "Integer", "defName": "整数输入", "id": "9999AF2A-A44E-415C-A2DC-D7C613BD0073"}, {"defKey": "Money", "defName": "金额输入", "id": "2B0C3D0C-7BAF-4B36-81AD-9362B5E5DC2E"}, {"defKey": "Date", "defName": "日期输入", "id": "E4D94E14-F695-487F-AFC2-4D888009B7DA"}, {"defKey": "DataYearMonth", "defName": "年月输入", "id": "936927E3-DD2D-4096-87FD-074CDE278D59"}, {"defKey": "Text", "defName": "长文本输入", "id": "D89DD4F1-ADAC-4469-BF8D-B3FF41AE7963"}, {"defKey": "RichText", "defName": "富文本输入", "id": "C134EB1F-4CFF-49E0-882F-2C6FB275CB20"}], "headers": [{"refKey": "def<PERSON><PERSON>", "hideInGraph": false, "value": "字段代码", "freeze": false}, {"refKey": "defName", "hideInGraph": false, "value": "显示名称", "freeze": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false, "value": "主键", "freeze": false}, {"refKey": "notNull", "hideInGraph": true, "value": "不为空", "freeze": false}, {"refKey": "autoIncrement", "hideInGraph": true, "value": "自增", "freeze": false}, {"refKey": "domain", "hideInGraph": true, "value": "数据域", "freeze": false}, {"refKey": "type", "hideInGraph": false, "value": "数据类型", "freeze": false}, {"refKey": "len", "hideInGraph": false, "value": "长度", "freeze": false}, {"refKey": "scale", "hideInGraph": false, "value": "小数位数", "freeze": false}, {"refKey": "comment", "hideInGraph": true, "value": "说明", "freeze": false}, {"refKey": "refDict", "hideInGraph": true, "value": "数据字典", "freeze": false}, {"refKey": "defaultValue", "hideInGraph": true, "value": "默认值", "freeze": false}, {"refKey": "isStandard", "hideInGraph": false, "value": "标准字段", "enable": false, "freeze": false}, {"refKey": "uiHint", "hideInGraph": true, "value": "UI建议", "enable": true, "freeze": false}, {"refKey": "extProps", "hideInGraph": true, "value": "拓展属性", "enable": true, "freeze": false}, {"refKey": "attr1", "value": "属性1", "hideInGraph": true, "enable": true, "freeze": false}, {"refKey": "attr2", "value": "属性2", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr3", "value": "属性3", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr4", "value": "属性4", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr5", "value": "属性5", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr6", "value": "属性6", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr7", "value": "属性7", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr8", "value": "属性8", "hideInGraph": true, "enable": false, "freeze": false}, {"refKey": "attr9", "value": "属性9", "hideInGraph": true, "enable": false, "freeze": false}], "modelType": "modalGroup", "recentColors": ["#d148d1", "#ce4bce", "#831b83", "#dd31dd", "#da2fda", "#e988e9", "#000000", "#DDE5FF"], "DDLToggleCase": "L", "menuWidth": "322px"}, "entities": [{"id": "CC1C655C-5F2C-4356-92B6-E41E089BA194", "defKey": "flow_template", "defName": "流程模版", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "模版名", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "流程说明", "comment": "", "domain": "3E948CEC-3070-472C-AF92-F3CA11EC9D15", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_schema", "defName": "流程设计整体结构", "comment": "", "domain": "", "type": "JSON", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_status", "defName": "状态", "comment": "1 正常 999 停止", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "8C10790D-F2A5-4271-A085-EF567C0AE48E"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "387E663A-4347-43A2-8DC2-EA750666EEDE", "defKey": "form_template", "defName": "表单模板", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": null, "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": null, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D2C45770-865F-4A91-89F5-43BB28116D3A", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_code", "defName": "表单编号", "comment": "", "type": "VARCHAR", "len": 36, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "18E7D75B-F14B-4393-A505-CEA0D2884172"}, {"defKey": "form_table_name", "defName": "表单绑定的Table名", "comment": "", "type": "VARCHAR", "len": 64, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "DD84CD50-AAD5-4BF2-B913-AD4BBB29B4D0"}, {"defKey": "form_title", "defName": "表单名", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "CE228C78-E01D-4F62-AABF-558BB89DFC3F"}, {"defKey": "form_type", "defName": "表单类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "4607B6CE-938B-41D3-9D10-4297C05A2C73"}, {"defKey": "form_desc", "defName": "表单介绍", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "EF573012-D0A3-4F0C-AC97-123F1700A21F"}, {"defKey": "form_schema", "defName": "低代码的原始结构", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "71213D16-AE7B-44AB-B74A-37B8E05B8AE9"}, {"defKey": "form_columns", "defName": "低代码获取的字段列表", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "9200958B-DAD4-47F8-B44B-F6D99D875E48"}, {"defKey": "history_columns", "defName": "历史删除的字段列表", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "A777D19F-087D-45C8-A3D0-F13DBE82B7D6"}, {"defKey": "table_design", "defName": "列表设计", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "B0502713-202D-4879-9376-C2DEE13F98A0"}, {"defKey": "open_flow", "defName": "开启审核流程", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "3AC4E6BA-B5B2-48DB-B6DB-6B6CD778C0E0"}, {"defKey": "flow_template_id", "defName": "绑定的流程模板id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "E87A90BF-1456-47EE-AAFA-5BFA552654FE"}, {"defKey": "open_log", "defName": "开启操作日志", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "686E29D1-81AD-4517-81CF-22D0C49FF30E"}, {"defKey": "allow_custom_audit", "defName": "允许增加自定义审核人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "21313AA9-272A-4ED5-B73A-C7BD15FBCC66"}, {"defKey": "allow_custom_cc", "defName": "允许增加自定义抄送人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "8BFB4E1F-340D-49D6-A1EE-D9157EDE1D6E"}, {"defKey": "flow_code", "defName": "绑定的审核流程编号", "comment": "", "type": "VARCHAR", "len": 36, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "CBA9152D-A612-476B-82F3-0459D245BB2F"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": null, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "CFDF2960-DCAB-40C5-9CDF-A0A9DC7EA60E", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": null, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "23E04F97-DA8E-415E-B218-A3CBD1FEF92A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": null, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "075776C6-B612-4F64-87C5-D6D12EEC09EB", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": null, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "FB7AE3A0-5D08-43D5-8B74-826FAC18172B", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": null, "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": null, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E8E7E8A5-E3C2-417B-9004-D7DCB3E548B0", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "db_status", "defName": "数据表状态", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "21F3F38B-1C46-4A5A-AEDD-02AC76D55C86"}, {"defKey": "permissions", "defName": "权限设置", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "89A20225-4DF9-453A-A7DB-46357018819E"}, {"defKey": "with_tables", "defName": "关联表信息", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "1B4EDF61-6772-4CD4-903B-8FBFF502EC6B"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "type": "P", "notes": {}}, {"id": "2D5DD4E9-F409-49B0-8911-04BA56EC6220", "defKey": "sys_dept", "defName": "部门表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "部门id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "322F2120-C60C-4831-8D16-2718DACA4E7A", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "parent_id", "defName": "父部门id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "330C9F4F-5B26-4EC9-97FE-B6F3225AE77C", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "ancestors", "defName": "祖级列表", "comment": "", "domain": "", "type": "VARCHAR", "len": 500, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "FC57FDA1-06EB-4372-8E53-DE6E3C7CB119", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "dept_name", "defName": "部门名称", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "9F8156E8-C618-4B42-8D3E-0C8A944EA664", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "order_num", "defName": "显示顺序", "comment": "", "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "77580C20-6BAD-423D-9F73-EF474968259B", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "leader", "defName": "负责人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "83C31DFD-26F4-4BDB-907B-DADD58C94EE4", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "phone", "defName": "联系电话", "comment": "", "domain": "", "type": "VARCHAR", "len": 50, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "CE40BCBC-6A9D-438A-8C6C-BFB5B2B3C917", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "email", "defName": "邮箱", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0096C1BA-1C94-46E8-937E-43DBC8611FC5", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "remark", "defName": "备注", "comment": "", "domain": "", "type": "VARCHAR", "len": 500, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "6EB22387-8EF2-4C12-8308-8D42749D1D6A", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "status", "defName": "部门状态（0正常 1停用）", "comment": "", "domain": "", "type": "TINYINT UNSIGNED", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "27A230C2-14D0-4D01-B357-A575136D58FC", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "75CF3AB7-15CE-475E-BC28-755B3FDF5D81", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_by", "defName": "修改人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A7583D72-C866-46DE-AD90-CD63F5A4BD8D", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E33C8D81-A576-4059-80AA-B063C4203447", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "修改时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F788C9B-C23F-47A0-989A-94AC0FA24F7F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "9B488FAE-4752-4E5C-B2EF-B980FCDF118C", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1DEFBCDF-0C7A-4005-989F-89211E41F0B0", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "3FDB8EE0-9321-4793-9E25-2F828F27162E", "defKey": "sys_role", "defName": "角色表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B90D61A7-508C-4164-870A-3F007550E751", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "code", "defName": "编码", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "67953906-9A6F-445E-965A-49704C0FFC2B", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "status", "defName": "状态", "comment": "0:禁用", "domain": "", "type": "TINYINT UNSIGNED", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "85D78E57-8F0F-41C5-823F-DCD1B5087F7B", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "list_order", "defName": "排序", "comment": "", "domain": "", "type": "INT UNSIGNED", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "469346FE-A8AE-44DC-80AA-5CD7794FEF33", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "角色名称", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "AA8D91E1-9353-4274-B641-EE07BC766E1B", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "permissions", "defName": "角色权限", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "7832D624-2DCA-456B-BD62-150F9FA804E5", "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "remark", "defName": "备注", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1CC1B1FA-0B78-43F9-83DA-2C28B861A590", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "6EF20BC0-76CB-4F6F-A774-D7B570FED54B", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E6ACF725-D658-40A3-91DD-8298333B893E", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "6BA90048-AC01-45B4-AFE9-55CAF4E0DE52", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "3FEFA0E4-5E9D-4083-839F-174C2914F87F", "defKey": "sys_user", "defName": "系统用户表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A0BFD7A9-80C3-4284-BC87-1770DF0C0009", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "code", "defName": "员工编号", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "117BC060-5567-4658-B582-4E368D8F9018", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "system_account", "defName": "系统账号", "comment": "", "domain": "", "type": "VARCHAR", "len": 50, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "********-9B0A-4010-BC45-BA7521D0E322", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "username", "defName": "用户名", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D001B860-1DE0-4175-91D3-D36F9D5EAF4D", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "avatar", "defName": "用户头像", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "49290723-A303-4849-A2C8-37B7736E662A"}, {"defKey": "email", "defName": "邮箱", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "11AC3E46-0D28-402D-A396-985075537350", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "contact_phone", "defName": "联系电话", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "7CCD671B-CD79-4795-9E4D-5A5FB775A890", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "id_number", "defName": "身份证号码", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "53FF86B9-5A1E-4AB5-AA4A-5073EA69A8FA", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "hire_date", "defName": "入职日期", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "00B576C5-DEBC-466E-BF8A-82FC81DC8B27", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "resignation_date", "defName": "离职日期", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A6AB52F4-1F3F-4AB4-97D0-67870FF04968", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "status", "defName": "状态", "comment": "", "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "C92366C7-8968-4AE1-AFB4-372024A51004", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "with_depts", "defName": "所在部门", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "03F04A88-0339-4C2C-B641-FF6A73F17FE1"}, {"defKey": "with_posts", "defName": "所属岗位", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "8C245D3B-5B0E-4209-96B8-8A5E53F5DC32"}, {"defKey": "with_roles", "defName": "拥有的角色", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "4C0785B5-6471-4D01-8C09-73540FCBAEDA"}, {"defKey": "with_companys", "defName": "关联的公司", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "EDAA0343-1B1D-4EF3-8B19-4E96E16DDC14"}, {"defKey": "with_projects", "defName": "关联的项目", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "CBCD7E0E-30A8-4DDA-BF9B-0A242BA34508"}, {"defKey": "gender", "defName": "性别", "comment": "", "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "945CCDCF-0550-4B97-8A57-C42500D704E1", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "usersale", "defName": "用户盐", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "F5F34E70-BBC3-4CB0-A066-231843C082F8"}, {"defKey": "password", "defName": "密码", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "6E335FF0-8D97-40D9-AF25-29EDAFB76FC7", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "last_update_pwd_time", "defName": "最后一次修改密码的时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "3F1B3B60-B3B5-4587-B9B5-790A533CF01A"}, {"defKey": "password_changed", "defName": "密码是否已修改", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "C0AF7569-FEFC-4EDC-9967-5316AB9C4EE8"}, {"defKey": "birthday", "defName": "生日", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E95AAA4C-1B51-46BD-B245-778ABD9D4E4B", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "entry_date", "defName": "入职日期", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "2B5CDD64-CA14-496A-B6D5-EA425A3985E6"}, {"defKey": "regular_date", "defName": "转正日期", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "DDC4309A-8173-444B-8A0E-8F69C799057D"}, {"defKey": "leave_date", "defName": "离职日期", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "53A41D04-18A0-4C7C-8008-973A6736C168"}, {"defKey": "contact_address", "defName": "联系地址", "comment": "", "domain": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "85A4A200-8D59-4B8F-8EDE-918E091118F1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "描述信息", "comment": "", "domain": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D617EB11-5506-426E-AD28-ADBA5B0409A6", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "last_login_ip", "defName": "最后登录ip", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D7B760FD-A648-467F-9305-862F99F13EAA", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "last_login_time", "defName": "最后登录时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "52C7A305-FAF5-4108-926C-C43304804CF3", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D26ED6CC-A8C6-4FEC-8AF2-749F0A481C29", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "DEB3D61E-0EBA-4334-9D9A-EA4F2E28AE49", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A382F9F0-69EC-4718-80D8-381648211B68", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "F19E93A5-251D-40E9-A5EF-BBFEFB0B188A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "38E95D35-FF71-49BE-ADBC-EE350D521920", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "BBC46C7D-081F-427C-8A24-98518838C21A", "defKey": "sys_menu", "defName": "系统菜单表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "48374359-D9FC-42CE-9BED-300D52EC4AB2", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "code", "defName": "编号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "13AE893A-94F9-48F2-A6E6-E2AC92255628", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "parent_id", "defName": "父菜单id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "FD0A716E-3EA3-468A-B6B6-D7A20ADBD19E", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "route", "defName": "路由地址", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A050E1F5-EF74-465F-80CC-D91F5182C4DA", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "mark", "defName": "菜单标记", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "9A497F7E-6785-49A8-B444-460626D6BE3E"}, {"defKey": "title", "defName": "菜单标题", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "7138561D-E55D-4DD1-AD41-4ABEE682C62A", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "icon", "defName": "图标", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1E167E22-D1AC-4020-9560-9CEE07221A5C", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "sort_order", "defName": "排序字段", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A6E6D900-89E7-4D66-9F80-A3462212C540", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "remark", "defName": "备注", "comment": "", "domain": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "8058CC65-A835-4B81-B57F-5E0DBAC6A0E2", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "menu_type", "defName": "菜单类型", "comment": "", "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "EE6B15A1-F4A5-4526-82BF-F115D2FFA119", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "is_display", "defName": "是否显示", "comment": "", "domain": "", "type": "TINYINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "078B6CF6-8CC1-4B60-ADAC-10349C94ABE0", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "api_association", "defName": "关联的API", "comment": "", "domain": "", "type": "JSON", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "C00AFBBA-E4CF-4209-A6C1-780421B51D94", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B9112534-5268-45FA-A66F-B6E6820F7B02", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "703F8B16-AC1A-41DC-ADF6-952131F054C9", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "2A1D0628-7CF7-4DAB-84E3-C69A43935ED5", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "AF2E4B72-6EAA-48EB-8EE5-60DB1C11E0AF", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E628F9F2-3036-4CF4-975C-4C6AB3137588", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "route_type", "defName": "路由类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "96F71B5B-BE93-4BE9-9CEA-6B5620D835D7"}, {"defKey": "menu_form_template_id", "defName": "绑定的表单模板id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "50FE7ADF-C54C-4A1B-AD67-9E685C3A28F4"}, {"defKey": "menu_form_template_type", "defName": "绑定的表单模板页面类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "AF7C053D-78FE-4525-86FF-BA78684016F8"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "4BAD36BC-457D-4A3C-8F53-4BA3A8F93D8B", "defKey": "sys_user_dept", "defName": "用户部门关联表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "关联ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "EDC3D5CE-231C-4451-841A-797DF3D4D99F", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "user_id", "defName": "用户ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B5BF4419-6AEF-49B3-80C4-66EAF234C567", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "dept_id", "defName": "部门ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3E1E3013-50F7-4248-A78A-8D9326720B7B", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "F58691BC-3E7F-45F2-8630-7922DEE440AB", "defKey": "sys_post_role", "defName": "岗位角色关联", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "关联ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1DAE02AC-2244-4C48-BFDC-CBB2B1A16AC8", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "post_id", "defName": "岗位ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "152C150D-E1A2-4EFA-B7E3-87CC9531FF15", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "role_id", "defName": "角色ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B7181548-7487-453D-99E4-597E4B917490", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "B85E2A45-41AE-40E4-BF6E-4270AB336A34", "defKey": "sys_config", "defName": "系统配置", "comment": "用于记录某些系统变量的值", "properties": {}, "fields": [{"defKey": "id", "defName": "ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "EDC3D5CE-231C-4451-841A-797DF3D4D99F", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "config_key", "defName": "配置KEY", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B5BF4419-6AEF-49B3-80C4-66EAF234C567", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "config_value", "defName": "配置内容", "comment": "", "domain": "", "type": "VARCHAR", "len": 2000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3E1E3013-50F7-4248-A78A-8D9326720B7B", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "version", "defName": "版本", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "50CD7018-48A4-4D1C-8800-6C386F6ADA00"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "6A21E2FA-66F8-48FE-BA9C-D7E4A38FD92C", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "93C6040D-E0A2-41A7-9D6F-BF9D7F2B59B7", "defKey": "sys_post", "defName": "系统岗位表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "48374359-D9FC-42CE-9BED-300D52EC4AB2", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "parent_id", "defName": "父id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "FD0A716E-3EA3-468A-B6B6-D7A20ADBD19E", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "post_code", "defName": "岗位编号", "comment": "", "type": "VARCHAR", "len": 36, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "51CA5A02-387F-4F08-985F-527E1B6366AA"}, {"defKey": "post_name", "defName": "岗位名", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "89B069E9-DD1E-442B-8B74-0A8649559A69"}, {"defKey": "node_path", "defName": "岗位树节点路径", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "4E3A7D7C-B98D-4F61-A448-812D71DBE747"}, {"defKey": "list_order", "defName": "排序", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "A0994253-6C4E-4AE0-BCE5-8B353B17E0C2"}, {"defKey": "with_roles", "defName": "关联角色", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "A05C3EE2-349A-4C49-9901-844F4D1B1A8F"}, {"defKey": "remark", "defName": "备注", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0FF6DB29-35C8-4367-99FE-24EF5B7FF984", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B9112534-5268-45FA-A66F-B6E6820F7B02", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "703F8B16-AC1A-41DC-ADF6-952131F054C9", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "2A1D0628-7CF7-4DAB-84E3-C69A43935ED5", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "AF2E4B72-6EAA-48EB-8EE5-60DB1C11E0AF", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E628F9F2-3036-4CF4-975C-4C6AB3137588", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "F40B1F15-B03E-47FB-B294-D27A6CFF2D07", "defKey": "sys_user_role", "defName": "用户角色关联表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "关联ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1DAE02AC-2244-4C48-BFDC-CBB2B1A16AC8", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "user_id", "defName": "用户ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "152C150D-E1A2-4EFA-B7E3-87CC9531FF15", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "role_id", "defName": "角色ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B7181548-7487-453D-99E4-597E4B917490", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "49B8753B-1170-4351-9212-C850806B320F", "defKey": "sys_user_post", "defName": "用户岗位关联表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "关联ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1DAE02AC-2244-4C48-BFDC-CBB2B1A16AC8", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "user_id", "defName": "用户ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "152C150D-E1A2-4EFA-B7E3-87CC9531FF15", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "post_id", "defName": "岗位ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B7181548-7487-453D-99E4-597E4B917490", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "CF9430B4-C3B8-4F65-A519-53E7BE3C4DEC", "defKey": "flow_instance", "defName": "流程实例", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_template_id", "defName": "流程模板id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_template_snap", "defName": "流程模板快照", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "44BA0F5F-3B40-4CA6-91D5-B18B37841552"}, {"defKey": "form_template_id", "defName": "绑定表单模板id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_title", "defName": "绑定表单模板标题", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "B293B0DA-3EDA-4C33-AD7D-B29FED2169F4"}, {"defKey": "form_template_snap", "defName": "绑定表单模板快照", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "A9A304FC-F507-4E57-A286-DD07B9A73249"}, {"defKey": "form_table_name", "defName": "绑定表单表名称", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E4BB9FA0-1285-4361-9EF9-FE4879C9092A", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_table_id", "defName": "绑定表单表id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_data_snap", "defName": "表单数据快照", "comment": "", "domain": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_extra_data", "defName": "表单工作流附加数据", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "4B59BFF9-3F63-43CC-B399-5EE678640A75"}, {"defKey": "current_steps", "defName": "当前正在进行的步骤", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "230D0084-EF80-4437-8A84-129A73C7E5BB"}, {"defKey": "finishd_steps", "defName": "已经完成的步骤ID", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "B000659B-23BA-4E77-96D2-0708CFC4BBBA"}, {"defKey": "flow_steps", "defName": "流程预生成的所有步骤", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "F03DE524-42A3-46DF-9CC7-27E328BF2441"}, {"defKey": "instance_state", "defName": "实例状态", "comment": "1 等待审批 2 审批中 3审批通过 4审批不通过 5 作废", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "B85546C0-92B4-40E5-AC8A-F111C11EB174"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "2F51A230-810E-4F7A-AB82-06C1C281F958"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "finishd_at", "defName": "完成时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "6C864B21-12FB-4932-9D66-BB0799176F42"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_instance_type", "defName": "流程实例类型", "comment": "1创建申请 2作废申请", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "D4817274-ED57-4449-ADC9-BB5F4532FFD4"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "95055BBB-058D-423E-87E5-A67DE2E0B288", "defKey": "flow_instance_history", "defName": "流程实例审核历史", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_instance_id", "defName": "流程实例id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "step_id", "defName": "步骤id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "196BE1F2-77E4-4939-99A3-75D63373D8A6"}, {"defKey": "pre_step_id", "defName": "上一个节点的id（从哪个节点来的）", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "A2FA91CD-215F-4935-B7EF-39D350D2BFD6"}, {"defKey": "pre_history_id", "defName": "上一个节点审核历史id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "30BEE415-68E8-41C9-8161-4C4736F1E099"}, {"defKey": "is_return", "defName": "是否是退回的节点", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "2C38CFEC-85A8-49CB-AD86-FF890D494C7E"}, {"defKey": "step_name", "defName": "步骤名", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "32A741B0-B075-4DC7-9BE5-51A07E01C67E"}, {"defKey": "node_type", "defName": "节点类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "160B73B7-8DB2-4CD5-B51A-6B0B51DC02B1"}, {"defKey": "status", "defName": "当前状态", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "0038AA38-CB2F-42EF-A319-E9ED90ED5334"}, {"defKey": "approval_users", "defName": "匹配的所有审批人", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "1121FE4E-3231-4B22-9CCA-E9CAF704C254"}, {"defKey": "condition", "defName": "存在多个审批人时的审批方式", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "31134135-8C16-4331-86DB-65A917A959A6"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "4039E8BD-A195-4759-BEFF-B644CA3C259A"}, {"defKey": "finishd_at", "defName": "完成时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "F0699B94-AD5C-4184-AB32-A0F3B962A868"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "14E7DD2E-CD9C-407B-A6B0-948D3E667580"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "E9CC11A1-02D9-484E-B893-B7571541D31E"}, {"defKey": "tenant_id", "defName": "租户id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "5FA34514-4BD2-4E76-B615-DD06AED804AA"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "0684E1CE-1884-4743-AB18-CD5B81156B34", "defKey": "flow_instance_history_auditor", "defName": "流程实例审核人历史", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "flow_instance_history_id", "defName": "审核历史id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "4A247FE3-1E2E-4AA1-87A2-94AF8D7E11EF"}, {"defKey": "flow_instance_id", "defName": "流程实例id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "BB41B07C-21EC-4045-B8DC-597A6023B054"}, {"defKey": "node_type", "defName": "节点类型（用于区分发起人、审批人、抄送人）", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "E861F550-E6D6-435E-8A53-AB6E5B73BCEC"}, {"defKey": "user_id", "defName": "审核人id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "F110A35C-7445-45FB-BD3C-1C23E0810BC6"}, {"defKey": "approval_status", "defName": "审批状态", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "35757D30-EDF1-4B51-B78B-6C08825584AB"}, {"defKey": "is_auto_pass", "defName": "是否是自动通过", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "1AFEC398-9BDF-419D-9BF3-D8B99AA81E5A"}, {"defKey": "flow_instance_history_status", "defName": "节点实例状态", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "C863B6DB-9BE8-4FE8-AC98-49A1C01C0657"}, {"defKey": "flow_instance_history_status_version", "defName": "节点实例状态同步时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "D35340F1-76D5-45DF-9CF4-55EA03D0C97A"}, {"defKey": "flow_instance_status", "defName": "流程实例状态", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "478812EB-C9AD-4E1E-9E07-43F05A1405E5"}, {"defKey": "flow_instance_status_version", "defName": "流程实例状态同步时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "7556FD4A-A834-460E-9F83-62E65D0286C7"}, {"defKey": "return_node_id", "defName": "退回节点id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "2B4D58B3-7E37-4EC2-B272-1603EA4BBFAE"}, {"defKey": "arrival_time", "defName": "到达时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "E7027A37-C426-4039-BC7E-598A3F9DD1ED"}, {"defKey": "completion_time", "defName": "完成时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "A079A0E5-4F52-4DD5-9291-7B5365610EB6"}, {"defKey": "approval_comment", "defName": "审批意见", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "D1D7E405-6A52-4567-A440-187058A15A78"}, {"defKey": "pics", "defName": "图片", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "3EC64402-5427-445A-809B-AD7431FC3EAD"}, {"defKey": "attachment", "defName": "附件", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "7FB53E6D-D870-42FE-8887-BF468254E381"}, {"defKey": "signature", "defName": "签名", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "F6A92DF5-5BF8-42B0-8D56-48FC79A4ACF6"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "52E6E1B9-5CFE-40BC-918A-ABA55AE8A590", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "9B8C009A-9A8C-4794-A5D4-F4A0F7E4DE8D", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0A2FD788-57CD-455C-B54C-BA3AA2B90FDA", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "tenant_id", "defName": "租户id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0209FB12-F850-438D-B7D6-866D426A9471", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "ACB4429A-A552-4522-A89D-44BBDD88CAAC", "defKey": "ad_project", "defName": "项目", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "48374359-D9FC-42CE-9BED-300D52EC4AB2", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "code", "defName": "编号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0680E9FB-E221-4EBA-A13B-42F0BCAB50D0", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "name", "defName": "项目名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "5C88A316-1695-4EC3-96DD-E99687B6C0B5"}, {"defKey": "leader", "defName": "负责人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "8D8E8052-4355-426D-BA62-E6776938AAA6"}, {"defKey": "instruction", "defName": "项目说明", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "D1BBE2AC-94BF-42FB-9016-441E77F8B963"}, {"defKey": "sort_order", "defName": "排序字段", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "26EF6904-FD99-448D-99B5-A9022D79298B"}, {"defKey": "source", "defName": "来源", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "6F3F667F-7800-4AA4-AE2E-77BF913D19A0"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0E6BF9D5-C009-4983-BFE7-F5E19232D0EF", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "updated_by", "defName": "修改人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A985868F-8C60-4EBA-9175-BD1FBA7D791D", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "9463FB03-5D83-454D-AAE3-CC372524053F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "updated_at", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E1461091-B983-4EF9-A49A-8EC8E00F315F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "7ABA1BA3-AF0D-4AD7-9049-AA0A3BD12FA9", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "F8D496E8-2595-4866-B714-A2BFACAEEEF1", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "921BFE74-2692-4C0E-89E8-8E3FC5834AAF", "defKey": "ad_project_role", "defName": "项目角色表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "48374359-D9FC-42CE-9BED-300D52EC4AB2", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "code", "defName": "编号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D0B093BD-07FC-42A4-BE73-F00F6144FDA7", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64"}, {"defKey": "name", "defName": "角色名", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "5C88A316-1695-4EC3-96DD-E99687B6C0B5"}, {"defKey": "is_default", "defName": "是否是默认角色", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "8D8E8052-4355-426D-BA62-E6776938AAA6"}, {"defKey": "instruction", "defName": "角色说明", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "D1BBE2AC-94BF-42FB-9016-441E77F8B963"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0E6BF9D5-C009-4983-BFE7-F5E19232D0EF", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "updated_by", "defName": "修改人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A985868F-8C60-4EBA-9175-BD1FBA7D791D", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "9463FB03-5D83-454D-AAE3-CC372524053F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "updated_at", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E1461091-B983-4EF9-A49A-8EC8E00F315F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "7ABA1BA3-AF0D-4AD7-9049-AA0A3BD12FA9", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "F8D496E8-2595-4866-B714-A2BFACAEEEF1", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "B54442DD-2D32-4635-8B97-2B4B11CF2FD4", "defKey": "sys_user_project", "defName": "用户项目关联表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "关联ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1DAE02AC-2244-4C48-BFDC-CBB2B1A16AC8", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "user_id", "defName": "用户ID", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "152C150D-E1A2-4EFA-B7E3-87CC9531FF15", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "project_id", "defName": "项目id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "B7181548-7487-453D-99E4-597E4B917490", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "project_role_id", "defName": "项目角色id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "FA34353F-9D72-41F9-A109-8B752342CD19"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "ACBEB7A5-57B9-4E55-BE1D-4BD4A1906789", "defKey": "am_config", "defName": "自动化配置", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "自动化名称", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "自动化说明", "comment": "", "domain": "3E948CEC-3070-472C-AF92-F3CA11EC9D15", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_id", "defName": "关联的表单id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "trigger_data", "defName": "触发器数据", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "62AEF091-2A73-4466-9B4A-8AD85BAA6048"}, {"defKey": "flow_schema", "defName": "自动化流程设计整体结构", "comment": "", "domain": "", "type": "JSON", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "status", "defName": "状态", "comment": "1 正常 999 停止", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "8C10790D-F2A5-4271-A085-EF567C0AE48E"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "version", "defName": "当前版本", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "0976B72D-E716-48CA-83FB-7A945BE5FDF9"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "791FB72A-4835-4CFB-8FF0-92F476380952", "defKey": "am_config_history", "defName": "自动化执行记录", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "am_config_id", "defName": "关联的自动化配置id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "status", "defName": "状态", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "3967741B-2805-4429-8002-D6B603E257DE"}, {"defKey": "result_code", "defName": "执行结果", "comment": "1 成功 999 失败", "domain": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "result_msg", "defName": "执行结果MSG", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "data_snap", "defName": "数据源快照", "comment": "", "domain": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "result_snap", "defName": "结果快照", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "2DAFEC2E-632E-44E0-A063-99E2ED70C64F"}, {"defKey": "node_log", "defName": "节点详细日志", "comment": "每个节点的执行信息、堆栈信息", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "1CD17330-415F-4F27-9F53-147E7B3F93E8"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "4BF22BCB-2558-4886-8202-2D7A95A34982", "defKey": "form_print_template", "defName": "表单打印模板", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "模板名", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "模板说明", "comment": "", "domain": "3E948CEC-3070-472C-AF92-F3CA11EC9D15", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_id", "defName": "关联的模板id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "enter_with_data", "defName": "关联表字段设置", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "40441735-EAD8-4251-881B-847E8E42154A"}, {"defKey": "template_content", "defName": "模版内容", "comment": "", "domain": "", "type": "TEXT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "B17BDED3-085F-40E1-9019-3B79CF2BF075", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "print_size", "defName": "纸张方向", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "0617EB02-165C-4E90-AE4A-65E90EC67E4E"}, {"defKey": "print_rotation", "defName": "尺寸大小", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "FEB13290-6CEB-4FC4-AC55-2DC67FB7DDE9"}, {"defKey": "status", "defName": "状态", "comment": "1 正常 999 停止", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "8C10790D-F2A5-4271-A085-EF567C0AE48E"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "289E2BA5-F320-4587-8D84-E0B4D285A1B6", "defKey": "form_check_rule", "defName": "表单验证规则", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "验证规则名称", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "规则说明", "comment": "", "domain": "3E948CEC-3070-472C-AF92-F3CA11EC9D15", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_id", "defName": "关联的模板id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "code_content", "defName": "代码内容", "comment": "", "domain": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "fail_msg", "defName": "验证失败提示内容", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "F52F1399-35C2-4C6D-9DB3-F463F6D71D80"}, {"defKey": "status", "defName": "状态", "comment": "1 正常 999 停止", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "8C10790D-F2A5-4271-A085-EF567C0AE48E"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "0454CB4B-93A2-4392-A75D-21888712CA80", "defKey": "code_history", "defName": "代码库（用于保存一些代码片段方便下次使用）", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "48374359-D9FC-42CE-9BED-300D52EC4AB2", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "title", "defName": "代码库标题", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "5C88A316-1695-4EC3-96DD-E99687B6C0B5"}, {"defKey": "instruction", "defName": "代码库说明", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "D1BBE2AC-94BF-42FB-9016-441E77F8B963"}, {"defKey": "content", "defName": "代码内容", "comment": "", "type": "TEXT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "B17BDED3-085F-40E1-9019-3B79CF2BF075", "extProps": {}, "domain": "", "id": "1A498335-CE50-49F0-9D8E-2A1428B110B5"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "0E6BF9D5-C009-4983-BFE7-F5E19232D0EF", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "updated_by", "defName": "修改人", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A985868F-8C60-4EBA-9175-BD1FBA7D791D", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "9463FB03-5D83-454D-AAE3-CC372524053F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "updated_at", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "E1461091-B983-4EF9-A49A-8EC8E00F315F", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "7ABA1BA3-AF0D-4AD7-9049-AA0A3BD12FA9", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "notes": {}, "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "F8D496E8-2595-4866-B714-A2BFACAEEEF1", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "AC111FEE-627B-4B61-B3B9-4CDEBB0B9867", "defKey": "form_import_template", "defName": "表单数据导入模板", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "模板名称", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "规则说明", "comment": "", "domain": "3E948CEC-3070-472C-AF92-F3CA11EC9D15", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_id", "defName": "关联的模板id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "start_row", "defName": "开始行数", "comment": "", "domain": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "field_mapping", "defName": "字段映射", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "F398D5AE-2E22-4589-A974-19E75363BBF2"}, {"defKey": "import_template", "defName": "导入模板", "comment": "", "type": "VARCHAR", "len": 512, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "834F0FC1-DD68-4475-8BB5-2B94B9AD7C6C"}, {"defKey": "duplicate_data_validation", "defName": "是否进行数据重复验证", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "9BCFD9EE-5D33-407D-9E6F-9CD681DB3862"}, {"defKey": "duplicate_check_field", "defName": "重复验证的标识字段", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "1FC43EDD-51E3-4F7C-86D3-2484A90C5013"}, {"defKey": "duplicate_handling_method", "defName": "数据重复时的处理方式", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "F52F1399-35C2-4C6D-9DB3-F463F6D71D80"}, {"defKey": "status", "defName": "状态", "comment": "1 正常 999 停止", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "8C10790D-F2A5-4271-A085-EF567C0AE48E"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "184084DB-BB18-4273-B79A-8024B935CF6A", "defKey": "form_import_history", "defName": "数据导入历史纪录", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_import_template_id", "defName": "导入的数据模板id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_id", "defName": "导入的表单模板id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "import_file", "defName": "导入的文件", "comment": "", "type": "VARCHAR", "len": 512, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "status", "defName": "状态", "comment": "", "domain": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "result", "defName": "结果信息", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "F398D5AE-2E22-4589-A974-19E75363BBF2"}, {"defKey": "total_count", "defName": "总数", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "126B2A6D-7577-4ADC-8B79-6CA03951D9D5"}, {"defKey": "completed_count", "defName": "已处理数", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "5ADA8701-7D05-4B65-88D4-8C00382C5A2B"}, {"defKey": "success_count", "defName": "成功数", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "D513DE71-FF97-46AA-AC85-885231366C0D"}, {"defKey": "failure_count", "defName": "失败数", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "7954F808-FD89-434E-BB99-884C1356E6A0"}, {"defKey": "current_step", "defName": "当前步骤", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "", "id": "F70AABF6-8ABD-466B-8624-2F229C223C8B"}, {"defKey": "finishd_at", "defName": "完成时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "B7C8B007-D400-4E86-9EEE-B295FE72F808"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "43F09F66-7899-4DFA-8CF2-C444EC52E77E", "defKey": "form_export_template", "defName": "表单数据导出模板", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "name", "defName": "模板名称", "comment": "", "domain": "", "type": "VARCHAR", "len": 100, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "description", "defName": "规则说明", "comment": "", "domain": "3E948CEC-3070-472C-AF92-F3CA11EC9D15", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_id", "defName": "关联的模板id", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "field_mapping", "defName": "字段映射", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "F398D5AE-2E22-4589-A974-19E75363BBF2"}, {"defKey": "status", "defName": "状态", "comment": "1 正常 999 停止", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "8C10790D-F2A5-4271-A085-EF567C0AE48E"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}, {"id": "8585EE4E-DF86-45B3-B876-E66C4BABC101", "defKey": "form_export_history", "defName": "数据导出历史纪录", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "1AB52C6C-D363-49AB-88DB-90AB649F9BD7", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_export_template_id", "defName": "导出的数据模板id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "42F42054-44EB-4CBF-9637-ED7BEDF25134", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "form_template_id", "defName": "导出的表单模板id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "4035C43A-73FE-4926-BD46-8F0C151685E1", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "filter_rules", "defName": "数据过滤规则", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "58C59A58-46AA-42D0-8BCC-BDC3F1C53E28"}, {"defKey": "export_file", "defName": "导出的文件", "comment": "", "type": "VARCHAR", "len": 512, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "extProps": {}, "domain": "", "id": "4C77B3C6-D895-40FA-8CDE-3E7033267A98"}, {"defKey": "status", "defName": "状态", "comment": "", "domain": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "05E844EF-E95C-488D-ADBC-9CB1D44897D9", "baseType": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "result", "defName": "结果信息", "comment": "", "type": "json", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "3F905A3E-0471-4642-944F-81D473A5DB7C", "extProps": {}, "domain": "", "id": "F398D5AE-2E22-4589-A974-19E75363BBF2"}, {"defKey": "total_count", "defName": "总数", "comment": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "domain": "", "id": "126B2A6D-7577-4ADC-8B79-6CA03951D9D5"}, {"defKey": "finishd_at", "defName": "完成时间", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "domain": "", "id": "B7C8B007-D400-4E86-9EEE-B295FE72F808"}, {"defKey": "created_by", "defName": "创建人", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "D29A324F-0C5B-4114-A313-DCF3DFB12411", "baseType": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "20821F9F-7390-4F94-B17E-C7523130E256", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "A97F63F2-03B7-4DEF-9B48-5E5A37EE133A", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "deleted_at", "defName": "删除时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "10BA2DAF-437C-42B0-9011-114354DE2EF1", "baseType": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "tenant_id", "defName": "数据租户id", "comment": "", "domain": "", "type": "BIGINT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "attr1": "", "attr2": "", "attr3": "", "attr4": "", "attr5": "", "attr6": "", "attr7": "", "attr8": "", "attr9": "", "id": "3F4F9604-6693-46DE-86F7-A5294802C361", "baseType": "", "extProps": {}, "uiHint": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}], "indexes": [], "sysProps": {"nameTemplate": "{defKey}[{defName}]"}, "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": [], "notes": {}}], "views": [], "dicts": [], "viewGroups": [{"defKey": "基础设置", "defName": "", "refEntities": ["2D5DD4E9-F409-49B0-8911-04BA56EC6220", "3FDB8EE0-9321-4793-9E25-2F828F27162E", "3FEFA0E4-5E9D-4083-839F-174C2914F87F", "BBC46C7D-081F-427C-8A24-98518838C21A", "4BAD36BC-457D-4A3C-8F53-4BA3A8F93D8B", "F58691BC-3E7F-45F2-8630-7922DEE440AB", "B85E2A45-41AE-40E4-BF6E-4270AB336A34", "93C6040D-E0A2-41A7-9D6F-BF9D7F2B59B7", "F40B1F15-B03E-47FB-B294-D27A6CFF2D07", "49B8753B-1170-4351-9212-C850806B320F", "ACB4429A-A552-4522-A89D-44BBDD88CAAC", "921BFE74-2692-4C0E-89E8-8E3FC5834AAF", "B54442DD-2D32-4635-8B97-2B4B11CF2FD4", "0454CB4B-93A2-4392-A75D-21888712CA80"], "refViews": [], "refDiagrams": ["49150121-F479-4F75-AFF9-F38FCA530938"], "refDicts": [], "id": "0BDDF289-23D1-442C-B7A3-7A81B86BEBAA", "refLogicEntities": []}, {"defKey": "表单和流程", "defName": "", "refEntities": ["CC1C655C-5F2C-4356-92B6-E41E089BA194", "CF9430B4-C3B8-4F65-A519-53E7BE3C4DEC", "95055BBB-058D-423E-87E5-A67DE2E0B288", "387E663A-4347-43A2-8DC2-EA750666EEDE", "0684E1CE-1884-4743-AB18-CD5B81156B34", "ACBEB7A5-57B9-4E55-BE1D-4BD4A1906789", "791FB72A-4835-4CFB-8FF0-92F476380952", "4BF22BCB-2558-4886-8202-2D7A95A34982", "289E2BA5-F320-4587-8D84-E0B4D285A1B6", "AC111FEE-627B-4B61-B3B9-4CDEBB0B9867", "184084DB-BB18-4273-B79A-8024B935CF6A", "43F09F66-7899-4DFA-8CF2-C444EC52E77E", "8585EE4E-DF86-45B3-B876-E66C4BABC101"], "refViews": [], "refDiagrams": ["CD9CA15E-6D27-4381-ADCB-61572C5F3F45"], "refDicts": [], "id": "535C18B8-6EA0-4A98-AEAA-1AE615C2F6E5", "refLogicEntities": []}], "dataTypeMapping": {"referURL": "", "mappings": [{"defKey": "string", "id": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "defName": "字串", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "VARCHAR", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "VARCHAR2", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "VARCHAR", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "VARCHAR", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "VARCHAR", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "VARCHAR2", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "VARCHAR", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "VARCHAR", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "string", "BDF457FD-9F98-4AC3-A705-7587B00A3BAB": "String", "56F4B55B-F0B8-4049-9E6B-50B95C1D793A": "VARCHAR", "483F9346-C99E-4014-A1D2-A554606BD8A3": "VARCHAR", "ABF5836C-0B7C-4007-A41C-F869325E5842": "VARCHAR"}, {"defKey": "double", "id": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "defName": "小数", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "DECIMAL", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "DECIMAL", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "DECIMAL", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "NUMERIC", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "DECIMAL", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "DECIMAL", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "NUMERIC", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "NUMERIC", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "DOUBLE", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "REAL", "797A1496-D649-4261-89B4-544132EC3F36": "Double", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Double", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Double", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "decimal", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "double", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*float64", "BDF457FD-9F98-4AC3-A705-7587B00A3BAB": "f64", "56F4B55B-F0B8-4049-9E6B-50B95C1D793A": "DECIMAL", "483F9346-C99E-4014-A1D2-A554606BD8A3": "DECIMAL", "ABF5836C-0B7C-4007-A41C-F869325E5842": "NUMERIC"}, {"defKey": "int", "id": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "defName": "整数", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "INT", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "INT", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "INT", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "INTEGER", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "INT", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "INTEGER", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "INTEGER", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "INT4", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "INT", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "INTEGER", "797A1496-D649-4261-89B4-544132EC3F36": "Integer", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Integer", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Integer", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "float", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "int", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*int", "BDF457FD-9F98-4AC3-A705-7587B00A3BAB": "i32", "56F4B55B-F0B8-4049-9E6B-50B95C1D793A": "INTEGER", "483F9346-C99E-4014-A1D2-A554606BD8A3": "INT", "ABF5836C-0B7C-4007-A41C-F869325E5842": "INTEGER"}, {"defKey": "date", "id": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "defName": "日期", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "DATETIME", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "DATE", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "DATETIME", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "TIMESTAMP", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "DATE", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "DATE", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "DATE", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "DATE", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "DATETIME", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "NUMERIC", "797A1496-D649-4261-89B4-544132EC3F36": "Date", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Date", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Date", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "DateTime", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "timestamp", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*time.Time", "BDF457FD-9F98-4AC3-A705-7587B00A3BAB": "DateTime<Local>", "56F4B55B-F0B8-4049-9E6B-50B95C1D793A": "DATE", "483F9346-C99E-4014-A1D2-A554606BD8A3": "DATETIME", "ABF5836C-0B7C-4007-A41C-F869325E5842": "DATE"}, {"defKey": "bytes", "id": "D516E75B-90F5-4741-B9B3-A186A263F04C", "defName": "二进制", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "BLOB", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "BLOB", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "VARBINARY", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "BYTEA", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "BLOB", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "BLOB", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "BYTEA", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "BYTEA", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "BINARY", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "NONE", "797A1496-D649-4261-89B4-544132EC3F36": "byte[]", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "byte[]", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "byte[]", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "binary", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "binary", "B91D99E0-9B7C-416C-8737-B760957DAF09": "[]byte", "56F4B55B-F0B8-4049-9E6B-50B95C1D793A": "BYTE", "ABF5836C-0B7C-4007-A41C-F869325E5842": "BYTEA"}, {"defKey": "largeText", "id": "B17BDED3-085F-40E1-9019-3B79CF2BF075", "defName": "大文本", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "TEXT", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "CLOB", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "TEXT", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "TEXT", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "CLOB", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "CLOB", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "TEXT", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "TEXT", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "string", "BDF457FD-9F98-4AC3-A705-7587B00A3BAB": "String", "56F4B55B-F0B8-4049-9E6B-50B95C1D793A": "TEXT", "483F9346-C99E-4014-A1D2-A554606BD8A3": "STRING", "ABF5836C-0B7C-4007-A41C-F869325E5842": "TEXT"}, {"defKey": "JSON", "id": "3F905A3E-0471-4642-944F-81D473A5DB7C", "defName": "JSON", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "json", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string"}, {"defKey": "大整数", "defName": "", "id": "E1247FDC-2AA1-4EF5-9C74-40D9BEA9CB5A", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "BIGINT"}]}, "domains": [{"defKey": "DefaultString", "defName": "默认字串", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 255, "scale": "", "uiHint": "", "id": "9092C4E0-1A54-4859-ABBB-5B62DBC27573"}, {"defKey": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "defName": "主键标识", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 32, "scale": "", "uiHint": "", "id": "16120F75-6AA7-4483-868D-F07F511BB081"}, {"defKey": "Name", "defName": "名称", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 90, "scale": "", "uiHint": "", "id": "54611CCC-CA4B-42E1-9F32-4944C85B85A6"}, {"defKey": "Int", "defName": "整数", "applyFor": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "len": "", "scale": "", "uiHint": "", "id": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E"}, {"defKey": "Double", "defName": "小数", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 24, "scale": 6, "uiHint": "", "id": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4"}, {"defKey": "Money", "defName": "金额", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 24, "scale": 6, "uiHint": "", "id": "C3B1681B-99F9-4818-9E80-DE1652A51D85"}, {"defKey": "DateTime", "defName": "日期时间", "applyFor": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "len": "", "scale": "", "uiHint": "", "id": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC"}, {"defKey": "YesNo", "defName": "是否", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": "1", "scale": "", "uiHint": "", "id": "6F7C1C5C-D159-41E6-BF9D-54DEEFA79AFF"}, {"defKey": "Dict", "defName": "数据字典", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": "32", "scale": "", "uiHint": "", "id": "73FD2BAD-2358-4336-B96D-45DC897BD792"}, {"defKey": "DescText", "defName": "描述文本", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": "900", "scale": "", "uiHint": "", "id": "3E948CEC-3070-472C-AF92-F3CA11EC9D15"}], "diagrams": [{"defKey": "base_settle", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "41702d4b-7cd8-40cb-9a02-47babbe0b337", "shape": "table", "position": {"x": -160, "y": 500.0000000000002}, "count": 0, "originKey": "3FDB8EE0-9321-4793-9E25-2F828F27162E", "type": "P", "size": {"width": 402, "height": 238}}, {"id": "ca9b72b8-11fc-48bc-b81c-2456dd98d745", "shape": "table", "position": {"x": -700, "y": 500}, "count": 0, "originKey": "2D5DD4E9-F409-49B0-8911-04BA56EC6220", "type": "P", "size": {"width": 498, "height": 399}}, {"id": "807c5b45-a690-4c03-9224-9ca3a6abf2d6", "shape": "table", "position": {"x": -700, "y": 930}, "count": 0, "originKey": "B85E2A45-41AE-40E4-BF6E-4270AB336A34", "type": "P", "size": {"width": 314, "height": 100}}, {"id": "e6f16536-01b0-465e-b788-8cb7a4834fc8", "shape": "table", "position": {"x": 288.99999999999835, "y": 1223}, "count": 0, "originKey": "F40B1F15-B03E-47FB-B294-D27A6CFF2D07", "type": "P", "size": {"width": 310, "height": 100}}, {"id": "df300af7-f31c-41ea-a369-cf01d5b0604e", "shape": "table", "position": {"x": 286.99999999999835, "y": 1342}, "count": 0, "originKey": "4BAD36BC-457D-4A3C-8F53-4BA3A8F93D8B", "type": "P", "size": {"width": 314, "height": 100}}, {"id": "9b3073f9-39c2-4dfe-9acb-474813c4fa22", "shape": "table", "position": {"x": 288.99999999999835, "y": 1469}, "count": 0, "originKey": "49B8753B-1170-4351-9212-C850806B320F", "type": "P", "size": {"width": 310, "height": 100}}, {"id": "cf33f5d7-73be-4938-a3e0-570ba8b753bc", "shape": "table", "position": {"x": 286.9999999999984, "y": 1589}, "count": 0, "originKey": "B54442DD-2D32-4635-8B97-2B4B11CF2FD4", "type": "P", "size": {"width": 310, "height": 140}}, {"id": "9b593642-8563-44a3-8673-5febb98048ec", "shape": "table", "position": {"x": 1160, "y": 490}, "count": 0, "originKey": "93C6040D-E0A2-41A7-9D6F-BF9D7F2B59B7", "type": "P", "size": {"width": 386, "height": 353}}, {"id": "ac406d2f-160f-4be7-9d97-ffbc664d37f8", "shape": "table", "position": {"x": 1160, "y": 873}, "count": 0, "originKey": "F58691BC-3E7F-45F2-8630-7922DEE440AB", "type": "P", "size": {"width": 310, "height": 100}}, {"id": "26c70af3-40dc-4e76-8f4b-bc85108d1cac", "shape": "table", "position": {"x": 733, "y": 500}, "count": 0, "originKey": "BBC46C7D-081F-427C-8A24-98518838C21A", "type": "P", "size": {"width": 386, "height": 353}}, {"id": "b9b0a92c-2807-4079-9975-04364a7aaee4", "shape": "table", "position": {"x": 288.99999999999835, "y": 500.00000000000045}, "count": 0, "originKey": "3FEFA0E4-5E9D-4083-839F-174C2914F87F", "type": "P", "size": {"width": 410, "height": 700}}, {"id": "e6003f5f-2ed0-433c-a1a2-c5976888baf6", "shape": "table", "position": {"x": 1589.9999999999998, "y": 480.0000000000001}, "count": 0, "originKey": "ACB4429A-A552-4522-A89D-44BBDD88CAAC", "type": "P", "size": {"width": 390, "height": 330}}, {"id": "c89283c6-e166-413e-ae18-00b730c37b7c", "shape": "table", "position": {"x": 1589.9999999999998, "y": 853.0000000000006}, "count": 0, "originKey": "921BFE74-2692-4C0E-89E8-8E3FC5834AAF", "type": "P", "size": {"width": 390, "height": 280}}, {"id": "852ba597-3dd3-4338-862a-c07a5dad108e", "shape": "table", "position": {"x": 2030, "y": 479.0000000000002}, "count": 0, "originKey": "0454CB4B-93A2-4392-A75D-21888712CA80", "type": "P", "size": {"width": 390, "height": 280}}]}, "id": "49150121-F479-4F75-AFF9-F38FCA530938", "comment": ""}, {"defKey": "表单流程", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "05f37ab8-6fa6-4bb2-a7c4-efdc940f76e5", "shape": "table", "position": {"x": 20.000000000000313, "y": 670.0000000000003}, "count": 0, "originKey": "95055BBB-058D-423E-87E5-A67DE2E0B288", "type": "P", "size": {"width": 420, "height": 300}}, {"id": "ee3a1d17-b2c7-4154-b962-c639731a00e3", "shape": "table", "position": {"x": 20.000000000000313, "y": 250.00000000000034}, "count": 0, "originKey": "CF9430B4-C3B8-4F65-A519-53E7BE3C4DEC", "type": "P", "size": {"width": 420, "height": 340}}, {"id": "5e86194b-7dd0-4259-9227-74e201de3142", "shape": "table", "position": {"x": 20, "y": -110}, "count": 0, "originKey": "CC1C655C-5F2C-4356-92B6-E41E089BA194", "type": "P", "size": {"width": 420, "height": 261}}, {"id": "c54ce791-2316-46ce-b122-948b6fe7a13b", "shape": "table", "position": {"x": 20.000000000000313, "y": 1030.0000000000005}, "count": 0, "originKey": "0684E1CE-1884-4743-AB18-CD5B81156B34", "type": "P", "size": {"width": 420, "height": 300}}, {"id": "b00f8f73-08cd-44ca-84a0-3ac8737e47d5", "shape": "table", "position": {"x": 1590, "y": -109.99999999999991}, "count": 0, "originKey": "4BF22BCB-2558-4886-8202-2D7A95A34982", "type": "P", "size": {"width": 470, "height": 380}}, {"id": "3d46432b-365d-455f-b248-f2cd4e19da62", "shape": "table", "position": {"x": 529.9999999999989, "y": -109.99999999999895}, "count": 0, "originKey": "387E663A-4347-43A2-8DC2-EA750666EEDE", "type": "P", "size": {"width": 480, "height": 600}}, {"id": "196dfb6b-e5f5-44db-903e-9fc8b014ee4b", "shape": "table", "position": {"x": 1590, "y": 1179.999999999999}, "count": 0, "originKey": "43F09F66-7899-4DFA-8CF2-C444EC52E77E", "type": "P", "size": {"width": 470, "height": 330}}, {"id": "b80cd603-3f0e-4dc8-9e08-be712bfc8e22", "shape": "table", "position": {"x": 2110, "y": 1179.9999999999977}, "count": 0, "originKey": "8585EE4E-DF86-45B3-B876-E66C4BABC101", "type": "P", "size": {"width": 470, "height": 400}}, {"id": "70353d03-7046-4868-a7f3-617566ce9d24", "shape": "table", "position": {"x": 2110, "y": 700.0000000000007}, "count": 0, "originKey": "184084DB-BB18-4273-B79A-8024B935CF6A", "type": "P", "size": {"width": 470, "height": 430}}, {"id": "d48bc6cb-8610-49ec-8565-e061c0938cb5", "shape": "table", "position": {"x": 1590, "y": 290.00000000000034}, "count": 0, "originKey": "289E2BA5-F320-4587-8D84-E0B4D285A1B6", "type": "P", "size": {"width": 470, "height": 380}}, {"id": "7110f65b-1319-4646-9ee9-14b12cb58ffc", "shape": "table", "position": {"x": 1110, "y": -109.99999999999929}, "count": 0, "originKey": "ACBEB7A5-57B9-4E55-BE1D-4BD4A1906789", "type": "P", "size": {"width": 420, "height": 310}}, {"id": "d0cda378-2f76-4aa7-98a2-3253cb3623d3", "shape": "table", "position": {"x": 1590, "y": 700.0000000000018}, "count": 0, "originKey": "AC111FEE-627B-4B61-B3B9-4CDEBB0B9867", "type": "P", "size": {"width": 470, "height": 430}}, {"id": "ba81dcf2-eb12-4821-86f0-ab5e9bb94acf", "shape": "table", "position": {"x": 1110, "y": 250.00000000000034}, "count": 0, "originKey": "791FB72A-4835-4CFB-8FF0-92F476380952", "type": "P", "size": {"width": 420, "height": 310}}]}, "id": "CD9CA15E-6D27-4381-ADCB-61572C5F3F45", "comment": ""}], "standardFields": [], "dbConn": [{"defKey": "FB68FAE0-0724-4AF7-8A32-51A722FAE7B9", "defName": "localhost", "type": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "properties": {"driver_class_name": "com.mysql.cj.jdbc.Driver", "url": "***************************************************************************************************************", "password": "123456", "username": "root"}}], "logicEntities": [], "namingRules": [{"id": "63F1DC0E-6A76-4B75-B3DA-4B00657B4E1B", "defName": "属性代码不能超过32", "intro": "", "controlIntensity": "S", "applyObjectType": "L", "applyFieldType": "field", "programCode": "return (data.field.defName||\"\").length <= 32", "enable": true}, {"id": "668CBEE6-E0B7-4ACE-B72E-63942963B191", "defName": "长度不能超过32位", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "return (data.entity.defName||\"\").length <= 32", "enable": true}, {"id": "11BD987F-82E7-418E-A752-FDD84F1582A2", "defName": "长度不能超过32位", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "field", "programCode": "return (data.field.defName||\"\").length <= 32", "enable": true}, {"id": "29D0A8D9-ABE2-451F-8A39-52FAB02E62B9", "defName": "索引名-长度不超过32个字符", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "index", "programCode": "return (data.index.defName||\"\").length <= 32", "enable": true}, {"id": "B425A96F-6A31-4DBD-8743-A00DE28FB50F", "defName": "不能使用保留字", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "index", "programCode": "let sysWords = \"action,add,aggregate,all,alter,after,and,as,asc,avg,avg_row_length,auto_increment,between,bigint,bit,binary,blob,bool,both,by,cascade,case,char,character,change,check,checksum,column,columns,comment,constraint,create,cross,current_date,current_time,current_timestamp,data,database,databases,date,datetime,day,day_hour,day_minute,day_second,dayofmonth,dayofweek,dayofyear,dec,decimal,default,delayed,delay_key_write,delete,desc,describe,distinct,distinctrow,double,drop,end,else,escape,escaped,enclosed,enum,explain,exists,fields,file,first,float,float4,float8,flush,foreign,from,for,full,function,global,grant,grants,group,having,heap,high_priority,hour,hour_minute,hour_second,hosts,identified,ignore,in,index,infile,inner,insert,insert_id,int,integer,interval,int1,int2,int3,int4,int8,into,if,is,isam,join,key,keys,kill,last_insert_id,leading,left,length,like,lines,limit,load,local,lock,logs,long,longblob,longtext,low_priority,max,max_rows,match,mediumblob,mediumtext,mediumint,middleint,min_rows,minute,minute_second,modify,month,monthname,myisam,natural,numeric,no,not,null,on,optimize,option,optionally,or,order,outer,outfile,pack_keys,partial,password,precision,primary,procedure,process,processlist,privileges,read,real,references,reload,regexp,rename,replace,restrict,returns,revoke,rlike,row,rows,second,select,set,show,shutdown,smallint,soname,sql_big_tables,sql_big_selects,sql_low_priority_updates,sql_log_off,sql_log_update,sql_select_limit,sql_small_result,sql_big_result,sql_warnings,straight_join,starting,status,string,table,tables,temporary,terminated,text,then,time,timestamp,tinyblob,tinytext,tinyint,trailing,to,type,use,using,unique,unlock,unsigned,update,usage,values,varchar,variables,varying,varbinary,with,write,when,where,year,year_month,zerofill\".split(\",\");\nreturn sysWords.indexOf(data.index.defKey.toLowerCase())<0;", "enable": true}, {"id": "EF9E44D0-691A-4352-A079-CFF300107531", "defName": "索引名-全小写", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "index", "programCode": "return !/[A-Z]+/.test(data.index.defKey);", "enable": true}, {"id": "972EB2FB-4428-429D-8B0A-F082A8C7A94D", "defName": "名称不能为空", "intro": "", "controlIntensity": "F", "applyObjectType": "L", "applyFieldType": "entity", "programCode": "return data.logicEntity.defName", "enable": true}, {"id": "EEAEB9C5-BB6C-4E92-949B-D27928690D85", "defName": "名称长度不超过32", "intro": "", "controlIntensity": "S", "applyObjectType": "L", "applyFieldType": "entity", "programCode": "return (data.logicEntity.defName||\"\").length <=32", "enable": true}, {"id": "24E3F7E5-730D-4378-B72D-195D6B940352", "defName": "不能使用保留字", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "let sysWords = \"action,add,aggregate,all,alter,after,and,as,asc,avg,avg_row_length,auto_increment,between,bigint,bit,binary,blob,bool,both,by,cascade,case,char,character,change,check,checksum,column,columns,comment,constraint,create,cross,current_date,current_time,current_timestamp,data,database,databases,date,datetime,day,day_hour,day_minute,day_second,dayofmonth,dayofweek,dayofyear,dec,decimal,default,delayed,delay_key_write,delete,desc,describe,distinct,distinctrow,double,drop,end,else,escape,escaped,enclosed,enum,explain,exists,fields,file,first,float,float4,float8,flush,foreign,from,for,full,function,global,grant,grants,group,having,heap,high_priority,hour,hour_minute,hour_second,hosts,identified,ignore,in,index,infile,inner,insert,insert_id,int,integer,interval,int1,int2,int3,int4,int8,into,if,is,isam,join,key,keys,kill,last_insert_id,leading,left,length,like,lines,limit,load,local,lock,logs,long,longblob,longtext,low_priority,max,max_rows,match,mediumblob,mediumtext,mediumint,middleint,min_rows,minute,minute_second,modify,month,monthname,myisam,natural,numeric,no,not,null,on,optimize,option,optionally,or,order,outer,outfile,pack_keys,partial,password,precision,primary,procedure,process,processlist,privileges,read,real,references,reload,regexp,rename,replace,restrict,returns,revoke,rlike,row,rows,second,select,set,show,shutdown,smallint,soname,sql_big_tables,sql_big_selects,sql_low_priority_updates,sql_log_off,sql_log_update,sql_select_limit,sql_small_result,sql_big_result,sql_warnings,straight_join,starting,status,string,table,tables,temporary,terminated,text,then,time,timestamp,tinyblob,tinytext,tinyint,trailing,to,type,use,using,unique,unlock,unsigned,update,usage,values,varchar,variables,varying,varbinary,with,write,when,where,year,year_month,zerofill\".split(\",\");\nreturn sysWords.indexOf(data.entity.defKey.toLowerCase())<0;", "enable": true}, {"id": "039BF435-DC77-4DA4-81C7-7F8076BF22BB", "defName": "表名-全小写", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "return !/[A-Z]+/.test(data.entity.defKey);", "enable": true}, {"id": "CBEB0E30-19C6-427D-A8BF-61FF10E27A0B", "defName": "表名-不允许空格", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "return !/\\s+/.test(data.entity.defKey);", "enable": true}, {"id": "1168C7C2-8E8E-4FB7-B639-B3DE839C395A", "defName": "表名-英文及下划线", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(data.entity.defKey);", "enable": true}, {"id": "D373637C-D3A6-4621-B656-6841A5444A76", "defName": "表必须有comment注释", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "return (data.entity.defName||\"\").length > 0 || (data.entity.comment||\"\").length > 0", "enable": true}, {"id": "2BAB122B-8811-40BB-89F3-CDC24B5862D3", "defName": "主键命名为 id，类型为 int 或 bigint，且为自增", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "let fields = data.entity.fields;\nfor(let i=0;i<fields.length;i++){\n    let field = fields[i];\n    if(field.primaryKey){\n        return field.autoIncrement && (field.dbType.toUpperCase()==\"INT\"||field.dbType==\"BIGINT\");\n    }\n}\nreturn false;", "enable": true}, {"id": "0B2F0BD2-3B84-4AB1-BA29-9DE9620AF608", "defName": "必须有数据的创建时间以及创建人字段", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "entity", "programCode": "let count = 0;\nlet fields = data.entity.fields;\nfor(let i=0;i<fields.length;i++){\n    let field = fields[i];\n    if(\"created_time,updated_time\".indexOf(field.defKey.toLowerCase())>=0){\n        count ++;\n    }\n}\nreturn count==2;", "enable": true}, {"id": "BEC54F19-52D5-4882-BCE1-4439785F8001", "defName": "不能使用保留字", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "field", "programCode": "let sysWords = \"action,add,aggregate,all,alter,after,and,as,asc,avg,avg_row_length,auto_increment,between,bigint,bit,binary,blob,bool,both,by,cascade,case,char,character,change,check,checksum,column,columns,comment,constraint,create,cross,current_date,current_time,current_timestamp,data,database,databases,date,datetime,day,day_hour,day_minute,day_second,dayofmonth,dayofweek,dayofyear,dec,decimal,default,delayed,delay_key_write,delete,desc,describe,distinct,distinctrow,double,drop,end,else,escape,escaped,enclosed,enum,explain,exists,fields,file,first,float,float4,float8,flush,foreign,from,for,full,function,global,grant,grants,group,having,heap,high_priority,hour,hour_minute,hour_second,hosts,identified,ignore,in,index,infile,inner,insert,insert_id,int,integer,interval,int1,int2,int3,int4,int8,into,if,is,isam,join,key,keys,kill,last_insert_id,leading,left,length,like,lines,limit,load,local,lock,logs,long,longblob,longtext,low_priority,max,max_rows,match,mediumblob,mediumtext,mediumint,middleint,min_rows,minute,minute_second,modify,month,monthname,myisam,natural,numeric,no,not,null,on,optimize,option,optionally,or,order,outer,outfile,pack_keys,partial,password,precision,primary,procedure,process,processlist,privileges,read,real,references,reload,regexp,rename,replace,restrict,returns,revoke,rlike,row,rows,second,select,set,show,shutdown,smallint,soname,sql_big_tables,sql_big_selects,sql_low_priority_updates,sql_log_off,sql_log_update,sql_select_limit,sql_small_result,sql_big_result,sql_warnings,straight_join,starting,status,string,table,tables,temporary,terminated,text,then,time,timestamp,tinyblob,tinytext,tinyint,trailing,to,type,use,using,unique,unlock,unsigned,update,usage,values,varchar,variables,varying,varbinary,with,write,when,where,year,year_month,zerofill\".split(\",\");\nreturn sysWords.indexOf(data.field.defKey.toLowerCase())<0;", "enable": true}, {"id": "082E186D-7B02-4F1C-9ECE-378AB98C4845", "defName": "字段-全小写", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "field", "programCode": "return !/[A-Z]+/.test(data.field.defKey);", "enable": true}, {"id": "F3CE5C67-23B6-4E7B-BA91-D5F0BCBC9E6A", "defName": "字段-不允许空格", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "field", "programCode": "return !/\\s+/.test(data.field.defKey);", "enable": true}, {"id": "21AFEAC8-96D7-467F-8320-A33887FC0C5D", "defName": "字段-英文及下划线", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "field", "programCode": "return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(data.field.defKey);", "enable": true}, {"id": "2BBDE47B-6926-4E1A-AE57-D4F6E5399EE6", "defName": "字段-必需有comment注释", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "field", "programCode": "return (data.field.defName||\"\").length > 0 || (data.field.comment||\"\").length > 0", "enable": true}, {"id": "5E181E43-0D72-498F-8178-4C1CDBC89A16", "defName": "字段-不能与表名相同", "intro": "", "controlIntensity": "F", "applyObjectType": "P", "applyFieldType": "field", "programCode": "return data.field.defKey != data.entity.defKey;", "enable": true}, {"id": "DE8F8598-5D53-4727-A837-7816C2AF99D9", "defName": "外键-字段必须具有表名及其主键", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "field", "programCode": "let relas = data.entity.correlations;\nfor(let i=0;i<relas.length;i++){\n    let rela = relas[i];\n    if(data.field.defKey==rela.myField&&rela.myRows==\"n\"){\n        if(rela.myField==(rela.refEntity+\"_\"+rela.refField)){\n            return true;\n        }else{\n            return false;\n        }\n    }\n}\nreturn true;", "enable": true}, {"id": "D330BCC3-DBAB-4677-8C5A-A301003A5878", "defName": "时间字段类型尽量选取 timestamp", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "field", "programCode": "if(data.field.defName.lastIndexOf(\"日期\")>=0 || data.field.defName.lastIndexOf(\"时间\")>=0 ){\n    if(data.field.dbType.toLowerCase().indexOf(\"date\")>=0){\n        return true;\n    }else{\n        return false;\n    }\n};\nreturn true;", "enable": true}, {"id": "2E7FDA44-989A-4C5B-A0C5-12B1E40E57B1", "defName": "索引名-英文及下划线", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "index", "programCode": "return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(data.index.defKey);", "enable": true}, {"id": "023450B3-AAE2-4DC1-AE63-2196DD82823D", "defName": "索引名-主键的名称以pk_开头，唯一键以uk_开头，普通索引以 ix_开头", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "index", "programCode": "if(data.index.unique){\n    return data.index.defKey.indexOf(\"uk_\")==0;\n}else{\n    return data.index.defKey.indexOf(\"ix_\")==0;\n}", "enable": true}, {"id": "1C563E17-262B-4EB6-87F0-203CAC667CF0", "defName": "不允许存在blob、text等大字段", "intro": "", "controlIntensity": "S", "applyObjectType": "P", "applyFieldType": "field", "programCode": "if(\"blob,text\".indexOf(data.field.dbType.toLowerCase())>=0){\n    return false;\n}\nreturn true;", "enable": true}]}